<template>
  <div class="app-container">
    <div class="select-box">
      <div class="top-title">筛选查询</div>
      <div class="select-info">
        <el-form size="mini" :inline="true" label-width="100px">
          <el-form-item label="编号">
            <el-input v-model="searchForm.consultNum" placeholder="请输入编号"></el-input>
          </el-form-item>
          <el-form-item label="被评价客服">
            <el-input v-model="searchForm.handleUserName" placeholder="请输入客服姓名"></el-input>
          </el-form-item>
          <el-form-item label="评价">
            <el-select v-model="searchForm.evaluation">
              <el-option value="" label="不限"></el-option>
              <el-option value="4" label="非常满意"></el-option>
              <el-option value="3" label="满意"></el-option>
              <el-option value="2" label="一般"></el-option>
              <el-option value="1" label="不满意"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="getList" type="primary">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
      </div>
      <div class="list-main">
        <el-table :data="data">
          <el-table-column label="序号" type="index"></el-table-column>
          <el-table-column label="编号" prop="consultNum" show-overflow-tooltip></el-table-column>
          <el-table-column label="被评价客服" prop="handleUserName"></el-table-column>
          <el-table-column label="评价" prop="evaluationEnumString" :formatter="nullValueTableFormatter"></el-table-column>
        </el-table>
        <el-pagination
          :current-page.sync="page.pageNumber"
          @current-change="getList"
          @size-change="handleSizeChange"
          :total="total"
          layout="total, sizes, prev, pager, next"
          class="pagination"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      page: {
        pageSize: 10,
        pageNumber: 1
      },
      searchForm: {},
      total: 0,
      data: []
    }
  },
  activated() {
    this.getList()
  },
  methods: {
    resetSearch() {
      this.searchForm = {}
      this.getList()
    },
    getList() {
      this.$post('/admin-center-server/admin/consult/consultEvaluatePage', {
        ...this.page,
        ...this.searchForm
      })
        .then(res => {
          this.data = res.list
          this.total = Number(res.total)
        })
    },
    handleSizeChange(v) {
      this.page.pageSize = v
      this.page.pageNumber = 1
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.select-box {
  background-color: #ffffff;

  .top-title {
    font-size: 16px;
    font-weight: 700;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    border-bottom: 1px solid #cccccc;
  }

  .select-info {
    padding-top: 30px;
    padding-bottom: 30px;
  }
}
.list-box {
  background-color: #ffffff;
  margin-top: 20px;
  padding: 10px;
  overflow: hidden;

  .list-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    div {
      height: 38px;
      line-height: 38px;
    }
  }

  .list-main {
    width: 100%;
    border: 1px solid #cccccc;
    margin-top: 10px;

    .el-pagination {
      text-align: right;
    }
  }
}
</style>