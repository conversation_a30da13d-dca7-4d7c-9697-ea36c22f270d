<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title flex">
        <div>修改运费</div>
        <el-button size="mini" @click="goReturn" type="primary" style="margin-right:10px">返回</el-button>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title flex">
        <div></div>
        <div>
          <span class="orderStatus">
            当前订单状态:
            <em>{{statusEnum}}</em>
          </span>
          <span class="unitPrice">
            当前订单运费:
            <em>{{freight}}</em>元/吨
          </span>
        </div>
      </div>
      <div class="list-main">
        <template>
          <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
            <el-tab-pane label="按订单修改运费" name="first">
              <div>
                <el-form class="firstContent">
                  <el-form-item label=" 修改说明：按订单修改运费，修改总订单运费单价，下一运单抢单/接单生效" required></el-form-item>
                  <el-form-item label="当前运费（单价）：">
                    <span>
                      <em>{{freight}}</em> 元/吨
                    </span>
                  </el-form-item>
                  <el-form-item label="修改后运费（单价）：">
                    <el-input
                      placeholder="请输入要修改的运费单价"
                      v-model="changFeeByOrder"
                      oninput="value=value.match(/\d+\.?\d{0,2}/,'')"
                      style="width:300px"
                    >
                      <template slot="append">元/吨</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="修改运费目标：">
                    <el-radio-group v-model="radio" @change="getValue()">
                      <el-radio v-model="radio" label="0">修改司机运费</el-radio>
                      <el-radio v-model="radio" label="1">修改信息费</el-radio>
                    </el-radio-group>

                    <span>修改信息费结果大于总运费35%时，修改失败</span>
                  </el-form-item>
                </el-form>
              </div>
              <div class="footer">
                <el-button class="confirmBtn" type="success" @click="sureByOrder" :disabled="btnClickFlag">确定</el-button>
              </div>
            </el-tab-pane>
            <el-tab-pane label="按运单修改运费" name="second">
              <el-form class="secondContent">
                <el-form-item
                  label="修改说明：订单内所有待装货、待卸货、待审核、待结算的运单均可自行选择某一个或多个运单运费修改。 以下运单均不包含调度员或货主已结算运单或已加
                 入结算单运单"
                  required
                ></el-form-item>

                <el-form-item label="修改运费目标：" v-if="!isSelect">
                  <el-radio-group v-model="wayBillType" @change="getWayBillTypeRadio()">
                    <el-radio v-model="wayBillType" label>全部</el-radio>
                    <el-radio v-model="wayBillType" label="0">待装货</el-radio>
                    <el-radio v-model="wayBillType" label="1">待卸货</el-radio>
                    <el-radio v-model="wayBillType" label="3">待审核</el-radio>
                    <el-radio v-model="wayBillType" label="10">待结算</el-radio>
                  </el-radio-group>
                  <span style="margin-left:30px">
                    <el-button @click="confirmSel" size="small">确认选择</el-button>
                  </span>
                  <span>
                    <el-button
                      @click="selectAndChange"
                      size="small"
                      style="float:right"
                      type="primary"
                    >确认选择并修改运费</el-button>
                  </span>
                </el-form-item>
                <div v-if="isSelect">
                  <el-form-item label="当前运费（单价）：">
                    <span>
                      <em>{{freight}}</em> 元/吨
                    </span>
                  </el-form-item>
                  <el-form-item label="修改后运费（单价）：">
                    <el-input
                      placeholder="请输入要修改的运费单价"
                      v-model="changedFee"
                      oninput="value=value.match(/\d+\.?\d{0,2}/,'')"
                      style="width:300px"
                    >
                      <template slot="append">元/吨</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="修改运费目标：" v-model="radio2">
                    <el-radio v-model="radio2" label="0">修改司机运费</el-radio>
                    <el-radio v-model="radio2" label="1">修改信息费</el-radio>
                    <span>修改信息费结果大于总运费35%时，修改失败</span>
                  </el-form-item>
                  <div class="thirdFooter">
                    <el-button size="small" @click="goBack">返回选择</el-button>
                    <el-button size="small" type="success" @click="wayBillConfirm" :disabled="btnClickFlag">确定</el-button>
                  </div>
                </div>

                <div class="selectTable" v-if="!isSelect">
                  <div class="selLeft">
                    <div class="search">
                      <el-input
                        v-model="wayBillSn"
                        placeholder="查询运单号"
                        style="width:200px"
                        size="small"
                      ></el-input>
                      <span>或</span>
                      <el-input
                        v-model="carNum"
                        placeholder="查询车牌号"
                        style="width:200px"
                        size="small"
                      ></el-input>
                      <el-button
                        type="primary"
                        icon="el-icon-search"
                        size="mini"
                        style="margin-left:5px"
                        @click="serchWayBill"
                      >搜索</el-button>
                    </div>
                    <div class="selBtn">
                      <el-button size="mini" @click="toggleSelection(tableData)">页面全选</el-button>
                      <!-- <el-button size="mini" @click="selectAllList">列表全选</el-button> -->
                    </div>
                    <el-table
                      ref="multipleTable"
                      :data="tableData"
                      tooltip-effect="dark"
                      border
                      style="width: 100%"
                      @selection-change="handleSelectionChange"
                    >
                      <el-table-column type="selection" width="55"></el-table-column>
                      <el-table-column prop="itemSn" label="运单编号" width="200"></el-table-column>
                      <el-table-column prop="carNumber" label="车牌号" width="100"></el-table-column>
                      <el-table-column prop="freight" label="运费单价(元/吨)" width="130"></el-table-column>
                      <el-table-column prop="statusTypeName" label="运单状态"></el-table-column>
                    </el-table>
                    <el-pagination
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="pageNumber"
                      :page-sizes="[20, 40, 60, 80,100]"
                      :page-size="pageSize"
                      layout="total, sizes, prev, pager, next, jumper"
                      :total="total"
                      style="margin: 10px auto"
                    ></el-pagination>
                  </div>
                  <div class="selRight">
                    <div class="search">
                      <el-input
                        v-model="selectWayBillSn"
                        placeholder="查询运单号"
                        style="width:200px"
                        size="small"
                      ></el-input>
                      <span>或</span>
                      <el-input
                        v-model="selectCarNum"
                        placeholder="查询车牌号"
                        style="width:200px"
                        size="small"
                      ></el-input>
                      <el-button
                        type="primary"
                        icon="el-icon-search"
                        size="mini"
                        style="margin-left:5px"
                        @click="selectWayBill"
                      >搜索</el-button>
                    </div>
                    <div class="flex">
                      <div>
                        <el-button size="mini" @click="removeSel">移除选择</el-button>
                      </div>
                      <div class="selBtn">
                        <el-button size="mini" @click="toggleSelection1(tableData1)">页面全选</el-button>
                      </div>
                    </div>

                    <el-table
                      :data="tableData1"
                      ref="multipleTable1"
                      tooltip-effect="dark"
                      border
                      style="width: 100%"
                      @selection-change="handleRemoveChange"
                    >
                      <el-table-column type="selection" width="55"></el-table-column>
                      <el-table-column prop="itemSn" label="运单编号" width="220"></el-table-column>
                      <el-table-column prop="carNumber" label="车牌号" width="100"></el-table-column>
                      <el-table-column prop="freight" label="运费单价(元/吨)" width="130"></el-table-column>
                      <el-table-column prop="statusTypeName" label="运单状态"></el-table-column>
                    </el-table>
                  </div>
                </div>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="按运单状态修改运费" name="third">
              <el-form class="thirdContent">
                <el-form-item label="修改说明：按运单状态修改运费，下列运单状态支持多项同时选择，选择后该状态下的运单生效。" required></el-form-item>
                <el-form-item>
                  <el-checkbox-group v-model="checkList">
                    <el-checkbox label="0">待装货</el-checkbox>
                    <el-checkbox label="1">待卸货</el-checkbox>
                    <el-checkbox label="3">待审核</el-checkbox>
                    <el-checkbox label="10">待结算（未生成结算单运费生效）</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="当前运费（单价）：">
                  <span>
                    <em>{{freight}}</em> 元/吨
                  </span>
                </el-form-item>
                <el-form-item label="修改后运费（单价）：">
                  <el-input
                    placeholder="请输入要修改的运费单价"
                    v-model="changedFeeByStatus"
                    oninput="value=value.match(/\d+\.?\d{0,2}/,'')"
                    style="width:300px"
                  >
                    <template slot="append">元/吨</template>
                  </el-input>
                </el-form-item>
                <el-form-item label="修改运费目标：" v-model="radio3">
                  <el-radio v-model="radio3" label="0">修改司机运费</el-radio>
                  <el-radio v-model="radio3" label="1">修改信息费</el-radio>
                  <span>修改信息费结果大于总运费35%时，修改失败</span>
                </el-form-item>
                <div class="thirdFooter">
                  <el-button size="small" type="success" @click="confirmByStatus" :disabled="btnClickFlag">确定</el-button>
                </div>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </template>
      </div>
    </div>
  </div>
</template>


<script>
export default {
  data() {
    return {
      activeName: "first",
      input2: "",
      radio: "0",
      input: "",
      currentPage: 1,
      btnClickFlag:false,
      tableData1: [],
      /* 按订单修改运费 */
      isSelect: false, // false 右侧还未选;true 右侧选了
      multipleSelection: [], //左侧选中的
      changFeeByOrder: "", //按订单修改的运费
      radio: "0", //修改运费目标
      /* 按运单修改运费 */
      wayBillType: "", // 0为修改司机运费，1为修改信息费
      wayBillSn: "", //查询时的运单号
      carNum: "", // 查询车牌号
      removeData: [], //移除的数据
      orderId: "", //订单id
      orderType: "", //订单类型
      freight: "", //当前订单运费单价
      statusEnum: "", //当前订单状态（汉字）
      checkList: [],
      tableData: [
        // {
        //   date: "2016-05-02",
        //   name: "王小虎",
        //   address: "上海市普陀区金沙江路 1518 弄",
        //   status: "运输中"
        // },
        // {
        //   date: "2016-05-04",
        //   name: "王小虎",
        //   address: "上海市普陀区金沙江路 1517 弄",
        //   status: "运输中"
        // },
        // {
        //   date: "2016-05-01",
        //   name: "王小虎",
        //   address: "上海市普陀区金沙江路 1519 弄",
        //   status: "运输中"
        // },
        // {
        //   date: "2016-05-03",
        //   name: "王小虎",
        //   address: "上海市普陀区金沙江路 1516 弄",
        //   status: "运输中"
        // }
      ],
      /* 按运单 */
      pageNumber: 1,
      pageSize: 20,
      total: null,
      itemIds: [], //运单号的集合
      changedFee: "", //按运单修改后的运费
      radio2: "0",
      selectWayBillSn: "", //已经选择的运单号查询
      selectCarNum: "", //已经选择的车牌号查询
      /* 按订单状态 */
      radio3: "0",
      changedFeeByStatus: "",
      selData:[]  
    };
  },
  mounted() {
    console.log(this.$route.query);
    this.freight = this.$route.query.freight; //运费单价
    this.orderId = this.$route.query.orderBusinessId; //订单id
    this.orderType = this.$route.query.orderType; //订单类型
    this.statusEnum = this.$route.query.statusEnum;
  },
  methods: {
    /* 选取每页多少条数据 */
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.pageNumber = 1;
      this.pageSize = JSON.parse(`${val}`);
      // console.log(this.formInline.pageSize);
      this.getFeeList();
    },
    /* 翻页 */
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.pageNumber = JSON.parse(`${val}`);
      // console.log(this.formInline.pageNumber);
      this.getFeeList();
    },
    /* 选项卡切换 */
    handleClick(tab, event) {
      console.log(tab.name);
      if (tab.name == "second") {
        this.getFeeList();
      }
    },
    /* 确认选择并修改运费 */
    selectAndChange() {
      if (this.itemIds.length == 0) {
        this.$message.warning("请选取要修改的运单");
        return;
      }
      this.isSelect = true;
    },
    /* 返回选择 */
    goBack() {
      this.isSelect = false;
    },
    /* table的选择 */
    handleSelectionChange(val) {
      this.multipleSelection = val;
      console.log(val);
    },
    /* 全选按钮 */
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
        console.log(rows);
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    /* 右侧列表 */
    toggleSelection1(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable1.toggleRowSelection(row);
        });
        console.log(rows);
      } else {
        this.$refs.multipleTable1.clearSelection();
      }
    },
    /* 按订单的运费修改的单选 */
    getValue() {
      console.log(this.radio); // 打印被选中的label的值
    },
    /* 按运单修改运费时的目标 */
    getWayBillTypeRadio() {
      console.log(this.wayBillType);
      this.getFeeList();
    },
    /* 确认选择 */
    confirmSel() {
      //合并之后去重
      let jsonA = this.multipleSelection;
      console.log(this.multipleSelection, "A");
      let jsonB = this.tableData1;
      console.log(this.multipleSelection, "B");
      let jsonArr = jsonA.concat(jsonB);

      let newJson = []; //盛放去重后数据的新数组
      for (const item1 of jsonArr) {
        //循环json数组对象的内容
        let flag = true; //建立标记，判断数据是否重复，true为不重复
        for (const item2 of newJson) {
          //循环新数组的内容
          if (item1.itemSn == item2.itemSn) {
            //让json数组对象的内容与新数组的内容作比较，相同的话，改变标记为false
            flag = false;
          }
        }
        if (flag) {
          //判断是否重复
          newJson.push(item1); //不重复的放入新数组。  新数组的内容会继续进行上边的循环。
        }
      }
      console.log("newJson", newJson);
      this.tableData1 = newJson;
      this.selData = newJson
    },
    /* 右侧 */
    /* 选中 */
    handleRemoveChange(val) {
      console.log(val);
      this.removeData = val;
      var result = this.getFields(this.removeData, "id");
      console.log(result);
      this.itemIds = result.map(Number); //运单号的集合
    },
    /* 移除选择 */
    // removeSel() {
    //   console.log(this.tableData1);
    //   let addTableData = this.tableData1; //右侧的数组
    //   let delTableData = this.removeData; //要移除的数组
    //   console.log(this.removeData);
    //   for (var i = 0; i < addTableData.length; i++) {
    //     for (var j = 0; j < delTableData.length; j++) {
    //       if (addTableData[i].date == delTableData[j].date) {
    //         addTableData.splice(i, 1);
    //         delTableData.splice(j, 1);
    //         i--;
    //         j--;
    //       } else {
    //         break;
    //       }
    //     }
    //   }
    //   console.log(this.tableData1);
    // },
    removeSel() {
      let addTableData = this.tableData1; //右侧的数组
      let delTableData = this.removeData; //要移除的数组
      let _temAddTableData = [];
      let has_ele = false;
      console.log(this.removeData);
      for (var i = 0; i < addTableData.length; i++) {
        has_ele = false;
        for (var j = 0; j < delTableData.length; j++) {
          if (delTableData[j].id == addTableData[i].id) {
            has_ele = true;
            break;
          }
        }
        if (!has_ele) {
          _temAddTableData.push(addTableData[i]);
        }
      }
      this.tableData1 = _temAddTableData;
      console.log(this.tableData1);
      return _temAddTableData;
    },
    /* 按订单修改运费 */
    sureByOrder() {
      
      if (this.changFeeByOrder.trim() == "") {
        this.$message.warning("请输入要修改的运费");
        return;
      }
      let postData = {
        id: Number(this.orderId), //订单的id
        freight: this.freight,
        flag: Number(this.radio), //0为修改司机运费，1为修改信息费
        newFreight: Number(this.changFeeByOrder) //修改后的运费
      };
      // console.log(postData);
      this.btnClickFlag = true;
      this.$http
        .post("/admin-center-server/order/updateOrderFreight", postData)
        .then(res => {
          if (res.data.code == "200") {
            this.$message({
              type: "success",
              message: "修改成功!"
            });
            this.btnClickFlag = false;
            this.$router.go(-1);
          } else {
            this.$message.warning(res.data.message);
             this.btnClickFlag = false;
          }
        });
    },
    /* 查询运费列表 */
    serchWayBill() {
      this.getFeeList();
    },
    /* 运费列表 */
    getFeeList() {
      let postData = {
        carNumber: this.carNum,
        itemSn: this.wayBillSn,
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        orderBusinessId: this.orderId, //订单id
        statusType: this.wayBillType //全部为空，0待装货，1待卸货3待审核 10 为待结算
      };

      this.$http
        .get("/admin-center-server/orderItem/queryOrderItemListForUpdate", {
          params: postData
        })
        .then(res => {
          if (res.data.code == "200") {
            this.tableData = res.data.data.list; //列表
            this.total = Number(res.data.data.total);
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },
    /* 按运单修改的确定 */
    wayBillConfirm() {
      if (this.changedFee.trim() == "") {
        this.$message.warning("请输入要修改的运费");
        return;
      }
      let postData = {
        newFreight: this.changedFee, //修改的运费
        freight: this.freight, //当前运费
        itemIds: this.itemIds, //运单的集合
        orderBusinessId: this.orderId, //订单id
        orderType: this.orderType, //订单类型
        statusType: "", //
        flag: this.radio2
      };
      this.btnClickFlag =true;
      this.$http
        .post("/admin-center-server/orderItem/updateDringMoney", postData)
        .then(res => {
          if (res.data.code == "200") {
            this.$message({
              type: "success",
              message: "修改成功!"
            });
            this.btnClickFlag = false;
            this.$router.go(-1);
          } else {
            this.$message.warning(res.data.message);
             this.btnClickFlag = false;
          }
        });
    },
    /* 按照状态修改运费 */
    confirmByStatus() {
      console.log(this.checkList);
      let postData = {
        orderBusinessId: this.orderId,
        flag: this.radio3,
        freight: this.freight,
        statusTypes: this.checkList.map(Number), //状态的集合
        orderType: this.orderType, //订单类型
        newFreight: this.changedFeeByStatus //修改的运费
      };
      this.btnClickFlag = true;
      this.$http
        .post(
          "/admin-center-server/orderItem/updateDringMoneyByStatus",
          postData
        )
        .then(res => {
          if (res.data.code == "200") {
            this.$message({
              type: "success",
              message: "修改成功!"
            });
            this.btnClickFlag = false;
            this.$router.go(-1);
          } else {
            this.$message.warning(res.data.message);
             this.btnClickFlag = false;
          }
        });
    },

    /* 查询已经选择的 */
    selectWayBill() {

      let that = this;
      var arr = [];
      if (that.selectWayBillSn.trim() == "" && that.selectCarNum.trim() == "") {
        that.tableData1 = this.selData;
      } else {
        this.tableData1.forEach(function(val) {
          if (
            val.itemSn == that.selectWayBillSn ||
            val.carNumber == that.selectCarNum
          ) {
            console.log(val);
            arr.push(val);
          }
        });

        that.tableData1 = arr;
      }

      // console.log(that.tableData1);
      // console.log(selData)
    },
    /* 列表全选 */
    // selectAllList() {
    //   let postData = {
    //     carNumber: this.carNum,
    //     itemSn: this.wayBillSn,
    //     pageNumber: this.pageNumber,
    //     pageSize: this.pageSize,
    //     orderBusinessId: this.orderId, //订单id
    //     statusType: this.wayBillType //全部为空，0待装货，1待卸货3待审核 10 为待结算
    //   };
    //   this.$http
    //     .get("/admin-center-server/orderItem/queryOrderItemListForUpdate", {
    //       params: postData
    //     })
    //     .then(res => {
    //       if (res.data.code == "200") {
    //         this.tableData1 = res.data.data.list; //列表
    //       } else {
    //         this.$message.warning(res.data.message);
    //       }
    //     });
    // },
    /*在数组对象中提取某个字段的值组成新的数组*/
    getFields(input, field) {
      var output = [];
      for (var i = 0; i < input.length; ++i) output.push(input[i][field]);
      return output;
    },
    /* 返回上一页 */
    goReturn() {
      this.$router.go(-1);
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.el-form-item {
  margin-bottom: 0;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.firstContent,
.secondContent,
.thirdContent {
  margin: auto 30px;
}
.firstContent span em {
  color: red;
}
.secondContent .secondFooter {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}
.thirdContent .thirdFooter {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.thirdContent em {
  color: red;
}
.selectTable {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  .selLeft .search,
  .selRight .search {
    margin-bottom: 20px;
    span {
      margin: 5px;
    }
  }
  .selLeft .selBtn,
  .selRight .selBtn {
    margin-bottom: 10px;
  }
  span {
    display: inline-block;
  }
}
.footer {
  text-align: center;
  .confirmBtn {
    margin: 30px auto;
  }
}

.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    // margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      span {
        margin-left: 20px;
        em {
          color: red;
        }
      }
      div {
        height: 38px;
        line-height: 38px;
      }
    }
    .page {
      text-align: right;
    }
    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }
  }
}
</style>
