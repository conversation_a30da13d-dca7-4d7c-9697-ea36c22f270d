<template>
    <div class="app-container reason">
        <div class="top-title">
            <div>驳回原因管理 <span>（驳回原因可在驳回操作时选择以此作为标准话术）</span></div>
            <div class="button">
                <el-button class="left"
                           icon="el-icon-plus"
                           size="mini"
                           type="primary"
                           @click="dialogVisible = true"
                >添加原因
                </el-button>
            </div>
        </div>
        <div class="main-list">
            <template>
                <el-table
                        :data="tableData"
                        border
                        style="width: 100%">
                    <el-table-column
                            prop="date"
                            type="index"
                            label="序号"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="reason"
                            label="驳回原因"
                    >
                    </el-table-column>
                    <el-table-column
                            fixed="right"
                            label="操作"
                            width="100">
                        <template slot-scope="scope">
                            <el-button @click="editReason(scope.row)" type="text" size="small">编辑</el-button>
                            <el-button type="text" size="small" @click="deleteReason(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
        </div>


        <!--新增弹框-->
        <el-dialog
                title="驳回原因新增"
                :visible.sync="dialogVisible"
                width="30%"
                :before-close="handleClose">
            <div>
                <el-form>
                    <el-form-item>
                        <el-input type="textarea" v-model="form.addDesc"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="saveReason">保 存</el-button>
  </span>
        </el-dialog>
        <!--编辑弹框-->
        <el-dialog
                title="驳回原因编辑"
                :visible.sync="editDialog"
                width="30%"
                :before-close="handleClose">
            <div>
                <el-form>
                    <el-form-item>
                        <el-input type="textarea" v-model="form.editDesc"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="saveEdit">确 定</el-button>
  </span>
        </el-dialog>
        <!--删除弹框-->
        <el-dialog
                title="警告提示"
                :visible.sync="deleteDialog"
                width="30%"
                :before-close="handleClose">
            <div style="display: flex">
                <div class="el-icon-warning" style="width: 40px;height: 40px;margin-top: 15px"></div>
                <div style="margin-left: 20px">
                    <p>确定要删除当前驳回原因?</p>
                    <span>删除后，当前已保存的驳回原因不可恢复</span>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="deleteRow">确 定</el-button>
  </span>
        </el-dialog>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                form: {
                    addDesc: '',
                    editDesc: '',
                },
                dialogVisible: false,
                editDialog: false,
                deleteDialog: false,
                tableData: [],
                editId:'',
            }
        },
        methods: {
            handleClose(done) {
                this.dialogVisible = false
                this.editDialog = false
                this.deleteDialog = false
                // this.$confirm('确认关闭？')
                //     .then(_ => {
                //         done();
                //     })
                //     .catch(_ => {
                //     });
            },
            /** 确定添加原因 **/
            saveReason() {
                this.dialogVisible = false
                let resason = this.form.addDesc;
                if(resason === ''){
                    this.$message.warning('请填写原因')
                }else {
                    this.$http.post('/admin-center-server/reject/reason/add?resason='+resason).then(res=>{
                        let data = res.data;
                        if(data.code==='200'){
                            this.$message.success('添加成功')
                            this.getReasonList()
                            this.form.editDesc=''
                            this.form.addDesc=''
                        }
                    })
                }
            },
            /** 点击编辑回显 **/
            editReason(row) {
                 this.editId = row.id;
                 this.form.editDesc = row.reason;
                 this.editDialog = true
            },
            /** 保存编辑 **/
            saveEdit() {
                let id =  this.editId;
                let reason = this.form.editDesc;
                if(reason === ''){
                    this.$message.warning('请填写原因')
                }else{
                    this.$http.post('/admin-center-server/reject/reason/update?id='+id+'&resason='+reason).then(res=>{
                        let data = res.data;
                        if(data.code === '200'){
                            this.$message.success('修改成功')
                            this.getReasonList();
                            this.editDialog = false;
                            this.form.editDesc=''
                            this.form.addDesc=''
                        }
                    })
                }
            },
            deleteReason(row) {
                this.editId = row.id;
                this.deleteDialog = true

            },
            deleteRow() {
               let id = this.editId
                this.$http.post('/admin-center-server/reject/reason/del?id='+id).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                        this.deleteDialog = false;
                        this.$message.success('删除成功')
                        this.getReasonList()
                    }
                })
            },

            getReasonList(){
               this.$http.get('/admin-center-server/reject/reason/list',{
                   params:{
                       pageNumber:1,
                       pageSize:20,
                   }
               }).then(res=>{
                   let data = res.data;
                   if(data.code==='200'){
                       this.tableData=data.data.list
                   }
               })
            },
        },
        activated() {
            this.getReasonList()
        }
    }
</script>
<style>
    .el-icon-warning {
        width: 40px;
        height: 40px;
        font-size: 40px;
        color: red;
    }
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
    .reason {
        .top-title {
            font-size: 16px;
            font-weight: 700;
            height: 40px;
            line-height: 40px;
            padding-left: 10px;
            background-color: white;
            border-bottom: 1px solid #cccccc;
            display: flex;
            justify-content: space-between;

            .button {
                margin-right: 20px;
            }

            span {
                color: #999999;
                font-size: 14px;
                padding-left: 20px;
            }
        }

        .main-list {
            padding: 20px;
            /*height: 600px;*/
            background-color: white;
        }
    }
</style>
