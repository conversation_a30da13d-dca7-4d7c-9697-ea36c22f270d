<template>
  <div class="transfer_accounts">
    <div class="waring-text">转账操作涉及用户钱包金额，请务必谨慎操作！！！</div>
    <div>
      <div class="base">当前平台主体：{{ currentBase.baseName }}</div>
    </div>
    <div class="content">
      <el-form ref="ruleForm" :model="form" :rules="rules" label-width="0">
        <el-row :gutter="20">
          <el-col :span="6">
              <el-form-item label="" required prop="manualTransferEnum">
                <div><span style="color: red">*</span> 转账类型</div>
                <el-select v-model="form.manualTransferEnum" placeholder="请选择" @change="handleTransferType" clearable>
                    <el-option label="司机油费转现金" value="1"></el-option>
                    <el-option label="退还货主油费" value="2"></el-option>
                    <el-option label="司机转车队长" value="3"></el-option>
                    <el-option label="其他" value="4"></el-option> 
                </el-select>
              </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="" prop="transferReason">
              <div><span style="color: red">*</span> 转账原因</div>
              <el-input v-model="form.transferReason"></el-input> 
            </el-form-item>
        </el-col>
          <el-col :span="6">
            <el-form-item v-if="form.manualTransferEnum==='1'||form.manualTransferEnum==='2'" label="" prop="orderItemSn">
              <div><span style="color: red">*</span> 关联运单号</div>
              <el-input v-model="form.orderItemSn" @blur="getOrderItemDetail"></el-input> 
            </el-form-item>
          </el-col>
          <el-col :span="6">
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="" label-width="0" prop="outUserAccount">
              <div><span style="color: red">*</span> 转出账户</div>
              <div class="account_info">
                <el-select v-model="form.outManualTransferAccountType" :disabled="form.manualTransferEnum==='1'||form.manualTransferEnum==='2' || form.manualTransferEnum ==='3'" placeholder="请选择" @change="(val) => { validateInfoField('outUserAccount'); handleAccountTypeChange(val, 'outUserAccount'); }" clearable>
                  <!-- v-if="form.manualTransferEnum!=='4'" -->
                  <el-option label="平台"  value="1"></el-option>
                  <el-option label="货主" value="2"></el-option>
                  <el-option label="司机/车队长" value="3"></el-option>
                  <el-option label="运力供应商" value="4"></el-option>
                </el-select>
                <!-- 平台子账户 -->
                <el-form-item  :prop="form.outManualTransferAccountType==='1'?'outUserChildAccount':'noRules'" v-show="form.outManualTransferAccountType==='1'">
                  <el-select style="width:100%"  :disabled="form.manualTransferEnum=='1'||form.manualTransferEnum=='2'"  v-model="form.outUserChildAccount"  placeholder="请选择"   clearable @change="validateInfoField('outUserChildAccount')">
                    <el-option v-for="item in platformAccountType" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
                <el-select 
                v-model="form.outUserAccount" 
                filterable
                remote
                :disabled="form.manualTransferEnum==='1'||form.manualTransferEnum==='2'"
                placeholder="请选择" 
                :remote-method="searchOutUsers"
                @change="validateInfoField('outUserAccount')" 
                clearable>
                  <el-option v-for="item in outUserList" :key="item.id" :label="item.name + '/' + item.mobile" :value="item.id"></el-option>
                </el-select>
                <el-input disabled v-model="form.outUserSubAcctNo"></el-input>
              </div> 
            </el-form-item>
          </el-col>
          <el-col :span="1">
            <div class="account_info" style="background: white; align-items: center">
              <img src="https://tjhfd.oss-cn-beijing.aliyuncs.com/tjhfd/upload/image/202411/*************/a4e390e203f0cf87b27bdc064407dae0.png" alt=""/>
            </div>
          </el-col>
          <el-col :span="8">
            <el-form-item label="" label-width="0" prop="inUserAccount">
              <div><span style="color: red">*</span> 转入账户</div>
              <div class="account_info">
                <el-select v-model="form.inManualTransferAccountType" :disabled="form.manualTransferEnum==='1'||form.manualTransferEnum==='2' || form.manualTransferEnum ==='3'" placeholder="请选择" clearable @change="(val)=>{ validateInfoField('inUserAccount'); handleAccountTypeChange(val, 'inUserAccount')}">
                  <el-option label="平台"  value="1"></el-option>
                  <el-option label="货主" value="2"></el-option>
                  <el-option label="司机/车队长" value="3"></el-option>
                  <el-option label="运力供应商" value="4"></el-option>
                </el-select>
                <!-- 平台子账户 -->
                <el-form-item  :prop="form.inManualTransferAccountType==='1'?'inUserChildAccount':'noRules'" v-show="form.inManualTransferAccountType==='1'">
                <el-select style="width:100%" v-show="form.inManualTransferAccountType==='1'" :disabled="form.manualTransferEnum=='1'||form.manualTransferEnum=='2'"  v-model="form.inUserChildAccount"  placeholder="请选择" clearable @change="validateInfoField('inUserChildAccount')">
                  <el-option v-for="item in platformAccountType" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                </el-form-item>
                <el-select 
                v-model="form.inUserAccount" 
                filterable
                remote
                placeholder="请选择" 
                :remote-method="searchInUsers"
                clearable 
                @change="validateInfoField('inUserAccount')">
                  <el-option v-for="item in inUserList" :key="item.id" :label="item.name + '/' + item.mobile" :value="item.id"></el-option>
                </el-select>

                <el-input disabled v-model="form.inUserSubAcctNo"></el-input>
              </div> 
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="" prop="transferAmount">
              <div><span style="color: red">*</span> 转账金额</div>
              <el-input v-model="form.transferAmount" :disabled="form.manualTransferEnum == 2"></el-input> 
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
          <el-button type="primary" @click="submitForm('ruleForm')">确认转账</el-button>
          <el-button @click="resetForm('ruleForm')">取消</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-dialog
      title="确认转账"
      :visible.sync="dialogVisible">
      <div style="color:red;margin-bottom: 10px" v-if="form.inManualTransferAccountType==1||form.outManualTransferAccountType==1">您当前选择转出/转入账户为“平台账户”，请谨慎操作！！！</div>
      <span v-if="form.manualTransferEnum == 1">请在转账前确保已在【分油系统】中收回司机油费 <span style="color: red">{{form.transferAmount}}</span> 元，是否继续？</span>
      <span v-if="form.manualTransferEnum == 2">确认退还货主油费 <span style="color: red">{{form.transferAmount}}</span> 元？</span>
      <span v-if="form.manualTransferEnum == 3">确认从{{form.outUserAccountName}}转账 <span style="color: red">{{form.transferAmount}}</span> 元至{{form.inUserAccountName}}？</span>
      <span v-if="form.manualTransferEnum == 4">确认从「{{!form.outUserChildAccount?form.outUserAccountName:form.outUserChildAccount==1?'平台/油款账户':'平台/垫款账户'}}」转账 <span style="color: red">{{form.transferAmount}}</span> 元至「{{!form.inUserChildAccount?form.inUserAccountName:form.inUserChildAccount==1?'平台/油款账户':'平台/垫款账户'}}」？</span>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :disabled="confrimButtonDisabled" @click="confirmSubmit">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
export default {
  data() {
    return {
      baseInfo:[],
      currentBase: {},
      dialogVisible: false,
      key: '',
      outUserList: [],
      inUserList: [],
      oilMoneyValue: '0',
      form: {
        manualTransferEnum: "",
        transferReason: "",
        orderItemSn: "",
        outManualTransferAccountType: "",
        outUserAccount: "",
        outUserAccountName: "",
        outUserSubAcctNo: "",
        inManualTransferAccountType: "",
        inUserAccount: "",
        inUserAccountName: "",
        inUserSubAcctNo: "",
        transferAmount: "",
        // 平台子账户
        outUserChildAccount: '',
        inUserChildAccount: '',

      },
      manualTransferType: '',
      outType: '',
      inType: '',
      platformList: null,
      confrimButtonDisabled: false,
      rules: {
          manualTransferEnum: [
            { required: true, message: '请选择类型', trigger: 'change' }
          ],
          transferReason: [
            { required: true, message: '请填写原因', trigger: 'blur' }
          ],
          orderItemSn: [
            { required: true, message: '请填写运单号', trigger: 'blur' }
          ],
          outUserAccount: [
            { required: true, validator: this.checkOutInfo, trigger: 'blur' }
          ],
          inUserAccount: [
            { required: true, validator: this.checkOutInfo, trigger: 'blur' }
          ],
          transferAmount: [
            { required: true,
              validator: async (rule, value, callback) => {
                
                if (!value) {
                    callback(new Error("请输入"));
                } else {
                    let num = Number(value)
                    let decimals = String(num).split('.')[1],
                        decimalsLength = 0
                    if (decimals) {
                        decimalsLength = String(decimals).length
                    }
                    if (Number.isNaN(num) || num < 0 || decimalsLength > 2) {
                        callback('请输入大于0的数，支持输入两位小数')
                    }
                    if (this.form.manualTransferEnum === '1' && this.form.orderItemSn && parseFloat(value) > parseFloat(this.oilMoneyValue)) {
                      callback('金额超过油费最大值！')
                    }
                }
                callback()
            },
            trigger: "blur",}
          ],
          outUserChildAccount: [
            { required: true, message: '', trigger: 'blur' }
          ],
          inUserChildAccount: [
            { required: true, message: '', trigger: 'blur' }
          ],
      },
      //平台账户类型
      platformAccountType: [
        { label: '油费账户', value: '1'},
        { label: '垫款账户', value: '3' },
      ], 
    }
  },
  mounted() {
    let baseInfo = (this.baseInfo = this.$store.state.user.baseInfo);
    let currentBaseId = localStorage.getItem("UserCurrentBaseId");
    if (currentBaseId) {
      this.currentBase = baseInfo.find((v) => v.id == currentBaseId);
    } else {
      this.currentBase = baseInfo.find((v) => v.defaultFlag);
      localStorage.setItem("UserCurrentBaseId", this.currentBase.id);
    }
    console.log('this.currentBase',this.currentBase)
    if(process.env.VUE_APP_ENV && process.env.VUE_APP_ENV !='production'){
        this.currentBase.companyName ='天安系统'
    }
    this.$get('/admin-center-server/transferAccount/getAccountInfo', {nameOrMobile: this.currentBase.companyName, manualTransferAccount: '1', baseId: this.currentBase.id}).then(
      res => {
        if (res.length > 0) {
          this.platformList = res
        }
      }
    )
  },
  methods: {
    //切换账户类型
    handleAccountTypeChange(value,type) {   
      console.log('账户类型',value);
      if(type=='outUserAccount'){//转出账户
        if (value === '1') { //平台账户
        this.outUserList = this.platformList
          
        }else{//非平台账户
          this.outUserList = []
          this.form.outUserAccount = ''
          this.form.outUserAccountName = ''
          this.form.outUserSubAcctNo = ''
          this.form.outUserChildAccount = ''

        }
        
      }else{ //转出账户
        if (value === '1') { //平台账户
          this.inUserList = this.platformList
        }else{//非平台账户
          this.inUserList = []
          this.form.inUserAccount = ''
          this.form.inUserAccountName = ''
          this.form.inUserSubAcctNo = ''
          this.form.inUserChildAccount = ''

        }

      }
      
    },
    getOrderItemDetail() {
      this.$get('/admin-center-server/transferAccount/getTransferOilBySn?sn='+this.form.orderItemSn).then(
        res => {
          if (this.form.manualTransferEnum === '2'||this.form.manualTransferEnum === '1') {
            this.form.transferAmount = res.amount
          }
          this.oilMoneyValue = res.amount
        },
        () => {
          this.oilMoneyValue = '0'
        }
      )
    },
    handleTransferType(value) {
      // 切换类型相同不处理
      if (this.manualTransferType === value) {
        return
      }
      // 不相同
      this.manualTransferType = value
      this.resetForm('ruleForm')
      this.form.manualTransferEnum = value
      this.outUserList = []
      this.inUserList = []
      switch (value) {
        case '1':
          this.outType = '1'
          this.inType = '3'
          this.form.inManualTransferAccountType = '3'
          break
        case '2':
          this.outType = '1'
          this.inType = '2'
          this.form.inManualTransferAccountType = '2'
          break;
        case '3':
          this.outType = '3'
          this.inType = '3'
          this.form.outManualTransferAccountType = '3'
          this.form.inManualTransferAccountType = '3'
          break
        default:
          break
      }

      if (value === '1' || value === '2') {
        let nameOrMobile = this.currentBase.companyName
        // console.log('process.env.VUE_APP_ENV',process.env.VUE_APP_ENV)
        if(process.env.VUE_APP_ENV && process.env.VUE_APP_ENV !='production'){
          nameOrMobile = '天安系统'
        }
        console.log('nameOrMobile',nameOrMobile);
        this.$get('/admin-center-server/transferAccount/getAccountInfo', {nameOrMobile: nameOrMobile, manualTransferAccount: '1', baseId: this.currentBase.id}).then(
          res => {
            if (res.length > 0) {
              let item = res[0]
              this.outUserList = res
              this.form.outManualTransferAccountType = '1'
              this.form.outUserAccount = item.id
              this.form.outUserAccountName = item.name + '/' + item.mobile
              this.form.outUserSubAcctNo = item.subAcctNo
              this.form.outUserChildAccount = '1'
            }
          }
        )
      }
    },
    checkOutInfo(rule, value, callback) {
      if (rule.field == 'outUserAccount') {
        if (!value || !this.form.outManualTransferAccountType) {
          callback(new Error('请选择'));
        } else {
          let list = this.outUserList.filter((item) => item.id == value)
          
          if (list.length > 0) {
            let item = list[0]
            this.form.outUserAccountName = item.name + '/' + item.mobile
            this.form.outUserSubAcctNo = item.subAcctNo
          }
          callback()
        }
      } else {
        if (!value || !this.form.inManualTransferAccountType) {
          callback(new Error('请选择'));
        } else {
          let list = this.inUserList.filter((item) => item.id == value)
          if (list.length > 0) {
            let item = list[0]
            this.form.inUserAccountName = item.name + '/' + item.mobile
            this.form.inUserSubAcctNo = item.subAcctNo
          }
          callback()
        }
      }
    },
    validateInfoField(type) {
      if (type === 'outUserAccount') {
        if (this.outType !== this.form.outManualTransferAccountType) {
          // this.outUserList = []
          // this.form.outUserAccount = ''
          // this.form.outUserAccountName = ''
          // this.form.outUserSubAcctNo = ''
          // this.form.outUserChildAccount = ''
        }
        if (this.form.outManualTransferAccountType === '1') {
          // this.outUserList = this.platformList
          // let item = this.platformList[0]
          // this.form.outUserAccount = item.id
          // this.form.outUserAccountName = item.name + '/' + item.mobile
          // this.form.outUserSubAcctNo = item.subAcctNo
        }
        this.outType = this.form.outManualTransferAccountType
      } else {
        if (this.inType !== this.form.inManualTransferAccountType) {
          // this.inUserList = []
          // this.form.inUserAccount = ''
          // this.form.inUserAccountName = ''
          // this.form.inUserSubAcctNo = ''
          // this.form.inUserChildAccount = ''
        }
        if (this.form.inManualTransferAccountType === '1') {
          // this.inUserList = this.platformList
          // let item = this.platformList[0]
          // this.form.inUserAccount = item.id
          // this.form.inUserAccountName = item.name + '/' + item.mobile
          // this.form.inUserSubAcctNo = item.subAcctNo
        }
        this.inType = this.form.inManualTransferAccountType
      }
      this.$refs["ruleForm"].validateField(type)
    },
    searchOutUsers(query) {
      if (!this.form.outManualTransferAccountType) {
        this.$message.warning('请选择转出账户类型')
        return
      }
      this.$get('/admin-center-server/transferAccount/getAccountInfo', {nameOrMobile: query, manualTransferAccount: this.form.outManualTransferAccountType, baseId: this.currentBase.id}).then(
        res => {
          this.outUserList = res
        }
      )
    },
    searchInUsers(query) {
      
      if (!this.form.inManualTransferAccountType) {
          this.$message.warning('请选择转入账户类型')
          return
        }
      this.$get('/admin-center-server/transferAccount/getAccountInfo', {nameOrMobile: query, manualTransferAccount: this.form.inManualTransferAccountType, baseId: this.currentBase.id}).then(
        res => {
          this.inUserList = res
        }
      )
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.dialogVisible = true
        } else {
          return false;
        }
      });
    },

    async confirmSubmit() {
      this.confrimButtonDisabled = true
      let key = await this.$get('/admin-center-server/transferAccount/getTransferAccountKey')
      this.$post('/admin-center-server/transferAccount/manualTransferAccount', {...this.form, keyNo: key, baseId: this.currentBase.id}).then(
        res => {
          // dialog隐藏
          this.dialogVisible = false
          this.$message.success('操作成功')
          // 重置数据
          this.resetForm('ruleForm')
          // button可点击
          this.confrimButtonDisabled = false
          // 类型初始化
          this.manualTransferType = ''
        },
        () => {
          this.confrimButtonDisabled = false
        }
      )
    },
    resetForm(formName) {
      this.form = {
        manualTransferEnum: "",
        transferReason: "",
        orderItemSn: "",
        outManualTransferAccountType: "",
        outUserAccount: "",
        outUserAccountName: "",
        outUserSubAcctNo: "",
        inManualTransferAccountType: "",
        inUserAccount: "",
        inUserAccountName: "",
        inUserSubAcctNo: "",
        transferAmount: "",
      },
      this.$refs[formName].resetFields();
    }
  }
};
</script>

<style lang="scss" scoped>
.base {
  text-align: right;
  color: #888;
}
.transfer_accounts {
  margin: 10px;
  padding: 10px;
  background-color: white;
  height: 98%;

  .waring-text {
    margin: 20px;
    font-size: 18px;
    color: red;
  }
  .account_info {
    padding: 10px;
    height: 180px;
    border-radius: 10px;

    background: #f3f3f3;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
  }
  .content {
      margin: 20px;
  }
}
</style>