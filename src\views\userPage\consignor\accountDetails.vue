<template>
  <div>
    <el-tabs type="border-card"
             v-model="activeName"
             @tab-click="handleClick">
      <el-row style="text-align:right;display:inline-block">
        <el-button size="mini"
                   type="primary"
                   class="btn"
                   @click="enablefn">{{deleteFlagText}}</el-button>
      </el-row>
      <el-tab-pane name='first'
                   label="账户详情">
        <div class="tableList">
          <h2 class="keyName">基本信息</h2>
          <detail-row>
            <detail-col :span="6" label="二维码">
              <div class="detail-value">
                <el-button @click="seefn" size="mini">查看</el-button>
              </div>
            </detail-col>
            <detail-col :span="6" label="公司名称" :value="tableData[0].name"></detail-col>
            <detail-col  :span="6" label="统一社会信用代码" :value="tableData[0].organizationCode"></detail-col>
            <detail-col  :span="6" label="手机号码（注册）" :value="tableData[0].mobile"></detail-col>
            <detail-col  :span="6" label="营业执照">
              <el-button @click="onPreview" size="mini">查看</el-button>
              <el-image-viewer v-if="showViewer"
                :on-close="closeViewer"
                :url-list="[tableData[0].licenseImage]" />
            </detail-col>
            <detail-col  :span="6" label="账户状态" :value="tableData[0].deleteFlagTxt"></detail-col>
            <detail-col  :span="6" label="注册时间" :value="handleTime(tableData[0].createdTime)"></detail-col>
            <detail-col  :span="6" label="法人姓名" :value="tableData[0].corporationName"></detail-col>
            <detail-col  :span="6" label="法人身份证号" :value="tableData[0].corporationIdCard"></detail-col>
            
            <detail-col  :span="6" label="公司地址" :value="tableData[0].companyAddress" :tooltip="true"></detail-col>
            <detail-col  :span="6" label="注册资本" :value="tableData[0].registeredCapital"></detail-col>
            <detail-col  :span="6" label="成立日期" :value="tableData[0].establishmentDate"></detail-col>
            <detail-col  :span="6" label="经营范围" :value="tableData[0].businessScope" :tooltip="true"></detail-col>
            <detail-col  :span="6" label="主管税务机关" :value="tableData[0].taxAuthority" :tooltip="true"></detail-col>
            <detail-col  :span="6" label="企业类型" :value="tableData[0].enterpriseType">
              <template>
                <span v-if="tableData[0].enterpriseType == 0">一般人</span>
                <span v-if="tableData[0].enterpriseType == 1">小规模</span>
              </template>
            </detail-col>
            <detail-col  :span="6" label="邮寄地址" :value="tableData[0].mailAddress"></detail-col>
            <detail-col  :span="6" label="合作平台主体" :value="tableData[0].baseName"></detail-col>
          </detail-row>
        </div>
        
        <div class="tableList">
          <h2 class="keyName">开户行信息</h2>
          <el-table :data="tableData1"
                    border
                    style="width: 100%"
                    highlight-current-row>
            <el-table-column show-overflow-tooltip
                            v-for="item in tableLabel1"
                            :label="item.label"
                            :width="item.width">
              <template slot-scope="scope">
                <span style="margin-left: 10px">{{ handleTime(scope.row[item.prop]) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="tableList">
          <h2 class="keyName">常用地址</h2>
          <el-table :data="tableData2"
                    border
                    style="width: 100%"
                    highlight-current-row>
            <el-table-column show-overflow-tooltip
                            v-for="item in tableLabel2"
                            :label="item.label"
                            :width="item.width">
              <template slot-scope="scope">
                <span style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="tableList">
          <h2 class="keyName">业务概况</h2>
          <detail-row>
            <detail-col  :span="6" label="发单总数量" :value="tableData3[0].orderCount">
              <div class="detail-title"></div>
              <div class="detail-value">{{  }}</div>
            </detail-col>
            <detail-col  :span="6" label="发单总金额" :value="tableData3[0].orderCountMoney"></detail-col>
            <detail-col  :span="6" label="实际运费总额" :value="tableData3[0].orderRealMoney"></detail-col>
            <detail-col  :span="6" label="账户登录次数" :value="tableData3[0].loginCount"></detail-col>
            <detail-col  :span="6" label="账户余额" :value="tableData3[0].accountLeave"></detail-col>
            <detail-col  :span="6" label="评价" :value="tableData3[0].assess"></detail-col>
            <detail-col  :span="6" label="客户星级" :value="tableData3[0].level"></detail-col>
            <detail-col  :span="6" label="好友数量" :value="tableData3[0].friendNum"></detail-col>

            <detail-col  :span="6" label="上一次发单时间" :value="tableData3[0].lastOrderTime"></detail-col>
            <detail-col  :span="6" label="上一次登录时间" :value="tableData3[0].lastLoginTime"></detail-col>
            <detail-col  :span="6" label="未结算运单数" :value="tableData3[0].itemNotInvoice"></detail-col>
            <detail-col  :span="6" label="未结算总金额" :value="tableData3[0].notInviceMoney"></detail-col>
            <detail-col  :span="6" label="首要联系人" :value="tableData3[0].primaryContact"></detail-col>
            <detail-col  :span="6" label="首要联系人电话" :value="tableData3[0].primaryContactPhone"></detail-col>
            <detail-col  :span="6" label="客户税点" :value="tableData3[0].taxThreshold"></detail-col>
            <detail-col  :span="6" label="代理商名称" :value="tableData3[0].factorName"></detail-col>
          </detail-row>
          <!-- <el-table :data="tableData3"
                    border
                    style="width: 100%"
                    highlight-current-row>
            <el-table-column show-overflow-tooltip
                            v-for="item in tableLabel3"
                            :label="item.label"
                            :width="item.width">
              <template slot-scope="scope">
                <span style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-table :data="tableData3"
                    border
                    style="width: 100%"
                    highlight-current-row>
            <el-table-column show-overflow-tooltip
                            v-for="item in tableLabel4"
                            :label="item.label"
                            :width="item.width">
              <template slot-scope="scope">
                <span style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
              </template>
            </el-table-column>
          </el-table> -->
        </div>
      </el-tab-pane>
      <el-tab-pane name='second'
                   label="登录日志">
        <h2 class="keyName">登录日志</h2>
        <el-table :data="tableData"
                  border
                  style="width: 100%"
                  highlight-current-row>
          <el-table-column show-overflow-tooltip
                           v-for="item in tableLabel5"
                           :label="item.label"
                           :width="item.width">
            <template slot-scope="scope">
              <span style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane name='three'
                   label="功能管理">
        <Sign ref="sign" @success="getData"></Sign>
      </el-tab-pane>
    </el-tabs>
    <el-dialog title="二维码"
               :visible.sync="dialogFormVisible">
      <span id="qrCode"
            ref="qrCodeDiv"></span>
      <div slot="footer"
           class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary"
                   @click="dialogFormVisible = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ElImageViewer from 'cf-element-ui/packages/image/src/image-viewer'
const info = '/admin-center-server/commonUser/info'//详情
const logList = '/admin-center-server/commonUser/logList'//日志
const updateUserStatus = '/admin-center-server/commonUser/updateUserStatus'//启用、禁用
const openShortFall = '/admin-center-server/update/openShortFall'//修改短倒
import QRCode from 'qrcodejs2';
import Sign from './components/Sign/Sign'
export default {
  components: { ElImageViewer, Sign },
  data () {
    return {
      showViewer: false, // 显示查看器
      activeName: 'first',
      dialogFormVisible: false,
      id: '',
      deleteFlags: '',
      deleteFlagText: '',
      tableData: [],
      tableData1: [],
      tableData3: [],
      tableData2: [],
      tableLabel: [
        {
          prop: '无',
          label: '二维码'
        },
        {
          prop: 'name',
          label: '公司名称'
        },
        {
          prop: 'organizationCode',
          label: '统一社会信用代码'
        },
        {
          prop: 'mobile',
          label: '手机号码（注册）'
        },
        {
          prop: 'licenseImage',
          label: '营业执照'
        }

      ],
      tableLabel1: [
        {
          prop: 'name',
          label: '公司名称'
        },
        {
          prop: 'openingBank',
          label: '开户行'
        },
        {
          prop: 'accountNumber',
          label: '账号'
        },
        {
          prop: 'authFristTime',
          label: '首次鉴权时间'
        }
      ],
      tableLabel2: [
        {
          prop: 'userName',
          label: '联系人'
        },
        {
          prop: 'userPhone',
          label: '手机号码'
        },
        {
          prop: 'shortName',
          label: '地址昵称'
        },
        {
          prop: 'name',
          label: '详细地址'
        }
      ],
      tableLabel3: [
        {
          prop: 'orderCount',
          label: '发单总数量'
        },
        {
          prop: 'orderCountMoney',
          label: '发单总金额'
        },
        {
          prop: 'orderRealMoney',
          label: '实际运费总额'
        },
        {
          prop: 'loginCount',
          label: '账户登录次数'
        },
        {
          prop: 'accountLeave',
          label: '账户余额'
        },
        {
          prop: 'assess',
          label: '评价'
        },
        {
          prop: 'level',
          label: '客户星级'
        },
        {
          prop: 'friendNum',
          label: '好友数量'
        }
      ],
      tableLabel4: [
        {
          prop: 'lastOrderTime',
          label: '上一次发单时间'
        },
        {
          prop: 'lastLoginTime',
          label: '上一次登录时间'
        },
        {
          prop: 'itemNotInvoice',
          label: '未结算运单数'
        },
        {
          prop: 'notInviceMoney',
          label: '未结算总金额'
        },
        {
          prop: 'primaryContact',
          label: '首要联系人'
        },
        {
          prop: 'primaryContactPhone',
          label: '首要联系人电话'
        },
        {
          prop: 'taxThreshold',
          label: '客户税点'
        },
        {
          prop: 'factorName',
          label: '代理商名称'
        }
      ],
      tableLabel5: [
        {
          prop: 'loginTime',
          label: '时间'
        },
        {
          prop: 'loginIp',
          label: 'IP'
        },
        {
          prop: 'loginTerminal',
          label: '登录设备'
        },
        {
          prop: 'systemVersion',
          label: '系统版本'
        },
        {
          prop: 'appVersion',
          label: 'App版本'
        },
        {
          prop: 'area',
          label: '地区'
        },
        {
          prop: 'loginType',
          label: '登录方式'
        }
      ],
      tableLabel6: [
        {
          prop: 'deleteFlagTxt',
          label: '账户状态'
        },
        {
          prop: 'createdTime',
          label: '注册时间'
        },
        {
          prop: 'corporationName',
          label: '法人姓名'
        },
        {
          prop: 'corporationIdCard',
          label: '法人身份证号'
        },
        {
          prop: 'mailAddress',
          label: '邮寄地址'
        }
      ]
    };
  },
  methods: {
    handleTime(time){
      if (time && time.indexOf('.')!== -1) {
        return time.split('.')[0]
      }
      return time
    },

    seefn () {
      this.dialogFormVisible = true
      var userid = this.tableData[0].userId
      this.$nextTick(function () {
        this.bindQRCode(userid);
      })
    },
    bindQRCode: function (id) {
      document.getElementById("qrCode").innerHTML = ''
      new QRCode(this.$refs.qrCodeDiv, {
        text: 'type:1,id:' + id,
        width: 80,
        height: 80,
        colorDark: "#333333", //二维码颜色
        colorLight: "#ffffff", //二维码背景色
        correctLevel: QRCode.CorrectLevel.L//容错率，L/M/H
      })

    },
    onPreview () {
      this.showViewer = true
    },
    // 关闭查看器
    closeViewer () {
      this.showViewer = false
    },
    getData () {
      return this.$http.get(info + `?id=${this.id}&userType=1&driverType=`).then(res => {
        this.tableData = []
        this.tableData3 = []
        if (res.data.data.userBasicinformation.deleteFlag == -1) {
          this.deleteFlagText = '启动账户'
          this.deleteFlags = 0
        } else {
          this.deleteFlagText = '禁用账户'
          this.deleteFlags = -1
        }
        this.tableData.push(res.data.data.userBasicinformation)

        this.tableData3.push(res.data.data.orderBusinessInfo)//业务概况
        this.tableData1 = res.data.data.bankInformation//开户行信息
        this.tableData2 = res.data.data.address
      })

    },
    enablefn () {
      var that = this
      this.$http.post(updateUserStatus + `/${this.id}?deleteFlag=${this.deleteFlags}`).then(res => {
        console.log(res)
        if (res.data.code == 200) {
          this.$message({
            message: res.data.message,
            type: 'success'
          });
          setTimeout(function () {
            that.getData()
          }, 3000);
        } else {
          this.$message.error(res.data.message);
        }

      })
    },
    handleClick (tab, event) {
      console.log(tab, event);
      if (tab.name == 'second') {
        // 触发‘配置管理’事件
        this.second();
      } else {
        // 触发‘用户管理’事件
        this.getData()
          .then(() => {
            if (tab.name === 'three') {
              this.$refs.sign.init(this.tableData[0])
            }
          });
      }
    },
    /**
     * 触发‘配置管理’事件
     */
    second () {
      this.$http.get(logList + `?order=desc&pageNumber=1&pageSize=10&userId=${this.id}`).then(res => {
        this.tableData = res.data.data.list
      })
    }

  },
  activated () {
    this.id = this.$route.query.id;
    this.getData()
      .then(() => {
        if (this.activeName === 'three') {
          this.$refs.sign.init(this.tableData[0])
        }
      })
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
h2 {
  font-weight: normal;
  font-size: 16px;
  margin: 10px 0;
  color: #1898ff;
}
.el-row {
  width: 100%;
}
.tableList {
  margin-bottom: 30px;
}
</style>
