/**
结算单管理下 编辑结算单
**/
<template>
    <div class="app-container detailedList">
        <div class="select-box" style="overflow: hidden">
            <div class="top-title">编辑结算单</div>
            <div class="select-info">
                <el-form
                        size="mini"
                        :inline="true"
                        :model="seachForm"
                        class="demo-form-inline"
                        label-width="100px"
                >
                    <el-form-item label="订单号:">
                        <el-input v-model="seachForm.sn" :οnkeyup="seachForm.sn=seachForm.sn.replace(/\s/g, '')" placeholder="请输入订单编号"></el-input>
                    </el-form-item>
                    <el-form-item label="日期筛选:">
                        <el-date-picker
                                :clearable="false"
                                @blur="selectTime"
                                v-model="seachForm.date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetimerange"
                                :default-time="['00:00:00', '00:00:00']"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button
                                class="left"
                                icon="el-icon-search"
                                size="mini"
                                type="primary"
                                @click="onSearch"
                        >查询
                        </el-button>
                        <el-button
                                class="left"
                                icon="el-icon-delete"
                                size="mini"
                                type="danger"
                                @click="resetSubmit"
                        >清空筛选
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="curTotalInfo" style="float: left">结算单号：{{finalStatementSn}} 当前搜索结果总计：开票吨数 <span
                    style="color: red">{{allInvoiceTon}}</span> 吨；开票金额 <span
                    style="color: red">{{allInvoiceAmout}}</span> 元 选择计数：开票吨数 <span
                    style="color: blue">{{waitPiao}}</span> 吨；开票金额 <span style="color: blue">{{waitMoney}}</span> 元
            </div>
            <div style="float: right;margin-right: 10px;margin-bottom: 10px">
                <el-button :loading="loading" icon="el-icon-folder-delete" size=mini type="danger"
                           @click="refuteMask = true">
                    驳回结算单
                </el-button>
                <el-button :loading="loading" icon="el-icon-tickets" size=mini type="primary" @click="createListFn">
                    生成清单
                </el-button>
            </div>
        </div>
        <div class="list-box">
            <div class="list-right">
                <div class="right-title">数据列表</div>
                <div class="list-main">
                    <template>
                        <el-table
                                :data="tableData" border style="width: 100%"
                                @selection-change="handleSelectionChange"
                        >
                            <el-table-column
                                    type="selection"
                                    width="55">
                            </el-table-column>
                            <el-table-column label="序号" type="index" width="50"></el-table-column>
                            <el-table-column show-overflow-tooltip prop="sn" label="订单号" width="220">
                                <template slot-scope="scope">
                                    <span @click="goDdDetail(scope.row)" style="color: blue;cursor: pointer">{{scope.row.sn}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="cargoType" label="货物类型" width="100"></el-table-column>
                            <el-table-column prop="openTickTon" label="待开票吨数(吨)" width="120"
                                             :formatter="isNullKaipiao"></el-table-column>
                            <el-table-column prop="openTickCount" label="待开票运单" width="120"></el-table-column>
                            <el-table-column prop="openTickAmount" label="待开票金额(元)" width="120"
                                             :formatter="isNullMoney"></el-table-column>
                            <el-table-column show-overflow-tooltip prop="transportAmount" label="结算运费(元)" width="120"></el-table-column>
                            <el-table-column show-overflow-tooltip prop="gasAmount" label="结算油气(元)" width="120"></el-table-column>
                            <el-table-column show-overflow-tooltip prop="deliveryAddressesName" label="订单发货地址"
                                             width="200"></el-table-column>
                            <el-table-column show-overflow-tooltip prop="deliveryAddressesBusinessName" label="开票发货地址"
                                             width="200" :formatter="deliveryAddressesBusinessName"></el-table-column>
                            <el-table-column show-overflow-tooltip prop="receiveAddressesName" label="订单收货地址"
                                             width="200"></el-table-column>
                            <el-table-column show-overflow-tooltip prop="receiveAddressesBusinessName" label="开票收货地址"
                                             width="200" :formatter="receiveAddressesBusinessName"></el-table-column>
                            <el-table-column show-overflow-tooltip prop="createdDate" label="创建日期"
                                             width="150"></el-table-column>
                            <el-table-column fixed="right" label="操作" width="180">
                                <template slot-scope="scope">
                                    <el-button type="text" size="small"
                                               @click="editRow(scope.row)">编辑运单
                                    </el-button>
                                    <el-button type="text" size="small"
                                               @click="adressRow(scope.row)">地址编辑
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </template>
                </div>
            </div>
        </div>
        <!-- 编辑运单-->
        <el-dialog title="拣选订单" :visible.sync="dialogVisible" :before-close="handleClose" width="80%">
            <div>
                <el-form size=mini :inline="true" :model="innerFormData" class="demo-form-inline">
                    <el-form-item label="">
                        <el-input v-model="innerFormData.sn" :οnkeyup="innerFormData.sn=innerFormData.sn.replace(/\s/g, '')" placeholder="请输入运单号"></el-input>
                    </el-form-item>
                    <el-form-item label="">
                        <el-input v-model="innerFormData.driverName" :οnkeyup="innerFormData.driverName=innerFormData.driverName.replace(/\s/g, '')" placeholder="请输入司机"></el-input>
                    </el-form-item>
                    <el-form-item label="运单状态:">
                        <el-select v-model="innerFormData.type" placeholder="筛选运单状态">
                            <el-option label="待付款" value="0"></el-option>
                            <el-option label="已结算" value="1"></el-option>
                            <el-option label="结算中" value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSubmitMask">查询</el-button>
                        <el-button icon="el-icon-delete" size="mini" type="danger" @click="clearchSubmitInner">清空筛选
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="curTotalInfo" style="margin-bottom: 10px">当前搜索结果总计：开票吨数 <span
                    style="color: red">{{innerSumTon}}</span> 吨；开票金额 <span
                    style="color: red">{{innerSumAmount}}</span> 元 选择计数：开票吨数 <span
                    style="color: blue">{{innerWaitPiao}}</span> 吨；开票金额 <span
                    style="color: blue">{{innerWaitMoney}}</span> 元
            </div>
            <div>
                <template>
                    <el-table
                            ref="maskTable"
                            :data="innerTabel"
                            row-key="id"
                            border
                            style="width: 100%"
                            @selection-change="innerMaskSelectRow"
                            :row-key="getRowKey"
                    >
                        <el-table-column
                                type="selection"
                                :reserve-selection="true"
                                width="55">
                        </el-table-column>
                        <el-table-column type="index" label="序号" width="50"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="sn" label="运单号" width="280">
                            <template slot-scope="scope">
                                <span @click="goYdDetail(scope.row)" style="color: blue;cursor: pointer">{{scope.row.sn}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="freight" label="运费单价(元)" width="180"></el-table-column>
                        <el-table-column prop="paymentTon" label="结算吨数(吨)" width="180"></el-table-column>
                        <el-table-column prop="amount" label="结算金额(元)" width="180"></el-table-column>
<!--                        <el-table-column show-overflow-tooltip prop="transportAmount" label="结算运费(元)" width="120"></el-table-column>-->
<!--                        <el-table-column show-overflow-tooltip prop="gasAmount" label="结算油气(元)" width="120"></el-table-column>-->
                        <el-table-column prop="driverName" label="司机" width="180"></el-table-column>
                        <el-table-column prop="driverMobile" label="电话" width="180"></el-table-column>
                        <el-table-column prop="plateNumber" label="车牌号" width="180"></el-table-column>
                        <el-table-column prop="lastModifiedDate" label="结算时间" width="180"></el-table-column>
                        <el-table-column fixed="right" label="操作" width="180">
                            <template slot-scope="scope">
                                <el-button
                                        :disabled="canMark"
                                        v-if="scope.row.scheduleNonPayment==='1'" type="text" size="small"
                                        @click="changeInnerRow(scope.row)">标记为不可结算
                                </el-button>
                                <el-button
                                        :disabled="canMark"
                                        style="color: #00cb8a" v-if="scope.row.scheduleNonPayment==='2'" type="text"
                                        size="small" @click="changeInnerRow(scope.row)">标记为可结算
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </div>
            <div style="margin-top: 20px;overflow: hidden">
                <div style="float: left">
                    <el-button type="primary" size="mini" @click="ascOrDesc(innerTabel)">全选/反选</el-button>
                    <el-button type="danger" size="mini" @click="clearSelect">清空全部选中</el-button>
                    <el-button type="success" size="mini" @click="markerOnekey">一键标记</el-button>
                </div>
                <div style="float: right">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 30, 40]"
                            :page-size="pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="innerTotal">
                    </el-pagination>
                </div>
            </div>

        </el-dialog>
        <!-- 编辑地址弹窗 -->
        <el-dialog title="地址编辑" :visible.sync="dialogFormVisible">
            <el-form :model="form" label-width="120px">
                <el-form-item label="发货单位名称:">
                    <el-input v-model="form.deliveryAddressesBusinessName"></el-input>
                </el-form-item>
                <el-form-item label="发货地址名称:">
                    <el-input v-model="form.deliveryAddressesName" :disabled="true"></el-input>
                </el-form-item>
                <el-form-item label="收货单位名称:">
                    <el-input v-model="form.receiveAddressesBusinessName"></el-input>
                </el-form-item>
                <el-form-item label="收货地址名称:">
                    <el-input v-model="form.receiveAddressesName" :disabled="true"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取 消</el-button>
                <el-button type="primary" @click="sureAdressChange">确 定</el-button>
            </div>
        </el-dialog>
        <!--生成清单-->
        <el-dialog
                title="提示"
                :visible.sync="createListMask"
                width="30%"
                :before-close="handleClose">
            <div>
                <p>当前用户名称</p>
                <div>
                    <el-input :disabled="true" v-model="createListMaskUserName"></el-input>
                </div>
                <p>当前用户服务费比率</p>
                <div>
                    <el-input :disabled="MaskValueFlag" v-model="createListMaskValue"
                              oninput="value=value.match(/\d+\.?\d{0,2}/,'')"><span slot="append">%</span>
                    </el-input>
                </div>
                <div style="margin-top: 30px">
                    <el-row>
                        <el-button size="mini" type="primary" @click="modifyMask">修 改</el-button>
                        <el-button :loading="loading" size="mini" type="primary" @click="saveModifyMask">保存并生成清单
                        </el-button>
                    </el-row>
                </div>
            </div>
        </el-dialog>
        <!--驳回结算单-->
        <el-dialog
                title="驳回提示"
                :visible.sync="refuteMask"
                width="30%"
                :before-close="handleClose">
            <div>
                <el-radio v-model="radio" label="2">驳回结算单</el-radio>
                <el-radio v-model="radio" label="3">删除结算单</el-radio>
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button @click="refuteMask = false">取 消</el-button>
    <el-button type="primary" @click="refuteSure">确 定</el-button>
  </span>
        </el-dialog>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                radio: '2',
                refuteMask: false,
                MaskValueFlag: true,
                createListMaskUserName: '',
                createListMaskValue: '',
                loading: false,
                createListMask: false,
                canMark: false,
                allInvoiceAmout: '', //合计可开票金额
                allInvoiceTon: '', //合计可开票吨数
                waitPiao: 0, //用户选择 开票吨数
                waitMoney: 0, //用户选择 开票金额

                innerSumAmount: '', //内层开票金额
                innerSumTon: '', //内层开票吨数
                innerWaitPiao: 0, //内层待开票吨数
                innerWaitMoney: 0, //内层待开票金额


                finalStatementSn: '',

                dialogVisible: false,
                dialogFormVisible: false,
                formInline: {
                    user: "",
                    region: "",
                    date: ""
                },
                seachForm: {
                    sn: "",
                    date: "",
                },
                startTime: "",
                endTime: "",
                form: {
                    deliveryAddressesBusinessName: "",
                    deliveryAddressesName: "",
                    receiveAddressesBusinessName: "",
                    receiveAddressesName: ""
                },
                tableData: [],
                innerTabel: [],
                multipleSelection: [],
                innerTabelSelectRow: [],
                innerFormData: {
                    sn: '',
                    driverName: '',
                    type: '',
                },
                editInnerId: '',
                idsInner: [],
                ids: [],
                allSelect: false, //列表全选 或者反选
                currentPage: 1,
                pageSize: 20,
                innerTotal: 1,
                canClick: true,
            };
        },
        methods: {
            /** 驳回结算单 **/
            refuteSure() {
                let finalStatementId = this.$route.query.id;
                let invoiceStatus = this.radio;
                this.$http.post('/admin-center-server/app/final_statement/edit_invoice_?finalStatementId=' + finalStatementId + '&invoiceStatus=' + invoiceStatus).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.$message.success(data.message);
                        this.refuteMask = false;
                        this.$router.push({
                            name: 'Statements'
                        })
                    } else {
                        this.$message.warning(data.message)
                    }
                })
            },
            /** 点击订单号 跳转至订单详情 **/
            goDdDetail(row) {
                this.$router.push({
                    name: "OrderDetail",
                    query: {
                        orderBusinessId: row.businessOrderId,
                    }
                });
            },
            /** 点击运单号 跳转至运单详情 **/
            goYdDetail(row) {
                let id = row.orderItemId;
                this.dialogVisible= false;
                this.$router.push({
                    name: "TransportListDetail",
                    query: {
                        orderItemId: id,
                    }
                });
            },
            deliveryAddressesBusinessName(row) {
                if (row.deliveryAddressesBusinessName === null || row.deliveryAddressesBusinessName === 'undefined') {
                    return '暂无'
                } else {
                    return row.deliveryAddressesBusinessName
                }
            },
            receiveAddressesBusinessName(row) {
                if (row.receiveAddressesBusinessName === null || row.receiveAddressesBusinessName === 'null') {
                    return '暂无'
                } else {
                    return row.receiveAddressesBusinessName
                }
            },
            /** 主表 可结算运单变成null **/
            isNullKaipiao(row) {
                if (row.openTickTon === null) {
                    return '0'
                } else {
                    return row.openTickTon
                }
            },
            isNullMoney(row) {
                if (row.openTickAmount === null) {
                    return '0'
                } else {
                    return row.openTickAmount
                }
            },
            /** 编辑运单 **/
            editRow(row) {
                let id = row.id;
                this.editInnerId = id;
                this.refreshMaskTable()
            },
            /** 内表分页后 刷新表格 **/
            refreshMaskTable() {
                let id = this.editInnerId;
                let finalStatementId = this.$route.query.id;
                this.$http
                    .get("/admin-center-server/app/invoice_order_item/editDoneList", {
                        params: {
                            finalStatementId: finalStatementId,
                            invoiceBusinessOrderId: id,
                            pageNumber: this.currentPage,
                            pageSize: this.pageSize,
                            driverName: this.innerFormData.driverName,
                            sn: this.innerFormData.sn,
                            type: this.innerFormData.type,
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.dialogVisible = true;
                            this.innerTabel = data.data.pageInfo.list;
                            this.innerTotal = Number(data.data.pageInfo.total);
                            this.innerSumAmount = data.data.sumAmount;
                            this.innerSumTon = data.data.sumTon;
                        } else if (data.code === "422") {
                            this.$message.warning(data.message);
                        } else {
                            this.$message.warning(data.message)
                        }
                    });
            },
            /** 内表分页 **/
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1;
                this.refreshMaskTable();
            },
            handleCurrentChange(val) {
                this.currentPage = val
                this.refreshMaskTable();
            },
            /**内表格 编辑运单下的标记可结算不可结算 **/
            changeInnerRow(row) {
                let type = row.scheduleNonPayment;
                this.canMark = true;
                if (type === '1') {
                    let str = [];
                    let obj = {
                        invoiceItemId: row.id,
                        scheduleNonPayment: 2
                    };
                    str.push(obj);
                    this.$http.post('/admin-center-server/app/invoice_order_item/operatePaySchdule', str).then(res => {
                        let data = res.data;
                        if (data.code === '200') {
                            this.refreshMaskTable();
                            this.$message.success(data.message);
                            this.canMark = false;
                        } else {
                            this.$message.warning(data.message);
                            this.canMark = false;
                        }
                    })
                } else if (type === '2') {
                    let str = [];
                    let obj = {
                        invoiceItemId: row.id,
                        scheduleNonPayment: 1
                    };
                    str.push(obj);
                    this.$http.post('/admin-center-server/app/invoice_order_item/operatePaySchdule', str).then(res => {
                        let data = res.data;
                        if (data.code === '200') {
                            this.refreshMaskTable();
                            this.$message.success(data.message);
                            this.canMark = false;
                        } else {
                            this.$message.warning(data.message);
                            this.canMark = false;
                        }
                    })
                }
            },
            /** 弹出框 表格多选 **/
            innerMaskSelectRow(val) {
                this.innerTabelSelectRow = val;
                let idsInner = [];
                this.innerWaitPiao = 0;
                this.innerWaitMoney = 0;
                val.map((item, index) => {
                    this.innerWaitPiao = this.plus(this.innerWaitPiao, item.paymentTon);
                    this.innerWaitMoney = this.plus(this.innerWaitMoney, item.amount);
                    idsInner.push(item.id)
                });
                this.idsInner = idsInner;
            },
            /** 一键标记 **/
            markerOnekey() {
                if (this.innerTabelSelectRow.length < 1) {
                    this.$message.warning('至少选择一条运单')
                } else {
                    this.canMark = true;
                    let flag = this.innerTabelSelectRow[0].scheduleNonPayment;
                    let isGo = true;
                    for (let i = 0; i < this.innerTabelSelectRow.length; i++) {
                        let curItem = this.innerTabelSelectRow[i];
                        if (curItem.scheduleNonPayment !== flag) {
                            isGo = false;
                            break
                        }
                    }
                    if (isGo === false) {
                        this.$message.warning('所选中有不一致的状态');
                        this.canMark = false;
                    } else {
                        let type = this.innerTabelSelectRow[0].scheduleNonPayment;
                        if (type === '1') {
                            let str = [];
                            this.innerTabelSelectRow.map((item, index) => {
                                let obj = {
                                    invoiceItemId: item.id,
                                    scheduleNonPayment: 2
                                };
                                str.push(obj)
                            });
                            this.$http.post('/admin-center-server/app/invoice_order_item/operatePaySchdule', str).then(res => {
                                let data = res.data;
                                if (data.code === '200') {
                                    this.refreshMaskTable();
                                    this.$refs.maskTable.clearSelection();
                                    this.$message.success(data.message);
                                    this.canMark = false;
                                } else {
                                    this.$message.warning(data.message);
                                    this.canMark = false;
                                }
                            })
                        } else if (type === '2') {
                            let str = [];
                            this.innerTabelSelectRow.map((item, index) => {
                                let obj = {
                                    invoiceItemId: item.id,
                                    scheduleNonPayment: 1
                                };
                                str.push(obj)
                            });
                            this.$http.post('/admin-center-server/app/invoice_order_item/operatePaySchdule', str).then(res => {
                                let data = res.data;
                                if (data.code === '200') {
                                    this.refreshMaskTable();
                                    this.$refs.maskTable.clearSelection();
                                    this.$message.success(data.message);
                                    this.canMark = false;
                                } else {
                                    this.$message.warning(data.message);
                                    this.canMark = false;
                                }
                            })
                        }
                    }

                }
            },
            /** 全选反选 **/
            ascOrDesc(rows) {
                if (rows) {
                    rows.forEach(row => {
                        this.$refs.maskTable.toggleAllSelection(row, !this.allSelect)
                    });
                    this.allSelect = !this.allSelect
                }
            },
            // 清空选择的值
            clearSelect() {
                this.$refs.maskTable.clearSelection()
            },
            /** 记住选中状态 **/
            getRowKey(row) {
                return row.id
            },

            /** 地址编辑 **/
            adressRow(row) {
                let id = row.id;
                this.$http
                    .get("/admin-center-server/app/invoice_business_order/addresses_view", {
                        params: {
                            id: id
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            (this.form = {
                                deliveryAddressesBusinessName: data.data.deliveryBusinessName,
                                deliveryAddressesName: data.data.deliveryAddressName,
                                receiveAddressesBusinessName: data.data.receiveBusinessName,
                                receiveAddressesName: data.data.receiveAddressName,
                                deliveryId: data.data.deliveryId,
                                receiveId: data.data.receiveId
                            }),
                                (this.dialogFormVisible = true);
                        }
                    });
            },
            /** 确定修改地址 **/
            sureAdressChange() {
                let deliveryBusinessName = this.form.deliveryAddressesBusinessName;
                let deliveryId = this.form.deliveryId;
                let receiveBusinessName = this.form.receiveAddressesBusinessName;
                let receiveId = this.form.receiveId;
                this.$http
                    .post(
                        "/admin-center-server/app/invoice_business_order/addresses_edit?deliveryBusinessName=" +
                        deliveryBusinessName +
                        "&deliveryId=" +
                        deliveryId +
                        "&receiveBusinessName=" +
                        receiveBusinessName +
                        "&receiveId=" +
                        receiveId
                    )
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.getDataList();
                            this.$message.success("修改成功");
                            this.dialogFormVisible = false;
                        }
                    });
            },
            /** 解决浮点数计算 **/
            times(num1, num2) {
                const num1String = num1.toString();
                const num2String = num2.toString();
                const num1Digits = (num1String.split('.')[1] || '').length;
                const num2Digits = (num2String.split('.')[1] || '').length;
                const baseNum = Math.pow(10, num1Digits + num2Digits);
                return Number(num1String.replace('.', '')) * Number(num2String.replace('.', '')) / baseNum
            },
            plus(num1, num2) {
                const num1Digits = (num1.toString().split('.')[1] || '').length;
                const num2Digits = (num2.toString().split('.')[1] || '').length;
                const baseNum = Math.pow(10, Math.max(num1Digits, num2Digits));
                return (this.times(num1, baseNum) + this.times(num2, baseNum)) / baseNum
            },
            /** 多选  计算开票总额和总吨数**/
            handleSelectionChange(val) {
                let ids = [];
                this.multipleSelection = val;
                this.waitPiao = 0;
                this.waitMoney = 0;
                val.map((item, index) => {
                    if (item.openTickTon === null) {
                        item.openTickTon = 0;
                        this.waitPiao = this.plus(this.waitPiao, item.openTickTon);
                        ids.push(item.id)
                    } else {
                        this.waitPiao = this.plus(this.waitPiao, item.openTickTon);
                        ids.push(item.id)
                    }
                });
                val.map((item, index) => {
                    if (item.openTickAmount === null) {
                        item.openTickAmount = 0;
                        this.waitMoney = this.plus(this.waitMoney, item.openTickAmount);
                        ids.push(item.id)
                    } else {
                        this.waitMoney = this.plus(this.waitMoney, item.openTickAmount);
                        ids.push(item.id)
                    }
                });
                this.ids = ids;
            },
            /** 内层表格查询 **/
            onSubmitMask() {
                this.currentPage = 1;
                this.refreshMaskTable();
            },
            /** mask 清除查询 **/
            clearchSubmitInner() {
                this.innerFormData = {
                    sn: '',
                    driverName: '',
                    type: '',
                };
                this.refreshMaskTable();
            },
            /** 生成清单 **/
            createListFn() {
                let str = this.multipleSelection;
                let finalStatementId = this.$route.query.id;
                if (str.length < 1) {
                    this.$message.warning('请选择一条运单')
                } else {
                    this.$http.get('/admin-center-server/app/invoice_schedule/checkSchedules', {
                        params: {
                            finalStatementId: finalStatementId
                        }
                    }).then(res => {
                        let data = res.data;
                        if (data.data === false) {
                            let flag = true;
                            let ids = [];
                            str.map((item, index) => {
                                if (item.deliveryAddressesBusinessName === null || item.receiveAddressesBusinessName === null || item.deliveryAddressesBusinessName === "undefined" || item.receiveAddressesBusinessName === 'null') {
                                    flag = false;
                                } else {
                                    ids.push(item.id)
                                }
                            });
                            if (flag === true) {
                                this.loading = true;
                                this.$http.post('/admin-center-server/app/invoice_schedule/business_order_create', {
                                    finalStatementId: finalStatementId,
                                    ids: ids,
                                }).then(res => {
                                    let data = res.data;
                                    if (data.code === '200') {
                                        this.$message.success(data.message);
                                        setTimeout(() => {
                                            this.loading = false;
                                            this.$router.push({
                                                path: 'statementList',
                                                query: {
                                                    id: finalStatementId,
                                                }
                                            })
                                        }, 1000)
                                    } else if (data.code === '422') {
                                        this.loading = false;
                                        this.$message.warning(data.message)
                                    } else {
                                        this.loading = false;
                                        this.$message.error(data.message)
                                    }
                                })
                            } else if (flag === false) {
                                this.$message.warning('开票发货地址或开票收货地址不能为空')
                            } else {
                                flag = true
                            }
                        } else if (data.data === true) {
                            this.createListMask = true;
                            let userId = this.$route.query.userId;
                            this.$http.get('/admin-center-server/getUserTax', {
                                params: {
                                    id: userId
                                }
                            }).then(res => {
                                let data = res.data;
                                if (data.code === '200') {
                                    this.createListMaskUserName = data.data.name;
                                    this.createListMaskValue = data.data.taxThreshold;
                                } else {
                                    this.$message.warning(data.message)
                                }
                            })
                        }
                    });

                }
            },
            /** 修改 **/
            modifyMask() {
                this.MaskValueFlag = false
            },
            /** 保存服务费比率 并生成清单 **/
            saveModifyMask() {
                this.MaskValueFlag = true;
                let finalStatementId = this.$route.query.id;
                let str = this.multipleSelection;
                let userId = this.$route.query.userId;
                let taxThreshold = this.createListMaskValue;
                let form = {
                    finalStatementId: finalStatementId,
                    userId: userId,
                    taxThreshold: taxThreshold,
                };
                this.loading = true;
                if (Number(form.taxThreshold) > Number(9) && Number(form.taxThreshold) != Number(9)) {
                    this.loading = false;
                    this.$message.warning('用户服务费比率不能大于9')
                } else {
                    this.$http.post('/admin-center-server/app/invoice_schedule/repeatTax', form).then(res => {
                        let data = res.data;
                        if (data.code === '200') {
                            let flag = true;
                            let ids = [];
                            str.map((item, index) => {
                                if (item.deliveryAddressesBusinessName === null || item.receiveAddressesBusinessName === null || item.deliveryAddressesBusinessName === "undefined" || item.receiveAddressesBusinessName === 'null') {
                                    flag = false;
                                } else {
                                    ids.push(item.id)
                                }
                            });
                            if (flag === true) {
                                this.loading = true;
                                this.$http.post('/admin-center-server/app/invoice_schedule/business_order_create', {
                                    finalStatementId: finalStatementId,
                                    ids: ids,
                                }).then(res => {
                                    let data = res.data;
                                    if (data.code === '200') {
                                        this.$message.success(data.message);
                                        let userId = this.$route.query.userId;
                                        this.createListMask = false;
                                        // setTimeout(() => {
                                            this.loading = false;
                                            this.$router.push({
                                                path: 'statementList',
                                                query: {
                                                    id: finalStatementId,
                                                    ownerId: userId,
                                                    invoiceStatus: '0',
                                                }
                                            })
                                        // }, 1000)
                                    } else if (data.code === '422') {
                                        this.loading = false;
                                        this.$message.warning(data.message)
                                    } else {
                                        this.loading = false;
                                        this.$message.error(data.message)
                                    }
                                })
                            } else if (flag === false) {
                                this.loading = false;
                                this.$message.warning('开票发货地址或开票收货地址不能为空')
                            } else {
                                flag = true;
                                this.loading = false;
                            }
                        } else {
                            this.$message.warning(data.message);
                            this.loading = false;
                        }
                    })
                }
            },
            /** 弹窗 上叉号关闭 支持异常回调**/
            handleClose(done) {
                this.dialogVisible = false;
                this.createListMask = false;
                this.refuteMask = false;
                this.MaskValueFlag = true;
                this.getDataList();
            },
            /** 根据时间态搜索 **/
            selectTime() {
                if(this.seachForm.date!==null){
                    let startTime = this.seachForm.date[0];
                    let endTime = this.seachForm.date[1];
                    this.startTime = startTime;
                    this.endTime = endTime;
                }else {
                    this.seachForm.date=[]
                }

            },
            /** 订单列表查询 **/
            onSearch() {
                this.getDataList();
            },
            /** 清空筛选 **/
            resetSubmit() {
                this.seachForm = {
                    sn: "",
                    date: ""
                };
                this.startTime = "";
                this.endTime = "";
                this.getDataList();
            },
            /** 获取数据列表 **/
            getDataList() {
                let id = this.$route.query.id;
                let finalStatementSn = sessionStorage.getItem("finalStatementSn");
                this.finalStatementSn = finalStatementSn;
                this.$http
                    .get("/admin-center-server/app/invoice_business_order/view_list", {
                        params: {
                            finalStatementId: id,
                            startDate: this.startTime,
                            endDate: this.endTime,
                            sn: this.seachForm.sn
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.tableData = data.data.list;
                            this.allInvoiceAmout = data.data.allInvoiceAmout;
                            this.allInvoiceTon = data.data.allInvoiceTon;

                        }
                    });
            }
        },
        activated() {
            this.getDataList();
        }
    };
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .tip {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 40px 40px 0;
        font-size: 12px;

        em {
            margin-right: 5px;
        }
    }

    .inner-box {
        margin-left: 10%;
        width: 70%;

        .upload-box {
            width: 100%;
            height: 100%;
            position: relative;

            .icon-XZ {
                width: 92px;
                height: 92px;
                margin: 0 auto;
                background-size: 100% 100%;
            }

            .icon-word {
                width: 100%;
                height: 20px;
                line-height: 20px;
                font-size: 10px;
                position: absolute;
                bottom: 25px;
                left: 0px;
                color: #cccccc;
            }
        }
    }

    .detailedList {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
            }

            .select-info {
                padding-top: 20px;
            }

            .curTotalInfo {
                padding-left: 20px;
                height: 30px;
                line-height: 30px;
                font-size: 12px;
                color: #999999;
            }
        }

        .createList {
            height: 30px;
            margin-top: 20px;
            text-align: right;
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 10px;
            padding: 10px;
            overflow: hidden;

            .list-right {
                padding-left: 10px;

                .right-title {
                    font-size: 14;
                    font-weight: 700;
                }

                .list-main {
                    width: 100%;
                    border: 1px solid #cccccc;
                    margin-top: 10px;
                }

                .paging {
                    margin-top: 10px;
                    float: right;
                }
            }
        }
    }
</style>
