<template>
  <div class="app-container">
    <div class="select-box">
      <div class="top-title">筛选查询</div>
      <div class="select-info">
        <el-form size="mini" :inline="true" label-width="100px">
          <el-form-item label="编号">
            <el-input v-model="searchForm.consultNum" placeholder="请输入编号"></el-input>
          </el-form-item>
          <el-form-item label="提交用户">
            <el-input v-model="searchForm.createUserName" placeholder="请输入用户名称"></el-input>
          </el-form-item>
          <el-form-item label="类型">
            <el-select v-model="searchForm.type">
              <el-option value="" label="不限"></el-option>
              <el-option value="1" label="咨询"></el-option>
              <el-option value="2" label="投诉"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="getList" type="primary">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
      </div>
      <div class="list-main">
        <el-table :data="data">
          <el-table-column label="序号" type="index"></el-table-column>
          <el-table-column label="编号" prop="consultNum" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column label="主题" prop="title" width="400" show-overflow-tooltip=""></el-table-column>
          <el-table-column label="类型" prop="typeEnumString"></el-table-column>
          <el-table-column label="提交用户" prop="createUserName"></el-table-column>
          <el-table-column label="用户类型" prop=""></el-table-column>
          <el-table-column label="提交时间" prop="createTime" width="150"></el-table-column>
          <el-table-column label="处理人" prop="handleUserName" :formatter="nullValueTableFormatter"></el-table-column>
          <el-table-column label="处理时间" prop="handleTime" width="150" :formatter="nullValueTableFormatter"></el-table-column>
          <el-table-column label="状态" prop="statusEnumString"></el-table-column>
          <el-table-column label="评价" prop="evaluationEnumString" :formatter="nullValueTableFormatter"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button @click="view(scope.row)" type="text">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page.sync="page.pageNumber"
          @current-change="getList"
          @size-change="handleSizeChange"
          :total="total"
          layout="total, sizes, prev, pager, next"
          class="pagination"></el-pagination>
      </div>
    </div>
    <el-dialog
      title="查看"
      width="1000px"
      :visible.sync="isDetailShow">
      <el-row class="info">
        <el-col :span="6">
          <span class="info-title">类型：</span>
          <span class="info-content">{{ info.typeEnumString }}</span>
        </el-col>
        <el-col :span="6">
          <span class="info-title">状态：</span>
          <span class="info-content">{{ info.statusEnumString }}</span>
        </el-col>
        <el-col :span="6">
          <span class="info-title">提交时间：</span>
          <span class="info-content">{{ info.createTime }}</span>
        </el-col>
        <el-col :span="6">
          <span class="info-title">更新时间：</span>
          <span class="info-content">{{ info.updateTime || '-' }}</span>
        </el-col>
      </el-row>
      <el-row class="info">
        <el-col :span="24">
          <span class="info-title">主题：</span>
          <span class="info-content">{{ info.title }}</span>
        </el-col>
      </el-row>
      <el-row class="info pics">
        <imageUploader2
          v-for="item in info.consultImageUrl"
          :defaultUrl="item"
          :key="item"
          type="viewer"
          :size="[120, 120]"></imageUploader2>
      </el-row>
      <el-divider></el-divider>
      <div class="messages">
        <template v-if="messages.length > 0">
          <div v-for="(item, index) in messages" :key="index" class="message" :class="{'message-user': item.sendUserRole === '1'}">
            <div class="message-info">
              <span class="message-time">{{ item.sendTime }}</span>
              <span class="message-name">{{ item.sendUserRole === '2' ? '我' : '用户' }}</span>
            </div>
            <div class="message-content">{{ item.content }}</div>
          </div>
        </template>
        <div v-else class="message-empty">暂无数据</div>
      </div>
      <el-divider></el-divider>
      <template v-if="info.status !== '3'">
        <el-form label-width="60px">
          <el-form-item label="回复：">
            <el-input v-model="replyContent" type="textarea" :rows="3" resize="none"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="reply" type="primary">提交</el-button>
          </el-form-item>
        </el-form>
      </template>
      <template v-else>
        <el-row class="info">
          <el-col :span="6">
            <span class="info-title">评价：</span>
            <span class="info-content">{{ info.evaluationEnumString }}</span>
          </el-col>
        </el-row>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      page: {
        pageSize: 10,
        pageNumber: 1
      },
      searchForm: {},
      total: 0,
      data: [],
      isDetailShow: false,
      info: {},
      messages: [],
      replyContent: ''
    }
  },
  activated() {
    this.getList()
  },
  methods: {
    resetSearch() {
      this.searchForm = {}
      this.getList()
    },
    getList() {
      this.$post('/admin-center-server/admin/consult/pageConsult', {
        ...this.page,
        ...this.searchForm
      })
        .then(res => {
          this.data = res.list
          this.total = Number(res.total)
        })
    },
    handleSizeChange(v) {
      this.page.pageSize = v
      this.page.pageNumber = 1
      this.getList()
    },
    getDetail(id) {
      return this.$get('/admin-center-server/admin/consult/getConsultDetail', {
         id
      })
        .then(res => {
          this.info = res.consultVO
          if (this.info.consultImageUrl) {
            this.info.consultImageUrl = this.info.consultImageUrl.split(',')
          }
          this.messages = res.consultMsgVOS
        })
    },
    view(item) {
      this.getDetail(item.id)
        .then(() => {
          this.isDetailShow = true
        })
    },
    reply() {
      if (!this.replyContent || !this.replyContent.trim()) {
        this.$message.error('回复内容不能为空')
        return
      }
      this.$post('/admin-center-server/admin/consult/customerServiceReply', {
        consultNum: this.info.consultNum,
        content: this.replyContent
      })
        .then(() => {
          this.getDetail(this.info.id)
          this.replyContent = ''
          this.isDetailShow = false
          this.$message.success('操作成功')
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.select-box {
  background-color: #ffffff;

  .top-title {
    font-size: 16px;
    font-weight: 700;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    border-bottom: 1px solid #cccccc;
  }

  .select-info {
    padding-top: 30px;
    padding-bottom: 30px;
  }
}
.list-box {
  background-color: #ffffff;
  margin-top: 20px;
  padding: 10px;
  overflow: hidden;

  .list-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    div {
      height: 38px;
      line-height: 38px;
    }
  }

  .list-main {
    width: 100%;
    border: 1px solid #cccccc;
    margin-top: 10px;

    .el-pagination {
      text-align: right;
    }
  }
}
.info {
  margin-bottom: 20px;
  line-height: 24px;
}
.info > .el-col {
  display: flex;
}
.info-title {
  flex-shrink: 0;
}
.pics {
  margin-left: 70px;
}
.messages {
  overflow-y: auto;
  height: 300px;
  line-height: 22px;
}
.message {
  margin-bottom: 20px;
}
.message-user {
  color: #ED970F;
}
.message-time {
  margin-right: 30px;
}
.message-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
</style>