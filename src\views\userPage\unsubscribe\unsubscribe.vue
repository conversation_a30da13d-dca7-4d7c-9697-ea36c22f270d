<template>
  <div>
    <div class="list-box">
      <div class="list-title">
        <div>注销审核</div>
      </div>
      <div class="list-main">
        <template>
          <el-table :data="data" border style="width: 100%" ref="table">
            <el-table-column type="index" label="序号" width="50"></el-table-column>
            <el-table-column prop="applyDelTime" label="注销申请时间"></el-table-column>
            <el-table-column label="身份">
              <template slot-scope="scope">
                <span v-if="scope.row.userType === '1'">货主</span>
                <span v-else-if="scope.row.userType === '2'">调度员</span>
                <span v-else-if="scope.row.userType === '3'">司机</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="用户姓名"></el-table-column>
            <el-table-column prop="mobile" label="用户手机号"></el-table-column>
            <el-table-column fixed="right" label="操作" width="150">
              <template slot-scope="scope">
                <el-button @click="audit(scope.row, 'pass')" type="text" size="small">允许注销</el-button>
                <el-button @click="audit(scope.row, 'reject')" type="text" size="small">驳回</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="page">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page.sync="page.pageNumber"
              :page-sizes="[20, 40, 60, 80,100]"
              :page-size="page.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              style="margin: 10px auto"
            ></el-pagination>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  activated() {
    this.getList()
  },
  data() {
    return {
      data: [],
      page: {
        pageSize: 20,
        pageNumber: 1
      },
      total: 0
    }
  },
  methods: {
    getList() {
      this.$post('/admin-center-server/applyDelUserList', {
        ...this.page
      })
        .then(res => {
          this.data = res.list
          this.total = Number(res.total)
        })
    },
    audit(item, type) {
      let promise
      if (type === 'pass') {
        promise = this.$confirm('确认允许用户注销该账号？该操作不可逆，请谨慎操作')
      } else {
        promise = this.$prompt('请输入驳回原因', {
          inputValidator(v) {
            if (v === '' || v === null) return '请输入驳回原因'
          }
        })
      }
      promise
        .then(v => {
          let params = {
            userId: item.id
          }
          if (type === 'pass') {
            params.applyDelStatus = 2
          } else {
            params.applyDelStatus = 3
            params.applyDelRejectReason = v.value
          }

          this.$post('/admin-center-server/updateApplyDelStatus', params)
            .then(res => {
              this.getList()
            })
        })
    },
    handleSizeChange(v) {
      this.page.pageSize = v
      this.getList()
    },
    handleCurrentChange() {
      this.getList()
    }
  }
}
</script>
<style scoped lang="scss">
.list-box {
  background-color: #ffffff;
  margin-top: 20px;
  padding: 10px;

  .list-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    div {
      height: 38px;
      line-height: 38px;
    }
  }
  .page {
    text-align: right;
  }
  .list-main {
    width: 100%;
    border: 1px solid #cccccc;
    margin-top: 10px;
  }
}
</style>