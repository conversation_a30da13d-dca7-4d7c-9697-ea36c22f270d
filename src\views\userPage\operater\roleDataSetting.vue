<template>
  <div class="limit-config">
    <div class="limit-title">
      角色名称：{{ query.name }} 当前角色id：{{ query.id }}
    </div>
    <div class="main">
      <div class="item">
        <el-checkbox :value="true" disabled>本人创建的数据</el-checkbox>
      </div>
      <div class="item">
        <el-checkbox v-model="isAppoint" @change="handleAppointChange"
          >指定数据</el-checkbox
        >
        <div v-if="isAppoint" class="appoint">
          <el-select v-model="form.ruleCode">
            <el-option label="账号" value="USER"></el-option>
          </el-select>
          <el-select v-model="form.ruleRelation">
            <el-option label="包含" value="IN"></el-option>
          </el-select>
          <el-select
            v-model="form.ruleData"
            multiple
            @change="handleUserSelect"
          >
            <el-option label="所有" value="all"></el-option>
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="`${item.name}/${item.mobile}`"
              :value="item.id"
              :disabled="
                Array.isArray(form.ruleData) &&
                form.ruleData.length === 1 &&
                form.ruleData[0] === 'all'
              "
            ></el-option>
          </el-select>
        </div>
      </div>
    </div>
     <div class="btns">
       <el-button class="button" type="primary" plain @click="$router.go(-1)">返回</el-button>
       <el-button class="button" @click="save" type="primary">保存</el-button>
     </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      query: {},
      isAppoint: false,
      userList: [],
      form: {},
      hasOtherData: false // 记录是否修改或者新增，新增不传id
    };
  },
  activated() {
    this.query = this.$route.query;

    this.$get("/admin-center-server/operationalPeo/getRelevanceBussinessList").then(
      (res) => {
        this.userList = res.map((item) => {
          item.id = item.userId
          return item
        });
        this.$get("/admin-center-server/roleDataPermission/getByRoleId", {
          roleId: this.query.id,
        }).then((res) => {
          if (!res[0]) {
            this.form = {
              ruleCode: "USER",
              ruleRelation: "IN",
            };
            this.isAppoint = false;
            this.hasOtherData = false
          } else {
            this.form = res[0];
            this.form.ruleData = res[0].ruleData.split("&");
            this.isAppoint = true;
            this.hasOtherData = true
          }
        });
      }
    );
  },
  methods: {
    handleUserSelect() {
      if (
        Array.isArray(this.form.ruleData) &&
        this.form.ruleData.includes("all")
      ) {
        this.form.ruleData = ["all"];
      }
    },
    save() {
      let data = [];
      if (this.isAppoint) {
        let params = {
          ...this.form,
        };
        // 新增
        if (!this.hasOtherData && params.id) {
          params.id = null
        }
        if (!Array.isArray(params.ruleData) || params.ruleData.length === 0) {
          this.$message.error("请选择账号");
          return;
        } else if (params.ruleData[0] === "all") {
          params.ruleData = this.userList.map((v) => v.id).join("&");
        } else {
          params.ruleData = params.ruleData.join("&");
        }
        data = [params];
      }
      this.$post("/admin-center-server/roleDataPermission/saveBatch", {
        roleId: this.query.id,
        saveRoleDataPermissionParams: data,
      }).then(() => {
        this.$message.success("保存成功");

        //将信息保存到本地，用户取消勾选并保存后，再勾选仍能加载
        if (this.isAppoint) {
          let dataConfig = localStorage.getItem("dataConfig");
          dataConfig = dataConfig ? JSON.parse(dataConfig) : {};
          dataConfig[this.query.id] = data[0];
          localStorage.setItem("dataConfig", JSON.stringify(dataConfig));
        }

        this.$router.go(-1);
      });
    },
    handleAppointChange() {
      if (this.isAppoint) {
        let dataConfig = localStorage.getItem("dataConfig");
        if (!dataConfig) return;
        dataConfig = JSON.parse(dataConfig);
        let item = dataConfig[this.$route.query.id];
        if (!item) return;
        this.form = item;
        this.form.ruleData = item.ruleData.split("&");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.limit-config {
  margin: 10px;
  width: 100%;
  min-height: 800px;
  background-color: white;
  .limit-title {
    height: 60px;
    width: 100%;
    line-height: 60px;
    padding-left: 10px;
    font-size: 14px;
    background-color: white;
    // border-bottom: 1px solid #cccccc;
  }
}
.main {
  margin-top: 30px;
  margin-bottom: 70px;
  padding-left: 30px;
}
.item {
  margin-bottom: 15px;
}
.appoint {
  margin-left: 22px;
  margin-top: 10px;
  .el-select:not(:last-child) {
    width: 100px;
    margin-right: 10px;
  }
}
.btns {
  position: fixed;
    bottom: 0;
    right: 0;
    left: 200px;
    height: 70px;
    background-color: white;
    z-index: 99;
    .button {
      margin-left: 20px;
      margin-top: 10px;
      height: 40px;
      width: 90px;
    }
}
</style>