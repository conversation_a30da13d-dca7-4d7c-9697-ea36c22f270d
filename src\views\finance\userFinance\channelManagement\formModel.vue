<template>
    <div class="formModel" v-loading="loading">
        <div class="come_back">
            <el-button icon="el-icon-back" plan @click="handleBack"
                >返 回</el-button
            >
        </div>
        <el-form
            :model="form"
            class="demo-form-inline"
            label-width="120px"
            ref="form"
        >
            <el-row class="global-div-search">
                <el-col :span="24">
                    <div class="title">账户基本信息</div>
                    <div class="interval"></div>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                    <el-form-item label="名称:" prop="channelName">
                        {{ form.channelName || "-" }}
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                    <el-form-item label="平安子账户:" prop="subAcctNo">
                        {{ form.subAcctNo }}
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                    <el-form-item label="开户人姓名:" prop="name">
                        {{ form.name || "-" }}
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                    <el-form-item label="开户人手机号:" prop="mobile">
                        {{ form.mobile || "-" }}
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                    <el-form-item label="开户人身份证号:" prop="idCard">
                        {{ form.idCard || "-" }}
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                    <el-form-item label="开户时间:" prop="createTime">
                        {{ form.createTime || "-" }}
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                    <el-form-item label="绑卡状态:" prop="bindCardStatusName">
                        {{ form.bindCardStatusName || "-" }}
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                    <el-form-item label="折扣比例:" prop="taxThresholdStr">
                        {{ form.taxThresholdStr || "-" }}
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <div class="interval"></div>
                    <div class="title">银行卡信息</div>
                    <div class="interval"></div>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                    <el-form-item label="持卡人:" prop="cardOwnerName">
                        {{ form.cardOwnerName || "-" }}
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                    <el-form-item label="身份证:" prop="cardOwnerIdCard">
                        {{ form.cardOwnerIdCard || "-" }}
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                    <el-form-item label="手机号:" prop="cardOwnerMobile">
                        {{ form.cardOwnerMobile || "-" }}
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                    <el-form-item label="卡号:" prop="acctNo">
                        {{ form.acctNo || "-" }}
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                    <el-form-item label="开户行:" prop="bankName">
                        {{ form.bankName || "-" }}
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                    <el-form-item label="开户支行:" prop="bankLname">
                        {{ form.bankLname || "-" }}
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>
  
<script>
export default {
    name: "formModel",
    data() {
        return {
            loading: true,
            form: {
                channelName: undefined,
                subAcctNo: undefined,
                name: undefined,
                mobile: undefined,
                idCard: undefined,
                createTime: undefined,
                bindCardStatusName: undefined,
                taxThresholdStr: undefined,
                cardOwnerName: undefined,
                cardOwnerIdCard: undefined,
                cardOwnerMobile: undefined,
                acctNo: undefined,
                bankName: undefined,
                bankLname: undefined,
            },
        };
    },

    /** 事件监听 */
    watch: {},

    /** 计算属性 */
    computed: {},

    /** 生命周期 -- 实例创建后调用 */
    created() {},

    /** 生命周期 -- 实例挂载后调用 */
    mounted() {
        document.onkeydown = (e) => {
            if (e.keyCode == 27) {
                // 监听Esc键
                this.handleBack();
            }
        };
    },

    /** 生命周期 -- 实例销毁后调用 */
    destroyed() {
        document.onkeydown = null;
    },

    methods: {
        detail(row) {
            this.reset(3);
            this.getDetail(row);
        },

        /** 获取数据详情 */
        getDetail(row) {
            this.$post(
                `/admin-center-server/commonUser/channelUserDetail?id=${row.id}`
            ).then((res) => {
                this.loading = false;
                this.form = res;
            });
        },

        // 表单重置
        reset(status) {
            this.form = {
                channelName: undefined,
                subAcctNo: undefined,
                name: undefined,
                mobile: undefined,
                idCard: undefined,
                createTime: undefined,
                bindCardStatusName: undefined,
                taxThresholdStr: undefined,
                cardOwnerName: undefined,
                cardOwnerIdCard: undefined,
                cardOwnerMobile: undefined,
                acctNo: undefined,
                bankName: undefined,
                bankLname: undefined,
            };
            this.$refs.form.resetFields();
            this.status = status;
            switch (status) {
                case 1: // 新增
                    break;
                case 2: // 编辑
                    break;
                case 3: // 详情
                    break;
                case 4: // 取消
                    break;
            }
        },

        handleBack() {
            this.reset();
            this.$emit("cancel");
        },
    },
};
</script>
  
<style rel="stylesheet/scss" lang="scss" scoped>
.el-form-item {
    margin-bottom: 0;
}

.formModel {
    background-color: #ffffff;
    padding: 30px;
    position: relative;
    .come_back {
        float: right;
    }
    .list-title {
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        div {
            height: 38px;
            line-height: 38px;
        }
    }
    .page {
        text-align: right;
    }
    .list-main {
        width: 100%;
        border: 1px solid #cccccc;
        margin-top: 10px;
    }
    .title {
        font-size: 16px;
        font-weight: bold;
    }
}

.global-div-search {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    /* font-size: 14px; */
    /* color: #555; */
    position: relative;
}

.el-select {
    width: 100% !important;
}

.el-date-editor.el-input,
.el-date-editor.el-input__inner {
    width: 100% !important;
}
.el-input-number--medium {
    width: 100%;
}

.interval {
    width: 100%;
    padding: 5px 0px;
}
</style>
  