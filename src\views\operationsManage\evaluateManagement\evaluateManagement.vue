<template>
  <div class="app-container">
    <div class="select-box">
      <div class="top-title">筛选查询</div>
      <div class="select-info">
        <el-form size="mini" :inline="true" label-width="100px">
          <el-form-item label="司机姓名">
            <el-input v-model="searchForm.driverName"></el-input>
          </el-form-item>
          <el-form-item label="司机手机号">
            <el-input v-model="searchForm.driverTel"></el-input>
          </el-form-item>
          <el-form-item label="月份">
            <el-date-picker
              v-model="searchForm.month"
              type="month"
              value-format="yyyy-MM"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button @click="getList" type="primary">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
      </div>
      <div class="list-main">
        <el-table :data="data">
          <el-table-column label="序号" type="index"></el-table-column>
          <el-table-column label="运单号" prop="orderItemSn" show-overflow-tooltip></el-table-column>
          <el-table-column label="司机姓名" prop="driverName" show-overflow-tooltip></el-table-column>
          <el-table-column label="司机手机号" prop="driverTel" show-overflow-tooltip></el-table-column>
          <el-table-column label="综合评分" prop="overallScoring" show-overflow-tooltip></el-table-column>
          <el-table-column label="运输效率" prop="transportEfficiency" show-overflow-tooltip></el-table-column>
          <el-table-column label="运输安全" prop="transportSafety" show-overflow-tooltip></el-table-column>
          <el-table-column label="服务质量" prop="serviceQuality" show-overflow-tooltip></el-table-column>
          <el-table-column label="客户满意度" prop="customerSatisfaction" show-overflow-tooltip></el-table-column>
          <el-table-column label="建议" prop="suggestion" show-overflow-tooltip></el-table-column>
          <el-table-column label="评价时间" prop="feedbackTime" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-pagination
          :current-page.sync="page.pageNumber"
          @current-change="getList"
          @size-change="handleSizeChange"
          :total="total"
          layout="total, sizes, prev, pager, next"
          class="pagination"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      page: {
        pageSize: 10,
        pageNumber: 1
      },
      searchForm: {},
      total: 0,
      data: []
    }
  },
  activated() {
    this.getList()
  },
  methods: {
    resetSearch() {
      this.searchForm = {}
      this.getList()
    },
    getList() {
      this.$post('/admin-center-server/feedbackStar/findPage', {
        ...this.page,
        ...this.searchForm
      })
        .then(res => {
          this.data = res.list
          this.total = Number(res.total)
        })
    },
    handleSizeChange(v) {
      this.page.pageSize = v
      this.page.pageNumber = 1
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.select-box {
  background-color: #ffffff;

  .top-title {
    font-size: 16px;
    font-weight: 700;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    border-bottom: 1px solid #cccccc;
  }

  .select-info {
    padding-top: 30px;
    padding-bottom: 30px;
  }
}
.list-box {
  background-color: #ffffff;
  margin-top: 20px;
  padding: 10px;
  overflow: hidden;

  .list-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    div {
      height: 38px;
      line-height: 38px;
    }
  }

  .list-main {
    width: 100%;
    border: 1px solid #cccccc;
    margin-top: 10px;

    .el-pagination {
      text-align: right;
    }
  }
}
.info {
  margin-bottom: 20px;
  line-height: 24px;
}
.info > .el-col {
  display: flex;
}
.info-title {
  flex-shrink: 0;
}
.pics {
  margin-left: 70px;
}
.messages {
  overflow-y: auto;
  height: 300px;
  line-height: 22px;
}
.message {
  margin-bottom: 20px;
}
.message-service {
  color: #ED970F;
}
.message-time {
  margin-right: 30px;
}
.message-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
</style>