<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">筛选查询</div>
      <div class="select-info">
        <el-form :inline="true"
                 :model="formInline"
                 class="demo-form-inline"
                 size="mini"
                 label-width="90px">
          <el-form-item label="司机名称:">
            <el-input v-model="formInline.nameOrCompanyName"
                      class="driver-name-input"
                      placeholder="司机/个人车队长姓名、运力承运商公司名称"></el-input>
          </el-form-item>
          <el-form-item label="认证状态："
                        label-width="130px">
            <el-select v-model="formInline.authStatus"
                       placeholder="请选择认证状态"
                       clearable
                       label-width="10px">
              <el-option v-for="(item, index) in statusList"
                :key="index"
                :label="item.name"
                :value="item.id"></el-option>
            </el-select>
            <el-radio-group v-if="!$store.state.user.userInfo2.hasStandardModeFlag" v-model="formInline.driverType" class="type-radio">
              <el-radio label="99">所有</el-radio>
              <el-radio label="1">司机</el-radio>
              <el-radio label="3">个人车队长</el-radio>
              <el-radio label="5">运力供应商</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="手机号:">
            <el-input v-model="formInline.mobile"
                      oninput="value=value.replace(/[^\d]/g,'')"
                      maxlength="11"
                      placeholder="请输入手机号"></el-input>
          </el-form-item>
          <el-form-item label="身份证:">
            <el-input v-model="formInline.idCard"
                      placeholder="请输入身份证"></el-input>
          </el-form-item>
          <el-form-item label="从业资格证是否异常：" label-width="180px">
            <el-select v-model="formInline.employmentCertWarnFlag">
              <el-option label="不限" value=""></el-option>
              <el-option label="是" value="1"></el-option>
              <el-option label="否" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="临期状态：" prop="nearExpirationStatus">
            <el-select v-model="formInline.nearExpirationStatus">
              <el-option label="不限" value=""></el-option>
              <el-option label="即将到期" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop='updateUserName'
                        label="操作人姓名:">
            <el-input placeholder="请输入操作人姓名"
                      v-model="formInline.updateUserName"></el-input>
          </el-form-item>
          <el-form-item prop='updateUserNick'
                        label="操作人账号:">
            <el-input v-model="formInline.updateUserNick"
                      placeholder="请输入操作人账号"></el-input>
          </el-form-item>
          <el-form-item prop='timeType'
                        label-width="100px">
            <el-select v-model="formInline.timeType"
                       placeholder=""
                       label-width="10px">
              <el-option label='注册时间'
                         value="0">注册时间</el-option>
              <el-option label='操作日期'
                         value="1">操作日期</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label-width="100px"
                        prop='date'>
            <el-col>
              <el-date-picker v-model="searchDate"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              type="datetimerange"
                              range-separator="至"
                              start-placeholder="选择开始时间"
                              end-placeholder="选择结束时间"
                              :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button class="left"
                       icon="el-icon-search"
                       @click="onSubmit">查询</el-button>
            <el-button class="left"
                       @click="refreshfn"
                       icon="el-icon-refresh-right">刷新</el-button>
            <el-button class="left"
                       icon="el-icon-plus"
                       @click="addnewfn">驳回原因管理</el-button>

          </el-form-item>
        </el-form>

      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table ref="multipleTable"
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%"
                    :row-class-name="tableRowClassName"
                    :height="tableHeight"
                    @selection-change="handleSelectionChange"
                    border>
            <el-table-column type="selection"
                             width="55"></el-table-column>
            <el-table-column type="index"
                             label="序号"
                             width="55">
            </el-table-column>
            <el-table-column label="司机名称" prop="name" width="200" show-overflow-tooltip></el-table-column>
            <el-table-column label="手机号" prop="mobile" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column label="身份证" prop="idCard" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column label="申请用户" prop="createUser" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column label="司机认证状态" prop="driverAuthStatusName" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="车队长认证状态" prop="carOwnerStatusName" width="120" show-overflow-tooltip></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="运力承运商认证状态" prop="transportationCapacitySupplierStatusName" width="160" show-overflow-tooltip></el-table-column>
            <el-table-column label="从业资格证异常" width="120" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.employmentCertWarnFlag === '1' ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column label="操作人名称" prop="updateUserName" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column label="操作人账号" prop="updateUserNick" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column label="操作日期" prop="updateDateTime" width="180" show-overflow-tooltip></el-table-column>
            <el-table-column fixed="right"
                             label="操作"
                             min-width="180">
              <template slot-scope="scope">
                <el-button v-if='scope.row.driverAuthStatus!=1'
                           type="text"
                           @click='examinefn(scope.row)'
                           size="small">司机审核</el-button>
                <el-button v-if='scope.row.carAuthType!=0'
                           type="text"
                           @click='examinefn1(scope.row)'
                           size="small">车队长审核</el-button>
                <el-button v-if="scope.row.transportationCapacitySupplierStatus !== '1'" @click="supplierAudit(scope.row)" type="text" size="small">运力供应商审核</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page="pageNumber"
                         :page-sizes="[10,20, 40, 60, 80,100]"
                         :page-size="pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total"
                         class="pagination"></el-pagination>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CarsList",
  data () {
    return {
      tableHeight: null, //表格的高度
      statusList: [
        {
          name: '未认证',
          id: '1'
        },
        {
          name: '认证成功',
          id: '2'
        },
        {
          name: '认证失败（驳回）',
          id: '3'
        },
        {
          name: '认证中',
          id: '4'
        },
        {
          name: '认证过期',
          id: '5'
        },
        {
          name: '补充资源认证中',
          id: '6'
        },
        {
          name: '补充资料驳回',
          id: '7'
        }
      ],
      pageSize: 10,
      total: 0,
      pageNumber: 1,
      formInline: {
        nameOrCompanyName: "",
        authStatus: '4',
        driverType: '99',
        mobile: '',
        idCard: '',
        employmentCertWarnFlag: '',
        nearExpirationStatus: '',
        updateUserName: '',
        updateUserNick: '',
        timeType: '0'
      },
      searchDate: null,
      tableData: []
    };
  },
  methods: {
    // 刷新当前页
    refreshfn () {
      this.$router.go(0)
    },
    deCertificationfn () { this.$router.push("/consignorAccount/addShipper?urlType=3"); },
    addnewfn () { this.$router.push("/consignorExamine/rejectReason"); },
    onSubmit () {
      this.pageNumber = 1
      this.getData()
    },
    //司机审核
    examinefn (row) {
      let routerData
      if (row.driverAuthStatus != 1 && row.carAuthType != 0) {
        routerData = this.$router.resolve("/driverExamine/auditDetails?type=10&id=" + row.id + '&operation=2&tab=1');
      } else {
        routerData = this.$router.resolve("/driverExamine/auditDetails?type=3&id=" + row.id + '&operation=2');
      }
      let auditWindow = window.open(routerData.href)
      this.openTimers = this.openTimers || []
      let openTimer = setInterval(() => {
        if (auditWindow.closed) {
          this.getData()
          clearInterval(openTimer)
        }
      }, 200)
      this.openTimers.push(openTimer)
    },
    //车队长审核
    examinefn1 (row) {
      //carAuthType    '车队长认证类型 1 个人 、2 企业',
      //num 4 个人车队长   5企业车队长
      var num = ''
      if (row.carAuthType == 1) {
        num = 4
      } else if (row.carAuthType == 2) {
        num = 5
      }
      if (row.driverAuthStatus != 1 && row.carAuthType != 0) {
        num = 10
      }
      let routerData = this.$router.resolve("/driverExamine/auditDetails?type=" + num + "&id=" + row.id + '&operation=2&tab=2');
      window.open(routerData.href)
    },
    supplierAudit(row) {
      let routerData = this.$router.resolve({
        path: '/driverExamine/supplierAuditDetail',
        query: {
          id: row.id,
          type: 'audit'
        }
      })
      let auditWindow = window.open(routerData.href)
      this.openTimers = this.openTimers || []
      let openTimer = setInterval(() => {
        if (auditWindow.closed) {
          this.getData()
          clearInterval(openTimer)
        }
      }, 200)
      this.openTimers.push(openTimer)
    },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },

    handleSizeChange (val) {
      this.pageNumber = 1
      this.pageSize = val
      this.getData()
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange (val) {
      this.pageNumber = val
      this.getData()
      console.log(`当前页: ${val}`);
    },
    getData () {
      let params = {
        ...this.formInline,
        pageNumber: this.pageNumber,
        pageSize: this.pageSize
      }
      if (this.searchDate !== null) {
        params.minCreateTime = this.searchDate[0]
        params.maxCreateTime = this.searchDate[1]
      }
      this.$post('/admin-center-server/verify/getDriverVerifyPage', params)
        .then(res => {
          this.tableData = res.list
          this.total = Number(res.total)
        })
    },
    tableRowClassName ({ row, rowIndex }) {
      if (row.driverAuthStatus === '2') {
        return 'warning-row';
      } else if (row.driverAuthStatus === '3') {
        return 'success-row';
      } else if (row.driverAuthStatus === '4' || row.carOwnerAuthStatus === '4' || row.transportationCapacitySupplierStatus === '4') {
        return 'inAudit-row';
      }
      return '';
    }
  },
  activated () {
    if (this.$store.state.user.userInfo2.hasStandardModeFlag) {
      this.formInline.driverType = '1'
    } else {
      this.formInline.driverType = '99'
    }
    
    this.getData()
    this.tableHeight =
      window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 160;
  },
  deactivated() {
    this.openTimers&&this.openTimers.forEach(v => clearInterval(v))
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
.driver-name-input {
  width: 280px;
}
.type-radio {
  margin-left: 15px;
  .el-radio {
    margin-right: 10px;
  }
}
</style>
