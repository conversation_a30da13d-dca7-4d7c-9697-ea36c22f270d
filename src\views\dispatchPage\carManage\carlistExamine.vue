<template>
    <div class="app-container carInfo">
        <div class="main-box">
            <div class="list-info">
                <el-form ref="form" label-width="130px">
                    <el-form-item label="车牌号:">
                        <span>{{plateNumber}}</span>
                    </el-form-item>
                    <el-form-item label="车辆颜色:">
                        <span>{{ data.plateColor === '1' ? '蓝色' : '黄色'}}</span>
                    </el-form-item>
                    <el-form-item label="是否挂靠车辆:">
                        <span>{{ data.affiliatedFlag === '1' ? '是' : '否'}}</span>
                    </el-form-item>
                    <el-form-item label="车辆车队长:">
                        <span>{{data.ownerName}}</span>
                    </el-form-item>
                    <el-form-item label="行驶证档案编号:">
                        <span>{{data.licenseNumber}}</span>
                    </el-form-item>
                    <el-form-item label="行驶证-所有人:">
                        <span>{{data.licenseOwner}}</span>
                    </el-form-item>
                    <el-form-item label="车型:">
                        <span>{{data.carModelName}}</span>
                    </el-form-item>
                    <el-form-item label="车辆品牌:">
                        <span>{{data.cypp}}</span>
                    </el-form-item>
                    <el-form-item label="总质量:">
                        <span>{{data.sumCapacityTonnage}}KG</span>
                    </el-form-item>
                    <el-form-item label="核定载质量:">
                        <span>{{data.capacityTonnage}}KG</span>
                    </el-form-item>
                    <el-form-item label="外廓尺寸:">
                        <span>{{ `${data.carLong}*${data.carWeight}*${data.carHigh}mm`}}</span>
                    </el-form-item>
                    <el-form-item label="行驶证有效期:">
                        <span>{{data.licenseExpire.slice(0, 7)}}</span>
                    </el-form-item>
                    <el-form-item label="运输证号:">
                        <span>{{data.shippingCert}}</span>
                    </el-form-item>
                    <el-form-item label="车辆审核状态:">
                        <span style="color: red">{{carAuthStatus}}</span>
                    </el-form-item>
                    <el-form-item>
                        <el-button v-show="carAuthStatusFlag" :loading="loading1" type="primary" style="width: 200px;" @click="adopt">通过审核</el-button>
                        <el-button :loading="loading" type="warning" style="width: 200px;" @click="dialogVisible = true">驳回</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="bot-tab">
                <div class="tab-title">
                    车辆审核记录
                </div>
                <div class="tab-box">
                    <template>
                        <el-table
                                :data="tableData"
                                stripe
                                border
                                style="width: 100%">
                            <el-table-column
                                    prop="createddate"
                                    label="创建日期"
                            >
                            </el-table-column>
                            <el-table-column
                                    prop="authStatusParams"
                                    label="车辆审核状态"
                            >
                            </el-table-column>
                            <el-table-column
                                    prop="operator"
                                    label="操作员">
                            </el-table-column>
                            <el-table-column
                                    prop="memo"
                                    label="车辆审核备注">
                            </el-table-column>
                        </el-table>
                    </template>
                </div>
            </div>
        </div>
        <!-- 驳回原因弹窗 -->
        <el-dialog
                title="请选择驳回原因"
                :visible.sync="dialogVisible"
                width="30%"
                :before-close="handleDialogClose">
            <div class="reject-box" style="height: 400px;overflow-y: scroll">
                <template>
                    <el-checkbox-group v-model="checkList">
                        <div class="reson-select" v-for="check in reasonList" :key="check.id">
                            <el-checkbox v-model="radio" :label="check.id">{{check.reason}}</el-checkbox>
                        </div>
                    </el-checkbox-group>
                </template>
                <div style="color: #33aef0;cursor: pointer" @click="otherReason">其他原因</div>
                <div class="other-reson">
                    <el-input type="textarea" :disabled="isWrite" v-model="formTxt.desc"></el-input>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button @click="dialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="reject">确认驳回</el-button>
  </span>
        </el-dialog>
        <!-- 查看大图 -->
        <el-image-viewer v-if="showViewer"
                         :on-close="closeViewer"
                         :url-list="[licenseImage]"/>
        <el-image-viewer v-if="showViewer1"
                         :on-close="closeViewer"
                         :url-list="[shippingCertImage]"/>
<!--        <el-image-viewer v-if="showViewer2"-->
<!--                         :on-close="closeViewer"-->
<!--                         :url-list="[shippingCertFrontSideImage]"/>-->
        <el-image-viewer
            v-if="showedPic"
            :on-close="handleClose"
            :url-list="[showedPic]"/>
        <img-list ref="imgList">
            <el-form label-width="130px">
                <el-form-item label="车辆行驶证正页:">
                    <div class="item-box">
                        <div class="img-box" @click="showPic(data.licenseImage)">
                            <img :src="licenseImage">
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="车辆行驶证副页:">
                    <div class="item-box">
                        <div class="img-box" @click="showPic(data.licenseBackImage)">
                            <img :src="licenseBackImage">
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="行驶证有效期:">
                    <div class="item-box">
                        <div class="img-box" @click="showPic(data.licenseExpireImage)">
                            <img :src="data.licenseExpireImage">
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="车辆道路运输证">
                    <div class="item-box">
                        <div class="img-box" @click="showPic(data.shippingCertImage)">
                            <img :src="shippingCertImage">
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="车主声明">
                    <div class="item-box">
                        <div class="img-box" @click="showPic(data.statementImage)">
                            <img :src="data.statementImage">
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="车辆照片">
                    <div class="item-box">
                        <div class="img-box" @click="showPic(data.carImage)">
                            <img :src="data.carImage">
                        </div>
                    </div>
                </el-form-item>
            </el-form>
        </img-list>
    </div>
</template>
<script>
    import ElImageViewer from 'cf-element-ui/packages/image/src/image-viewer'
    import ImgList from '@/components/ImgList/ImgList'
    export default {
        components: {ElImageViewer, ImgList},
        data() {
            return {
                carAuthStatusFlag:true,
                showViewer: false,
                showViewer1: false,
                showViewer2: false,
                loading:false,
                loading1:false,
                detailType:'',// 判断是前俩 或后俩 依据
                checkList: [],
                radio: '',
                isWrite:false,
                dialogVisible: false,
                formTxt: {
                    desc: '',
                },
                plateNumber: '',
                carAuthStatus: '',
                axleNumber: '',
                sumCapacityTonnage: '',
                carModelName: '',
                capacityTonnage: '',
                ownerName: '',
                licenseImage: '',
                shippingCertImage: '',
                // shippingCertFrontSideImage: '',
                shippingCert: '',
                // shippingCertExpire: '',
                // shippingCertExpireSurplus: '',
                reasonList:[],
                tableData: [],
                data: {},
                showedPic: null
            }
        },
        methods: {
            // 关闭查看器
            closeViewer() {
                this.showViewer = false;
                this.showViewer1 = false;
                this.showViewer2 = false;
            },
            handleDialogClose() {
                this.dialogVisible = false
            },
            onChange(value) {
                this.formTxt.desc=value;
                let checkedCount = value.length;
                if(checkedCount>0){
                    this.isWrite = true
                }else if(checkedCount===0){
                    this.isWrite = false;
                }
            },
            otherReason(){
                this.isWrite = false;
                this.radio=''
            },
            /** 驳回 **/
            reject() {
                let id = this.$route.query.carId;
                let dec = this.formTxt.desc;
                let authMemoId = this.checkList.join(',')
                if(dec==='' && authMemoId.length === 0){
                    this.$message.warning('请选中或输入驳回原因')
                }else {
                    this.loading=true;
                    let params = {
                        carId: id,
                        isCarAuthPassed: false,
                        authMemoId,
                        authOtherMemo: dec
                    }
                    this.$http.post('/admin-center-server/car/review', params).then(res=>{
                        let data= res.data;
                        if(data.code ==='200'){
                            this.$message.success('驳回成功');
                            this.dialogVisible = false;
                            this.getCarInfo()
                            this.loading=false;
                        }else if(data.code==='422'){
                            this.$message.warning(data.message)
                            this.loading=false;
                        }else {
                            this.$message.error(data.message)
                            this.loading=false;
                        }
                    })
                }
            },
            /** 通过审核 **/
            adopt() {
                this.loading1 = true;
                let id = this.$route.query.carId;
                let dec='';
                this.$http.post('/admin-center-server/car/review?isCarAuthPassed='+true+'&cbId='+id+'&authMemo='+dec).then(res=>{
                    let data= res.data;
                    if(data.code ==='200'){
                        this.$message.success('通过审核');
                        this.getCarInfo();
                        setTimeout(()=>{
                            this.$router.go(-1)
                            this.loading1 = false;
                        },1500)
                    }else if(data.code==='422'){
                        this.$message.error(data.message)
                        this.loading1 = false;
                    }
                })

            },
            /** 审核回显详情  车辆管理 认证管理详情 **/
            getCarInfo() {
                let id = this.$route.query.carId;
                this.$http.post('/admin-center-server/car/carDesc?carId=' + id).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.data = data.data
                        this.plateNumber = data.data.plateNumber;
                        this.axleNumber = data.data.axleNumber;
                        this.sumCapacityTonnage = data.data.sumCapacityTonnage;
                        this.carModelName = data.data.carModelName;
                        this.capacityTonnage = data.data.capacityTonnage;
                        this.ownerName = data.data.ownerName;
                        this.licenseImage = data.data.licenseImage;
                        this.shippingCertImage = data.data.shippingCertImage;
                        // this.shippingCertFrontSideImage = data.data.shippingCertFrontSideImage;
                        this.shippingCert = data.data.shippingCert;
                        // this.shippingCertExpire = data.data.shippingCertExpire;
                        // this.shippingCertExpireSurplus = data.data.shippingCertExpireSurplus;
                        this.tableData = data.data.carAuthLogs
                    }
                })
                this.$http.get('/admin-center-server/car/view', {
                    params: {
                        carId: id
                    }
                })
                    .then(res => {
                        let data = res.data
                        if (data.code === '200') {
                            let carAuthStatus = data.data.carAuthStatus;
                            if (carAuthStatus === '0') {
                                this.carAuthStatus = '未认证'
                            }else if(carAuthStatus === '1'){
                                this.carAuthStatus = '认证成功';
                                this.carAuthStatusFlag = false;
                            }else if(carAuthStatus === '2'){
                                this.carAuthStatus = '认证失败'
                            }else if(carAuthStatus === '3'){
                                this.carAuthStatus = '认证中'
                            }
                        }
                    })
            },

            /** 驳回原因列表 **/
            getReasonList(){
                this.$http.get('/admin-center-server/reject/reason/list',{
                    params:{
                        pageNumber:1,
                        pageSize:2000,
                    }
                }).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                        this.reasonList=data.data.list
                    }
                })
            },
            showPic(url) {
                this.showedPic = url
            },
            handleClose() {
                this.showedPic = null
            },
            showPic(pic) {
                this.$refs.imgList.showPic(pic)
            },
        },
        activated() {
            this.getCarInfo();
            this.getReasonList()
        }

    }
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .carInfo {
        .main-box {
            background-color: white;
            margin-bottom: 50px;

            .title {
                height: 40px;
                line-height: 40px;
                font-size: 16px;
                font-weight: 700;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
            }

            .list-info {
                width: 35%;
                /*height: 400px;*/
                padding-top: 20px;
                ::v-deep .el-form-item__content {
                    word-break: break-all;
                }
            }

            .bot-tab {
                border-top: 10px solid #f2f2f2;
                .tab-title {
                    position: relative;
                    height: 30px;
                    line-height: 30px;
                    padding-left: 10px;
                    font-size: 14px;
                    &::after {
                        content: '';
                        position: absolute;
                        top: 7px;
                        left: 0;
                        width: 4px;
                        height: 14px;
                        background-color: #F6A018;
                        border-radius: 2px;
                    }
                }

                padding: 10px;
            }
        }

        .reject-box {
            height: 350px;

            .reson-select {
                height: 40px;
                line-height: 40px;
            }

            .other-reson {
                margin-top: 10px;
            }
        }
    }

</style>
