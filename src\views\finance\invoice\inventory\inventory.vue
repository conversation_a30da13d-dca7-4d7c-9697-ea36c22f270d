<template>
  <div class="app-container">
    <div class="select-box">
      <div class="top-title">
        <div>筛选查询</div>
        <div class="button">
          <el-button
            class="left"
            icon="el-icon-refresh-right"
            size="mini"
            type="success"
            @click="()=>{this.$router.go(0)}"
            >刷新
          </el-button>
          <el-button
            class="left"
            icon="el-icon-search"
            size="mini"
            type="primary"
            @click="doSearch"
            >查询
          </el-button>
          <el-button
            class="left"
            icon="el-icon-delete"
            size="mini"
            type="danger"
            @click="searchReset"
            >清空筛选
          </el-button>
        </div>
      </div>
      <div class="select-info">
        <el-form
          class="demo-form-inline"
          :inline="true"
          :model="searchForm"
          label-width="110px"
          size="mini"
        >

          <el-form-item label="清单号:">
            <el-input
              class="form-item-content-width"
              v-model="searchForm.sn"
              placeholder="请输入订单号"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建时间:">
            <el-date-picker
              :clearable="false"
              v-model="date"
              value-format="yyyy-MM-dd"
              type="daterange"
              :default-time="['00:00:00', '00:00:00']"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-form>
        <el-form
          :inline="true"
          :model="searchForm"
          class="demo-form-inline"
          label-width="110px"
          size="mini"
        >
          <el-form-item label="状态:">
            <el-select
              class="form-item-content-width"
              v-model="searchForm.status"
              placeholder="全部"
            >
              <el-option value="" label="全部"></el-option>
              <el-option :value="1" label="待审核"></el-option>
              <el-option :value="3" label="审核通过"></el-option>
              <el-option :value="2" label="已驳回"></el-option>
              <el-option :value="4" label="已开票"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开票编号:">
            <el-input
              class="form-item-content-width"
              v-model="searchForm.invoiceNumber"
              placeholder="请输入开票编号"
            ></el-input>
          </el-form-item>
          <el-form-item label="合作平台主体:">
            <el-select
              class="form-item-content-width"
              v-model="searchForm.baseId"
              placeholder="请选择"
            >
              <el-option v-for="item in $store.state.user.baseInfo" :key="item.id" :value="item.id" :label="item.baseName"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属项目:">
            <el-input
              class="form-item-content-width"
              v-model="searchForm.projectName"
              placeholder="请输入所属项目"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-main">
        <el-table 
        border 
        :data="data" 
        cell-class-name="table_cell_gray"
        header-cell-class-name="table_header_cell_gray">
          <el-table-column label="序号" type="index"></el-table-column>
          <el-table-column label="清单号" prop="sn"></el-table-column>
          <el-table-column label="开票编号" prop="invoiceNumber"></el-table-column>
          <el-table-column label="公司名称" prop="buyName"></el-table-column>
          <el-table-column label="合作平台主体" prop="baseName"></el-table-column>
          <el-table-column label="所属项目" prop="projectName">
            <template slot-scope="scope">
              {{scope.row.projectName ? scope.row.projectName : '-'}}
            </template>
          </el-table-column>
          <el-table-column label="规格型号" prop="specs"></el-table-column>
          <el-table-column label="单位" prop="name">
            <template slot-scope="scope">
              <span v-if="scope.row.freightCalcType ==='0'">吨</span>
              <span v-else-if="scope.row.freightCalcType ==='1'">车</span>
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="ton"></el-table-column>
          <!-- <el-table-column label="单价" prop="freight"></el-table-column> -->
          <el-table-column label="开票金额" prop="amount"></el-table-column>
          <el-table-column label="状态" prop="status"></el-table-column>
          <el-table-column label="创建时间" prop="createdDate"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="viewWaybill(scope.row.id)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="page">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="getList"
            :current-page.sync="page.pageNumber"
            :page-sizes="[20, 40, 60, 80, 100]"
            :page-size="page.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { objToFormData } from '@/utils/tools'
export default {
  data() {
    return {
      searchForm: {
        sn: '',
        status: '',
        invoiceNumber: '',
        startDate: '',
        endDate: '',
        baseId: ''
      },
      date: [],
      data: [],
      total: 0,
      page: {
        pageNumber: 1,
        pageSize: 20,
      }
    }
  },
  created() {
    this.searchDefault = { ...this.searchForm }
    this.searchFinal = { ...this.searchForm }
  },
  activated() {
    this.getList()
  },
  methods: {
    getList() {
      let params = {
        ...this.page,
        ...this.searchFinal
      }
      this.$http.post('/admin-center-server/app/invoice_schedule/getInvoiceScheduleList', objToFormData(params))
        .then(res => {
          let data = res.data
          if (data.code === '200') {
            data = data.data
            this.data = data.list
            this.total = Number(data.total)
          } else {
            this.$message.error(data.message)
          }
        })
    },
    doSearch() {
      this.searchFinal = {
        ...this.searchForm,
        startDate: this.date && this.date[0] || '',
        endDate: this.date && this.date[1] || ''
      }
      this.getList()
    },
    searchReset() {
      this.searchForm = { ...this.searchDefault }
      this.date = []
      this.searchFinal = { ...this.searchDefault }
      this.page.pageNumber = 1
      this.getList()
    },
    handleSizeChange(v) {
      this.page.pageSize = v
      this.getList()
    },
    viewWaybill(id) {
      this.$router.push('/finance/invoice/waybill?id=' + id)
    }
  }
}
</script>

<style src="@/assets/scss/list.scss" scoped lang="scss"></style>
<style scoped lang="scss">
.page {
  text-align: right;
  .el-pagination {
    margin: 10px 0;
  }
}
</style>