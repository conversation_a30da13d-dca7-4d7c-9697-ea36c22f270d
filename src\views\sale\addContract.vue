<template>
  <div>
    <!-- <div class='title'>添加合同</div> -->
    <el-form :model="ruleForm"
             :rules="rules"
             ref="ruleForm"
             label-width="150px"
             size="medium"
             class="demo-ruleForm">
      <el-form-item label="合同名称"
                    prop="name">
        <el-input v-model="ruleForm.name"
                  maxlength="50"
                  placeholder="请输入合同名称，须录入完整的合同名称"></el-input>
      </el-form-item>
      <el-form-item label="合同编号"
                    prop="contractNumber">
        <el-input v-model="ruleForm.contractNumber"
                  maxlength="30"
                  placeholder="请输入合同编号"></el-input>
      </el-form-item>
      <el-form-item label="合同有效期"
                    prop="date">
        <el-date-picker v-model="ruleForm.date"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="合同开始日期"
                        end-placeholder="合同结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="甲方名称"
                    prop="nailName">
        <el-input v-model="ruleForm.nailName"
                  maxlength="50"
                  placeholder="请输入甲方名称"></el-input>
      </el-form-item>

      <el-form-item label="乙方名称"
                    prop="BName">
        <el-input v-model="ruleForm.BName"
                  maxlength="50"
                  placeholder="请输入乙方名称"></el-input>
      </el-form-item>
      <el-form-item label="所属公司"
                    prop="company">
        <el-select v-model="ruleForm.company"
                   placeholder="请选择">
          <el-option v-for="item in options"
                     :key="item.depName"
                     :label="item.depName"
                     :value="item.depName">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="合同录入人"
                    prop="entryPerson">
        <el-input v-model="ruleForm.entryPerson"
                  maxlength="30"
                  placeholder="请输入合同录入人"></el-input>
      </el-form-item>
      <el-form-item label="合同录入时间"
                    prop="date1">
        <el-date-picker v-model="ruleForm.date1"
                        type="datetime"
                        placeholder="请选择合同录入时间"
                        style="width: 100%;"> </el-date-picker>
      </el-form-item>
      <el-form-item label="合同附件">
        <el-upload class="upload-demo"
                   action
                   :on-preview="handlePreview"
                   :on-remove="handleRemove"
                   :file-list="fileList"
                   :on-change='imgChange'
                   list-type="picture"
                   :http-request="ossUpload"
                   :show-file-list="false"
                   :multiple="false">
          <el-button size="small"
                     type="primary">点 击 上传</el-button>
        </el-upload>
        <img v-show="Logo1.value" :src="Logo1.value" class="image">
      </el-form-item>
      <!-- <el-form-item label="合同附件">
        <el-upload action="https://jsonplaceholder.typicode.com/posts/"
                   list-type="picture-card"
                   :on-preview="handlePictureCardPreview"
                   :on-remove="handleRemove">
          <div class="upload-box">
            <div class="icon-XZ"></div>
            <div class="icon-word">支持.rar .zip .doc .docx .pdf .jpg格式不超过300kb</div>
          </div>
        </el-upload>
        <el-dialog :visible.sync="dialogVisible"
                   size="tiny">
          <img width="100%"
               :src="dialogImageUrl"
               alt />
        </el-dialog>
      </el-form-item> -->

      <el-form-item>
        <el-button type="primary"
                   @click="submitForm('ruleForm')">提交</el-button>

      </el-form-item>
    </el-form>

  </div>
</template>

<script>
import { client, OSS_REGION } from '@/utils/alioss'
const addContract = '/admin-center-server/contract/addContract'//添加合同
const getDepartmentListIsBillModels = '/admin-center-server/sys/getDepartmentListIsBillModels'//所属公司
export default {
  data () {
    return {
      id: null,
      Aliyun: {},
      fileList: [],
      Logo1: {
        value: ''
      },
      options: [],
      addFlag: true,
      ruleForm: {
        name: '',
        contractNumber: '',
        date: '',
        nailName: '',
        BName: '',
        entryPerson: '',
        date1: '',
        company: ''
      },
      rules: {
        company: [{ required: true, message: '请选择所属公司', trigger: 'change' }],
        name: [
          { required: true, message: '请输入合同名称', trigger: 'blur' }
        ],
        tel: [
          { required: true, message: '请输入客户电话', trigger: 'blur' },
          { min: 8, max: 11, message: '手机号格式不正确', trigger: 'blur' }
        ],
        contractNumber: [
          { required: true, message: '请输入合同编号', trigger: 'blur' },
        ],
        nailName: [
          { required: true, message: '请输入甲方名称', trigger: 'blur' }
        ],
        BName: [
          { required: true, message: '请输入乙方名称', trigger: 'blur' }
        ],
        entryPerson: [
          { required: true, message: '请输入合同录入人', trigger: 'blur' }
        ],
        date: [{ required: true, message: '请选择合同有效期', trigger: 'change' }],
        date1: [{ required: true, message: '请选择合同录入时间', trigger: 'change' }]
      },
      dialogVisible: false,
      dialogImageUrl: "",
      //通讯录
    }  },
  methods: {
    getAliyunData () {
      const ossApiUrl = "/base-center-server/sts/oss_signature"; // 获取oss签名的地址
      // 获取oss签名
      this.$http.get(ossApiUrl).then(res => {
        if (res.data.code == "200") {
          let data = res.data;
          this.Aliyun = data.data;
          this.Aliyun.region = OSS_REGION;
          // return data
        }
      });
    },
    imgChange (file, fileList) {
      if (fileList.length > 1) {
        fileList.shift()
      }
    },
    handleRemove (file, fileList) {
      // console.log(file, fileList);
    },
    handlePreview (file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    ossUpload (param, type) {
      let file = param.file; // 文件的
      const tmpcnt = file.name.lastIndexOf('.');
      const exname = file.name.substring(tmpcnt + 1);
      const fileName = '/' + this.Aliyun.bucket + '/' + this.Aliyun.dir + this.$md5(name) + '.' + exname;
      console.log(fileName);
      client(this.Aliyun).put(fileName, file).then(res => {
        console.log('uploadResult', JSON.stringify(res))
        if (res.res.status === 200) {
          let imgUrl = res.res.requestUrls[0];
          this.$set(this.Logo1, 'value', imgUrl)
          this.$message.success('上传成功')
        } else {
          this.$message.error(res.res.message)
        }
      })
    },
    submitForm (formName) {
      var that = this
      this.$refs[formName].validate(valid => {
        var v = that.ruleForm
        if (valid) {
          if (this.addFlag) {
            this.addFlag = false
            if (!that.Logo1.value) {
              this.addFlag = true
              this.$message.error('请上传合同附件');
              return false
            }
            var data = {
              name: v.name,
              no: v.contractNumber,
              startExpirationDate: that.getDate1(v.date[0]),//有效期开始
              expirationDate: that.getDate1(v.date[1]),//有效期结束
              partyA: v.nailName,
              partyB: v.BName,
              company: v.company,
              entryPerson: v.entryPerson,
              entryDate: that.getDate(v.date1),
              attachment: that.Logo1.value //合同附件
            }
            console.log(data)
            let api = addContract
            if (this.id) {
              api = '/admin-center-server/contract/updateContract'
              data.id = this.id
            }
            
            this.$http.post(api, data).then(res => {
              var that = this
              if (res.data.code == '200') {
                this.$message({
                  message: res.data.message,
                  type: 'success'
                });
                setTimeout(function () {
                  that.$router.go(-1);
                }, 3000);
              } else {
                this.addFlag = true
                this.$message.error(res.data.message);
              }
            })
          }

        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getCom() {
      this.$http.get(getDepartmentListIsBillModels).then(res => {
        console.log(res.data.data)
        this.options = res.data.data
      })
    }
  },
  activated () {
    this.getCom()
  },
  created () {
    this.getAliyunData();
    this.getCom()

    let id = this.$route.query.id
    if (id) {
      this.id = id
      this.$http.get('/admin-center-server/contract/getByContractId', {
        params: { id }
      })
        .then(res => {
          let data = res.data.data
          this.ruleForm = {
            name: data.name,
            contractNumber: data.no,
            nailName: data.partyA,
            BName: data.partyB,
            company: data.company,
            entryPerson: data.entryPerson,
            date: [data.startExpirationDate, data.expirationDate],
            date1: data.entryDate
          }
          this.$set(this.Logo1, 'value', data.attachment)
        })
    }
  }

}
</script>

<style lang="scss" scoped>
.demo-ruleForm {
  width: 500px;
  margin-top: 20px;
}
.upload-box {
  width: 100%;
  height: 100%;
  /*border: 1px solid #00cb8a;*/
  position: relative;

  .icon-XZ {
    width: 92px;
    height: 92px;
    margin: 0 auto;
    background: url("../userPage/consignor/images/xiazai.png") no-repeat;
    background-size: 100% 100%;
  }

  .icon-word {
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 10px;
    position: absolute;
    bottom: 25px;
    left: 0px;
    color: #cccccc;
  }
}
.image {
  margin-top: 20px;
  max-width: 200px;
  max-height: 200px;
}
</style>

