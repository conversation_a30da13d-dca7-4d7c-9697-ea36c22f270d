<template>
    <div class="app-container systemBase">
        <el-tabs v-model="tab" @tab-click="changeTab" type="border-card">
            <el-tab-pane label="官网设置" name="site">
                <div class="masterStation">
                    <el-form label-width="100px" class="demo-ruleForm">
                        <el-form-item label="网站名称:" prop="name">
                            <el-input v-model="SiteName.value" placeholder="请输入网站名称" style="width: 40%"></el-input>
                        </el-form-item>
                        <el-form-item label="网站网址:" prop="net">
                            <el-input v-model="SiteUrl.value" placeholder="请输入网站网址" style="width: 40%"></el-input>
                        </el-form-item>
                        <el-form-item label="网站Logo:" prop="net">
                            <el-upload
                                    class="upload-demo"
                                    action
                                    :on-preview="handlePreview"
                                    :on-remove="handleRemove"
                                    :file-list="fileList"
                                    :on-change='imgChange'
                                    list-type="picture"
                                    :http-request="ossUpload"
                                    :multiple="false"
                            >
                                <el-button size="small" type="primary">点 击 上传</el-button>
                            </el-upload>
                        </el-form-item>
                        <el-form-item label="热门搜索:" prop="hot">
                            <el-input v-model="Keywords.value" placeholder="请输入热门搜索" style="width: 40%"></el-input>
                        </el-form-item>
                        <el-form-item label="联系地址:" prop="address">
                            <el-input v-model="Address.value" placeholder="请输入联系地址" style="width: 40%"></el-input>
                        </el-form-item>
                        <el-form-item label="联系电话:" prop="phone">
                            <el-input v-model="Phone.value" placeholder="请输入联系电话" style="width: 40%"></el-input>
                        </el-form-item>
                        <el-form-item label="邮政编码:" prop="post">
                            <el-input v-model="ZipCode.value" placeholder="请输入邮政编码" style="width: 40%"></el-input>
                        </el-form-item>
                        <el-form-item label="E-mail:" prop="email">
                            <el-input v-model="Email.value" placeholder="请输入E-mail" style="width: 40%"></el-input>
                        </el-form-item>
                        <el-form-item label="备案编号:" prop="record">
                            <el-input v-model="CertText.value" placeholder="请输入备案编号" style="width: 40%"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button :loading="loading" type="primary" @click="saveSite">保存</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </el-tab-pane>
            <el-tab-pane label="时间类设置" name="time">
                <div class="time-box">
                    <el-form label-width="170px" v-for="item in timeGroup">
                        <el-form-item v-if="item.code==='OrderOverTime'" :label="item.desc">
                            <el-input
                                    v-model="item.value"
                                    style="width: 30%"
                                    :οnkeyup="item.value=item.value.replace(/[^\d\.]/g,'')"
                            ></el-input>
                            <span
                                    style="display: inline-block;width: 20px;height: 20px;line-height: 20px;border: 1px solid #cccccc;border-radius: 50%;text-align: center;
                             cursor: pointer;margin-left: 20px"
                                    title="此时间为客户下单后无司机接单的超时时间，若无司机在此时间内进行接单操作，则该订单自动取消。"
                            >?</span>
                        </el-form-item>
                        <el-form-item v-if="item.code==='BrokerGetOrderOverTime'" :label="item.desc">
                            <el-input
                                    v-model="item.value"
                                    style="width: 30%"
                                    :οnkeyup="item.value=item.value.replace(/[^\d\.]/g,'')"
                            ></el-input>
                            <span
                                    style="display: inline-block;width: 20px;height: 20px;line-height: 20px;border: 1px solid #cccccc;border-radius: 50%;text-align: center;
                             cursor: pointer;margin-left: 20px"
                                    title="此时间为客户下定向单后调度员未做任何操作则调度员单自动取消的时间。"
                            >?</span>
                        </el-form-item>
                        <el-form-item v-if="item.code==='VerifyDriver'" :label="item.desc">
                            <el-input
                                    v-model="item.value"
                                    style="width: 30%"
                                    :οnkeyup="item.value=item.value.replace(/[^\d\.]/g,'')"
                            ></el-input>
                            <span
                                    style="display: inline-block;width: 20px;height: 20px;line-height: 20px;border: 1px solid #cccccc;border-radius: 50%;text-align: center;
                             cursor: pointer;margin-left: 20px"
                                    title="此时长为司机完成运单，客户未在该时长内进行评价，则系统代客户自动评价的时间。"
                            >?</span>
                        </el-form-item>
                        <el-form-item v-if="item.code==='CustomConfig'" :label="item.desc">
                            <el-input
                                    v-model="item.value"
                                    style="width: 30%"
                                    :οnkeyup="item.value=item.value.replace(/[^\d\.]/g,'')"
                            ></el-input>
                            <span
                                    style="display: inline-block;width: 20px;height: 20px;line-height: 20px;border: 1px solid #cccccc;border-radius: 50%;text-align: center;
                             cursor: pointer;margin-left: 20px"
                                    title="此时长为调度员代客户发单，客户未在该时长内确认代发则自动取消的时间。"
                            >?</span>
                        </el-form-item>
                        <el-form-item v-if="item.code==='OrderSend'" :label="item.desc">
                            <el-input
                                    v-model="item.value"
                                    style="width: 30%"
                                    :οnkeyup="item.value=item.value.replace(/[^\d\.]/g,'')"
                            ></el-input>
                            <span
                                    style="display: inline-block;width: 20px;height: 20px;line-height: 20px;border: 1px solid #cccccc;border-radius: 50%;text-align: center;
                             cursor: pointer;margin-left: 20px"
                                    title="此时长为在运单交接业务中，承接司机未在该时间内进程承接，则运单交接的申请自动超时的时间。"
                            >?</span>
                        </el-form-item>
                        <el-form-item v-if="item.code==='PayDely'" :label="item.desc">
                            <el-input
                                    v-model="item.value"
                                    style="width: 30%"
                                    :οnkeyup="item.value=item.value.replace(/[^\d\.]/g,'')"
                            ></el-input>
                            <span
                                    style="display: inline-block;width: 20px;height: 20px;line-height: 20px;border: 1px solid #cccccc;border-radius: 50%;text-align: center;
                             cursor: pointer;margin-left: 20px"
                                    title="此时长为货主或调度员选择预付结算方式的最晚结算期限时长。"
                            >?</span>
                        </el-form-item>
                        <el-form-item v-if="item.code==='LoadAndUnloadMinTime'" :label="item.desc">
                            <el-input
                                    v-model="item.value"
                                    style="width: 30%"
                                    :οnkeyup="item.value=item.value.replace(/[^\d\.]/g,'')"
                            ></el-input>
                            <span
                                    style="display: inline-block;width: 20px;height: 20px;line-height: 20px;border: 1px solid #cccccc;border-radius: 50%;text-align: center;
                             cursor: pointer;margin-left: 20px"
                                    title="此时长为货主或调度员选择预付结算方式的最晚结算期限时长。"
                            >?</span>
                        </el-form-item>
                        <el-form-item v-if="item.code==='VoiceAlertInterval'" :label="item.desc">
                            <el-select v-model="item.value" placeholder="请选择时长" :disabled="voiceAlertDidabled" style="width: 30%">
                                <el-option label="30s" value="4"></el-option>
                                <el-option label="1min" value="5"></el-option>
                                <el-option label="5min" value="9"></el-option>
                                <el-option label="10min" value="14"></el-option>
                                <el-option label="20min" value="15"></el-option>
                                <el-option label="30min" value="16"></el-option>
                            </el-select>
                            <el-tooltip class="item" effect="dark" content="司机进入围栏后的一定时间内，如果仍未完成装/卸货打卡，系统将语音通知司机进行装/卸货操作" placement="right-end">
                                <span
                                style="display: inline-block;width: 20px;height: 20px;line-height: 20px;border: 1px solid #cccccc;border-radius: 50%;text-align: center;
                                cursor: pointer;margin-left: 20px"
                                title=""
                                >?</span>
                            </el-tooltip>
                            <el-checkbox style="margin-left: 30px" v-model="voiceAlertDidabled"  @change="changeVoiceAlertInterval">不提醒</el-checkbox>
                        </el-form-item>
                        <el-form-item v-if="item.code==='TimelinessWarning'" :label="item.desc">
                            <el-input v-model="item.value" :disabled="timelinessWarningDisabled" style="width: 30%" :οnkeyup="item.value=item.value.replace(/^0+/, '').replace(/\D/g, '')">
                              <template slot="append">分钟</template>
                            </el-input>
                            <el-tooltip class="item" effect="dark" content="司机到达装货地、卸货地前系统会给予超时提醒" placement="right-end">
                                <span
                                style="display: inline-block;width: 20px;height: 20px;line-height: 20px;border: 1px solid #cccccc;border-radius: 50%;text-align: center;
                                cursor: pointer;margin-left: 20px"
                                title=""
                                >?</span>
                            </el-tooltip>
                            <el-checkbox style="margin-left: 30px" v-model="timelinessWarningDisabled"  @change="changeTimelinessWarning">不提醒</el-checkbox>
                        </el-form-item>
                        <el-form-item v-if="item.code==='certEarlyWarnDays'" :label="item.desc">
                            <el-select v-model="item.value" placeholder="请选择时长" style="width: 30%">
                                <el-option label="距离证件到期时间3天内预警" value="3"></el-option>
                                <el-option label="距离证件到期时间7天内预警" value="7"></el-option>
                                <el-option label="距离证件到期时间14天内预警" value="14"></el-option>
                                <el-option label="距离证件到期时间30天内预警" value="30"></el-option>
                            </el-select>
                            <el-tooltip class="item" effect="dark" content="司机、车辆相关证件到期前货主端、司机端、管理后台进行相应的预警提示" placement="right-end">
                                <span
                                style="display: inline-block;width: 20px;height: 20px;line-height: 20px;border: 1px solid #cccccc;border-radius: 50%;text-align: center;
                                cursor: pointer;margin-left: 20px"
                                title=""
                                >?</span>
                            </el-tooltip>
                        </el-form-item>
                    </el-form>
                    <el-form label-width="150px">
                        <el-form-item>
                            <el-button :loading="loading" type="primary" @click="saveTime">保存</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </el-tab-pane>
            <el-tab-pane label="金额类设置" name="money">
                <div class="money-box">
                    <el-form ref="moneyForm" label-width="200px">
                        <el-form-item label="单笔最小充值金额:">
                            <el-input
                                    v-model="MinChargMoney.value"
                                    :οnkeyup="MinChargMoney.value=MinChargMoney.value.replace(/[^\d\.]/g,'')"
                                    placeholder="请输入(元)"
                                    style="width: 30%"></el-input>
                        </el-form-item>

                        <el-form-item label="单笔最大提现金额:">
                            <el-input
                                    v-model="MaxGetMoney.value"
                                    :οnkeyup="MaxGetMoney.value=MaxGetMoney.value.replace(/[^\d\.]/g,'')"
                                    placeholder="请输入(元)"
                                    style="width: 30%"></el-input>
                        </el-form-item>
                        <el-form-item label="充值手续费比例:">
                            <el-input
                                    v-model="ChargPayScale.value"
                                    :disabled="true"
                                    :οnkeyup="ChargPayScale.value=ChargPayScale.value.replace(/[^\d\.]/g,'')"
                                    placeholder="请输入(元)"
                                    style="width: 30%"></el-input>
                        </el-form-item>
                        <!-- <el-form-item label="客户服务费默认比例:">
                            <el-input
                                    v-model="ServiceTaxScale.value"
                                    :οnkeyup="ServiceTaxScale.value=ServiceTaxScale.value.replace(/[^\d\.]/g,'')"
                                    placeholder="请输入(元)" style="width: 30%"></el-input>
                        </el-form-item> -->
                        <el-form-item label="单笔最小提现金额:">
                            <el-input
                                    v-model="MinGetMoney.value"
                                    :οnkeyup="MinGetMoney.value=MinGetMoney.value.replace(/[^\d\.]/g,'')"
                                    placeholder="请输入(元)" style="width: 30%"></el-input>
                        </el-form-item>
                        <el-form-item label="单日最大提现金额:" :rules="maxWithdrawSingleDayRules" prop="maxWithdrawSingleDay" class="withdraw-limit">
                            <!-- <el-input
                                    v-model="MaxGetMoneyDay.value"
                                    :οnkeyup="MaxGetMoneyDay.value=MaxGetMoneyDay.value.replace(/[^\d\.]/g,'')"
                                    placeholder="请输入(元)" style="width: 30%"></el-input> -->
                            <el-input v-model="maxWithdrawSingleDayBusiness.value" placeholder="请输入">
                                <template #prepend>货主:</template>
                            </el-input>
                            <el-input v-model="maxWithdrawSingleDayPersonalCaptain.value" placeholder="请输入">
                                <template #prepend>个人车队长:</template>
                            </el-input>
                            <el-input v-model="maxWithdrawSingleDayCapacityProvider.value" placeholder="请输入">
                                <template #prepend>运力供应商:</template>
                            </el-input>
                            <el-input v-model="maxWithdrawSingleDayDriver.value" placeholder="请输入">
                                <template #prepend>司机:</template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="单日提现次数:" prop="maxNumWithdrawSingleDay" :rules="maxNumWithdrawSingleDayRules">
                            <el-input v-model="maxNumWithdrawSingleDay.value" placeholder="请输入" style="width: 30%"></el-input>
                        </el-form-item>
                        <el-form-item label="单日夜间提现次数:" class="withdraw-night" prop="maxNumWithdrawAtNight" :rules="maxNumWithdrawAtNightRules">
                            <el-input v-model="maxNumWithdrawAtNight.value" placeholder="请输入"></el-input>
                            夜间时间范围：
                            <el-select v-model="maxNumWithdrawAtNightStartTimeBelongDate.value">
                                <el-option label="当日" value="CURRENT"></el-option>
                                <el-option label="次日" value="NEXT"></el-option>
                            </el-select>
                            <el-time-picker
                                v-model="maxNumWithdrawAtNightStartTime.value"
                                :disabled="maxNumWithdrawAtNight.value == 0"
                                value-format="HH:mm:ss"
                                placeholder="开始时间"
                                :picker-options="getPickerOptions('start')"></el-time-picker>
                            至
                            <el-select v-model="maxNumWithdrawAtNightEndTimeBelongDate.value">
                                <el-option label="当日" value="CURRENT"></el-option>
                                <el-option label="次日" value="NEXT"></el-option>
                            </el-select>
                            <el-time-picker
                                v-model="maxNumWithdrawAtNightEndTime.value"
                                :disabled="maxNumWithdrawAtNight.value == 0"
                                value-format="HH:mm:ss"
                                placeholder="结束时间"
                                :picker-options="getPickerOptions('end')"></el-time-picker>
                        </el-form-item>
                        <el-form-item label="充值提现时间间隔:" prop="withdrawIntervalSingleDay" :rules="withdrawIntervalSingleDayRules">
                            <el-input v-model="withdrawIntervalSingleDay.value" placeholder="请输入" style="width: 30%">
                                <template #append>小时</template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="单笔转账最大金额:" prop="manualTransferMax" :rules="manualTransferMaxRules">
                            <el-input v-model="manualTransferMax.value" placeholder="请输入" style="width: 30%" oninput="value=value.match(/\d+\.?\d{0,2}/,'')">
                                <template #append>元</template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="提现手续费比例:">
                            <el-input
                                    v-model="GetPayScale.value"
                                    :disabled="true"
                                    :οnkeyup="GetPayScale.value=GetPayScale.value.replace(/[^\d\.]/g,'')"
                                    placeholder="请输入(%)" style="width: 30%"></el-input>
                        </el-form-item>
                        <el-form-item v-if="!this.$store.state.user.userInfo2.hasStandardModeFlag" label="信息费不得大于运费比例:">
                            <el-input
                                    v-model="InfoFeeScale.value"
                                    :οnkeyup="InfoFeeScale.value=InfoFeeScale.value.replace(/[^\d\.]/g,'')"
                                    placeholder="请输入(%)" style="width: 30%"></el-input>
                        </el-form-item>
                        <el-form-item label="结算费用不得低于运费比例:" prop="RetentionMoney" :rules="RetentionMoneyRules">
                            <el-input
                                    v-model="RetentionMoney.value"
                                    :οnkeyup="RetentionMoney.value=RetentionMoney.value.replace(/[^\d\.]/g,'')"
                                    placeholder="请输入[10,100]之间的整数" style="width: 30%"></el-input>
                        </el-form-item>
                        <el-form-item v-if="!this.$store.state.user.userInfo2.hasStandardModeFlag" label="运单油费比例:" prop="oilRatio" :rules="oilRatioRules">
                            <el-input
                                    v-model="oilRatio.value"
                                    :οnkeyup="oilRatio.value=oilRatio.value.replace(/[^\d\.]/g,'')"
                                    placeholder="请输入[0,100]之间的整数" style="width: 30%"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button :loading="loading" type="primary" @click="savaMoney">保存</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </el-tab-pane>
            <el-tab-pane label="功能类设置" name="function">
                <div class="facility">
                    <el-form ref="functionForm" label-width="200px">
                        <el-form-item label="最小抢单吨数:">
                            <el-input
                                    v-model="MinGetOrderTonnage.value"
                                    :οnkeyup="MinGetOrderTonnage.value=MinGetOrderTonnage.value.replace(/[^\d\.]/g,'')"
                                    placeholder="请输入(吨)" style="width: 40%"></el-input>
                        </el-form-item>
                        <el-form-item label="最大装货吨数:">
                            <el-input
                                    v-model="MAXGetOrderTonnage.value"
                                    :οnkeyup="MAXGetOrderTonnage.value=MAXGetOrderTonnage.value.replace(/[^\d\.]/g,'')"
                                    placeholder="请输入(吨)" style="width: 40%"></el-input>
                        </el-form-item>
                        <el-form-item label="货币符号:">
                            <el-input v-model="CurrencySign.value" placeholder="" style="width: 40%"></el-input>
                        </el-form-item>
                        <el-form-item label="附近货源线路半径:">
                            <el-input
                                    v-model="CargoSourceRadius.value"
                                    :οnkeyup="CargoSourceRadius.value=CargoSourceRadius.value.replace(/[^\d\.]/g,'')"
                                    placeholder="请输入(吨)" style="width: 40%"></el-input>
                        </el-form-item>
                        <el-form-item label="重量单位:">
                            <el-input v-model="WeightUnit.value" placeholder="请输入(吨)" style="width: 40%"></el-input>
                        </el-form-item>
                        <el-form-item label="里程单位:">
                            <el-input v-model="MileageUnit.value" placeholder="请输入(吨)" style="width: 40%"></el-input>
                        </el-form-item>
                        <el-form-item label="短倒距离限制:">
                            <el-input :disabled="canShortInner" v-model="OpenShortFallDistance.value" placeholder="请输入大于0的数字,支持2位小数" style="width: 40%"   @keyup.native="keyupEvent($event,OpenShortFallDistance.value)">
                                <template slot="append">KM</template>
                            </el-input>
                            <el-checkbox style="margin-left: 30px"  @change="changeShortKm">无限制</el-checkbox>
                        </el-form-item>
                        <el-form-item label="接单距离校验限制:">
                            <el-input v-model="distanceCheck.value" placeholder="请输入大于0的数字,支持2位小数" style="width: 40%" @keyup.native="keyupEvent($event,distanceCheck.value)">
                                <template slot="append">KM</template>
                            </el-input>
                            <el-tooltip class="item" effect="dark" content="司机接单时会校验司机当前位置距离装货地的距离是否超过当前设置，如果超过，则会给司机弹窗提醒，司机确认后方可接单" placement="right-end">
                              <span style="display: inline-block;width: 25px;height: 25px;line-height: 22px;border: 1px solid #cccccc;border-radius: 50%;text-align: center;cursor: pointer;margin-left: 10px;">?</span>
                            </el-tooltip>
                        </el-form-item>
                        <el-form-item label="货主可开发票类型:">
                            <el-checkbox-group v-model="invoiceTypeList">
                                <el-checkbox label="1">纸质票</el-checkbox>
                                <el-checkbox label="2">全电票</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                        <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="司机审核通过前允许接单数:" prop="MaxAllowOrder" :rules="MaxAllowOrderRules">
                            <el-input v-model="MaxAllowOrder.value" placeholder="请输入" style="width: 40%"></el-input>
                        </el-form-item>
                        <el-form-item label="“圆形”围栏半径默认:" prop="fenceDefaultRadius" :rules="fenceDefaultRadiusRules">
                            <el-input v-model="fenceDefaultRadius.value" placeholder="请输入" style="width: 40%"><template slot="append">米</template></el-input>
                        </el-form-item>
                        <el-form-item label="围栏距离装/卸地最大值:" prop="maxDistanceFenceToPlace" :rules="maxDistanceFenceToPlaceRules">
                            <el-input v-model="maxDistanceFenceToPlace.value" placeholder="请输入" style="width: 40%"><template slot="append">米</template></el-input>
                        </el-form-item>
                        <el-form-item label="平台派车:">
                                <el-switch
                                        v-model="platformDesignation.value"
                                        active-color="#13ce66"
                                        inactive-color="#ff4949"
                                        @change="changePlatformDesignation"
                                ></el-switch>
                                <el-tooltip class="item" effect="dark" content="开启后，所有货主发货时无法指派司机，发货后订单进入到平台审核阶段，平台审核通过并指派司机后，司机方可接单" placement="right-end">
                                    <span style="display: inline-block;width: 25px;height: 25px;line-height: 22px;border: 1px solid #cccccc;border-radius: 50%;text-align: center;cursor: pointer;margin-left: 10px;">?</span>
                                </el-tooltip>
                            </el-form-item>
                        <el-form-item label="标准平台模式:">
                            <el-switch v-model="standardModeSwitch"></el-switch>
                            <div v-if="standardModeSwitch" class="standard-mode">
                                <div>以下货主开启标准平台模式：</div>
                                <el-select v-model="selectedStandardModeConsignor" multiple filterable>
                                    <el-option
                                        v-for="item in standardModeConsignor"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"></el-option>
                                </el-select>
                                <div>以下后台账号开启标准平台模式：</div>
                                <el-select v-model="selectedStandardModeAdministrators" multiple filterable>
                                    <el-option
                                        v-for="item in standardModeAdministrators"
                                        :key="item.id"
                                        :label="item.realName"
                                        :value="item.id"></el-option>
                                </el-select>
                            </div>
                        </el-form-item>
                        <el-form-item>
                            <el-button :loading="loading" type="primary" @click="saveFunction">保存</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </el-tab-pane>
            <el-tab-pane label="待定运费设置" name="freight">
                <div class="pending">
                    <div class="pending_item">
                        <el-form ref="form" label-width="180px">
                            <el-form-item label="待定运费:">
                                <el-switch
                                        v-model="daiDflag"
                                        active-color="#13ce66"
                                        inactive-color="#ff4949"
                                        @change="DdYfChange"
                                ></el-switch>
                            </el-form-item>
                            <el-form-item label="是否影响运费:">
                                <el-switch
                                        :disabled="isDdYf"
                                        v-model="yunFflag"
                                        active-color="#13ce66"
                                        inactive-color="#ff4949"
                                ></el-switch>
                            </el-form-item>
                            <el-form-item label="调整上限:">
                                <span style="margin-right: 20px">上调</span>
                                <el-input
                                        v-model="FreightPreMax.value"
                                        :disabled="isDdYf"
                                        :οnkeyup="FreightPreMax.value=FreightPreMax.value.replace(/[^\d\.]/g,'')"
                                        placeholder
                                        style="width:140px;"
                                >
                                    <template slot="append">%</template>
                                </el-input>
                            </el-form-item>
                            <el-form-item label="调整次数:">
                                <el-input
                                        v-model="FreightPreCount.value"
                                        :disabled="isDdYf"
                                        :οnkeyup="FreightPreCount.value=FreightPreCount.value.replace(/[^\d\.]/g,'')"
                                        placeholder
                                        style="width:140px;margin-left: 50px"
                                >
                                    <template slot="append">次</template>
                                </el-input>
                            </el-form-item>
                        </el-form>
                    </div>
                    <div class="pending_item" style="margin-top: 20px">
                        <el-form ref="form" label-width="180px">
                            <el-form-item label="短倒待定运费:">
                                <el-switch
                                        v-model="shortIsOpen"
                                        active-color="#13ce66"
                                        inactive-color="#ff4949"
                                        @change="DdYfChangeShort"
                                ></el-switch>
                            </el-form-item>
                            <el-form-item label="是否影响运费:">
                                <el-switch
                                        :disabled="isShortDdYf"
                                        v-model="shortYunFflag"
                                        active-color="#13ce66"
                                        inactive-color="#ff4949"
                                ></el-switch>
                            </el-form-item>
                            <el-form-item label="调整上限:">
                                <span style="margin-right: 20px">上调</span>
                                <el-input
                                        v-model="OpenShortFallMax.value"
                                        :disabled="isShortDdYf"
                                        :οnkeyup="OpenShortFallMax.value=OpenShortFallMax.value.replace(/[^\d\.]/g,'')"
                                        placeholder
                                        style="width:140px;"
                                >
                                    <template slot="append">%</template>
                                </el-input>
                            </el-form-item>
                            <el-form-item label="调整次数:">
                                <el-input
                                        v-model="OpenShortFallCount.value"
                                        :disabled="isShortDdYf"
                                        :οnkeyup="OpenShortFallCount.value=OpenShortFallCount.value.replace(/[^\d\.]/g,'')"
                                        placeholder
                                        style="width:140px;margin-left: 50px"
                                >
                                    <template slot="append">次</template>
                                </el-input>
                            </el-form-item>
                            <el-form-item>
                                <el-button :loading="loading" type="primary" @click="saveFreightPre">保 存 设 置</el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="APP更新信息" name="appData">
                <div class="appUpdate">
                    <!--          IOS 客户端-->
                    <div class="app-item" style="width:800px">
                        <el-form label-width="400px" v-for="item in iosAppClient">
                            <el-form-item v-if="item.code==='forceUpdate'" :label="item.desc">
                                <el-switch
                                        v-model="item.value"
                                        active-color="#13ce66"
                                        inactive-color="#ff4949"
                                        active-text="是"
                                        inactive-text="否"
                                >
                                </el-switch>
                            </el-form-item>
                            <el-form-item v-if="item.code==='underReview'" :label="item.desc">
                                <el-switch
                                        v-model='item.value'
                                        active-color="#13ce66"
                                        inactive-color="#ff4949"
                                        active-text="是"
                                        inactive-text="否">
                                </el-switch>
                            </el-form-item>
                            <el-form-item v-if="item.code==='forceUpdateMinimalVersion'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appVersion'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appVersionCode'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appUpdateVersionUrl'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='updateInformation'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                        </el-form>
                    </div>
                    <el-divider></el-divider>
                    <!--          IOS 司机端-->
                    <div class="app-item" style="width:800px">
                        <el-form label-width="400px" v-for="item in iosAppDriver">
                            <el-form-item v-if="item.code==='forceUpdate'" :label="item.desc">
                                <el-switch
                                        v-model="item.value"
                                        active-color="#13ce66"
                                        inactive-color="#ff4949"
                                        active-text="是"
                                        inactive-text="否">
                                </el-switch>
                            </el-form-item>
                            <el-form-item v-if="item.code==='underReview'" :label="item.desc">
                                <el-switch
                                        v-model="item.value"
                                        active-color="#13ce66"
                                        inactive-color="#ff4949"
                                        active-text="是"
                                        inactive-text="否">
                                </el-switch>
                            </el-form-item>
                            <el-form-item v-if="item.code==='forceUpdateMinimalVersion'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appVersion'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appVersionCode'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appUpdateVersionUrl'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='updateInformation'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                        </el-form>
                    </div>
                    <el-divider></el-divider>
                    <!--          IOS 调度员端-->
                    <div class="app-item" style="width:800px">
                        <el-form label-width="400px" v-for="item in iosAppBroker">
                            <el-form-item v-if="item.code==='forceUpdate'" :label="item.desc">
                                <el-switch
                                        v-model="item.value"
                                        active-color="#13ce66"
                                        inactive-color="#ff4949"
                                        active-text="是"
                                        inactive-text="否">
                                </el-switch>
                            </el-form-item>
                            <el-form-item v-if="item.code==='underReview'" :label="item.desc">
                                <el-switch
                                        v-model="item.value"
                                        active-color="#13ce66"
                                        inactive-color="#ff4949"
                                        active-text="是"
                                        inactive-text="否">
                                </el-switch>
                            </el-form-item>
                            <el-form-item v-if="item.code==='forceUpdateMinimalVersion'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appVersion'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appVersionCode'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appUpdateVersionUrl'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='updateInformation'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                        </el-form>
                    </div>
                    <el-divider></el-divider>
                    <!--          安卓 客户端-->
                    <div class="app-item" style="width:800px">
                        <el-form label-width="400px" v-for="item in androidAppClient">
                            <el-form-item v-if="item.code==='forceUpdate'" :label="item.desc">
                                <el-switch
                                        v-model="item.value"
                                        active-color="#13ce66"
                                        inactive-color="#ff4949"
                                        active-text="是"
                                        inactive-text="否">
                                </el-switch>
                            </el-form-item>
                            <el-form-item v-if="item.code==='forceUpdateMinimalVersion'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appVersion'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appVersionCode'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appUpdateVersionUrl'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='updateInformation'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                        </el-form>
                    </div>
                    <el-divider></el-divider>
                    <!--          安卓 司机端-->
                    <div class="app-item" style="width:800px">
                        <el-form label-width="400px" v-for="item in androidAppDriver">
                            <el-form-item v-if="item.code==='forceUpdate'" :label="item.desc">
                                <el-switch
                                        v-model="item.value"
                                        active-color="#13ce66"
                                        inactive-color="#ff4949"
                                        active-text="是"
                                        inactive-text="否">
                                </el-switch>
                            </el-form-item>
                            <el-form-item v-if="item.code==='forceUpdateMinimalVersion'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appVersion'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appVersionCode'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appUpdateVersionUrl'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='updateInformation'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                        </el-form>
                    </div>
                    <el-divider></el-divider>
                    <!--          安卓 调度员端-->
                    <div class="app-item" style="width:800px">
                        <el-form label-width="400px" v-for="item in androidAppBroker">
                            <el-form-item v-if="item.code==='forceUpdate'" :label="item.desc">
                                <el-switch
                                        v-model="item.value"
                                        active-color="#13ce66"
                                        inactive-color="#ff4949"
                                        active-text="是"
                                        inactive-text="否">
                                </el-switch>
                            </el-form-item>
                            <el-form-item v-if="item.code==='forceUpdateMinimalVersion'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appVersion'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appVersionCode'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='appUpdateVersionUrl'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='updateInformation'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                        </el-form>
                    </div>
                    <el-divider></el-divider>
                    <!--          Web 端-->
                    <div class="app-item" style="width:800px">
                        <el-form label-width="400px" v-for="item in applicationVersion" :key="item.code">
                            <el-form-item v-if="item.code==='PC_CONSIGNOR'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='PC_OWNER'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='PC_ADMIN'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='APP_H5_CONSIGNOR'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                            <el-form-item v-if="item.code==='APP_H5_DRIVER'" :label="item.desc" required>
                                <el-input v-model="item.value"></el-input>
                            </el-form-item>
                        </el-form>
                    </div>
                    <div class="app-item" style="width:800px">
                        <el-form label-width="400px">
                            <el-form-item>
                                <el-button :loading="loading" style="margin: 0 auto;" type="primary"
                                           @click="saveAppInfo">保 &nbsp;&nbsp;&nbsp;存
                                </el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>
        <!-- 图片查看大图 -->
        <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
    </div>
</template>
<script>
    import { uploadFile } from '@/utils/file.js'
    import dayjs from 'dayjs'

    export default {
        data() {
            return {
                tab: 'money',
                canShortInner:false,
                Aliyun: {},
                loading: false,
                // 金额类设置
                MinChargMoney: {
                    value: ''
                },
                MaxGetMoney: {
                    value: ''
                },
                ChargPayScale: {value: ''},
                ServiceTaxScale: {value: ''},
                MinGetMoney: {value: ''},
                MaxGetMoneyDay: {value: ''},
                GetPayScale: {value: ''},
                InfoFeeScale: {value: ''},
                RetentionMoney: { value: '' },
                oilRatio:{value:''},
                maxWithdrawSingleDayRules: [
                    {
                        validator: (rule, value, cb) => {
                            let vList = [this.maxWithdrawSingleDayBusiness.value, this.maxWithdrawSingleDayPersonalCaptain.value, this.maxWithdrawSingleDayCapacityProvider.value, this.maxWithdrawSingleDayDriver.value]
                            vList.forEach(v => {
                                if (v === '') {
                                    cb('不能为空')
                                }
                                if (/\./.test(v)) {
                                  cb('只能输入大于等于0的整数')
                                }
                                v = Number(v)
                                if (Number.isNaN(v) || v < 1 || !Number.isInteger(v)) {
                                    cb('只能输入正整数')
                                }
                            })
                            cb()
                        },
                        trigger: 'blur'
                    }
                ],
                maxNumWithdrawSingleDayRules: [
                    {
                        validator: (rule, value, cb) => {
                            let v = this.maxNumWithdrawSingleDay.value
                            if (v === '') {
                                cb('不能为空')
                            }
                            if (/\./.test(v)) {
                                cb('只能输入大于等于0的整数')
                            }
                            v = Number(v)
                            if (Number.isNaN(v) || v < 0 || !Number.isInteger(v)) {
                                cb('只能输入大于等于0的整数')
                            }
                            cb()
                        },
                        trigger: 'blur'
                    }
                ],
                maxNumWithdrawAtNightRules: [
                    {
                        validator: (rule, value, cb) => {
                            let v = this.maxNumWithdrawAtNight.value
                            if (v === '') {
                                cb('不能为空')
                            }
                            if (/\./.test(v)) {
                                cb('只能输入大于等于0的整数')
                            }
                            v = Number(v)
                            if (Number.isNaN(v) || v < 0 || !Number.isInteger(v)) {
                                cb('只能输入大于等于0的整数')
                            }
                            if(v == 0) {
                                this.maxNumWithdrawAtNightStartTime.value = ""
                                this.maxNumWithdrawAtNightEndTime.value = ""
                                cb()
                            }
                            let startType = this.maxNumWithdrawAtNightStartTimeBelongDate.value
                            let startTime = this.maxNumWithdrawAtNightStartTime.value
                            if (!startTime) {
                                cb('请选择夜间时间')
                            }
                            let startDate = dayjs(dayjs().format('YYYY-MM-DD') + ' ' + startTime)
                            let startHour = startDate.hour()
                            if (startType === 'CURRENT' && startHour < 18
                                || startType === 'CURRENT' && startHour > 23
                            ) {
                                cb('时间选择不合理')
                            }
                            if (startType === 'NEXT' && startHour > 8) {
                                cb('时间选择不合理')
                            }

                            let endType = this.maxNumWithdrawAtNightEndTimeBelongDate.value
                            let endTime = this.maxNumWithdrawAtNightEndTime.value
                            if (!endTime) {
                                cb('请选择夜间时间')
                            }
                            let endDate = dayjs(dayjs().format('YYYY-MM-DD') + ' ' + endTime)
                            let endHour = endDate.hour()
                            if (endType === 'CURRENT' && endHour < 18
                                || endType === 'CURRENT' && endHour > 23
                            ) {
                                cb('时间选择不合理')
                            }
                            if (endType === 'NEXT' && endHour > 8) {
                                cb('时间选择不合理')
                            }

                            if (startType === 'CURRENT' && endType === 'CURRENT' && startDate.isAfter(endDate)) cb('时间选择不合理')
                            if (startType === 'NEXT' && endType === 'CURRENT') cb('时间选择不合理')
                            if (startType === 'NEXT' && endType === 'NEXT' && startDate.isAfter(endDate)) cb('时间选择不合理')
                            cb()
                        },
                        trigger: 'blur'
                    }
                ],
                withdrawIntervalSingleDayRules: [
                    {
                        validator: (rule, value, cb) => {
                            let v = this.withdrawIntervalSingleDay.value
                            if (v === '') {
                                cb('不能为空')
                            }
                            if (/\./.test(v)) {
                                cb('只能输入大于等于0的整数')
                            }
                            v = Number(v)
                            if (Number.isNaN(v) || v < 0 || !Number.isInteger(v)) {
                                cb('只能输入大于等于0的整数')
                            }
                            cb()
                        },
                        trigger: 'blur'
                    }
                ],
                manualTransferMaxRules: [
                    {
                        validator: (rule, value, cb) => {
                            let v = this.manualTransferMax.value
                            if (v === '') {
                                cb('不能为空')
                            }
                            cb()
                        },
                        trigger: 'blur'
                    }
                ],
                RetentionMoneyRules: [
                    {
                        validator: (rule, value, cb) => {
                            let v = this.RetentionMoney.value
                            if (v === '') {
                                cb('不能为空')
                            }
                            v = Number(v)
                            if (Number.isNaN(v) || v < 1 || v > 100 || !Number.isInteger(v)) {
                                cb('请输入[1,100]之间的整数')
                            }
                            cb()
                        },
                        trigger: 'blur'
                    }
                ],
                oilRatioRules:[
                  {
                    validator: (rule, value, cb) => {
                        let v = this.oilRatio.value
                        if (v === '') {
                            cb('不能为空')
                        }
                        v = Number(v)
                        if (Number.isNaN(v) || v < 0 || v > 100 || !Number.isInteger(v)) {
                            cb('请输入[0,100]之间的整数')
                        }
                        cb()
                    },
                    trigger: 'blur'
                  }
                ],
                MaxServiceTaxScale: { value: '' },
                // WithholdAndRemitScale:{},
                // WithholdAndRemit:{},
                maxWithdrawSingleDayBusiness: {},
                maxWithdrawSingleDayPersonalCaptain: {},
                maxWithdrawSingleDayCapacityProvider: {},
                maxWithdrawSingleDayDriver: {},
                maxNumWithdrawSingleDay: {},
                maxNumWithdrawAtNight: {},
                maxNumWithdrawAtNightStartTimeBelongDate: {},
                maxNumWithdrawAtNightStartTime: {},
                maxNumWithdrawAtNightEndTimeBelongDate: {},
                maxNumWithdrawAtNightEndTime: {},
                withdrawIntervalSingleDay: {},
                manualTransferMax: {},

                //功能类设置
                MinGetOrderTonnage: {
                    value: ''
                },
                MAXGetOrderTonnage: {
                    value: ''
                },
                CurrencySign: {},
                CargoSourceRadius: {
                    value: ''
                },
                WeightUnit: {},
                MileageUnit: {},
                MaxAllowOrder: {},
                MaxAllowOrderRules: [
                    {
                        validator: (rule, value, cb) => {
                            value = this.MaxAllowOrder.value
                            if (!value) cb('只允许输入≥0的整数')
                            value = Number(value)
                            if (value < 0 || !Number.isInteger(value)) {
                                cb('只允许输入≥0的整数')
                            }
                            cb()
                        },
                        trigger: 'blur'
                    }
                ],
                OpenShortFallDistance: {},
                distanceCheck: {},
                remarkShortNum:'', //功能设置 记录 值
                invoiceTypeList: [],
                invoiceTypeCanUse: {},
                fenceDefaultRadius: {},
                fenceDefaultRadiusRules: [
                    {
                        validator: (rule, value, cb) => {
                            value = this.fenceDefaultRadius.value
                            if (!value) cb('请输入')
                            value = Number(value)
                            if (value < 0 || !Number.isInteger(value)) {
                                cb('请输入整数')
                            }
                            cb()
                        },
                        trigger: 'blur'
                    }
                ],
                maxDistanceFenceToPlace: {},
                maxDistanceFenceToPlaceRules: [
                    {
                        validator: (rule, value, cb) => {
                            value = this.maxDistanceFenceToPlace.value
                            if (!value) cb('请输入')
                            value = Number(value)
                            if (value < 0 || !Number.isInteger(value)) {
                                cb('请输入整数')
                            }
                            cb()
                        },
                        trigger: 'blur'
                    }
                ],
                platformDesignation:{},
                standardModeSwitch: false,
                standardModeSwitchValue: {},
                standardModeConsignorValue: {},
                standardModeAdministratorsValue: {},
                standardModeConsignor: [],
                standardModeAdministrators: [],
                selectedStandardModeConsignor: [],
                selectedStandardModeAdministrators: [],
                // 待定运费设置
                isDdYf: '',
                FreightPreIsOrNo: {},
                FreightEffectPay: {},
                FreightPreMax: {
                    value: ''
                },
                FreightPreCount: {
                    value: ''
                },
                daiDflag: '',
                yunFflag: '',

                //短倒待定运费
                isShortDdYf:'',
                OpenShortFallOrNo:{}, // 保存需要
                OpenShortFallPay:{}, // 保存需要
                OpenShortFallMax:{
                    value:''
                }, // 保存需要
                OpenShortFallCount:{
                    value:''
                }, // 保存需要
                shortIsOpen: '', //短倒待定运费 开启与否
                shortYunFflag: '', //倒短是否影响运费

                //官网设置
                SiteName: {},
                SiteUrl: {},
                Logo: {},
                Keywords: {},
                Address: {},
                Phone: {},
                ZipCode: {},
                Email: {},
                CertText: {},
                dialogImageUrl: '',
                dialogVisible: false,
                fileList: [],


                // 时间类设置
                timeGroup: [],
                voiceAlertDidabled: true,
                timelinessWarningDisabled: true,


                iosAppClient: [],
                iosAppDriver: [],
                iosAppBroker: [],
                androidAppClient: [],
                androidAppDriver: [],
                androidAppBroker: [],
                applicationVersion: [],
            };
        },
        methods: {
            dayjs,
            /** 输入数字 小数点两位 **/
            keyupEvent(e, input) {
                e.target.value = e.target.value.replace(/[^\d.]/g, "");
                e.target.value = e.target.value.replace(/\.{2,}/g, ".");
                e.target.value = e.target.value.replace(/^\./g, "0.");
                e.target.value = e.target.value.replace(
                    /^\d*\.\d*\./g,
                    e.target.value.substring(0, e.target.value.length - 1)
                );
                e.target.value = e.target.value.replace(/^0[^\.]+/g, "0");
                e.target.value = e.target.value.replace(/^(\d+)\.(\d\d).*$/, "$1.$2");
                this.input = e.target.value;
            },
            /** 短倒待定运费 勾选无限制 **/
            changeShortKm(value){
                if(value===true){
                    this.OpenShortFallDistance.value='';
                    this.canShortInner=true;
                }else if(value===false){
                    this.OpenShortFallDistance.value=this.remarkShortNum;
                    this.canShortInner=false;
                }
            },
            imgChange(file, fileList) {
                if (fileList.length > 1) {
                    fileList.shift()
                }
            },
            changePlatformDesignation(value) {
                this.platformDesignation.value = value
            },
            DdYfChange(val) {
                if (val === true) {
                    this.isDdYf = false;
                } else {
                    this.isDdYf = true;
                }
            },
            /** 短倒 待定运费 是否影响输入内容**/
            DdYfChangeShort(val) {
                if (val === true) {
                    this.isShortDdYf = false;
                } else {
                    this.isShortDdYf = true;
                }
            },
            ossUpload(param) {
                let file = param.file; // 文件的
                uploadFile(file).then(res => {
                    if (res.res.status === 200) {
                        let imgUrl = res.res.requestUrls[0];
                        this.Logo.value = imgUrl
                        this.$message.success('上传成功')
                    } else {
                        this.$message.error(res.res.message)
                    }
                })
            },
            handleRemove(file, fileList) {
            },
            handlePreview(file) {
                this.dialogImageUrl = file.url;
                this.dialogVisible = true;
            },
            /** 官网设置卡 **/
            getSiteTab() {
                this.$http
                    .get("/admin-center-server/base/getSetting", {
                        params: {
                            group: "Site"
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            let resData = data.data;
                            let Logo = resData[2];
                            this.fileList = [
                                {
                                    name: Logo.desc,
                                    url: Logo.value,
                                }
                            ];
                            this.SiteName = resData[0];
                            this.SiteUrl = resData[1];
                            this.Logo = Logo;


                            this.Keywords = resData[3];
                            this.Address = resData[4];
                            this.Phone = resData[5];
                            this.ZipCode = resData[6];
                            this.Email = resData[7];
                            this.CertText = resData[8];
                        }
                    });
            },
            /** 保存官网设置 **/
            saveSite() {
                let form = [];
                form.push(this.SiteName);
                form.push(this.SiteUrl);
                form.push(this.Keywords);
                form.push(this.Logo);
                form.push(this.Address);
                form.push(this.Phone);
                form.push(this.ZipCode);
                form.push(this.Email);
                form.push(this.CertText);
                this.loading = true;
                this.$http
                    .post("/admin-center-server/base/updateSetting", form)
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.$message.success("保存成功");
                            this.getSiteTab();
                            this.loading = false;
                        } else {
                            this.$message.warning(data.message);
                            this.loading = false;
                        }
                    });
            },
            /** 时间类设置卡 **/
            getTimeTab() {
                this.$http
                    .get("/admin-center-server/base/getSetting", {
                        params: {
                            group: "Time"
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            let resData = data.data;
                            this.timeGroup = resData;
                            let voiceItem = resData.find((item) => item.code == 'VoiceAlertSwitch')
                            this.voiceAlertDidabled = voiceItem.value == 0 ? true : false
                            let waringItem = resData.find((item) => item.code == 'TimelinessWarningSwitch')
                            this.timelinessWarningDisabled = waringItem.value == 0 ? true : false
                            if (this.$store.state.user.userInfo2.hasStandardModeFlag) {
                                let index = this.timeGroup.findIndex(item => item.code === 'BrokerGetOrderOverTime')
                                if (index) this.timeGroup.splice(index, 1)
                            }
                        }
                    });
            },
            changeVoiceAlertInterval(val) {
                this.voiceAlertDidabled = val
                let voiceItem = this.timeGroup.find((item) => item.code == 'VoiceAlertSwitch')
                voiceItem.value = val ? 0 : 1
                
            },
            changeTimelinessWarning(val) {
                this.timelinessWarningDisabled = val
                let waringItem = this.timeGroup.find((item) => item.code == 'TimelinessWarningSwitch')
                waringItem.value = val ? 0 : 1
                
            },
            /** 时间卡保存 **/
            saveTime() {
                let form = this.timeGroup;
                this.loading = true;
                this.$http
                    .post("/admin-center-server/base/updateSetting", form)
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.$message.success("保存成功");
                            this.getTimeTab();
                            this.loading = false;
                        } else {
                            this.$message.warning(data.message);
                            this.loading = false;
                        }
                    });
            },
            /** 金额类设置卡 **/
            getMoneyTab() {
                this.$http
                    .get("/admin-center-server/base/getSetting", {
                        params: {
                            group: "Money"
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            let resData = data.data;
                            this.MinChargMoney = resData[0];
                            this.MaxGetMoney = resData[2];
                            this.ChargPayScale = resData[4];
                            this.ServiceTaxScale = resData[6];
                            this.MinGetMoney = resData[1];
                            this.MaxGetMoneyDay = resData[3];
                            this.GetPayScale = resData[5];
                            this.InfoFeeScale = resData[7];
                            this.MaxServiceTaxScale = resData[8]
                            this.RetentionMoney = resData[9]
                            this.oilRatio = resData[10]
                            ;['maxWithdrawSingleDayBusiness',
                             'maxWithdrawSingleDayPersonalCaptain',
                              'maxWithdrawSingleDayCapacityProvider', 
                              'maxWithdrawSingleDayDriver', 
                              'maxNumWithdrawSingleDay', 
                              'maxNumWithdrawAtNight', 
                              'maxNumWithdrawAtNightStartTimeBelongDate',
                              'maxNumWithdrawAtNightStartTime', 
                              'maxNumWithdrawAtNightEndTimeBelongDate', 
                              'maxNumWithdrawAtNightEndTime', 
                              'withdrawIntervalSingleDay',
                              'manualTransferMax']
                                .forEach(v => {
                                    this[v] = resData.find(item => item.code === v) || {}
                                })
                        }
                    });
            },
            getPickerOptions(type) {
                let value = type === 'start' ? this.maxNumWithdrawAtNightStartTimeBelongDate.value : this.maxNumWithdrawAtNightEndTimeBelongDate.value
                if (value === 'CURRENT') {
                    return {
                        selectableRange: '18:00:00 - 23:59:59'
                    }
                } else {
                    return {
                        selectableRange: '00:00:00 - 08:59:59'
                    }
                }
            },
            /** 保存金额 **/
            async savaMoney() {
                let validateFieldPromise = fieldName => {
                    return new Promise((resolve, reject) => {
                        this.$refs.moneyForm.validateField(fieldName, res => {
                            if (res === '') {
                                resolve()
                            } else {
                                reject(res)
                            }
                        })
                    })
                }
                let fields = ['maxWithdrawSingleDay', 'RetentionMoney', 'maxNumWithdrawAtNight', 'withdrawIntervalSingleDay', 'manualTransferMax', 'oilRatio']
                let isPass = true
                for (let i = 0; i < fields.length; i++) {
                    try {
                        await validateFieldPromise(fields[i])
                    }
                    catch (res) { isPass = false }
                }
                if (!isPass) return
                let form = [];
                form.push(this.MinChargMoney);
                form.push(this.MaxGetMoney);
                form.push(this.ChargPayScale);
                form.push(this.ServiceTaxScale);
                form.push(this.MinGetMoney);
                form.push(this.MaxGetMoneyDay);
                form.push(this.GetPayScale);
                form.push(this.InfoFeeScale);
                form.push(this.MaxServiceTaxScale)
                form.push(this.RetentionMoney)
                form.push(this.oilRatio)
                ;['maxWithdrawSingleDayBusiness',
                 'maxWithdrawSingleDayPersonalCaptain', 
                 'maxWithdrawSingleDayCapacityProvider', 
                 'maxWithdrawSingleDayDriver', 
                 'maxNumWithdrawSingleDay', 
                 'maxNumWithdrawAtNight', 
                 'maxNumWithdrawAtNightStartTimeBelongDate',
                 'maxNumWithdrawAtNightStartTime', 
                 'maxNumWithdrawAtNightEndTimeBelongDate',
                 'maxNumWithdrawAtNightEndTime', 
                 'withdrawIntervalSingleDay',
                 'manualTransferMax']
                    .forEach(v => {
                        form.push(this[v])
                    })
                // form.push(this.WithholdAndRemitScale);
                this.loading = true;
                this.$http
                    .post("/admin-center-server/base/updateSetting", form)
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.$message.success("保存成功");
                            this.getMoneyTab();
                            this.loading = false;
                        } else {
                            this.$message.warning(data.message);
                            this.loading = false;
                        }
                    });
            },
            /** 功能类设置卡 **/
            getFunctionTab() {
                this.$http
                    .get("/admin-center-server/base/getSetting", {
                        params: {
                            group: "Function"
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            let resData = data.data;
                            this.MinGetOrderTonnage = resData[0];
                            this.MAXGetOrderTonnage = resData[5];
                            this.CurrencySign = resData[1];
                            this.CargoSourceRadius = resData[2];
                            this.WeightUnit = resData[3];
                            this.MileageUnit = resData[4];
                            this.OpenShortFallDistance = resData[6];
                            this.distanceCheck = resData[22]
                            this.remarkShortNum = resData[6].value;
                            this.MaxAllowOrder = resData[10]
                            this.fenceDefaultRadius = resData[12]
                            this.maxDistanceFenceToPlace = resData[13]
                            this.platformDesignation = resData[15]
                            this.platformDesignation.value = this.platformDesignation.value == 'true' ? true : false
                            this.invoiceTypeCanUse = resData[9]
                            this.invoiceTypeList = resData[9].value.split(',')
                            this.standardModeSwitchValue = resData[17]
                            this.standardModeSwitch = resData[17].value === 'true' ? true : false
                            this.standardModeConsignorValue = resData[18]
                            this.standardModeAdministratorsValue = resData[19]
                            this.selectedStandardModeConsignor = resData[18].value ? resData[18].value.split('&') : []
                            this.selectedStandardModeAdministrators = resData[19].value ? resData[19].value.split('&') : []
                        }
                    });
                this.$get('/admin-center-server/businessSetting/getBusinessList')
                    .then(res => this.standardModeConsignor = res)
                this.$get('/admin-center-server/admin/sysUser/getAdminUserList')
                    .then(res => this.standardModeAdministrators = res)
            },
            /** 保存功能类 **/
            saveFunction() {
                let validateList = [];
                
                this.$refs.functionForm.validateField(['MaxAllowOrder', 'fenceDefaultRadius', 'maxDistanceFenceToPlace'], valid => {
                    validateList.push(valid)
                })
                // 验证成功
                if (validateList.every((item) => item === '')) {
                    let form = [];
                    form.push(this.MinGetOrderTonnage);
                    form.push(this.MAXGetOrderTonnage);
                    form.push(this.CurrencySign);
                    form.push(this.CargoSourceRadius);
                    form.push(this.WeightUnit);
                    form.push(this.MileageUnit);
                    form.push(this.OpenShortFallDistance);
                    form.push(this.distanceCheck)
                    form.push(this.MaxAllowOrder)
                    form.push(this.fenceDefaultRadius)
                    form.push(this.maxDistanceFenceToPlace)
                    form.push(this.platformDesignation)
                    if (this.invoiceTypeList.length === 0) {
                        this.$message({
                            type: "warning",
                            message: "请选择货主可开发票类型"
                        })
                        return
                    } else {
                        this.invoiceTypeCanUse.value = this.invoiceTypeList.join(',')
                        form.push(this.invoiceTypeCanUse)
                    }
                    let standardModeSwitchValue = { ...this.standardModeSwitchValue }
                    standardModeSwitchValue.value = this.standardModeSwitch
                    form.push(standardModeSwitchValue)
                    let standardModeAdministratorsValue = {...this.standardModeAdministratorsValue}
                    standardModeAdministratorsValue.value = this.selectedStandardModeAdministrators.join('&')
                    form.push(standardModeAdministratorsValue)
                    let standardModeConsignorValue = {...this.standardModeConsignorValue}
                    standardModeConsignorValue.value = this.selectedStandardModeConsignor.join('&')
                    form.push(standardModeConsignorValue)
                    this.loading = true;
                    this.$http
                        .post("/admin-center-server/base/updateSetting", form)
                        .then(res => {
                            let data = res.data;
                            if (data.code === "200") {
                                this.$message.success("保存成功");
                                this.getFunctionTab();
                                this.loading = false;
                            } else {
                                this.$message.warning(data.message);
                                this.loading = false;
                            }
                        });
                }
            },
            /** 待定运费设置卡 **/
            getFreightPreTab() {
                this.$http
                    .get("/admin-center-server/base/getSetting", {
                        params: {
                            group: "FreightPre"
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            let resData = data.data;
                            if (resData.length === '0') {
                                return;
                            } else {
                                this.FreightPreIsOrNo = resData[0];
                                this.FreightEffectPay = resData[1];
                                this.FreightPreMax = resData[2];
                                this.FreightPreCount = resData[3];

                                let FreightValue = resData[0].value;
                                let Freightvalue1 = resData[1].value;
                                if (FreightValue === "0") {
                                    this.daiDflag = false;
                                    this.isDdYf = true;
                                } else if (FreightValue === "1") {
                                    this.daiDflag = true;
                                    this.isDdYf = false;
                                }
                                if (Freightvalue1 === "0") {
                                    this.yunFflag = false
                                } else if (Freightvalue1 === "1") {
                                    this.yunFflag = true
                                }
                            }

                        }
                    });
            },

            /** 短倒 待定运费设置 **/
            getShortSetting(){
                this.$http
                    .get("/admin-center-server/base/getSetting", {
                        params: {
                            group: "OpenShortFall"
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            let resData = data.data;
                            if (resData.length === '0') {
                                return;
                            } else {
                                this.OpenShortFallOrNo = resData[0];
                                this.OpenShortFallPay = resData[1];
                                this.OpenShortFallMax = resData[2];
                                this.OpenShortFallCount = resData[3];

                                let OpenShortFallOrNoValue = resData[0].value;
                                let OpenShortFallPayValue = resData[1].value;
                                if (OpenShortFallOrNoValue === "0") {
                                     this.shortIsOpen = false;
                                     this.isShortDdYf = true;
                                } else if (OpenShortFallOrNoValue === "1") {
                                    this.shortIsOpen = true;
                                    this.isShortDdYf = false;
                                }
                                if (OpenShortFallPayValue === "0") {
                                    this.shortYunFflag = false
                                } else if (OpenShortFallPayValue === "1") {
                                    this.shortYunFflag = true
                                }
                            }

                        }
                    });
            },
            /** 保存待定运费设置 **/
            saveFreightPre() {
                let form = [];
                let FreightPreIsOrNo = this.FreightPreIsOrNo;
                let FreightEffectPay = this.FreightEffectPay;

                if (this.daiDflag === false) {
                    FreightPreIsOrNo.value = '0';
                } else if (this.daiDflag === true) {
                    FreightPreIsOrNo.value = '1';
                }
                if (this.yunFflag === false) {
                    FreightEffectPay.value = '0';
                } else if (this.yunFflag === true) {
                    FreightEffectPay.value = '1';
                }
                form.push(FreightPreIsOrNo);
                form.push(FreightEffectPay);
                form.push(this.FreightPreMax);
                form.push(this.FreightPreCount);

                let OpenShortFallOrNo = this.OpenShortFallOrNo;
                let OpenShortFallPay = this.OpenShortFallPay;
                if (this.shortIsOpen === false) {
                    OpenShortFallOrNo.value = '0';
                } else if (this.shortIsOpen === true) {
                    OpenShortFallOrNo.value = '1';
                }
                if (this.shortYunFflag === false) {
                    OpenShortFallPay.value = '0';
                } else if (this.shortYunFflag === true) {
                    OpenShortFallPay.value = '1';
                }
                form.push(OpenShortFallOrNo);
                form.push(OpenShortFallPay);
                form.push(this.OpenShortFallMax);
                form.push(this.OpenShortFallCount);

                this.loading = true;
                this.$http
                    .post("/admin-center-server/base/updateSetting", form)
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.$message.success("保存成功");
                            this.getFreightPreTab();
                            this.getShortSetting();
                            this.loading = false;
                        } else {
                            this.$message.warning(data.message);
                            this.loading = false;
                        }
                    });
            },

            /** App 更新信息显示卡 **/
            getAppData() {
                this.$http.get('/admin-center-server/base/getSetting', {
                    params: {
                        group: 'iosAppClient'
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        let resData = data.data;
                        let waitPush = resData.pop();
                        resData.unshift(waitPush);
                        let useData = [];
                        resData.map((item, index) => {
                            if (item.code === 'forceUpdate') {
                                item.value = item.value === "true" ? true : false
                            }
                            if (item.code === 'underReview') {
                                item.value = item.value === "true" ? true : false
                            }
                            useData.push(item)
                        });
                        this.iosAppClient = useData;
                    }
                });
                this.$http.get('/admin-center-server/base/getSetting', {
                    params: {
                        group: 'iosAppDriver '
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        let resData = data.data;
                        let waitPush = resData.pop();
                        resData.unshift(waitPush);
                        let useData = [];
                        resData.map((item, index) => {
                            if (item.code === 'forceUpdate') {
                                item.value = item.value === "true" ? true : false
                            }
                            if (item.code === 'underReview') {
                                item.value = item.value === "true" ? true : false
                            }
                            useData.push(item)
                        });
                        this.iosAppDriver = useData;
                    }
                });
                this.$http.get('/admin-center-server/base/getSetting', {
                    params: {
                        group: 'iosAppBroker '
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        let resData = data.data;
                        let waitPush = resData.pop();
                        resData.unshift(waitPush);
                        let useData = [];
                        resData.map((item, index) => {
                            if (item.code === 'forceUpdate') {
                                item.value = item.value === "true" ? true : false
                            }
                            if (item.code === 'underReview') {
                                item.value = item.value === "true" ? true : false
                            }
                            useData.push(item)
                        });
                        this.iosAppBroker = useData;
                    }
                });
                this.$http.get('/admin-center-server/base/getSetting', {
                    params: {
                        group: 'androidAppClient'
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        let resData = data.data;
                        let useData = [];
                        resData.map((item, index) => {
                            if (item.code === 'forceUpdate') {
                                item.value = item.value === "true" ? true : false
                            }
                            useData.push(item)
                        });
                        this.androidAppClient = useData;
                    }
                });
                this.$http.get('/admin-center-server/base/getSetting', {
                    params: {
                        group: 'androidAppDriver'
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        let resData = data.data;
                        let useData = [];
                        resData.map((item, index) => {
                            if (item.code === 'forceUpdate') {
                                item.value = item.value === "true" ? true : false
                            }
                            useData.push(item)
                        });
                        this.androidAppDriver = useData;
                    }
                });
                this.$http.get('/admin-center-server/base/getSetting', {
                    params: {
                        group: 'androidAppBroker'
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        let resData = data.data;
                        let useData = [];
                        resData.map((item, index) => {
                            if (item.code === 'forceUpdate') {
                                item.value = item.value === "true" ? true : false
                            }
                            useData.push(item)
                        });
                        this.androidAppBroker = useData;
                    }
                });
                this.$http.get('/admin-center-server/base/getSetting', {
                    params: {
                        group: 'applicationVersion'
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        let resData = data.data;
                        let useData = [];
                        resData.map((item, index) => {
                            if (item.code === 'forceUpdate') {
                                item.value = item.value === "true" ? true : false
                            }
                            useData.push(item)
                        });
                        this.applicationVersion = useData;
                    }
                });
            },
            /** 保存App 信息**/
            saveAppInfo() {
                let str = [];
                this.loading = true;
                str = str.concat(this.iosAppClient).concat(this.iosAppDriver).concat(this.iosAppBroker).concat(this.androidAppClient).concat(this.androidAppDriver).concat(this.androidAppBroker).concat(this.applicationVersion);
                this.$http.post('/admin-center-server/base/updateSetting', str).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.$message.success(data.message);
                        this.getAppData();
                        this.loading = false;
                    } else {
                        this.loading = false;
                        this.$message.warning(data.message)
                    }
                })
            },
            changeTab() {
                switch (this.tab) {
                    case 'site':
                        this.getSiteTab()
                        break
                    case 'time':
                        this.getTimeTab()
                        break
                    case 'money':
                        this.getMoneyTab()
                        break
                    case 'function':
                        this.getFunctionTab()
                        break
                    case 'freight':
                        this.getFreightPreTab()
                        this.getShortSetting()
                        break
                    case 'appData':
                        this.getAppData()
                        break
                }
            }
        },
        activated() {
            this.changeTab()
        },
        created() {
        
        }
    };
</script>
<style>
    .systemBase .el-upload-list {
        float: left;
        width: 40%;
    }

    .systemBase .el-upload {
        margin-top: 65px;
        margin-left: 15px;
    }
</style>
<style rel="stylesheet/scss" lang="scss" scoped>

    .systemBase {
        .masterStation {
            height: 1100px;
        }

        .time-box {
            height: 1100px;
        }

        .money-box {
            height: 1100px;
        }

        .facility {
            height: 1100px;
        }

        .pending {
            height: 1100px;

            .pending_item {
                height: 290px;
                border: 1px solid #cccccc;
            }
        }

        .display {
            height: 1100px;
        }

        .appUpdate {
            border: 1px solid #d0d0d0;

            .app-item {
                margin-bottom: 20px;
                /*border: 1px solid red;*/
            }
        }
    }
    .standard-mode {
        .el-select {
            width: 40%;
        }
    }
    .withdraw-limit {
        .el-input {
            width: 22%;
            margin-right: 5px;
        }
        ::v-deep .el-input-group__prepend {
            padding: 0 10px;
        }
    }
    .withdraw-night {
        .el-input {
            width: 150px;
            margin-right: 5px;
        }
        .el-select {
            width: 100px;
            margin-right: 5px;
        }
    }
</style>
