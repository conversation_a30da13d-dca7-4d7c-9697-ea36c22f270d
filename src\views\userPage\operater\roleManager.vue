<template>
  <div class="app-container">
    <div class="list-add">
      <el-button @click="addNewRole">+ 添加</el-button>
    </div>
    <div class="list-main">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        ref="multipleTable"
        cell-class-name="table_cell_gray"
        header-cell-class-name="table_header_cell_gray"
      >
        <el-table-column prop="roleId" label="角色ID"> </el-table-column>
        <el-table-column prop="roleName" label="角色名称"></el-table-column>
        <el-table-column prop="createUserName" label="创建人">
        </el-table-column>
        <el-table-column prop="createDateTime" label="创建时间">
        </el-table-column>
        <el-table-column fixed="right" width="300" label="操作">
          <template slot-scope="scope">
            <el-button
              @click="functionSetting(scope.row)"
              type="text"
              size="small"
              >功能权限设置</el-button
            >
            <el-button type="text" @click="dataSetting(scope.row)" size="small"
              >数据权限设置</el-button
            >
            <el-button type="text" @click="editRole(scope.row)" size="small"
              >编辑</el-button
            >
            <el-button type="text" @click="deleteRole(scope.row)" size="small"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNumber"
        :page-sizes="[10, 20, 40, 60, 80, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
      ></el-pagination>
    </div>
    <!-- 新增dialog -->
    <el-dialog title="添加角色" :visible.sync="dialogAdd" @closed="dialogAddClosed">
      <el-form
        :model="addForm"
        :rules="addFormRules"
        ref="addForm"
        label-width="80px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input
            v-model="addForm.name"
            autocomplete="off"
            placeholder="请输入角色名称"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogAdd = false">取 消</el-button>
        <el-button type="primary" @click="submitNewRole('addForm')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- 编辑dialog -->
    <el-dialog
      title="编辑角色"
      :visible.sync="dialogEdit"
      @closed="dialogEditClosed"
    >
      <el-form :model="editForm" :rules="editFormRules" ref="editForm">
        <div style="margin-bottom: 10px">角色名称</div>
        <el-form-item label="" prop="name">
          <el-input
            v-model="editForm.name"
            autocomplete="off"
            placeholder="请输入角色名称"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEdit = false">取 消</el-button>
        <el-button type="primary" @click="editRoleInfo('editForm')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- 删除dialog -->
    <el-dialog title="删除角色" :visible.sync="dialogDeleteRole" width="30%">
      <span>删除后，使用该角色的运营账号将不再拥有该角色下的权限，确定删除？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogDeleteRole = false">取 消</el-button>
        <el-button type="primary" @click="confirmDeleteRole"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formInline: {
        name: "",
        mobile: "",
      },
      pageNumber: 1,
      tableData: [],
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      // 新增
      dialogAdd: false,
      addForm: {
        name: "",
      },
      addFormRules: {
        name: [{ required: true, message: "请输入角色名称", trigger: "blur" }],
      },
      // 编辑
      dialogEdit: false,
      editForm: {
        name: "",
      },
      editFormRules: {
        name: [{ required: true, message: "请输入角色名称", trigger: "blur" }],
      },
      // 删除角色
      dialogDeleteRole: false,
      selectRole: {},
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      const params = {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        userType: 5
      };
      this.$post(
        "/admin-center-server/user/role/getRolePageList",
        params
      ).then((res) => {
        this.total = Number(res.total);
        this.tableData = res.list;
      });
    },
    handleSizeChange(val) {
      this.pageNumber = 1;
      this.pageSize = val;
      this.getData();
    },
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.getData();
    },
    // 添加新的角色
    addNewRole() {
      this.dialogAdd = true;
    },
    submitNewRole(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$post('/admin-center-server/user/role/save', {roleName: this.addForm.name, userType: 5}).then(
            () => {
              this.$message.success('操作成功')
              this.dialogAdd = false
              this.getData()
            }
          )
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 编辑运营人员
    editRole(item) {
      this.dialogEdit = true;
      this.selectRole = item;
      this.editForm.name = item.roleName;
    },
    editRoleInfo(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {...this.selectRole, roleName: this.editForm.name, userType: 5}
          this.$post('/admin-center-server/user/role/save', params).then(
            () => {
              this.$message.success('操作成功')
              this.dialogEdit = false
              this.getData()
            }
          )
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    dialogAddClosed() {
      this.$refs['addForm'].resetFields()
    },
    dialogEditClosed() {
      this.$refs["editForm"].resetFields();
    },
    deleteRole(item) {
      this.dialogDeleteRole = true;
      this.selectRole = item;
    },
    confirmDeleteRole() {
      this.$post('/admin-center-server/user/role/logicalDelete', {roleId: this.selectRole.roleId}).then(
        () => {
          this.$message.success('操作成功')
          this.dialogDeleteRole = false
          this.getData()
        }
      )
    },
    functionSetting(item) {
      this.$router.push({
          path: '/roleManager/roleFunctionSetting',
          query: {
            name: item.roleName,
            id: item.roleId
          }
      })
    },
    dataSetting(item) {
      this.$router.push(
        {
          path: '/roleManager/roleDataSetting',
          query: {
            name: item.roleName,
            id: item.roleId
          }
        }
      )
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  background-color: white;
  margin: 20px;
}
.list-add {
  height: 38px;
}
.list-main {
  width: 100%;
  margin-top: 20px;
}

.pagination {
  text-align: right;
  margin-top: 10px;
}
</style>