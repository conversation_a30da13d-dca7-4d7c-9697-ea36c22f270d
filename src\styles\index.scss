@import './variables.scss';
@import './mixin.scss';
// @import './transition.scss';
// @import './element-ui.scss';
@import './sidebar.scss';
// @import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

code {
  background: #eef1f6;
  padding: 15px 16px;
  margin-bottom: 20px;
  display: block;
  line-height: 36px;
  font-size: 15px;
  font-family: "Source Sans Pro", "Helvetica Neue", Arial, sans-serif;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

.warn-content {
  background: rgba(66, 185, 131, .1);
  border-radius: 2px;
  padding: 16px;
  padding: 1rem;
  line-height: 1.6rem;
  word-spacing: .05rem;

  a {
    color: #42b983;
    font-weight: 600;
  }
}

//main-container全局样式
.app-container {
  padding:10px 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

.select-box {
  .el-form-item {
      margin-bottom: 10px;
  }
}

.el-button--text {
  user-select: text;
}

.required-asterisk {
  color: #f56c6c;
}

.table_cell_gray {
  text-align: center;
  border: 0.5px solid #EAF0FB;
}

.table_header_cell_gray {
  text-align: center;
  border: 0.3px solid #EAF0FB;
  background-color: #F5F6F9;
  height: 60px;
}

.table_header_cell_gray_height {
  text-align: center;
  border: 0.3px solid #EAF0FB;
  background-color: #F5F6F9;
  height: 38px;
}

.form-item-content-width {
  width: 180px;
}

.form-item-date-width {
  width: 360px;
}

:root {
  --theme-color: #ED970F;
}

.thumbnail {
  /* width: 146px;
  height: 146px; */
  width: calc(20% - 5px);
  height: 68px;
  margin-right: 5px;
  margin-bottom: 5px;
  cursor: pointer;
}
.img-view{
  height: 150px;
}
.reject-dialog{
  .el-checkbox{
    white-space:wrap;
    display: flex;
    margin-bottom: 10px;
  }
  ::v-deep .el-checkbox__label{
    position: relative;
    top: -2px;
  }
}