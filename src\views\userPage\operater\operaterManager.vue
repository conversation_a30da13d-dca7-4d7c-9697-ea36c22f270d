<template>
  <div class="app-container">
    <div class="select-box">
      <div class="top-title">筛选查询</div>
      <div class="select-info">
        <el-form
          :inline="true"
          :model="formInline"
          ref="formInline"
          class="demo-form-inline"
          size="mini"
          label-width="90px"
        >
          <el-form-item label="姓名:" prop="name">
            <el-input
              v-model="formInline.name"
              placeholder="请输入姓名"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号:" prop="mobile">
            <el-input
              v-model="formInline.mobile"
              oninput="value=value.replace(/[^\d]/g,'')"
              maxlength="11"
              placeholder="请输入手机号"
            ></el-input>
          </el-form-item>

          <el-form-item>
            <el-button class="left" icon="el-icon-search" @click="onSubmit"
              >查询</el-button
            >
            <el-button
              class="left"
              @click="resetForm('formInline')"
              icon="el-icon-refresh-right"
              >重置</el-button
            >
            <el-button
              class="left"
              @click="refreshfn"
              icon="el-icon-refresh-right"
              >刷新</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>账户列表</div>
        <el-button type="primary" @click="addNewOperater">+新增</el-button>
      </div>
      <div class="list-main">
        <el-table
          :data="tableData"
          border
          style="width: 100%"
          ref="multipleTable"
          cell-class-name="table_cell_gray"
          header-cell-class-name="table_header_cell_gray"
        >
          <el-table-column type="index" label="序号" width="55">
          </el-table-column>

          <el-table-column prop="name" label="姓名" width="90">
          </el-table-column>
          <el-table-column prop="mobile" label="手机号" width="120">
          </el-table-column>
          <el-table-column prop="relevanceBussinessList" label="关联货主">
            <template slot-scope="scope">
              <span v-for="(item, index) in scope.row.relevanceBussinessList" :key="item.id">{{item.name}}{{index == (scope.row.relevanceBussinessList.length -1) ? "" : '、'}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="roleName" label="角色">
            <template slot-scope="scope">
              {{scope.row.roleName ? scope.row.roleName : '-'}}
            </template>
          </el-table-column>
          <el-table-column prop="deleteFlag" label="启用状态" width="80">
            <template slot-scope="scope">
              <span :class="scope.row.deleteFlag == 0 ? 'table-delete-flag-open' : 'table-delete-flag-close'">{{scope.row.deleteFlag == 0 ? "启用" : "停用"}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createdUserName" label="创建人" width="90">
          </el-table-column>
          <el-table-column prop="createdDateTime" label="创建时间" width="120">
          </el-table-column>
          <el-table-column fixed="right" width="300" label="操作">
            <template slot-scope="scope">
              <el-button @click="edit(scope.row)" type="text" size="small"
                >编辑</el-button
              >
              <el-button
                type="text"
                @click="correlation(scope.row)"
                size="small"
                >关联货主</el-button
              >
              <el-button
                type="text"
                @click="getSetting(scope.row)"
                size="small"
                >运费支付方式</el-button
              >
              <br/>
              <el-button
                type="text"
                @click="modifyPassword(scope.row)"
                size="small"
                >修改密码</el-button
              >
              <el-button
                type="text"
                @click="resetPassword(scope.row)"
                size="small"
                >重置密码</el-button
              >
              <el-button
                type="text"
                @click="openOrStop(scope.row)"
                size="small"
                >{{scope.row.deleteFlag == 0 ? "停用" : "启用"}}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page="pageNumber"
                         :page-sizes="[10, 20, 40, 60, 80, 100]"
                         :page-size="pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total"
                         class="pagination"></el-pagination>
      </div>
    </div>
    <!-- 新增dialog -->
    <el-dialog title="新增运营账号" :visible.sync="dialogAdd">
      <el-form :model="addForm" :rules="addFormRules" ref="addForm" label-width="80px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="addForm.name" autocomplete="off" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input 
          v-model="addForm.mobile" 
          maxlength="11"
          oninput="value=value.replace(/[^\d]/g,'')" 
          autocomplete="off" 
          placeholder="可作为账号登录货主端"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="pwd">
          <el-row>
            <el-col :span="12"><el-input v-model="addForm.pwd" autocomplete="new-password" placeholder="请输入密码" show-password></el-input></el-col>
            <el-col :span="12"><div class="password">字母+数字8-24位，且首字母大写</div></el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="确认密码" prop="secondPwd">
          <el-row>
            <el-col :span="12"><el-input v-model="addForm.secondPwd" autocomplete="off" placeholder="请确认密码" show-password></el-input></el-col>
            <el-col :span="12"><div class="password">再次确认密码</div></el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="角色">
          <el-select style="width: 100%" v-model="addForm.roleId" clearable placeholder="请选择账号所关联角色，不是必填">
            <el-option
            v-for="item in roles"
            :key="item.roleId"
            :label="item.roleName"
            :value="item.roleId">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogAdd = false">取 消</el-button>
        <el-button type="primary" @click="submitNewOperaterInfo('addForm')">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 编辑dialog -->
    <el-dialog title="编辑运营账号" :visible.sync="dialogEdit" @closed="dialogEditClosed">
      <el-form :model="editForm" :rules="editFormRules" ref="editForm" label-width="80px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="editForm.name" autocomplete="off" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input 
          :disabled="true"
          v-model="editForm.mobile" 
          maxlength="11"
          oninput="value=value.replace(/[^\d]/g,'')" 
          autocomplete="off" 
          placeholder="可作为账号登录货主端"></el-input>
        </el-form-item>
        <el-form-item label="角色">
          <el-select style="width: 100%" v-model="editForm.roleId" clearable placeholder="请选择账号所关联角色，不是必填">
            <el-option
            v-for="item in roles"
            :key="item.roleId"
            :label="item.roleName"
            :value="item.roleId">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogEdit = false">取 消</el-button>
        <el-button type="primary" @click="editOperaterInfo('editForm')">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 关联货主dialog -->
    <el-dialog title="关联货主" :visible.sync="dialogCorrelation">
      <el-select
        class="select-shipper"
        v-model="correlationSelects"
        multiple
        filterable
        placeholder="请选择当前运营账号负责的货主，可按货主名称搜索">
        <el-option
          v-for="item in correlationList"
          :key="item.userId"
          :label="item.name"
          :value="item.userId">
        </el-option>
      </el-select>
      <div class="correlation-button">
         <el-button type="primary" @click="saveCorrelationInfo">确 定</el-button>
         <el-button @click="dialogCorrelation = false" style="margin-right: 20px">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 修改密码dialog -->
    <el-dialog title="修改密码" :visible.sync="dialogModifyPassword">
      <el-form :model="modifyPasswordForm" :rules="modifyPasswordRules" ref="modifyPasswordForm" label-width="80px">
        <el-form-item label="原密码" prop="oldPwd">
          <el-input v-model="modifyPasswordForm.oldPwd" autocomplete="off" placeholder="请输入原密码" show-password></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPwd">
          <el-row>
            <el-col :span="12"><el-input v-model="modifyPasswordForm.newPwd" autocomplete="off" placeholder="请输入新密码" show-password></el-input></el-col>
            <el-col :span="12"><div class="password">字母+数字8-24位，且首字母大写</div></el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogModifyPassword = false">取 消</el-button>
        <el-button type="primary" @click="modifyPasswordInfo('modifyPasswordForm')">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 重置dialog -->
    <el-dialog
      title="重置密码"
      :visible.sync="dialogResetPassword"
      width="30%">
      <span>确定将当前账号的密码重置为“a12345678”？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogResetPassword = false">取 消</el-button>
        <el-button type="primary" @click="submitResetPassword">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 启用停用dialog -->
    <el-dialog
      :title="selectOperater.deleteFlag === '0' ? '停用' : '启用'"
      :visible.sync="dialogOpenOrStop"
      width="30%">
      <span>确定{{selectOperater.deleteFlag === '0' ? '停用' : '启用'}}？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogOpenOrStop = false">取 消</el-button>
        <el-button type="primary" @click="submitOpenOrStop">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
        :visible.sync="showPayMethod"
        title="支付方式设置"
        width="500px"
        :close-on-click-modal="false"
        @close="closeFn"
        class="auth-dialog"
        >
        <el-form class="auth-form" label-width="100px" ref="authForm" :model="authForm" v-loading="dialogLoading">
            <div class="select-div">
                <div>开启自动支付后，支付司机运费时系统将自动支付。</div>
                <div>如钱包余额不足，则自动支付失败，需人为充值后手动发起支付。</div>
            </div>
            <el-form-item label="油费付:" prop="oilPay">
                <el-radio-group v-model="authForm.oilPay"  @change="changFn">
                    <el-radio label="1">手动支付</el-radio>
                    <el-radio label="2">自动支付</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="回单付:" prop="receiptPay">
                <el-radio-group v-model="authForm.receiptPay"  @change="changFn">
                    <el-radio label="1">手动支付</el-radio>
                    <el-radio label="2">自动支付</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="结算付:" prop="settlementPay">
                <el-radio-group v-model="authForm.settlementPay" @change="changFn">
                    <el-radio label="1">手动支付</el-radio>
                    <el-radio label="2">自动支付</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="申请费用:" prop="feesPay">
                <el-radio-group v-model="authForm.feesPay" @change="changFn">
                    <el-radio label="1">手动支付</el-radio>
                    <el-radio label="2">自动支付</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <template #footer>
        <el-button @click="closeFn">取消</el-button>
        <el-button @click="submitAuth" type="primary" :loading="btnLoading">确认</el-button>
        </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formInline: {
        name: "",
        mobile: "",
      },
      pageNumber: 1,
      tableData: [],
      pageSize: 10,
      total: 0,
      // 新增
      dialogAdd: false,
      addForm: {
        name: '',
        mobile: '',
        pwd: '',
        secondPwd: '',
        roleId: ''
      },
      addFormRules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        mobile: [
          {
            required: true,
            validator: (rule, value, callback) => {
                if (!value) {
                    callback(new Error("请输入手机号"));
                } else if (
                    !/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)
                ) {
                    callback(new Error("请输入正确格式的手机号"));
                } else {
                    callback();
                }
            },
            trigger: "blur",
          }
        ],
        pwd: [{ required: true, validator: (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入密码'));
        } else {
          if (!(/^[a-zA-Z]/.test(value))){
            callback(new Error('密码不符合规则'))
          } else {
            if (value.length >= 8 && value.length <=24) {
              if (this.addForm.secondPwd === '') {
                let firstChart = value.slice(0,1)
                if (firstChart === firstChart.toUpperCase()) {
                  callback();
                } else {
                  callback(new Error('密码不符合规则'))
                }
                
              } else {
                if (value !== this.addForm.secondPwd) {
                  callback(new Error('两次输入密码不一致!'));
                } else {
                  callback();
                }
                
              }
            } else {
              callback(new Error('请输入8-24位密码'))
            }
            
          }
        }
      }, trigger: 'blur' }],
      secondPwd: [{ required: true, validator: (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入密码'));
        } else if (value !== this.addForm.pwd) {
          callback(new Error('两次输入密码不一致!'));
        } else {
          callback();
        }
      }, trigger: 'blur' }]
      },
      // 编辑
      dialogEdit: false,
      editForm: {
        name: '',
        mobile: '',
        roleId: ''
      },
      editFormRules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        mobile: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
      },
      // 关联货主
      dialogCorrelation: false,
      correlationList: [],
      correlationSelects: [],
      // 修改密码
      dialogModifyPassword: false,
      modifyPasswordForm: {
        oldPwd: '',
        newPwd: ''
      },
      modifyPasswordRules: {
        oldPwd: [{ required: true, message: '请输入', trigger: 'blur' }],
        newPwd: [{ required: true, validator: (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入原密码'));
        } else {
          if (!(/^[a-zA-Z]/.test(value))){
            callback(new Error('密码不符合规则'))
          } else {
            if (value.length >= 8 && value.length <=24) {
            let firstChart = value.slice(0,1)
                if (firstChart === firstChart.toUpperCase()) {
                  callback();
                } else {
                  callback(new Error('密码不符合规则'))
                }
            } else {
              callback(new Error('请输入8-24位密码'))
            }
          }
         }
        }, trigger: 'blur' }],
      },
      // 重置密码
      dialogResetPassword: false,
      // 启用停用
      dialogOpenOrStop: false,
      selectOperater: {},
      roles: [{roleId: 1, roleName: '角色1'}, {roleId: 2, roleName: '角色2'}, {roleId: 3, roleName: '角色3'}],
      showPayMethod:false,
      authForm:{
          oilPay:'1',
          receiptPay:'1',
          settlementPay:'1',
          feesPay:'1'
      },
      dialogLoading:false,
      btnLoading:false,
      userId:''
    };
  },
  mounted() {},
  activated() {
    this.getData()
    this.getRoles()
  },
  methods: {
    changFn(){
        this.$forceUpdate()
    },
    closeFn(){
        this.$nextTick(() => {
        this.$refs.authForm.clearValidate()
        })
        this.showPayMethod = false;
        this.authForm={};
    },
    // 获取支付方式
    getSetting(row){
        this.dialogLoading = true
        this.showPayMethod =true
        this.userId = row.userId
        this.$post('/admin-center-server/payModelSetting/getByUserSetting?userId='+this.userId, {
        }).then((res) => {
            this.dialogLoading = false
            this.authForm={
                oilPay:'1',
                receiptPay:'1',
                settlementPay:'1',
                feesPay:'1'
            };
            if(res.length>0){
              res.forEach(ele=>{
                if(ele.payType == 1){
                    this.authForm.oilPay = ele.payMode
                    this.authForm.oilId = ele.id
                }else if(ele.payType == 2){
                    this.authForm.settlementPay = ele.payMode
                    this.authForm.settlementId = ele.id
                }else if(ele.payType == 3){
                    this.authForm.receiptPay = ele.payMode
                    this.authForm.receiptId = ele.id
                }else if(ele.payType == 5){
                    this.authForm.feesPay = ele.payMode
                    this.authForm.feeId = ele.id
                }
              })
            }
            
        })
    },
    submitAuth(){
        let arr = [
            {payType:1,payMode:this.authForm.oilPay,id:this.authForm.oilId},
            {payType:2,payMode:this.authForm.settlementPay,id:this.authForm.settlementId},
            {payType:3,payMode:this.authForm.receiptPay,id:this.authForm.receiptId},
            {payType:5,payMode:this.authForm.feesPay,id:this.authForm.feeId}
        ]
        this.btnLoading = true
        this.$post('/admin-center-server/payModelSetting/updateByUser?userId='+this.userId,arr).then((res) => {
            this.$message.success('操作成功')
            this.btnLoading = false
            this.showPayMethod = false;
            this.authForm={};
        })
    },
    // 刷新当前页
    refreshfn() {
      this.$router.go(0);
    },
    //重置
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.pageNumber = 1;
      this.getData();
    },
    onSubmit() {
      this.pageNumber = 1;
      this.getData();
    },
    getData() {
      const params = {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        name: this.formInline.name,
        mobile: this.formInline.mobile
      }
      this.$post('/admin-center-server/operationalPeo/operationalPeoManage', params).then(
        res => {
          this.total = Number(res.total)
          this.tableData = res.list
        }
      )
    },
    getRoles() {
      // 获取角色
      this.$post('/admin-center-server/user/role/getRolePageList', {pageNumber: 1, pageSize: 10000, userType: 5}).then(
        res => {
          this.roles = res.list
        }
      )
    },
    handleSizeChange (val) {
      this.pageNumber = 1
      this.pageSize = val
      this.getData()
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange (val) {
      this.pageNumber = val
      this.getData()
      console.log(`当前页: ${val}`);
    },
    // 添加新的运营人员
    addNewOperater() {
      this.dialogAdd = true
    },
    submitNewOperaterInfo(formName) {
      this.$refs[formName].validate((valid) => {
          if (valid) {
            const params = {...this.addForm, dType: 5, loginPort: 'PC'}
            params.pwd = this.passwordEncryption(this.addForm.pwd)
            params.secondPwd = this.passwordEncryption(this.addForm.secondPwd)
            this.$post('/admin-center-server/operationalPeo/operationalPeoAdd', params).then(
              res => {
                this.$message.success("新增运营账号成功");
                this.$refs[formName].resetFields();
                this.addForm = {
                  name: '',
                  mobile: '',
                  pwd: '',
                  secondPwd: ''
                }
                this.dialogAdd = false
                this.getData()
              },
              error => {
                // this.$message.error("新增运营账号失败");
              }
            )
          } else {
            console.log('error submit!!');
            return false;
          }
        });
    },
    // 编辑运营人员
    edit(item) {
      this.dialogEdit = true
      this.selectOperater = item
      this.editForm.mobile = item.mobile
      this.editForm.name = item.name
      this.editForm.roleId= item.roleId
    },
    editOperaterInfo(formName) {
      this.$refs[formName].validate((valid) => {
          if (valid) {
            const params = {...this.editForm, dType: 5, loginPort: 'PC', userId: this.selectOperater.userId}
            this.$post('/admin-center-server/operationalPeo/operationalPeoEditor', params).then(
              res => {
                this.$message.success("保存成功");
                this.dialogEdit = false
                this.$refs[formName].resetFields();
                this.getData()
              },
              error => {
                this.$message.error("保存失败");
              }
            )
          } else {
            console.log('error submit!!');
            return false;
          }
        });
    },
    dialogEditClosed() {
      this.$refs['editForm'].resetFields();
    },
    // 关联货主
    correlation(item) {
      this.selectOperater = item
      if (item.relevanceBussinessList) {
        this.correlationSelects = item.relevanceBussinessList.map( item => {
          return item.id
        })
      } else {
        this.correlationSelects = []
      }
      
      let params = {
        userType: 1,
        type: 1,
        mobile: '1',
        interfaceType: 1
      }
      this.$post('/admin-center-server/car/relevancyUser', params)
        .then(res => {
          this.correlationList = res
      })

      this.dialogCorrelation = true
    },
    saveCorrelationInfo() {
      this.$post('/admin-center-server/operationalPeo/operationalPeoRelevanceBussiness', {
        businessList: this.correlationSelects,
        operationalPeoUserId: this.selectOperater.userId
      }).then(
        res => {
          this.$message({
            type: 'success',
            message: '操作成功'
          })
          this.dialogCorrelation = false
          this.getData()
        }
      )
      
    },
    // 修改密码
    modifyPassword(item) {
      this.dialogModifyPassword = true
      this.selectOperater = item
    },
    modifyPasswordInfo(formName) {
      this.$refs[formName].validate((valid) => {
          if (valid) {
            this.modifyPasswordForm.newPwd = this.passwordEncryption(this.modifyPasswordForm.newPwd)
            this.modifyPasswordForm.oldPwd = this.passwordEncryption(this.modifyPasswordForm.oldPwd)
            const params = {...this.modifyPasswordForm, userId: this.selectOperater.userId}
            this.$post('/admin-center-server/operationalPeo/resetOperationalPeoPwd', params).then(
              res => {
                this.$message({
                  type: 'success',
                  message: '操作成功'
                });
                this.dialogModifyPassword = false
                this.modifyPasswordForm =  {
                  oldPwd: '',
                  newPwd: ''
                }
                this.$refs[formName].resetFields();
              },
              error => {
                this.modifyPasswordForm =  {
                  oldPwd: '',
                  newPwd: ''
                }
                this.$refs[formName].resetFields();
              }
            )
          } else {
            console.log('error submit!!');
            return false;
          }
        });
    },
    // 重置密码
    resetPassword(item) {
      this.selectOperater = item
      this.dialogResetPassword = true
    },
    submitResetPassword() {
      this.$http.post('/admin-center-server/commonUser/updateUserPwd' + '?pwd=' + this.passwordEncryption('a12345678') + '&userId=' + this.selectOperater.userId).then(res => {
          this.$message({
            type: 'success',
            message: '操作成功'
          });
          this.dialogResetPassword = false
        })
    },
    // 停用启用
    openOrStop(item) {
      this.selectOperater = item
      this.dialogOpenOrStop = true
    },
    submitOpenOrStop() {
      this.$post('/admin-center-server/commonUser/updateUserStatus' + `/${this.selectOperater.userId}?deleteFlag=${this.selectOperater.deleteFlag === '0' ? '-1' : '0'}`).then(
        res => {
          this.$message({
            type: 'success',
            message: '操作成功'
          });
          this.getData()
          this.dialogOpenOrStop = false
      })
    },
    passwordEncryption(pwd) {
      return this.$md5(pwd).toUpperCase()
    }
  },
};
</script>

<style lang="scss" scoped>
.select-shipper {
  width: 400px;
}
.select-box {
  /*height: 260px;*/
  background-color: #ffffff;

  .top-title {
    font-size: 14px;
    height: 30px;
    line-height: 30px;
    padding-left: 10px;
    border-bottom: 1px solid #cccccc;
  }

  .select-info {
    padding-top: 10px;
    padding-bottom: 10px;
  }
}

.list-box {
  background-color: #ffffff;
  margin-top: 20px;
  padding: 10px;

  .list-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    div {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
    }
  }

  .list-main {
    width: 100%;
    margin-top: 10px;
  }
  .table-delete-flag-close {
    color: rgb(197, 33, 46);
  }

  .table-delete-flag-open {
    color: rgb(113, 202, 113);
  }
}

.password {
  margin-left: 10px;
  color: red;
  font-size: 14px;
}
.correlation-button {
  margin: 30px;
  display: flex;
  flex-direction: row-reverse;
}
 .pagination {
      text-align: right;
      margin-top: 10px;
    }
    .select-div{
        width: 500px;
        position: relative;
        // left: -20px;
        // top: -20px;
        color: #f7b64d;
        padding: 12px 15px;
        line-height: 21px;
        font-size: 14px;
        // text-align: center;
        margin-bottom: 20px;
        background-color: #fdf6ed;
    }
    ::v-deep .auth-dialog .el-dialog__body{
        padding: 0px !important;
    }
    .auth-form .el-form-item{
        margin-bottom: 10px;
    }
</style>