<template>
  <div>
    <div class='title'>{{title}}</div>
    <el-form :model="ruleForm"
             :rules="rules"
             ref="ruleForm"
             label-width="150px"
             size="medium"
             class="demo-ruleForm">

      <el-form-item label="手机号"
                    prop="tel">
        <el-input oninput="value=value.replace(/[^\d]/g,'')"
                  maxlength="11"
                  v-model="ruleForm.tel"
                  placeholder="可作为登录名使用，支持全网号段"></el-input>
      </el-form-item>

      <el-form-item label="登录密码"
                    prop="pwd">
        <el-input v-model="ruleForm.pwd"
                  :disabled="id?true:false"
                  placeholder="该账户的登录密码，可输入24位任意字符。"></el-input>
      </el-form-item>

      <el-form-item label="是否启用账户"
                    prop='type'>
        <el-switch v-model="ruleForm.type"></el-switch>
        <p style='line-height:16px;color:#999;font-size:12px;'>注：如果选择是，当前添加账户被审核后，客户可直接使用，如果选择否，审核通过后账户将暂时为冻结状态，直至手动解冻</p>
      </el-form-item>
      <el-form-item>
        <el-button type="primary"
                   @click="submitForm('ruleForm')">提交注册</el-button>

      </el-form-item>
    </el-form>

  </div>
</template>

<script>

const add = '/admin-center-server/commonUser/add/user'//添加
const forUpdateInfo = '/admin-center-server/commonUser/forUpdateInfo'//回显
const updateUser = '/admin-center-server/commonUser/updateUser'//编辑-添加
export default {
  data () {
    return {
      id: '',
      title: '',
      urlType: '',
      addFlag: true,
      ruleForm: {
        tel: '',
        pwd: '',
        type: 'false',
      },
      rules: {
        tel: [
          { required: true, message: '请输入客户电话', trigger: 'blur' },
          { min: 8, max: 11, message: '手机号格式不正确', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: this.telReg }
        ],
        pwd: [
          { required: true, message: '请输入登录密码', trigger: 'blur' },
          { min: 5, max: 24, message: '登录密码为5个到25个字符', trigger: 'blur' },
          {
            validator: (rule, value, cb) => {
              let reg = new RegExp("([\u4E00-\u9FFF]|[\u3002\uff1b\uff0c\uff1a\u201c\u201d\uff08\uff09\u3001\uff1f\u300a\u300b\uff01\u3010\u3011\uffe5])+","g")
              if (reg.test(value)) cb('密码不能包含中文')
              cb()
            },
            trigger: 'blur'
          }
        ]
      },
      //通讯录
    }  },
  methods: {
    submitForm (formName) {
      var that = this
      this.$refs[formName].validate(valid => {
        var v = that.ruleForm
        if (valid) {
          if (this.addFlag) {
            this.addFlag = false
            var authenticateTypeNum = ''
            //1货主、2调度员、3司机
            var data = {
              userType: that.urlType,
              mobile: v.tel,
              activateAccountFlag: v.type
            }
            if (this.id) {
              data.id = this.id
              this.$http.post(updateUser, data).then(res => {
                var that = this
                if (res.data.code == '200') {
                  this.$message({
                    message: res.data.message,
                    type: 'success'
                  });

                  setTimeout(function () {
                    that.$router.go(-1);
                  }, 3000);


                } else {
                  this.addFlag = true
                  this.$message.error(res.data.message);
                }
              })
            } else {
              // data.password = this.$md5(v.pwd).toUpperCase();
              data.password = this.$sm3(v.pwd).toUpperCase();
              console.log(data)
              this.$http.post(add, data).then(res => {
                var that = this
                if (res.data.code == '200') {
                  this.$message({
                    message: res.data.message,
                    type: 'success'
                  });
                  setTimeout(function () {
                    that.$router.go(-1);
                  }, 3000);
                } else {
                  this.addFlag = true
                  this.$message.error(res.data.message);
                }
              })
            }
          }


        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

  },
  mounted () {
    this.id = this.$route.query.id;
    this.urlType = this.$route.query.urlType;

  }

}
</script>

<style lang="scss" scoped>
.demo-ruleForm {
  width: 500px;
  margin-top: 20px;
}

.title {
  font-size: 16px;
  margin: 20px 0 0 20px;
  color: #1898ff;
}
</style>

