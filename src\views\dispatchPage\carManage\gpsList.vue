<template>
  <div>
    <div class="list-box">
      <div class="main-box">
        <div class="list-info">
          <el-form ref="form" label-width="140px" :inline="true" size="mini">
            <el-form-item label="设备号：">
              <el-input v-model="search.deviceNo"></el-input>
            </el-form-item>
            <el-form-item label="车牌号：">
              <el-input v-model="search.plateNumber"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="doSearch">查询</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="list-box">
      <el-table 
        v-loading="loading"
        :data="data"  
        style="width: 100%"
        ref="table"
        :cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column label="序号" type="index"></el-table-column>
        <el-table-column label="设备号（SN）" prop="deviceNo"></el-table-column>
        <el-table-column label="IMEI" prop="deviceImei"></el-table-column>
        <el-table-column label="绑定车辆" prop="plateNumber"></el-table-column>
        <el-table-column label="申请车辆认证用户" width="230">
          <template slot-scope="scope">
            <div class="user-list-wrapper">
              <div v-for="item in scope.row.applicantUserList" class="user-list">
                {{ item.name && `${item.name} - ${item.mobile}` }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="当前驾驶司机" width="230">
          <template slot-scope="scope">
            <div class="user-list-wrapper">
              <div v-for="item in scope.row.currentDriverList" class="user-list">
                {{ item.name && `${item.name} - ${item.mobile}` }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="设备状态" prop="deviceStatus" width="100">
          <template slot-scope="scope">
            <div class="gps-status" :class="getGpsStatusClass(scope.row)">{{ getGpsStatusText(scope.row) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="绑定人" width="230">
          <template slot-scope="scope">
            {{ scope.row.bindingUser && scope.row.bindingUser.name && `${scope.row.bindingUser.name} - ${scope.row.bindingUser.mobile}` }}
          </template>
        </el-table-column>
        <el-table-column label="绑定时间" prop="bindingTime"></el-table-column>
      </el-table>
      <div style="margin-top: 10px; text-align: right">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="getList"
          :current-page.sync="page.page"
          :page-sizes="[10,20, 40, 60, 80, 100]"
          :page-size="page.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      search: {
        deviceNo: '',
        plateNumber: ''
      },
      loading: false,
      data: [],
      total: 0,
      page: {
        page: 1,
        pageSize: 10
      }
    }
  },
  activated() {
    this.getList()
  },
  methods: {
    getList() {
      this.$post('/admin-center-server/car/getGpaDeviceBindingCar', {
        ...this.page,
        ...this.search
      })
        .then(res => {
          this.total = Number(res.total)
          this.data = res.list
        })
    },
    doSearch() {
      this.getList()
    },
    reset() {
      this.search = {}
      this.page.page = 1
    },
    handleSizeChange(v) {
      this.page.pageSize = v
      this.getList()
    },
    getGpsStatusClass(row) {
      if (row.deviceStatus === '-1') {
        return 'gps-status-unbind'
      } else if (row.deviceStatus === '0') {
        return 'gps-status-offline'
      } else {
        return 'gps-status-online'
      }
    },
    getGpsStatusText(row) {
      if (row.deviceStatus === '-1') {
        return '未绑定'
      } else if (row.deviceStatus === '0') {
        return '离线'
      } else {
        return '在线'
      }
    }
  }
}
</script>

<style>
.list-box {
  background-color: #ffffff;
  margin: 20px;
  padding: 10px;
}
.gps-status {
  width: 54px;
  height: 22px;
  border-radius: 5px;
  text-align: center;
  line-height: 22px;
  font-size: 12px;
}
.gps-status-offline {
  border: 1px solid #fde2e2;
  color: rgb(245, 108, 108);
  background-color: #fef1f1;
}
.gps-status-online {
  border: 1px solid #e1f3d8;
  color: rgb(103, 194, 58);
  background-color: #f0f9ec;
}
.user-list-wrapper {
  padding: 10px 0;
}
.user-list {
  line-height: 20x;
}
</style>