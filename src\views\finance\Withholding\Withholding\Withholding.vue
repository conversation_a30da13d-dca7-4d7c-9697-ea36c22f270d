<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">
        <div>
          代扣代缴查询
        </div>
        <div>
          <!-- <el-button class="left"
                     size="mini"
                     :disabled="canUpLoad"
                     @click="uploadFn"
                     type="primary"
                     icon="el-icon-download"
                     :loading="loadLoading">下载
          </el-button> -->
        </div>
      </div>
      <div class="select-info">
        <el-form :inline="true"
                 :model="formInline"
                 ref='formInline'
                 class="demo-form-inline"
                 size="mini"
                 label-width="90px">
          <el-form-item label="选择年份:"
                        prop='date'>
            <el-date-picker v-model="formInline.date"
                            type="year"
                            value-format="yyyy"
                            style="width: 160px"
                            placeholder="选择年">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="选择月份:"
                        prop='transaction'>
            <el-select class='xxn'
                       style="width: 200px"
                       v-model="formInline.transaction"
                       multiple
                       @change="xxnfn"
                       placeholder="请选择月份">
              <el-option v-for="item in transactionDate"
                         :key="item.id"
                         :disabled="item.disabled"
                         :label="item.name"
                         :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户名称:"
                        prop='name'>
            <el-input v-model="formInline.name"
                      style="width: 160px"
                      placeholder="请输入用户名称"></el-input>
          </el-form-item>
          <el-form-item label="手机号码:"
                        prop='tel'>
            <el-input v-model="formInline.tel"
                      oninput="value=value.replace(/[^\d]/g,'')"
                      maxlength="11"
                      style="width: 160px"
                      placeholder="请输入手机号码"></el-input>
          </el-form-item>
          <el-form-item label="身份证号:"
                        prop='ID'>
            <el-input v-model="formInline.ID"
                      style="width: 160px"
                      placeholder="请输入身份证号号"></el-input>
          </el-form-item>

        </el-form>
        <el-form :inline="true"
                 :model="formInline"
                 ref='formInline2'
                 class="demo-form-inline"
                 size="mini"
                 label-width="90px">
          <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="收入类型:"
                        prop='dataSources'>
            <el-select v-model="formInline.dataSources"
                       style="width: 160px"
                       placeholder="请选择"
                       clearable>
              <el-option v-for="(item, lan) in statusWrap1"
                         :label="item.name"
                         :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="收入金额:"
                        prop='authenticateStatus'>
            <el-select v-model="formInline.authenticateStatus"
                       style="width: 200px"
                       placeholder="请选择收入金额"
                       clearable>
              <el-option v-for="(item, lan) in statusWrap"
                         :label="item.conditions"
                         :value="item.queryNum"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="角色类型:"
                        prop='roleType'>
            <el-select v-model="formInline.roleType"
                       placeholder="请选择角色类型"
                       clearable
                       style="width: 160px">
              <el-option v-for="(item, lan) in roleType"
                         :label="item.name"
                         :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="合作业务主体:" prop="baseId" label-width="120px">
            <el-select
              v-model="formInline.baseId"
              placeholder="请选择"
            >
              <el-option v-for="item in $store.state.user.baseInfo" :key="item.id" :value="item.id" :label="item.baseName"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button class="left"
                     icon="el-icon-search"
                     size="mini"
                     @click="onSubmit">查询
            </el-button>
            <el-button class="left"
                      @click="resetForm"
                      size="mini"
                      icon="el-icon-refresh-right">重置
            </el-button>
          </el-form-item>
        </el-form>

      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表 <span style='margin-left:50px'>合计金额：￥<b style='color:red'>{{transacteText}}</b>元</span> </div>
        <div>
        
        <el-button class="left"
                     size="mini"
                     @click="exportTaxfn"
                     :loading="loading"
                     style="float: right">导出
        </el-button>
        <el-button class="left"
                     size="mini"
                     type="warning"
                     @click="uploadTaxCertificate"
                     style="float: right; margin-right: 10px">上传完税证明
        </el-button>
        </div>
        
        
      </div>
      <div class="list-main">
        <template>
          <el-table ref="multipleTable"
                    :data="tableData"
                    tooltip-effect="dark"
                    :height="tableHeight"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                    border>

            <el-table-column type="index"
                             label="序号"
                             width="55">
            </el-table-column>
            <el-table-column label="用户名称" prop="userName" width='200' show-overflow-tooltip></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="角色类型" prop="userTypeText" width='200' show-overflow-tooltip></el-table-column>
            <el-table-column label="收入类型" prop="revenueText" width='100' show-overflow-tooltip></el-table-column>
            <el-table-column label="身份证号" prop="idCard" width='140' show-overflow-tooltip></el-table-column>
            <el-table-column label="手机号" prop="mobile" width='120' show-overflow-tooltip></el-table-column>
            <el-table-column label="税款所属期起" prop="taxStartDate" width='180' show-overflow-tooltip></el-table-column>
            <el-table-column label="税款所属期止" prop="taxEndDate" width='180' show-overflow-tooltip></el-table-column>
            <el-table-column label="收款现金合计" prop="totalPaymentAmount" width='120' show-overflow-tooltip></el-table-column>
            <el-table-column label="增值税" prop="ydzseZzs" show-overflow-tooltip></el-table-column>
            <el-table-column label="印花税" prop="ydzseYhs" width='120' show-overflow-tooltip></el-table-column>
            <el-table-column label="年累计收入" prop="yearAmount" width='120' show-overflow-tooltip></el-table-column>
            <el-table-column label="个人所得税" prop="bqybtse" width='120' show-overflow-tooltip></el-table-column>
            <el-table-column fixed="right"
                             label="操作">
              <template slot-scope="scope">
                <el-button @click="goDetail(scope.row)"
                           type="text"
                           size="small">查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page="pageNumber"
                         :page-sizes="[10,20, 40, 60, 80,100]"
                         :page-size="pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total"
                         class="pagination"></el-pagination>
        </template>
      </div>
    </div>
    <div v-html="buzhidao">

    </div>
  </div>
</template>
<script>
const list = '/admin-center-server/taxFrozen/queryTaxOrderItemSumPageList'//列表
const exportTax = '/order-center-server/exportTaxOrderItemSum'//导出
const queryTaxLines = '/admin-center-server/taxFrozen/queryTaxLines'//根据月份获取税额
export default {
  name: "CarsList",
  data () {
    return {
      canUpLoad: false,
      loading: false,
      loadLoading: false,
      tableHeight: null, //表格的高度
      nowData: '',
      buzhidao: '',
      transacteText: '',
      transactionDate: [
        {
          name: '1月',
          id: '01'
        },
        {
          name: '2月',
          id: '02'
        },
        {
          name: '3月',
          id: '03'
        },
        {
          name: '4月',
          id: '04'
        },
        {
          name: '5月',
          id: '05'
        },
        {
          name: '6月',
          id: '06'
        },
        {
          name: '7月',
          id: '07'
        },
        {
          name: '8月',
          id: '08'
        },
        {
          name: '9月',
          id: '09'
        },
        {
          name: '10月',
          id: '10'
        },
        {
          name: '11月',
          id: '11'
        },
        {
          name: '12月',
          id: '12'
        }

      ],
      statusWrap1: [
        {
          name: '商务辅助',
          id: '2'
        },
        {
          name: '运费收益',
          id: '1'
        },
      ],
      statusWrap: [],
      roleType: [
        // {
        //   name: '调度员',
        //   id: '2'
        // },
        {
          name: '司机',
          id: '1'
        },
        {
          name: '司机(个人车队长)',
          id: '2'
        },
        {
          name: '个人车队长',
          id: '3'
        },
        {
          name: '企业车队长',
          id: '4'
        }
      ],
      pageSize: 10,
      total: 0,
      pageNumber: 1,
      year: '',
      //目前查询时候还差个id查询
      formInline: {
        name: "",
        tel: "",
        ID: '',
        dataSources: '',
        date: '',
        authenticateStatus: '',
        roleType: '',//角色类型
        transaction: [],
        baseId: '1'
      },
      tableData: []
    };
  },
  methods: {
    //重置
    resetForm () {
      this.$refs['formInline'].resetFields();
      this.$refs['formInline2'].resetFields()
      this.pageNumber = 1
      this.getData()
    },
    queryTaxLinesfn () {
      var month = this.yearHTML();
      var data = {
        month: month
      }
      this.$http.post(queryTaxLines, data).then(res => {
        this.statusWrap = res.data.data
      })
    },
    xxnfn (selVal) {
      var arr = this.transactionDate
      if (selVal.length == 3) {
        for (var i = 0; i < arr.length; i++) {
          if (arr[i].id == selVal[0] || arr[i].id == selVal[1] || arr[i].id == selVal[2]) {
            arr[i].disabled = false
          } else {
            arr[i].disabled = true
          }
        }
      } else if (selVal.length == 0) {
        //获取当前月份
        var month = new Date().getMonth() + 1
        if (month < 10) {
          month = '0' + month
        }

        this.formInline.transaction.push(String(month))
      } else {
        for (var i = 0; i < arr.length; i++) {
          arr[i].disabled = false
        }
      }
      this.queryTaxLinesfn()
    },
    onSubmit () {
      this.pageNumber = 1
      this.getData()
    },
    editfn (row) {
      this.$router.push("/consignorAccount/addShipper?urlType=1&id=" + row.id);
    },
    yearHTML () {
      var v = this.formInline;
      var yearQuarters = '',
        str = '';
      if (v.date) {
        yearQuarters = this.getDate3(v.date)
      } else {
        yearQuarters = new Date().getFullYear()
      }


      if (v.transaction.length == 1) {
        str += yearQuarters + v.transaction[0]
        str = str.split(',')
      } else if (v.transaction.length > 1) {
        for (var i = 0; i < v.transaction.length; i++) {
          if (i == v.transaction.length - 1) {
            str += yearQuarters + v.transaction[i]
          } else {
            str += yearQuarters + v.transaction[i] + ','
          }
        }
        str = str.split(',')
      } else {
        str = []
      }

      return str
    },
    goDetail (row) {
      var yearQuarters = this.yearHTML();
      var year = new Date().getFullYear();
      var v = this.formInline;

      //2：调度员 3：司机 4：车主
      this.$router.push(`/paying/Record_payment?totalPaymentAmount=${row.totalPaymentAmount}&type=${row.userType}&baseId=${this.formInline.baseId}&revenueType=${row.revenueType}&id=${row.userId}&month=${JSON.stringify(yearQuarters)}&year=${v.date ? this.getDate3(v.date) : year}&moneyType=${v.authenticateStatus ? v.authenticateStatus : 0}`);
    },
    addnewfn () {
      this.$router.push("/consignorAccount/addShipper?urlType=1");
    },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },

    handleSizeChange (val) {
      this.pageNumber = 1
      this.pageSize = val
      this.getData()
    },
    handleCurrentChange (val) {
      this.pageNumber = val
      this.getData()
    },
    getData () {
      var v = this.formInline;
      var yearQuarters = this.yearHTML()
      var year = new Date().getFullYear()
      var data = {
        "idCard": v.ID,
        "mobile": v.tel,
        "moneyType": v.authenticateStatus ? v.authenticateStatus : 0,
        "pageNumber": this.pageNumber,
        "pageSize": this.pageSize,
        "revenueType": v.dataSources ? v.dataSources : 0,
        "userName": v.name,
        'userType': v.roleType,
        'year': v.date ? this.getDate3(v.date) : year,
        'month': yearQuarters,
        'baseId': v.baseId
      }
      this.$http.post(list, data).then(res => {

        var data = res.data.data.resultList
        this.transacteText = res.data.data.paymentAmount
        if (data.list.length > 0) {
          for (var i = 0; i < data.list.length; i++) {
            let text = ''
            switch (data.list[i].userType) {
              case '1':
                text = '司机'
                break
              case '2':
                text = '司机(个人车队长)'
                break
              case '3':
                text = '个人车队长'
                break
              case '4':
                text = '企业车队长'
                break
            }
            data.list[i].userTypeText = text
            // if (data.list[i].userType == 2) {
            //   data.list[i].userTypeText = '调度员'
            // } else if (data.list[i].userType == 3) {
            //   data.list[i].userTypeText = '司机'
            // } else {
            //   data.list[i].userTypeText = '车队长'
            // }
            if (data.list[i].revenueType == 2) {
              data.list[i].revenueText = '商务辅助'
            } else if (data.list[i].revenueType == 1) {
              data.list[i].revenueText = '运费收益'
            } else {
              data.list[i].revenueText = ''
            }
          }


        }
        this.tableData = data.list
        this.total = Number(data.total)

      })
    },
    /**  导出  **/
    exportTaxfn () {
      let v = this.formInline;
      let yearQuarters = this.yearHTML();

      if (!this.formInline.dataSources) {
        this.$message('请选择收入类型');
      } else if (!this.formInline.authenticateStatus) {
        this.$message('请选择收入金额');
      } else if (this.tableData.length == 0) {
        this.$message('暂无数据，不能导出');
      } else if (yearQuarters.length == 0) {
        this.$message.warning('月份不能为空')
      } else {
        this.loading = true;
        let sendData={
          businessKey:0,
          idCard:this.formInline.ID,
          mobile:this.formInline.tel,
          moneyType:this.formInline.authenticateStatus,
          month:yearQuarters.toString(),
          revenueType:this.formInline.dataSources,
          userName:this.formInline.name,
          userType:this.formInline.roleType,
          year:this.formInline.date,
          baseId: this.formInline.baseId
        };
        this.$http.post('/statistics-center-server/tax/taxOrderItemSumExport',sendData).then(res => {
          let data = res.data;
          if (data.code === '200') {
            this.loading = false;
            this.$message.success('正在生成，请稍后下载')
          } else {
            this.$message.warning(data.message);
            this.loading = false;
          }
        });
      }

    },
    uploadTaxCertificate() {
      this.$router.push(`/paying/tax_certificate`);
    },
    /** 下载 **/
    uploadFn () {
      this.loadLoading = true;
      this.$http.get('/statistics-center-server/tax/downTaxOrderItemExport',{
        params:{
          businessKey:0
        }
      }).then(res => {
        let data = res.data;
        if (data.code === 200) {
          if (data.data==='') {
            this.$message.warning('暂无导出任务，请先导出');
            this.loadLoading = false;
          } else {
            let url = data.data;
            window.location.href = url;
            this.loadLoading = false;
          }
        } else {
          this.$message.warning(data.message);
          this.loadLoading = false;
        }
      });
    },


    /** 页面进入  判断是否存在下载任务 **/
    enterCheck () {
      this.$http.get('/statistics-center-server/tax/downTaxOrderItemExport',{
        params:{
          businessKey:0
        }
      }).then(res => {
        let data = res.data;
        if(data.data===''){
          this.canUpLoad = true;
        }else {
          this.canUpLoad = false;
        }
      });
    },
  },
  created () {
    //获取当前月份
    var month = new Date().getMonth() + 1;
    if (month < 10) {
      month = '0' + month
    }
    this.formInline.transaction.push(String(month))
  },
  activated () {
    this.enterCheck();
    this.tableHeight =
      window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 160;

    // var arr = this.transactionDate
    // for (var i = 0; i < arr.length; i++) {
    //   if (arr[i].id == this.formInline.transaction[0]) {
    //     arr[i].disabled = true
    //   } else {
    //     arr[i].disabled = false
    //   }
    // }
    this.getData()
    this.queryTaxLinesfn()
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .xxn {
    width: 210px;
  }

  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      padding-right: 10px;
      border-bottom: 1px solid #cccccc;
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }

    .releaseMessage {
      margin-right: 20px;
    }

    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
