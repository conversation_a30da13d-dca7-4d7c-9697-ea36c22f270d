<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">筛选查询</div>
      <div class="select-info">
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="150px">
          <el-form-item label="单位名称:">
            <el-input v-model.trim="formInline.businessName" placeholder="单位名称" maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="地址名称:">
            <el-input v-model.trim="formInline.addressName" placeholder="地址名称" maxlength="50"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>账户列表</div>
        <div>
          <el-button type="primary" @click="addAccount">添加</el-button>
        </div>
      </div>
      <div class="list-main">
        <template>
          <el-table :data="tableData" border style="width: 100%" :height="tableHeight" ref="table">
            <el-table-column type="index" label="序号" width="50"></el-table-column>
            <el-table-column prop="businessName" label="单位名称"></el-table-column>
            <el-table-column prop="addressName" label="地址名称"></el-table-column>
            <el-table-column prop="createdDate" label="创建日期"></el-table-column>
            <el-table-column prop="lastModifiedDate" label="最后修改日期"></el-table-column>
            <el-table-column prop="operator" label="操作人姓名"></el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
              <template slot-scope="scope">
                <el-button @click="editInfo(scope.row)" type="text" size="small">编辑</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="page">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInline.pageNumber"
              :page-sizes="[20, 40, 60, 80,100]"
              :page-size="formInline.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              style="margin: 10px auto"
            ></el-pagination>
          </div>
        </template>
        <!-- 添加的弹窗-->
        <el-dialog title="添加地址" :visible.sync="addDlalog" class="addTitle">
          <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            class="demo-ruleForm"
            label-width="160px"
          >
            <el-form-item label="单位名称" prop="businessName">
              <el-input v-model="ruleForm.businessName" maxlength="50"></el-input>
            </el-form-item>

            <el-form-item label="地址名称" prop="addressName">
              <el-input v-model="ruleForm.addressName" maxlength="50"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="addDlalog = false">取 消</el-button>
            <el-button type="primary" @click="addPreservation">保存</el-button>
          </span>
        </el-dialog>
        <!-- 编辑的弹窗-->
        <el-dialog :title="addTitle" :visible.sync="editDlalog" class="addTitle">
          <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            class="demo-ruleForm"
            label-width="160px"
          >
            <el-form-item label="单位名称" prop="businessName">
              <el-input v-model="ruleForm.businessName"></el-input>
            </el-form-item>

            <el-form-item label="地址名称" prop="addressName">
              <el-input v-model="ruleForm.addressName" :disabled="true"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="editDlalog = false">取 消</el-button>
            <el-button type="primary" @click="preservation">保存</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CarsList",
  data() {
    return {

      editDlalog: false, //编辑的弹窗
      addDlalog: false, //添加的弹窗
      addTitle: "编辑地址", //弹窗的title
      formInline: {
        businessName: "", //单位名称
        addressName: "" //地址名称
      },
      id: "", //主键ID 编辑时的传参'
      ruleForm: {
        businessName: "", //添加和编辑中的 '单位名称'
        addressName: "" // 添加和编辑中的 '地址名称'
      },
      rules: {
        businessName: [
          { required: true, message: "请输入单位名称", trigger: "blur" },
          { min: 3, max: 50, message: "请输入单位名称", trigger: "blur" }
        ],
        addressName: [
          { required: true, message: "请输入地址名称", trigger: "blur" }
        ],
        date1: [
          {
            type: "date",
            required: true,
            message: "请选择日期",
            trigger: "change"
          }
        ],
        date2: [
          {
            type: "date",
            required: true,
            message: "请选择时间",
            trigger: "change"
          }
        ],
        type: [
          {
            type: "array",
            required: true,
            message: "请至少选择一个活动性质",
            trigger: "change"
          }
        ],
        resource: [
          { required: true, message: "请选择活动资源", trigger: "change" }
        ],
        desc: [{ required: true, message: "请填写活动形式", trigger: "blur" }]
      },
      tableHeight: null, //表格的高度
      tableData: [],
      total: null //总数
    };
  },
  activated() {
    this.getData();
    this.tableHeight =
      window.innerHeight - this.$refs.table.$el.offsetTop - 160;
  },
  methods: {
    onSubmit() {
      this.formInline.pageNumber =1;
      this.getData();
    },

    /* 选取每页多少条数据 */
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.formInline.pageNumber =1;
      this.formInline.pageSize = JSON.parse(`${val}`);
      console.log(this.formInline.pageSize);
      this.getData();
    },
    /* 翻页 */
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.formInline.pageNumber = JSON.parse(`${val}`);
      // console.log(this.formInline.pageNumber);
      this.getData();
    },
    /** 获取数据列表 **/
    getData() {
      let postData = this.formInline;
      this.$http
        .get("/admin-center-server/admin/addresses/list", {
          params: postData
        })
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.tableData = data.data.list;
            console.log(data.data.total);
            this.total = Number(data.data.total);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 编辑信息 */

    goAttestation(row) {
      this.$router.push("/carsList/carInfo");
    },
    /* 添加账户 */
    addAccount(row) {
      this.addDlalog = true;
      this.ruleForm ={}
    },

    /* 添加保存 */
    addPreservation(){
      var postData={
        businessName:this.ruleForm.businessName,
        addressName:this.ruleForm.addressName,
      }
      console.log(postData,'postData');
      this.$http
        .post(
          "/admin-center-server/admin/addresses/add",
          this.$qs.stringify(postData)
        )
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            //成功之后刷新列表
            this.addDlalog = false;
            this.getData();
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /*编辑 --- 保存 */
    preservation(row) {
      this.editDlalog = true;
      var postData = {
        id: this.id,
        businessName: this.ruleForm.businessName
      };
      this.$http
        .post(
          "/admin-center-server/admin/addresses/update",
          this.$qs.stringify(postData)
        )
        .then(res => {
          let data = res.data;

          if (data.code === "200") {
            //成功之后刷新列表
            this.editDlalog = false;
            this.getData();
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /*编辑  */
    editInfo(row) {
      // console.log(row);
      // console.log(row.businessName, "单位名称");
      // console.log(row.addressName, "地址名称");
      this.editDlalog = true;
      this.ruleForm.businessName = row.businessName; //单位名称
      this.ruleForm.addressName = row.addressName; //地址名称
      this.id = row.id;
      //编辑（修改的接口）
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }
    .page {
      text-align: right;
    }
    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }
  }
}
</style>
