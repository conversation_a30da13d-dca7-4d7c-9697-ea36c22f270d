<template>
    <div class="app-container receiptDeatil">
        <div class="title">
            <div>
                代收运费信息
            </div>
        </div>
        <div class="main-box" style="overflow: hidden">
            <div class="base-info">
                <div class="title-box">
                    <div>代收信息</div>
                </div>
                <div class="list-box">
                    <div class="item-title">
                        <div>代收人</div>
                        <div>代收人用户类型</div>
                        <div>代收人手机号</div>
                        <div>代收运费金额</div>
                        <div>
<!--                            入账时间-->
                        </div>
                    </div>
                    <div class="item-info">
                        <div>{{agentName}}</div>
                        <div>{{agentTypeName}}</div>
                        <div>{{agentPhone}}</div>
                        <div style="color: red">{{agentAmount}}</div>
                        <div></div>
                    </div>
                </div>
                <div class="list-box" style="border-top: none">
                    <div class="item-title">
                        <div>运单编号</div>
                        <div>货主</div>
                        <div>调度员</div>
                        <div>司机</div>
                        <div>车牌号</div>
                    </div>
                    <div class="item-info">
                        <div>{{sn}}</div>
                        <div>{{businessName}}</div>
                        <div>{{brokerName}}</div>
                        <div>{{driverName}}</div>
                        <div>{{carNumber}}</div>
                    </div>
                </div>
                <div class="list-box" style="border-top: none">
                    <div class="item-title">
                        <div>订单编号</div>
                        <div>调度员单号</div>
                        <div>运费单价</div>
                        <div></div>
                        <div></div>
                    </div>
                    <div class="item-info">
                        <div>{{orderBusinessSn}}</div>
                        <div>{{orderBrokerSn}}</div>
                        <div>{{freight}}</div>
                        <div></div>
                        <div></div>
                    </div>
                </div>
            </div>
            <div class="drive-info">
                <div class="title-box">
                    <div>收发信息</div>
                </div>
                <div class="list-box">
                    <el-table :data="tableData" border style="width: 100%">
                        <el-table-column prop="deliveryPlace" label="装车点" width="550"></el-table-column>
                        <el-table-column prop="consignerName" label="发货联系人" width="180"></el-table-column>
                        <el-table-column prop="consignerPhone" label="发货人电话"></el-table-column>
                        <el-table-column prop="loadDateBegin" label="装车开始时间"></el-table-column>
                    </el-table>

                    <el-table :data="tableData" border style="width: 100%">
                        <el-table-column prop="receivePlace" label="卸货点" width="550"></el-table-column>
                        <el-table-column prop="consigneeName" label="收货联系人" width="180"></el-table-column>
                        <el-table-column prop="consigneePhone" label="收货人电话"></el-table-column>
                        <el-table-column prop="loadDateEnd" label="装货截止时间"></el-table-column>
                    </el-table>
                </div>
            </div>
            <div class="drive-info">
                <div class="title-box">
                    <div>承运信息</div>
                </div>
                <div class="list-box">
                    <el-table :data="tableData" border style="width: 100%">
                        <el-table-column prop="freightTonTypeEnum" label="货物类型" width="180"></el-table-column>
                        <el-table-column prop="ton" label="抢单吨数（吨）" width="180"></el-table-column>
                        <el-table-column prop="originalTon" label="装车吨数（吨）" width="180"></el-table-column>
                        <el-table-column prop="currentTon" label="卸货吨数（吨）"></el-table-column>
                        <el-table-column prop="currentTon" label="实收吨数（吨）"></el-table-column>
                        <el-table-column prop="serviceCharge" label="信息费（元）"></el-table-column>
                    </el-table>
                </div>
            </div>
            <div class="imageInfo">
                <div class="title-box">
                    <div>磅单信息</div>
                </div>
                <div class="img-box">
                    <div class="item-title">
                        <div></div>
                        <div>装货磅单</div>
                        <div>卸货磅单</div>
                    </div>
                    <div class="item-info">
                        <div>磅单图片</div>
                        <div @click="ZHimg"><img :src="originalTonImageUrl" alt=""></div>
                        <div @click="XHimg"><img :src="dischargeCargoImageUrl" alt=""></div>
                    </div>
                </div>
            </div>
        </div>
        <el-image-viewer v-if="showViewer"
                         :on-close="closeViewer"
                         :url-list="[originalTonImageUrl]"/>
        <el-image-viewer v-if="showViewer1"
                         :on-close="closeViewer"
                         :url-list="[dischargeCargoImageUrl]"/>
    </div>
</template>
<script>
    import ElImageViewer from 'cf-element-ui/packages/image/src/image-viewer'
    export default {
        components: {ElImageViewer},
        data() {
            return {
                showViewer: false,
                showViewer1: false,
                agentName:'',
                agentTypeName:'',
                agentPhone:'',
                agentAmount:'',
                sn:'',
                driverName:'',
                carNumber:'',
                orderBusinessSn:'',
                orderBrokerSn:'',
                freight:'',
                brokerName:'',
                businessName:'',
                originalTonImageUrl:'',
                dischargeCargoImageUrl:'',
                tableData:[]
            }
        },
        methods: {
            ZHimg(){
                this.showViewer = true;
            },
            XHimg(){
                this.showViewer1 = true;
            },
            // 关闭查看器
            closeViewer() {
                this.showViewer = false;
                this.showViewer1 = false;
            },
            getDate() {
                let orderItemId = this.$route.query.id;
                this.$http.get("/admin-center-server/orderItem/getOrderItemDetail", {
                    params: {
                        OrderItemId: orderItemId
                    }
                }).then(res => {
                    let data = res.data;
                    if(data.code==='200'){
                        this.tableData = data.data.orderItemByOrderBrokerDetail;
                        let useData = data.data.orderItemByOrderBrokerDetail[0];
                        this.freight = useData.freight;
                        this.originalTonImageUrl = useData.originalTonImageUrl;
                        this.dischargeCargoImageUrl = useData.dischargeCargoImageUrl;
                    }
                });
                this.$http.get('/admin-center-server/orderItem/agentItemInfo', {
                    params: {
                        id: orderItemId
                    }
                }).then(res => {
                    let data = res.data;
                    if(data.code==='200'){
                        let useData = data.data;
                        this.agentName = useData.agentName;
                        this.agentTypeName = useData.agentTypeName;
                        this.agentPhone = useData.agentPhone;
                        this.agentAmount = useData.agentAmount;
                        this.sn = useData.sn;
                        this.driverName = useData.driverName;
                        this.carNumber = useData.carNumber;
                        this.orderBusinessSn = useData.orderBusinessSn;
                        this.orderBrokerSn = useData.orderBrokerSn;
                        this.brokerName = useData.brokerName;
                        this.businessName = useData.businessName;
                    }
                })
            }
        },
        activated() {
            this.getDate()
        }
    }
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .receiptDeatil {
        .title {
            background-color: #ffffff;
            height: 40px;
            border: 1px solid #cccccc;
            line-height: 40px;

            div {
                width: 160px;
                height: 40px;
                line-height: 40px;
                text-align: center;
                font-weight: 600;
                font-size: 16px;
                /*color: rgb(0,121,254);*/
            }
        }
        .main-box {
            background-color: white;
            margin-top: 20px;

            .title-box {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
            }
            .list-box {
                border: 1px solid #cccccc;
                .item-title {
                    display: flex;
                    flex-direction: row;
                    div {
                        width: 500px;
                        height: 50px;
                        font-weight: bold;
                        font-size: 16px;
                        line-height: 50px;
                        border: 1px solid #cccccc;
                        border-top: none;
                        border-right: none;
                        background-color: rgb(249, 252, 250);
                        text-align: center;
                    }
                }
                .item-info {
                    display: flex;
                    flex-direction: row;
                    div {
                        font-size: 14px;
                        width: 500px;
                        height: 50px;
                        line-height: 50px;
                        text-align: center;
                        border: 1px solid #cccccc;
                        border-top: none;
                        border-right: none;
                        border-bottom: none;
                    }
                }
            }
            .base-info,
            .drive-info,
            .other-info {
                padding: 20px;
            }
            .imageInfo{
                padding: 20px;
                .img-box{
                    border: 1px solid #cccccc;
                    .item-title {
                        display: flex;
                        flex-direction: row;
                        div {
                            width: 500px;
                            height: 50px;
                            font-weight: bold;
                            font-size: 16px;
                            line-height: 50px;
                            border: 1px solid #cccccc;
                            border-top: none;
                            border-right: none;
                            background-color: rgb(249, 252, 250);
                            text-align: center;
                        }
                    }
                    .item-info {
                        display: flex;
                        flex-direction: row;
                        div {
                            font-size: 14px;
                            width: 500px;
                            height: 300px;
                            line-height: 300px;
                            text-align: center;
                            border: 1px solid #cccccc;
                            border-top: none;
                            border-right: none;
                            border-bottom: none;
                            padding: 5px;
                            img{
                                display: block;
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }
                }
            }
        }

    }
</style>
