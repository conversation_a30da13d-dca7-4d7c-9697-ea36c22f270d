<template>
    <div class="app-container addSystemUser">
        <div class="tip">
            <div>新增管理员</div>
            <div>
                <em style="color: red">*</em>为必填项
            </div>
        </div>
        <div class="inner-box">
            <el-form
                    :model="ruleForm"
                    label-width="200px"
                    class="demo-ruleForm"
            >
                <el-form-item label="系统用户名称:" required>
                    <el-input v-model="ruleForm.realName" style="width: 220px" placeholder="请输入系统用户名称"></el-input>
                </el-form-item>
                <el-form-item label="系统用户账号:" required>
                    <el-input v-model="ruleForm.nickName" style="width: 220px" placeholder="请输入系统用户账号"
                              readonly onfocus="this.removeAttribute('readonly');" autocomplete="off"
                              :οnkeyup="ruleForm.nickName=ruleForm.nickName.replace(/[^\w\.\/]/ig,'')"
                    ></el-input>
                </el-form-item>
                <el-form-item label="密码:" required>
                    <el-input v-model="ruleForm.pwd" style="width: 220px"
                              readonly onfocus="this.removeAttribute('readonly');" autocomplete="off"
                              maxlength="24"
                              minlength="8"
                              :οnkeyup="ruleForm.pwd=ruleForm.pwd.replace(/[^\w\.\/]/ig,'')"
                              placeholder="请输入密码"></el-input>
                    <span style="margin-left: 10px;color: red">字母(首字母大写)+数字8-24位</span>
                </el-form-item>
                <el-form-item label="确认密码:" required>
                    <el-input v-model="ruleForm.checkpwd" style="width: 220px"
                              readonly onfocus="this.removeAttribute('readonly');" autocomplete="off"
                              minlength="8"
                              maxlength="24"
                              :οnkeyup="ruleForm.checkpwd=ruleForm.checkpwd.replace(/[^\w\.\/]/ig,'')"
                              placeholder="请确认密码"></el-input>
                    <span style="margin-left: 10px;color: red">请重复密码</span>
                </el-form-item>
                <el-form-item label="系统用户职位:" required>
                    <el-input v-model="ruleForm.postName" style="width: 220px" placeholder="请输入系统用户职位"></el-input>
                </el-form-item>
                <el-form-item label="性别:" required>
                    <el-select v-model="ruleForm.gender" placeholder="请选择性别">
                        <el-option label="男" value="0"></el-option>
                        <el-option label="女" value="1"></el-option>
                        <el-option label="保密" value="2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="系统用户手机:" required>
                    <el-input v-model="ruleForm.mobile" style="width: 220px" placeholder="请输入系统用户手机" maxlength="11"
                              :οnkeyup="ruleForm.mobile=ruleForm.mobile.replace(/[^\d\.]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="备注:">
                    <el-input v-model="ruleForm.remarks" style="width: 220px" placeholder="请输入备注"></el-input>
                </el-form-item>
                <el-form-item label="角色名称:" required>
                    <div class="radioBox">
                        <el-checkbox-group v-model="ruleForm.user">
                            <el-checkbox v-for="query in queryAll" :label="query.id">{{query.name}}</el-checkbox>
                        </el-checkbox-group>
                    </div>
                </el-form-item>
                <el-form-item label="启用状态:" prop="resource" required>
                    <el-radio-group v-model="ruleForm.resource">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item>
                    <el-button :loading="loading" type="primary" @click="submitForm" style="width: 120px">保存</el-button>
                    <el-button type="primary" @click="gopPrev" style="width: 120px">返回</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                loading: false,
                queryAll: [],
                ruleForm: {
                    realName: "",
                    nickName: "",
                    pwd: "",
                    postName: "",
                    gender: "",
                    mobile: "",
                    remarks: "",
                    user: [],
                    resource: "",
                    checkpwd: "",
                },
                pid: ""
            };
        },
        methods: {
            submitForm() {
                //   this.$message.warning("填写完整");
                let form = {
                    departmentId: this.pid, //主键ID
                    realName: this.ruleForm.realName, //系统用户名称
                    nickName: this.ruleForm.nickName, //系统用户账号
                    pwd: this.ruleForm.pwd, //密码
                    postName: this.ruleForm.postName, //系统用户职位
                    gender: this.ruleForm.gender, //性别
                    mobile: this.ruleForm.mobile, //系统用户手机
                    roleIds: this.ruleForm.user, //角色名称
                    remarks: this.ruleForm.remarks, //备注
                    isEnabled: this.ruleForm.resource //启用状态
                };
                let checkpwd = this.ruleForm.checkpwd;
                if (form.pwd !== checkpwd) {
                    this.$message.warning('两次输入密码不一致')
                } else if (form.departmentId === '' || form.realName === '' || form.nickName === '' || form.pwd === '' || form.postName === '' || form.gender === '' || form.mobile === '' || form.roleIds === '' || form.isEnabled === '') {
                    this.$message.warning('请填写完整')
                } else if(form.pwd.length<8){
                    this.$message.warning('密码长度最小8位')
                }else
                    if(!(/^[A-Z][A-z0-9]*$/).test(form.pwd)){
                    this.$message.warning('密码首字母必须大写')
                }else {
                    this.loading = true;
                    this.$http
                        .post("/admin-center-server/sys/user/addSysUser", form)
                        .then(res => {
                            let data = res.data;
                            if (data.code === "200") {
                                this.$message.success("添加成功");
                                setTimeout(() => {
                                    this.$router.push("systemUser");
                                    this.loading = false;
                                    sessionStorage.removeItem("curPid");
                                }, 1000);
                            } else {
                                this.loading = false;
                                this.$message.warning(data.message)
                            }
                        });
                }
            },
            gopPrev() {
                this.$router.push('systemUser')
                sessionStorage.removeItem('curPid');
            },
            getPid() {
                let pid = sessionStorage.getItem("curPid");
                this.pid = pid;
            },
            getQueryAll() {
                this.$http.post("/admin-center-server/sys/role/queryAll").then(res => {
                    let data = res.data;
                    if (data.code === "200") {
                        this.queryAll = data.data;
                    }
                });
            }
        },
        activated() {
            this.getPid();
            this.getQueryAll();
        }
    };
</script>
<style>
    .radioBox {
        width: 410px;
        flex-wrap: wrap;
    }

    .radioBox .el-checkbox {
        width: 150px;
    }
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
    .addSystemUser {
        .tip {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            padding: 0 40px 40px 0;
            font-size: 12px;

            em {
                margin-right: 5px;
            }
        }

        .inner-box {
            margin-left: 10%;
            width: 70%;

            .upload-box {
                width: 100%;
                height: 100%;
                position: relative;

                .icon-XZ {
                    width: 92px;
                    height: 92px;
                    margin: 0 auto;
                    background-size: 100% 100%;
                }

                .icon-word {
                    width: 100%;
                    height: 20px;
                    line-height: 20px;
                    font-size: 10px;
                    position: absolute;
                    bottom: 25px;
                    left: 0px;
                    color: #cccccc;
                }
            }
        }
    }
</style>
