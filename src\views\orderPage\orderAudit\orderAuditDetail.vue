<template>
  <div class="app-container orderAuditDetail">
    <div class="error-display">
      <el-collapse v-model="activeNames">
        <el-collapse-item name="1">
          <template slot="title">
            <div class="error-collapse">投保异常信息</div>
          </template>
          <div v-if="errorData.length === 0">未检查出异常</div>
          <div v-else>
              <div v-for="(item,index) in errorData" :key="index">
                <div class="error-title"><i class="el-icon-warning warn-icon"></i>{{item.hitMsg}}</div>
                <div class="error-detail">
                  {{item.hitDetailMsg }}
                  <!-- <span v-for="errorDetail of info.hitDetailMsg :key="errorDetail" class="error-item">
                    &nbsp;<span v-html="errorDetail"></span>
                  </span> -->
                </div>
              </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <template>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick" class="tab-top-all">
        <el-tab-pane label="订单详情" name="first">
          <OrderDetail :tableData="tableData" :ruleData="ruleData"></OrderDetail>
        </el-tab-pane>
        <el-tab-pane label="所属调度员单" name="second" v-if="type == 1">
          <div class="main-box">
            <div class="base-info">
              <div class="title-box">
                <div>当前订单状态：{{statusEnum}}</div>
              </div>
              <div class="list-main">
                <template>
                  <el-table :data="agentTableData" border style="width: 100%">
                    <el-table-column prop="sn" label="调度员单号" width="100"></el-table-column>
                    <el-table-column prop="businessName" label="客户名称"></el-table-column>
                    <el-table-column prop="cargoType" label="货物类型"></el-table-column>
                    <el-table-column prop="brokerName" label="调度员"></el-table-column>
                    <el-table-column prop="totalTon" label="货物总量" :formatter="unitTableFormatter"></el-table-column>
                    <el-table-column prop="leftTon" label="剩余货物" :formatter="unitTableFormatter"></el-table-column>
                    <el-table-column prop="pendingShippingTons" label="待装货" :formatter="unitTableFormatter"></el-table-column>
                    <el-table-column prop="shippingTons" label="运输中" :formatter="unitTableFormatter"></el-table-column>
                    <el-table-column prop="receivedTons" label="已收货" :formatter="unitTableFormatter"></el-table-column>
                    <el-table-column prop="statusEnum" label="订单状态"></el-table-column>
                    <el-table-column prop="createdDate" label="创建日期"></el-table-column>
                  </el-table>
                </template>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="所属运单" name="third">
          <div class="main-box">
            <div class="base-info">
              <div class="title-box flex">
                <div>当前订单状态：{{statusEnum}}</div>
              </div>
              <div class="list-main">
                <template>
                  <el-table :data="waybillTableData" border style="width: 100%">
                    <el-table-column prop="sn" label="运单单号" width="200">
                      <template slot-scope="scope">
                        <el-button type="text" @click="$router.push('/transport/transportListDetail?orderItemId=' + scope.row.orderItemId)">{{ scope.row.sn }}</el-button>
                      </template>
                    </el-table-column>
                    <el-table-column prop="amount" label="司机运费" :formatter="formatMoney"></el-table-column>
                    <el-table-column prop="driverName" label="接单司机"></el-table-column>
                    <el-table-column prop="carNumber" label="接单车辆"></el-table-column>
                    <el-table-column prop="ton" label="抢单吨数" :formatter="formatTon"></el-table-column>
                    <el-table-column prop="originalTon" label="装货吨数" :formatter="formatTon"></el-table-column>
                    <el-table-column prop="currentTon" label="卸货吨数" :formatter="formatTon"></el-table-column>
                  </el-table>
                </template>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="订单日志" name="fourth">
          <div class="main-box">
            <div class="base-info">
              <div class="title-box">
                <div>当前订单状态：{{statusEnum}}</div>
              </div>
              <div class="list-main">
                <template>
                  <el-table :data="logTableData" border style="width: 100%">
                    <el-table-column type="index" label="编号" width="50"></el-table-column>
                    <el-table-column prop="operator" label="操作者"></el-table-column>
                    <el-table-column prop="createdDate" label="操作时间"></el-table-column>
                    <el-table-column prop="statusEnum" label="订单状态"></el-table-column>
                    <el-table-column prop="content" label="备注"></el-table-column>
                  </el-table>
                </template>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div class="audit-detail" v-if="tableData[0].status == 0">
        <el-button size="small" type="" @click="orderAuditDialog=true">驳回</el-button>
        <el-button class="audit-item" size="small" type="primary" @click="orderBusinessAudit(1, '')">通过审核</el-button>
      </div>
      <div class="buttons flex">
        <el-button @click="insure" size="small" type="primary">在线投保</el-button>
        <el-button size="small" type="primary" @click="queryPlatformDesignationRecord">承运司机</el-button>
      </div>

      <!-- 承运车辆 -->
      <el-dialog
        title="承运司机"
        :visible.sync="addDriverDialog">
        <div>
          <div class="add-driver-item" v-for="(item, index) in addDrivers" :key="index">
            <el-autocomplete
              class="add-driver-input"
              v-model="item.driver.name"
              :fetch-suggestions="querySearchDriver"
              placeholder="请选择承运司机，可按姓名或手机号搜索"
              @select="handleSelectDriver($event, index)"
            ></el-autocomplete>
            <el-select
              class="add-driver-input"
              v-model="item.selectedCars"
              multiple
              placeholder="请选择承运车辆"
              >
              <el-option
                v-for="i in item.cars"
                :key="i.id"
                :label="i.label"
                :value="i.id">
              </el-option>
            </el-select>
            <div class="add-driver-delete" @click="addOneDriver(2, index)">—</div>
          </div>
          <el-button size="small" style="margin-left:20px" @click="addOneDriver(1)">+添加承运司机</el-button>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="addDriverDialog = false">取 消</el-button>
          <el-button type="primary" @click="confrimAddDriver">确 定</el-button>
        </span>
      </el-dialog>

      <!-- 订单驳回 -->
      <el-dialog
        title="请选择驳回原因"
        :visible.sync="orderAuditDialog"
        width="500px"
        >
        <el-checkbox-group v-model="rejectReason">
          <div v-for="item in reasons" :key="item.id" class="reason-item">
            <el-checkbox :label="item.reason">{{ item.reason }}</el-checkbox>
          </div>
        </el-checkbox-group>
        <div class="reason-other">其他原因</div>
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请驳回原因"
          v-model="orderAuditMemo">
        </el-input>

        <span slot="footer" class="dialog-footer">
          <el-button @click="orderAuditDialog = false">取 消</el-button>
          <el-button type="primary" @click="orderBusinessAudit(2, orderAuditMemo)">确 定</el-button>
        </span>
      </el-dialog>
      <el-dialog
        :visible.sync="isInsureOpen"
        title="在线投保"
        width="670px">
        <div class="insure-text">先录入货运险信息，司机接单后系统将自动投保</div>
        <el-form
          :model="insureForm"
          :rules="insureRules"
          ref="insureForm"
          label-width="110px">
          <el-form-item prop="type" label="险种" class="self-adaption-form-item">
            <el-radio-group v-model="insureForm.type">
              <el-radio label="0">基本险</el-radio>
              <el-radio label="1">综合险</el-radio>
              <el-radio label="2">单车货运险</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="conveyanceId" label="投保货物类型" class="self-adaption-form-item">
            <el-select v-model="insureForm.conveyanceId" class="insure-select">
              <el-option v-for="item in cargoTypeList" :key="item.conveyanceId" :value="item.conveyanceId" :label="item.message"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="投保货值" prop="goodsValue">
            <el-input v-model="insureForm.goodsValue" placeholder="请输入每车货物的货值">
              <template #append>万元/车</template>
            </el-input>
          </el-form-item>
          <el-form-item label="货值证明" prop="proofValueImg">
            <ImageUploader2
              v-for="index in certValueImages.length"
              :key="index + Date.now()"
              type="upload"
              @change="url => uploadCertValueImages(index - 1, url)"
              :defaultUrl="certValueImages[index - 1]"
              :size="[120, 120]"></ImageUploader2>
            <ImageUploader2
              v-if="certValueImages.length < 3"
              :key="999 + Date.now()"
              type="upload"
              @change="url => uploadCertValueImages(certValueImages.length, url)"
              :size="[120, 120]"></ImageUploader2>
          </el-form-item>
          <el-form-item prop="fqzz" label="放弃对实际承运人的代位追偿权" class="self-adaption-form-item">
            <el-radio-group v-model="insureForm.fqzz">
              <el-radio label="1">放弃</el-radio>
              <el-radio label="0">不放弃</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="isInsureOpen = false">取消</el-button>
          <el-button @click="insureSubmit" type="primary">确定</el-button>
        </template>
      </el-dialog>
    </template>
  </div>
</template>
<script>
import DetailRow from '../../../components/OrderDetail/DetailRow.vue';
import { getDecimalsLenth } from '@/utils/tools'
export default {
  components: { DetailRow },
  name: "orderAuditDetail",
  data() {
    return {
      businessId: "",
      activeName: "first",
      freezeStatus: null, //判断冻结 活着解冻的状态
      statusEnum: "", //当前订单状态
      creatTime: "", //当前订单创建时间
      baseInfo: {},
      statusBd: "", // 订单详情下标识时货主还是调度员1是货主 2是调度员
      formLabelWidth: "120px",
      tableData: [{}],
      agentTableData: [], // 调度员单
      waybillTableData: [], //所属运单
      logTableData: [], //日志
      options: [], //  取消/冻结 原因
      form: {
        cancelReason: "", //取消原因的ID
        otherReason: "", //其他原因
        fronzeReason: "", //冻结原因 ID
        fronzeOterReason: "" //冻结的其他原因
      },
      type: "", //订单类型
      paymentType: "", //付款方式
      freight: "", //运费单价
      ruleData: [],
      addDriverDialog: false,
      addDrivers: [],
      orderAuditDialog: false,
      orderAuditMemo: '',
      reasons: [],
      rejectReason: [],
      isInsureOpen: false,
      insureForm: {
        type: '',
        conveyanceId: '',
        goodsValue: '',
        fqzz: ''
      },
      cargoTypeList: [],
      certValueImages: [],
      activeNames: ['1'],
      errorData: [],
      insureRules: {
        type: [
          {
            validator: (rule, value, cb) => {
              if (!value) {
                return cb('请选择')
              }
              cb()
            }
          }
        ],
        conveyanceId: [
          {
            validator: (rule, value, cb) => {
              if (!value) {
                return cb('请选择')
              }
              cb()
            }
          }
        ],
        goodsValue: [
          {
            validator: (rule, value, cb) => {
              if (!value) {
                return cb('请输入')
              }
              let num = Number(value)
              if (Number.isNaN(num)) {
                return cb('必须为数字')
              }
              if (getDecimalsLenth(num) > 2) {
                return cb('最多两位小数')
              }
              cb()
            }
          }
        ],
        proofValueImg: [
          {
            validator: (rule, value, cb) => {
              if (this.certValueImages.length === 0) {
                return cb('请上传')
              }
              cb()
            }
          }
        ],
        fqzz: [
          {
            validator: (rule, value, cb) => {
              if (!value) {
                return cb('请选择')
              }
              cb()
            }
          }
        ]
      }
    };
  },
  activated() {
    this.getOrderDetail(); //获取订单详情
    this.type = this.$route.query.type;

    this.$get('/admin-center-server/queryOrderRejectReasonList')
      .then(res => {
        this.reasons = res
      })   
  },
  methods: {
    // 获取投保异常信息
    getErrorData(detailData){
      console.log('111',detailData)
      let data ={
        "deliveryPlace": detailData.deliveryPlace,
        "goodsValue": detailData.goodsValue,
        "receivePlace": detailData.receivePlace,
      }
      this.$post('/admin-center-server/rule/engine/insuranceRule',data)
      .then(res => {
        this.errorData = res.hitResultList.filter(res=> res.isHit==='hit')
      })  
    },
    goRevise() {
      this.$router.push("/carsList/carRevise");
    },
    orderBusinessAudit(type, memo) {
      let params = {orderBusinessId: this.$route.query.orderBusinessId, reviewStatusEnum: type}
      if (type === 2) {
        params.reviewMemo = this.rejectReason.join(',')
        params.authOtherMemo = memo
      }
      this.$post('/admin-center-server/order/orderBusinessAudit', params).then(
        res => {
          this.$message.success("操作成功");
          this.orderAuditDialog = false
          this.getOrderDetail()
          setTimeout(() => {
            window.close()
          }, 1000)
        }
      )
    },
    async queryPlatformDesignationRecord() {
      this.addDrivers = []
      this.addDriverDialog = true
    },
    addOneDriver(isAdd, index) {
      if (isAdd == 1) {
        let item = {
          driver: {
            name: '',
            value: '',
            driverId: ''
          },
          id: '',
          cars: [],
          selectedCars: []
        }
        this.addDrivers.push(item)
      } else {
        this.addDrivers.splice(index, 1)
      }
      
    },
    querySearchDriver(search, cb) {
      this.$post('/admin-center-server/order/orderBusinessDriver', {driverNameOrMobile: search, orderBusinessId: this.$route.query.orderBusinessId}).then(
        res => {
          let drivers = res.map(item => {
            return {
              name: item.name + ' ' + item.mobile,
              value: item.name + ' ' + item.mobile,
              driverId: item.userId
            }
          })
          cb(drivers)
        }
      )
    },
    handleSelectDriver(item, index) {
      this.addDrivers[index].driver = item
      this.addDrivers[index].selectedCars = []

      this.$post('/admin-center-server/order/orderBusinessCar',{driverId: item.driverId, orderBusinessId: this.$route.query.orderBusinessId}).then(
        res => {
          this.addDrivers[index].cars = res.map(v => {
            return {
              id: v.carId,
              value: v.carNumber + ' ' + v.name + '' + v.capacityTonnage + 't',
              label: v.carNumber + ' ' + v.name + '' + v.capacityTonnage + 't'
            }
          })
        }
      )

    },
    confrimAddDriver() {
      if (this.addDrivers.length == 0) {
        this.$message.warning("请添加司机与车辆");
        return
      }
      
      let params = this.addDrivers.map(v => {
        return {
          driverId: v.driver.driverId,
          id: v.id,
          orderBusinessId: this.$route.query.orderBusinessId,
          carIdList: v.selectedCars
        }
      })

      this.$post('/admin-center-server/order/platformDesignation',params).then(
        res => {
          this.$message.success("操作成功");
          this.addDriverDialog = false
        }
      )
    },
    /* 获取订单详情 */
    getOrderDetail() {
      let orderBusinessId = Number(this.$route.query.orderBusinessId);
      var postData = {
        orderBusinessId: orderBusinessId
      };
      this.$http
        .get(
          "/admin-center-server/order/getOrderBusinessDetailOrder?orderBusinessId=" +
            orderBusinessId
        )
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            //订单详情
            this.tableData = data.data.orderBusinessDetail;
            //this.isBrevityName = data.data.orderBusinessDetail[0].isBrevityName; //是否短倒
            this.statusBd = data.data.orderBusinessDetail[0].userType;
            this.type = data.data.orderBusinessDetail[0].type;
            this.statusEnum = data.data.orderBusinessDetail[0].statusEnum;
            this.paymentType = data.data.orderBusinessDetail[0].paymentType; //付款方式
            this.freight = data.data.orderBusinessDetail[0].freight; //运费单价
            this.businessId = data.data.orderBusinessDetail[0].businessId;
            this.creatTime = data.data.orderBusinessDetail[0].createdDate; //订单创建日期
            //冻结解冻的状态
            this.freezeStatus = data.data.orderBusinessDetail[0].freezeStatus;
            this.getErrorData(data.data.orderBusinessDetail[0])
            this.$get(`/admin-center-server/rule/getRuleRormulatianById?id=${this.tableData[0].ruleId}&isOperated=${this.tableData[0].operationalPeoFlag}`).then(
              res => {
                this.ruleData = res
              }
            )
            }
        });
    },
    handleClick(tab, event) {
      if (tab.name == "first") {
        //订单详情
        this.getOrderDetail();
      }
      if (tab.name == "second") {
        //所属调度员单
        //所属调度员单
        this.belongAgentOrder();
      }
      if (tab.name == "third") {
        //所属运单
        let orderBusinessId = Number(this.$route.query.orderBusinessId);
        //运单详情
        this.$http
          .get(
            "/admin-center-server/order/getOrderBusinessItemDetailYd?orderBusinessId=" +
              orderBusinessId
          )
          .then(res => {
            let data = res.data;
            // console.log(res.data.data);
            if (data.code === "200") {
              this.waybillTableData = res.data.data;
            } else {
              this.$message.warning(res.data.message);
            }
          });
      }
      if (tab.name == "fourth") {
        //订单日志
        this.orderLog();
      }
    },
    /* 给金额添加单位 */
    formatMoney(row, column) {
      let money = row[column.property];
      // console.log(row, "money");
      if (money == null) {
        return "";
      } else {
        return money + " 元 ";
      }
    },
    moneyFormatter(value) {
      if (value === '' || value === null) return ''
      return String(value) + '元'
    },
    /* 给吨数加单位 */
    formatTon(row, column) {
      let ton = row[column.property];
      if (ton == null) {
        return "";
      } else {
        return ton + " 吨 ";
      }
    },
    tonFormatter(value) {
      if (value === '' || value === null) return ''
      return String(value) + '吨'
    },
    /* 单价 */
    formatPrice(row, column) {
      let price = row[column.property];
      return price + "元/吨";
    },
    priceFormatter(value) {
      if (value === '' || value === null) return ''
      return String(value) + '元/吨'
    },
    /* 所属调度员单 */
    belongAgentOrder() {
      let orderBusinessId = Number(this.$route.query.orderBusinessId);
      this.$http
        .get(
          "/admin-center-server/order/getOrderBusinessItemDetailBroker?orderBusinessId=" +
            orderBusinessId
        )
        .then(res => {
          let data = res.data;
          // console.log(res.data.data);
          if (data.code === "200") {
            this.agentTableData = res.data.data;
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },
    /* 订单日志 */
    orderLog() {
      let orderBusinessId = Number(this.$route.query.orderBusinessId);
      this.$http
        .get(
          "/admin-center-server/order/getOrderBusinessItemDetailLog?orderBusinessId=" +
            orderBusinessId
        )
        .then(res => {
          let data = rmkkes.data;
          if (data.code === "200") {
            this.logTableData = res.data.data;
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },
    insure() {
      if (this.tableData[0].cargoTypeClassificationCode !== '1700' && this.tableData[0].cargoTypeClassificationCode !== '1702') {
        this.$message.error(`货物分类为“${this.tableData[0].cargoTypeClassificationValue}”的货物暂不支持在线投保`)
        return false
      }
      // if (this.tableData[0].freightCalcType !== '1') {
      //   this.$message.error('暂不支持“按吨付费”的订单在线投保')
      //   return false
      // }
      if (this.tableData[0].orderInsuranceId) {
        this.insureForm.conveyanceId = this.tableData[0].conveyanceId
        this.insureForm.type = this.tableData[0].insuranceType
        this.insureForm.goodsValue = this.tableData[0].goodsValue
        this.insureForm.fqzz = this.tableData[0].fqzz
        this.certValueImages = this.tableData[0].proofValueImg.split(';')
      } else {
        this.insureForm.fqzz = '1' // 默认选择
      }
      this.getCargoType()
      this.isInsureOpen = true
    },
    uploadCertValueImages(index, url) {
      if (url === '') {
        this.certValueImages.splice(index, 1)
      } else {
        this.$set(this.certValueImages, index, url)
      }
    },
    insureSubmit() {
      this.$refs.insureForm.validate()
        .then(valid => {
          if (!valid) return
          this.$post('/admin-center-server/orderInsurance/add', {
            ...this.insureForm,
            proofValueImg: this.certValueImages.join(';'),
            orderCommonId: this.tableData[0].orderCommonId
          })
            .then(() => {
              this.$message.success('操作成功')
              this.isInsureOpen = false
              this.getOrderDetail()
            })
        })
    },
    getCargoType() {
      this.$post('/admin-center-server/orderInsurance/admin/queryConveyanceList')
        .then(res => {
          this.cargoTypeList = res
        })
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.buttons {
  position: absolute;
  right: 20px;
  top: 60px;
}
ul li {
  list-style: none;
}
.flex{
  display: flex;
}
.audit-detail {
  position: fixed;
  bottom: 0;
  right: 0;
  width: calc(100% - 200px);
  display: flex;
  flex-direction: row-reverse;
  background-color: white;
  padding: 20px 20px 20px 0;
  .audit-item {
    margin-right: 10px;
  }
}

.add-driver-item {
  display: flex;
  align-items: center;
  margin: 10px;
  .add-driver-input {
    margin: 10px;
    width: 35%;
  }
  .add-driver-delete {
    border: solid 1px #cccccc;
    border-radius: 5px;
    height: 35px;
    width: 35px;
    margin-left: 10px;
    text-align: center;
    line-height: 35px;
  }
}
.orderAuditDetail {
  padding-bottom: 80px;
  .main-box {
    background-color: white;
    .title-box {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .list-box {
      margin-top: 20px;
      border: 1px solid #cccccc;
      border-left: none;
      .item-title {
        display: flex;
        flex-direction: row;
        div {
          width: 500px;
          height: 50px;
          font-weight: bold;
          font-size: 16px;
          line-height: 50px;
          border: 1px solid #cccccc;
          border-top: none;
          border-right: none;
          background-color: rgb(249, 252, 250);
          text-align: center;
        }
      }
      .item-info {
        display: flex;
        flex-direction: row;
        div {
          font-size: 14px;
          width: 500px;
          height: 50px;
          line-height: 50px;
          text-align: center;
          border: 1px solid #cccccc;
          border-top: none;
          border-right: none;
          border-bottom: none;
        }
      }
    }
    .base-info,
    .drive-info,
    .other-info,
    .driver-info {
      padding: 20px;
    }
  }
}
.reason-item {
  line-height: 30px;
  ::v-deep .el-checkbox {
    white-space: normal;
  }
}
.reason-other {
  margin: 10px 0;
  color: rgb(51, 174, 240);
}
.insure-text {
  margin: 0 0 20px 10px;
  color: #DD2042;
}
.insure-select {
  width: 100%;
}

.error-display {
    position: fixed;
    top: 93px;
    left: 210px;
    right: 20px;
    z-index: 100;
    // margin: 5px;
    // padding-left: 5px;
    padding: 0 20px;
    .error-title {
      font-weight: 600;
    }
    .error-collapse {
      font-size: 16px;
      font-weight: 700;
    }
    .error-item > span ::v-deep > span  {
      color: red;
    }
    background-color: white;
  }
  .tab-top-all{
    margin-top: 40px;
  }
  .warn-icon{
    color: #f56c6c;
    margin-right: 8px;
  }
</style>
