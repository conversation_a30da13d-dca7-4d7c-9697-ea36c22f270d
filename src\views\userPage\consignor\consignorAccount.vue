<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">筛选查询</div>
      <div class="select-info">
        <el-form :inline="true"
                 :model="formInline"
                 ref="formInline"
                 class="demo-form-inline"
                 size="mini"
                 label-width="90px">
          <el-form-item label="公司名称:"
                        prop="name">
            <el-input v-model="formInline.name"
                      placeholder="请输入公司名称"></el-input>
          </el-form-item>
          <el-form-item label="手机号:"
                        prop='tel'>
            <el-input v-model="formInline.tel"
                      oninput="value=value.replace(/[^\d]/g,'')"
                      maxlength="11"
                      placeholder="请输入货主注册手机号"></el-input>
          </el-form-item>
          <el-form-item label="数据来源："
                        label-width="100px"
                        prop='dataSources'>
            <el-select v-model="formInline.dataSources"
                       placeholder="请选择"
                       clearable
                       label-width="10px">
              <el-option v-for="(item, lan) in statusWrap1"
                         :label="item.name"
                         :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="认证状态："
                        label-width="100px"
                        prop='authenticateStatus'>
            <el-select v-model="formInline.authenticateStatus"
                       placeholder="请选择认证状态"
                       clearable
                       label-width="10px">
              <el-option v-for="(item, lan) in statusWrap"
                         :label="item.name"
                         :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="注册时间："
                        label-width="100px"
                        prop='date'>
            <el-col>
              <el-date-picker v-model="formInline.date"
                              value-format=""
                              type="datetimerange"
                              range-separator="至"
                              start-placeholder="选择开始时间"
                              end-placeholder="选择结束时间"
                              :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button class="left"
                       icon="el-icon-search"
                       @click="onSubmit">查询</el-button>
            <el-button class="left"
                       @click="resetForm('formInline')"
                       icon="el-icon-refresh-right">重置</el-button>
            <el-button class="left"
                       @click="refreshfn"
                       icon="el-icon-refresh-right">刷新</el-button>

            <!-- <el-button class="left"
                       icon="el-icon-plus"
                       @click="addnewfn">添加用户</el-button> -->

          </el-form-item>
        </el-form>

      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>账户列表</div>
        <el-button type="primary" @click="addNewShipper">+新增货主</el-button>
      </div>
      <div class="list-main">
        <template>
          <el-table ref="multipleTable"
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                    :height="tableHeight"
                    cell-class-name="table_cell_gray"
                    header-cell-class-name="table_header_cell_gray"
                    border>
            <el-table-column type="selection"
                             width="55"></el-table-column>
            <el-table-column type="index"
                             label="序号"
                             width="55">
            </el-table-column>
            <el-table-column show-overflow-tooltip
                             v-for="item in tableLabel"
                             :key="item.prop"
                             :label="item.label"
                             :width='item.width?item.width:""'>
              <template slot-scope="scope">
                <span>{{ scope.row[item.prop]}}</span>
              </template>
            </el-table-column>

            <el-table-column fixed="right"
                             min-width="160"
                             label="操作">
              <template slot-scope="scope">
                <el-button @click="goDetail(scope.row)"
                           type="text"
                           size="small">查看</el-button>
                <el-button type="text"
                           @click="editfn(scope.row)"
                           size="small">编辑</el-button>
                <el-button type="text"
                           @click="resetPassword(scope.row)"
                           size="small">重置密码</el-button>
                <el-button type="text"
                           v-if="scope.row.authStatus == 2"
                           @click="serviceConfiguration(scope.row)"
                           size="small">业务配置</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page="pageNumber"
                         :page-sizes="[10,20, 40, 60, 80,100]"
                         :page-size="pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total"
                         class="pagination"></el-pagination>
        </template>
      </div>
    </div>
    <el-dialog
      title="业务配置"
      :visible.sync="dialogVisible"
      >
      <el-form ref="from" :model="serviceForm" label-width="120px">
        <el-form-item label="所在地">
          <el-autocomplete
            class="inline-input"
            v-model="serviceForm.areaName"
            :fetch-suggestions="querySearch"
            placeholder="请输入所在地"
            ></el-autocomplete>
        </el-form-item>
        <el-form-item label="所属代理">
          <el-input v-model="serviceForm.inviteCode" placeholder="请输入所属代理"></el-input>
        </el-form-item>
        <el-form-item label="固定油费比例">
          <el-input 
          v-model="serviceForm.oilRatio"
          placeholder="请输入1-100的数字"
          type="number"
          @input="(value) => {
                    serviceForm.oilRatio = value.replace(/[^0-9]/g,'');
                    if(value > 100) {
                      serviceForm.oilRatio = 100
                    }
                 }" >
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item label="司机油折扣">
          <el-input 
          v-model="serviceForm.driverOilRatio" 
          placeholder="请输入1-100的数字，支持2位小数"
          type="number"
          oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>100)value=100;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);">
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitConfig">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
const list = '/admin-center-server/commonUser/list'//列表
const updateUserPwd = '/admin-center-server/commonUser/updateUserPwd'//重置密码
import { mixins } from "@/mixin/table.js";
export default {
  name: "consignorAccount",
  mixins: [mixins],
  data () {
    return {
      tableHeight: null, //表格的高度
      tableLabel: [
        {
          prop: 'name',
          label: '公司名称',
          width: 200
        },
        {
          prop: 'mobile',
          label: '手机号（注册）',
          width: 120
        },
        {
          prop: 'createTime',
          label: '注册时间',
          width: 180
        },
        {
          prop: 'inviteCode',
          label: '邀请码'
        },
        {
          prop: 'authStatusName',
          label: '认证状态',
          width: 120
        },
        {
          prop: 'deleteFlag',
          label: '账户状态'
        },
        {
          prop: 'dataFrom',
          label: '数据来源'
        }
      ],
      statusWrap1: [
        {
          name: '注册',
          id: '0'
        },
        {
          name: '新增',
          id: '1'
        },
      ],
      statusWrap: [
        {
          name: '未认证',
          id: '1'
        },
        {
          name: '已认证',
          id: '2'
        },
        {
          name: '认证驳回',
          id: '3'
        },
        {
          name: '审核中',
          id: '4'
        },
        {
          name: '认证资质过期',
          id: '5'
        },
        {
          name: '补充资料认证中',
          id: '6'
        },
        {
          name: '补充资料驳回',
          id: '7'
        }
      ],
      pageSize: 10,
      total: 0,
      pageNumber: 1,
      formInline: {
        name: "",
        tel: "",
        dataSources: '',
        date: '',
        authenticateStatus: ''
      },
      tableData: [],
      dialogVisible: false,
      serviceForm: {
        areaName: '',
        inviteCode: '',
        oilRatio: '',
        driverOilRatio: '',
        userId: ''
      },
      restaurants: []
    };
  },
  methods: {
    // 刷新当前页
    refreshfn () {
      this.$router.go(0)
    },
    //重置
    resetForm (formName) {
      this.$refs[formName].resetFields();
      this.formInline.date = ''
      this.pageNumber = 1
      this.getData()
    },
    onSubmit () {
      this.pageNumber = 1
      this.getData()
    },
    addNewShipper() {
      this.$router.push("/consignorAccount/addShipper?urlType=1");
    },
    editfn (row) {
      this.$router.push("/consignorAccount/addShipper?urlType=1&id=" + row.id);
    },
    goDetail (row) {
      this.$router.push("/consignorAccount/accountDetails?id=" + row.id);
    },
    addnewfn () {
      this.$router.push("/consignorAccount/addUser?urlType=1");
    },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },

    handleSizeChange (val) {
      this.pageNumber = 1
      this.pageSize = val
      this.getData()
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange (val) {
      this.pageNumber = val
      this.getData()
      console.log(`当前页: ${val}`);
    },
    //重置密码
    resetPassword (row) {
      var that = this
      this.$confirm('是否确认重置该账户密码？重置之后该账户登录密码即为a12345678？', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.post(updateUserPwd + '?pwd=' + this.$md5('a12345678').toUpperCase() + '&userId=' + row.id).then(res => {
          this.$message({
            type: 'success',
            message: '操作成功'
          });
        })

      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消重置'
        });
      });
    },
    serviceConfiguration(item) {
        this.dialogVisible = true
        this.serviceForm.areaName = item.areaName
        this.serviceForm.inviteCode = item.inviteCode
        this.serviceForm.oilRatio = item.oilRatio
        this.serviceForm.driverOilRatio = item.driverOilRatio
        this.serviceForm.userId = item.id
    },
    getData () {
      var v = this.formInline,
        data1 = '',
        data2 = '';
      if (v.date) {
        data1 = this.getDate(v.date[0])
        data2 = this.getDate(v.date[1])
      } else {
        data1 = ''
        data2 = ''
      }
      v.name = this.Trim(v.name, 'g')
      var url = list + `?userType=1&flag=0&mobile=${v.tel}&brokerName=${v.name}&dataFrom=${v.dataSources}&authStatus=${v.authenticateStatus}&pageNumber=${this.pageNumber}&pageSize=${this.pageSize}&minCreateTime=${data1}&maxCreateTime=${data2}`
      // var url = list + `?userType=1&flag=0&mobile=${v.tel}&companyName=${v.name}&dataFrom=${v.dataSources}&authStatus=${v.authenticateStatus}&pageNumber=${this.pageNumber}&pageSize=${this.pageSize}&minCreateTime=${data1}&maxCreateTime=${data2}`
      this.$http.get(url).then(res => {
        var data = res.data.data
        this.tableData = data.list
        this.total = Number(data.total)
      })
    },
    getArea() {
      this.$get("admin-center-server/area/list?pageNumber=1&pageSize=1000&parentId=0 ").then(
        (res) => {
          this.restaurants = res.list.map((item) => {
            return {value: item.name}
          })
        }
      )
    },
    submitConfig() {
      if (this.serviceForm.areaName != "") {
        const citys = this.restaurants.map((item) => {
          return item.value
        })
        const includes = citys.includes(this.serviceForm.areaName)
        if (!includes) {
          this.$message.error("请输入正确的所在地")
          return
        }
      }
      this.$post("admin-center-server/updateBusinessConfig", this.serviceForm).then(
        (res) => {
          if (res.code == 1) {
            this.$message.error(res.message)
          } else {
            this.dialogVisible = false
            this.getData()
            this.$message({
                type: "success",
                message: "配置成功"
            }
            )
          }
        }
      )
    },
    querySearch(queryString, cb) {
      let restaurants = this.restaurants;
      let results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
        };
    },
  },
  activated () {
    this.getData()
    this.getArea()
    this.tableHeight =
      window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 160;
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
::v-deep input[type="number"] {
  -moz-appearance: textfield;
}
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
