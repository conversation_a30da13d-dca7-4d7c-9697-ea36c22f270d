<template>
  <div class="detail-col" :style="{width: getWidth}">
    <div class="detail-title">{{ label }}：</div>
    <div class="detail-value" v-if="!tooltip && value!='-'">
      <slot>{{ value }}</slot>
    </div>
    <el-tooltip v-else placement="top">
      <div class="remark-div" >
        <slot>{{ value }}</slot>
      </div>
      <template slot="content">{{ value }}</template>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  props: {
    label: {
      type: String
    },
    value: {
      type: [String, Number]
    },
    span: {
      type: Number,
      default: 6
    },
    formatter: {
      type: Function
    },
    tooltip: {
      type: Boolean,
      default: false
    },
  },
  computed: {
    getWidth() {
      let percent = this.span / 24 * 100
      percent = percent.toFixed(3)
      return percent + '%'
    },
    getValue() {
      if (this.formatter) {
        return this.formatter(this.value)
      }
      return this.value
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-col {
  display: flex;
  overflow: hidden;
  box-sizing: border-box;
  padding: 0 10px;
  white-space: nowrap;
}
.detail-title {
  flex-shrink: 0;
  line-height: 36px;
  font-size: 14px;
  font-weight: bold;
  color: #666;
  vertical-align: top;
}
.detail-value {
  flex: 1;
  margin-left: 10px;
  line-height: 36px;
  font-size: 14px;
  word-break: break-all;
  white-space: normal;
}
.remark-div{
  margin-left: 10px;
  line-height: 36px;
  font-size: 14px;
  color: #333;
  white-space: nowrap;      /* 确保文本在一行内显示 */
  overflow: hidden;         /* 超出容器部分隐藏 */
  text-overflow: ellipsis;
}
.detail-slot {
  display: inline-block;
  vertical-align: top;
}
</style>
<style>
.el-tooltip__popper{
  max-width: 400px !important; /* 限制提示框的最大宽度 */
  word-break: break-all !important;
}
</style>