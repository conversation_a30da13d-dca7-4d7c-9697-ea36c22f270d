import FenceConfigurator from './FenceConfigurator.vue'
import { $post } from '@/utils/http2'
import Vue from 'vue'

//后台配置的默认半径
let defaltRadiusPromise = () => {
  return $post('/base-center-server/settings/get?group=Function&code=fenceDefaultRadius')
    .then(res => Number(res.value))
}
export const showFenceConfigurator = async options => {
  let defaltRadius = await defaltRadiusPromise()
  let fanceConstructor = Vue.extend(FenceConfigurator)
  let instance = new fanceConstructor()

  if (options.deliveryFenceId) instance.deliveryFenceId = options.deliveryFenceId
  if (options.receiveFenceId) instance.receiveFenceId = options.receiveFenceId
  instance.deliveryPoint = options.deliveryPoint
  instance.receivePoint = options.receivePoint
  //对话框关闭后执行
  instance.onClose = () => {
    document.body.removeChild(instance.$el)
  }
  //保存围栏后执行
  instance.onSave = (data, cb) => {
    typeof options.onSave === 'function' && options.onSave(data, cb)
  }
  instance.defaltRadius = defaltRadius

  instance.$mount()
  document.body.appendChild(instance.$el)
}

export const getDefaultFenceData = async () => {
  let defaltRadius = await defaltRadiusPromise()
  return {
    radius: defaltRadius,
    coordType: "WGS84",
    fenceShape: "0"
  }
}