<template>
    <div class="app-container carsBelong">
        <div class="select-box">
            <div class="top-title">
                <div>筛选查询</div>
                <div class="button">
                    <el-button
                            class="left"
                            icon="el-icon-refresh-right"
                            size="mini"
                            type="success"
                            @click="()=>{this.$router.go(0)}"
                    >刷新
                    </el-button>
                    <el-button class="left"
                               icon="el-icon-search"
                               size="mini"
                               type="primary"
                               @click="onSubmit">查询
                    </el-button>
                    <el-button class="left"
                               icon="el-icon-delete"
                               size="mini"
                               type="danger"
                               @click="resetSubmit">清空筛选
                    </el-button>
                </div>
            </div>
            <div class="select-info">
                <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="150px" size="mini">
                    <el-form-item label="车牌号:">
                        <el-input v-model="formInline.plateNumber" :οnkeyup="formInline.plateNumber=formInline.plateNumber.replace(/\s/g, '')" placeholder="请输入车牌号"></el-input>
                    </el-form-item>
                    <el-form-item label="归属授权状态:">
                        <el-select v-model="formInline.region" placeholder="不限">
                            <el-option label="不限" value=""></el-option>
                            <el-option label="授权审核中" value="0"></el-option>
                            <el-option label="已授权" value="1"></el-option>
                            <el-option label="授权已驳回" value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="申请司机:">
                        <el-input v-model="formInline.drivingName" :οnkeyup="formInline.drivingName=formInline.drivingName.replace(/\s/g, '')" placeholder="请输入申请司机名称"></el-input>
                    </el-form-item>
                    <el-form-item label="是否优先通过:">
                        <el-select v-model="formInline.priority" placeholder="不限" style="width: 80px">
                            <el-option label="不限" value=""></el-option>
                            <el-option label="否" value="0"></el-option>
                            <el-option label="是" value="1"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="150px"
                         style="margin-top: 20px" size="mini">
                    <el-form-item label="申请司机手机号:">
                        <el-input v-model="formInline.mobile" :οnkeyup="formInline.mobile=formInline.mobile.replace(/\s/g, '')" placeholder="请输入司机手机号"></el-input>
                    </el-form-item>
                    <el-form-item label="操作人名称:">
                        <el-input v-model="formInline.updateUserName" :οnkeyup="formInline.updateUserName=formInline.updateUserName.replace(/\s/g, '')" placeholder="请输入操作人名称"
                                  style="width: 195px"></el-input>
                    </el-form-item>
                    <el-form-item label="操作人账号:">
                        <el-input v-model="formInline.updateUserNick" :οnkeyup="formInline.updateUserNick=formInline.updateUserNick.replace(/\s/g, '')" placeholder="请输入操作人账号"></el-input>
                    </el-form-item>
                </el-form>
                <el-form :inline="true" :model="formInline" class="noneSelectForm" label-width="150px"
                         style="margin-top: 20px;margin-left: 50px" size="mini">
                    <el-select v-model="formInline.timeType" size="mini" style="width: 100px;">
                        <el-option label="创建日期" value="0"></el-option>
                        <el-option label="操作日期" value="1"></el-option>
                    </el-select>
                    <el-form-item label="">
                        <el-date-picker
                                :clearable="false"
                                @blur="selectTime"
                                v-model="formInline.date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetimerange"
                                :default-time="['00:00:00', '00:00:00']"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="list-box">
            <div class="list-title">
                <div>数据列表</div>
            </div>
            <div class="list-main">
                <template>
                    <el-table
                            :data="tableData"
                            border
                            :row-class-name="tableRowClassName"
                            style="width: 100%">
                        <el-table-column
                                prop="num"
                                label="序号"
                                type="index"
                                width="50">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="plateNumber"
                                label="车牌号"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="carModelName"
                                label="车型"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="sumCapacityTonnage"
                                label="总重量"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="capacityTonnage"
                                label="载重"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="axleNumber"
                                label="轴数"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="driverName"
                                label="申请司机"
                                width="120">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="driverMobile"
                                label="申请司机手机号"
                                width="120">
                        </el-table-column>

                        <el-table-column
                                show-overflow-tooltip
                                prop="authStatus"
                                label="归属授权状态"
                                :formatter="jiashiStatus"
                                width="120">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="priority"
                                label="是否优先通过"
                                :formatter="carPriority"
                                width="120">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="updateUserName"
                                label="操作人名称"
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="updateUserNick"
                                label="操作人账号"
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="updateDateTime"
                                label="操作日期"
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="createdDate"
                                label="创建日期"
                        >
                        </el-table-column>
                        <el-table-column
                                fixed="right"
                                label="操作"
                                width="100">
                            <template slot-scope="scope">
                                <el-button type="text" size="small" @click="goAttestation(scope.row)">审核</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </div>
            <div class="pagination" style="float: right;margin-top: 20px">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 40,50,100,200]"
                        :page-size="20"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total=total>
                </el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'CarsList',
        data() {
            return {
                formInline: {
                    priority: '',
                    plateNumber: '',
                    drivingName: '',
                    timeType: '0',
                    mobile: '',
                    date: '',
                    region: '0',
                    datehandle: '',
                    updateUserName: '',
                    updateUserNick: '',
                },
                tableData: [],
                currentPage: 1,
                pageSize: 20,
                total: 1,

                drivingName: '',
                startTime: '',
                endTime: '',
                mobile: '',
                plateNumber: '',
                status: '',
                type: '0',
                startTimehandle: '',
                endTimehandle: '',
            }
        },
        methods: {
            carPriority(row){
                if(row.priority==='0'){
                    return '否'
                }else if(row.priority==='1'){
                    return '是'
                }else if(row.priority==='2'){
                    return ''
                }
            },
            jiashiStatus(row) {
                if (row.status === '0') {
                    return '认证中'
                } else if (row.status === '1') {
                    return '认证成功'
                } else if (row.status === '2') {
                    return '认证失败'
                } else if (row.status === '3') {
                    return '认证中'
                }
            },
            tableRowClassName({row, rowIndex}) {
                if (row.status === '0' || row.status === '3') {
                    return 'inAudit-row ';
                } else if (row.status === '2') {
                    return 'success-row';
                } else if (row.status === '1') {
                    return 'warning-row';
                }
                return '';
            },
            onSubmit() {
                this.currentPage = 1;
                this.getCarList();
            },
            goAttestation(row) {
                this.$router.push({
                    path:'carsBelong/carsBelongExamine',
                    query:{
                        carId:row.id,
                        time:new Date().getTime()
                    }
                })
            },
            /** 清空搜索选项 **/
            resetSubmit() {
                this.formInline = {
                    priority: '',
                    plateNumber: '',
                    drivingName: '',
                    mobile: '',
                    date: '',
                    region: '',
                    updateUserName: '',
                    updateUserNick: '',
                    timeType: '0',
                },
                    this.getCarList();
            },
            /** 根据时间态搜索 **/
            selectTime() {
                if(this.formInline.date!==null){
                    let startTime = this.formInline.date[0];
                    let endTime = this.formInline.date[1];
                    this.startTime = startTime;
                    this.endTime = endTime
                }else {
                    this.formInline.date=[];
                }
            },
            /** 分页方法 **/
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1;
                this.getCarList()
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getCarList()
            },
            getCarList() {
                this.$http.get('/admin-center-server/car/driving/list', {
                    params: {
                        priority:this.formInline.priority,
                        timeType:this.formInline.timeType,
                        updateUserName:this.formInline.updateUserName,
                        updateUserNick:this.formInline.updateUserNick,
                        plateNumber: this.formInline.plateNumber,
                        drivingName: this.formInline.drivingName,
                        mobile: this.formInline.mobile,
                        startTime: this.startTime,
                        endTime: this.endTime,

                        pageNumber: this.currentPage,
                        pageSize: this.pageSize,

                        status: this.formInline.region,
                        type: '1',
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.total = Number(data.data.total)
                        this.tableData = data.data.list
                    }
                })
            },
        },
        activated() {
            this.getCarList();
        }
    }
</script>
<style>
    .noneSelectForm .el-select .el-input__inner {
        font-size: 14px;
        color: #606266;
        font-weight: 700;
        border: none !important;
    }
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
    .el-form-item {
        margin-bottom: 10px;
    }

    .carsBelong {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
                display: flex;
                justify-content: space-between;

                .button {
                    margin-right: 20px;
                }
            }

            .select-info {
                padding-top: 30px;
                /*padding-bottom: 30px;*/
            }
        }

        .list-box {
            overflow: hidden;
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;

            .list-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                div {
                    height: 38px;
                    line-height: 38px;
                }
            }

            .list-main {
                width: 100%;
                border: 1px solid #cccccc;
                margin-top: 10px;
            }
        }
    }
</style>
