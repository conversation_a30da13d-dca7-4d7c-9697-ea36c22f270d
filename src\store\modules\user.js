import {loginByUsername, logout, getUserInfo, getUserInfo2, getBaseInfo} from '@/api/login'
import {getToken, setToken, removeToken} from '@/utils/auth'
import {Message} from "element-ui";

const user = {
    state: {
        user: '',
        status: '',
        code: '',
        token: getToken(),
        name: '',
        avatar: '',
        introduction: '',
        roles: [],
        setting: {
            articlePlatform: []
        },
        userInfo2: {},
        baseInfo: []
    },

    mutations: {
        SET_CODE: (state, code) => {
            state.code = code
        },
        SET_TOKEN: (state, token) => {
            state.token = token
        },
        SET_INTRODUCTION: (state, introduction) => {
            state.introduction = introduction
        },
        // SET_SETTING: (state, setting) => {
        //   state.setting = setting
        // },
        // SET_STATUS: (state, status) => {
        //   state.status = status
        // },
        SET_NAME: (state, name) => {
            state.name = name
        },
        SET_AVATAR: (state, avatar) => {
            state.avatar = avatar
        },
        SET_ROLES: (state, roles) => {
            state.roles = roles
        },
        SET_BASEINFO: (state, info) => {
            state.baseInfo = info
            console.log("获取成功");
        }
    },

    actions: {
        // 用户名登录
        LoginByUsername({commit}, userInfo) {
            const username = userInfo.nickName.trim();
            return new Promise((resolve, reject) => {
                loginByUsername(username, userInfo.pwd).then(response => {
                    const data = response.data;
                    if(data.code==='200'){
                        commit('SET_TOKEN', data.data.token);
                        setToken(response.data.data.token);
                    }else {
                        Message({
                            message: data.message,
                            type: 'error',
                        });
                    }
                    resolve()
                }).catch(error => {
                    reject(error)
                })
            })
        },

        // 获取用户 菜单信息
        GetUserInfo({commit, state}) {
            return new Promise((resolve, reject) => {
                getUserInfo2().then(res => {
                    state.userInfo2 = res.data.data
                    return getUserInfo()
                })
                    .then(response => {
                        const data = response.data;
                        let menuList = data.data.menuList;
                        if (data.data.menuList && data.data.menuList.length > 0) { // 验证返回的roles是否是一个非空数组
                            commit('SET_ROLES', menuList)
                        } else {
                            reject('该账号暂无权限，请联系管理员')
                        }
                        commit('SET_NAME', data.data.userInfo.nickName);
                        // commit('SET_AVATAR', data.avatar)
                        // commit('SET_INTRODUCTION', data.introduction);
                        resolve(response)
                    }).catch(error => {
                        reject(error)
                    })
            })
        },

        // 登出
        LogOut({commit, state}) {
            return new Promise((resolve, reject) => {
                logout(state.token).then(() => {
                    commit('SET_TOKEN', '');
                    commit('SET_ROLES', []);
                    removeToken();
                    resolve()
                }).catch(error => {
                    reject(error)
                })
            })
        },

        // 前端 登出
        FedLogOut({commit}) {
            return new Promise(resolve => {
                commit('SET_TOKEN', '');
                removeToken();
                resolve()
            })
        },

        // 动态修改权限
        // ChangeRoles({ commit, dispatchPage }, role) {
        //   return new Promise(resolve => {
        //     commit('SET_TOKEN', role)
        //     setToken(role)
        //     getUserInfo(role).then(response => {
        //       const data = response.data
        //       commit('SET_ROLES', data.roles)
        //       commit('SET_NAME', data.name)
        //       commit('SET_AVATAR', data.avatar)
        //       commit('SET_INTRODUCTION', data.introduction)
        //       dispatchPage('GenerateRoutes', data) // 动态修改权限后 重绘侧边菜单
        //       resolve()
        //     })
        //   })
        // }

        GetBaseInfo({commit}) {
            getBaseInfo().then(
                res => {
                    commit("SET_BASEINFO", res.data.data)
                }, 
                error => {
                    
                }
            )
        }
    }
}

export default user
