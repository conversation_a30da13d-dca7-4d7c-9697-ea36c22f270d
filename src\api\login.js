import request from '@/utils/request'
import requestHeader from '@/utils/http'

export function loginByUsername(nickName, pwd) {
  const data = {
    nickName,
    pwd
  };
  return request({
    url: '/admin-center-server/sys/login',
    method: 'post',
    data
  })
}

export function logout() {
  return request({
    url: '/admin-center-server/sys/quit',
    method: 'post'
  })
}

export function getUserInfo(token) {
  return requestHeader({
    url: '/admin-center-server/sys/resource/queryInfoAndMenu',
    method: 'post',
  })
}

export function getUserInfo2() {             
  return requestHeader({
    url: '/admin-center-server/sys/detailed',
    method: 'post',
  })
}

export function getBaseInfo() {             
  return requestHeader({
    url: '/admin-center-server/commonUser/getBaseInfo',
    method: 'post',
  })
}