import request from '@/utils/request'
import requestHeader from '@/utils/http'
import {sm3} from 'sm-crypto' 
import base64 from 'Base64'
export function loginByUsername(nickName, pwd) {
  const data = {
    nickName,
    pwd:base64.btoa(pwd),
    pwdNew:sm3(pwd).toUpperCase()
  };
  return request({
    url: '/admin-center-server/sys/login',
    method: 'post',
    data
  })
}

export function logout() {
  return request({
    url: '/admin-center-server/sys/quit',
    method: 'post'
  })
}

export function getUserInfo(token) {
  return requestHeader({
    url: '/admin-center-server/sys/resource/queryInfoAndMenu',
    method: 'post',
  })
}

export function getUserInfo2() {             
  return requestHeader({
    url: '/admin-center-server/sys/detailed',
    method: 'post',
  })
}

export function getBaseInfo() {             
  return requestHeader({
    url: '/admin-center-server/commonUser/getBaseInfo',
    method: 'post',
  })
}