<template>
  <div class="container">
    <div class="auto-base">
      <el-collapse>
        <el-collapse-item>
          <template slot="title">
            <div style="font-size: 15px">系统自动审核结果：
              <span v-if="examineResult.resultCode=='PASS'" class="auto-text" style="color: green;">审核通过</span>
              <span v-if="examineResult.resultCode=='reject'" class="auto-text" style="color: red;"> 审核驳回</span>
              <span v-if="examineResult.resultCode=='toStaff'" class="auto-text" style="color: #ED970F;">转人工</span>
              </div>
          </template>
          <div class="auto">
            <div class="auto-display-text" v-for="(item, index) in examineResult.hitResultList" :key="index">
              <i v-if="item.isHit === 'miss'" class="el-icon-check" style="color: green;"></i>
              <i v-if="item.isHit === 'hit'" class="el-icon-close" style="color: red;"></i>
              <i v-if="item.isHit === 'unExecuted'" class="el-icon-remove-outline" style="color: #ED970F;"></i>
              {{item.ruleName}}
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
  
    </div>
    <div class="audit-bottom" v-if="operation === '2'">
      <span v-if="isDriver">司机审核状态：{{ isDriver ? form.driverAuthStatusName : form.carOwnerStatusName}} （<span>{{isDriver ? '实名认证状态：' : ''}}</span> <span>{{form.realNameAuthStatusName}}</span>） </span>
      <span v-else></span>
      <el-button @click="pass" v-if="isShowPassBtn" :loading="isLoading" type="primary">通过审核</el-button>
      <el-button @click="isRejectDialogShow = true"  v-if="isShowRejectBtn" :loading="isLoading" type="danger">驳回</el-button>
    </div>
    <div class="audit-top">
      <div class="top-content">
        <div class="top-title">司机审核 <span class="top-sn"> {{form.name}}</span></div>
        <div class="top-button">
          <div class="top-button-info" :class="{'top-button-select': selectIndex == 0}" @click="selectIndex = 0">基本信息</div>
          <div class="top-button-log" :class="{'top-button-select': selectIndex == 1}" @click="selectIndex = 1">审核日志</div>
        </div>
      </div>
    </div>
    <div class="left" v-if="selectIndex == 0">
      <div v-if="isDriverAndLeaderAuth" class="tab">
        <el-tabs v-model="tab" @tab-click="handleTabChange" type="card">
          <el-tab-pane name="1" label="司机身份审核"></el-tab-pane>
          <el-tab-pane name="2" label="个人车队长身份审核"></el-tab-pane>
        </el-tabs>
      </div>
      <div class="audit">
        <div class="audit-title">{{ isDriver ? '司机' : '个人车队长' }}身份信息：</div>
        <el-form
          :model="form"
          :rules="formRules"
          ref="form"
          label-width="140px"
          class="form">
          <el-form-item label="姓名：" prop="companyName">
            <el-input v-model="form.companyName"></el-input>
          </el-form-item>
          <el-form-item label="手机号：" prop="mobile">
            <el-input v-model="form.mobile"></el-input>
          </el-form-item>
          <el-form-item label="身份证号：" prop="idCard">
            <el-input v-model="form.idCard"></el-input>
          </el-form-item>
          <el-form-item prop="idCardExpireDate">
            <template #label><span class="required-asterisk">*</span> 身份证有效期：</template>
            <el-date-picker v-model="form.idCardExpireDate" :disabled="isIdLong" value-format="yyyy-MM-dd"></el-date-picker>
            <el-checkbox v-model="isIdLong" class="long">长期</el-checkbox>
            <div class="expire-text">
              <template v-if="idCardExpireDateStatus === -1">（已过期）</template>
              <template v-else-if="idCardExpireDateStatus !== -2">（{{ idCardExpireDateStatus }}天后到期）</template>
            </div>
          </el-form-item>
          <template v-if="isDriver">
            <el-form-item label="驾驶证号：" prop="drivingLicencesNumber">
              <el-input v-model="form.drivingLicencesNumber"></el-input>
            </el-form-item>
            <el-form-item prop="drivingLicensesExpireDate">
              <template #label><span class="required-asterisk">*</span> 驾驶证有效期：</template>
              <div v-if="isDriverLisenceLong">
                <el-date-picker  type="date" v-model="drivingLicensesExpireStartDate" value-format="yyyy-MM-dd"></el-date-picker>
                <el-checkbox v-model="isDriverLisenceLong" class="long"  @change="changeFn">长期</el-checkbox>
              </div>
              <div class="driving_licenses_expireDate" v-if="!isDriverLisenceLong">
                <el-date-picker style="width:137px" v-model="drivingLicensesExpireDate[0]" :disabled="isDriverLisenceLong" type="date" value-format="yyyy-MM-dd" :picker-options="pickerOptionsStart"></el-date-picker>
                <span style="color:#888;width:6px">-</span>
                <el-date-picker style="width:137px" v-model="drivingLicensesExpireDate[1]" :disabled="isDriverLisenceLong" type="date" value-format="yyyy-MM-dd" :picker-options="pickerOptionsEnd"></el-date-picker>
                <el-checkbox v-model="isDriverLisenceLong" class="long"  @change="changeFn">长期</el-checkbox>
              </div>
              <div class="expire-text" v-if="!isDriverLisenceLong">
                <template v-if="drivingLicensesExpireDateStatus === -1">（已过期）</template>
                <template v-else-if="drivingLicensesExpireDateStatus !== -2">（{{ drivingLicensesExpireDateStatus }}天后到期）</template>
              </div>
            </el-form-item>
            <el-form-item label="准驾车型：" prop="drivenVehicleModel">
              <el-input v-model="form.drivenVehicleModel"></el-input>
            </el-form-item>
            <el-form-item label="驾驶证发证机关：" prop="drivingLicensesIssueOrg">
              <el-input v-model="form.drivingLicensesIssueOrg"></el-input>
            </el-form-item>
            <el-form-item v-if="form.employmentCertImage && form.driverAuthStatus == 6" label="从业资格证号：" prop="qualificationCertificate">
              <el-input v-model="form.qualificationCertificate"></el-input>
              <div class="abnormal">
                <el-checkbox v-model="isCertAbnormal">存在异常</el-checkbox>
                <el-select v-if="isCertAbnormal" v-model="form.employmentCertWarnMemo">
                  <el-option value="网站无相关信息"></el-option>
                  <el-option value="网站查询过期"></el-option>
                  <el-option value="从业资格证号与网站不一致"></el-option>
                </el-select>
              </div>
            </el-form-item>
            <el-form-item v-if="form.employmentCertImage && form.driverAuthStatus == 6" label="从业资格证有效期：" prop="employmentCertExpireDate">
              <el-date-picker v-model="form.employmentCertExpireDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
              <div class="expire-text">
                <template v-if="employmentCertExpireDateStatus === -1">（已过期）</template>
                <template v-else-if="employmentCertExpireDateStatus !== -2">（{{ employmentCertExpireDateStatus }}天后到期）</template>
              </div>
            </el-form-item>
          </template>
          <el-form-item label="认证状态：">
            {{ isDriver ? form.driverAuthStatusName : form.carOwnerStatusName}}
          </el-form-item>
          <el-form-item v-if="false">
            <el-button @click="pass" v-if="isShowPassBtn" :loading="isLoading" type="primary">通过审核</el-button>
            <el-button @click="isRejectDialogShow = true"  v-if="isShowRejectBtn" :loading="isLoading" type="danger">驳回</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <img-list class="right" v-if="selectIndex == 0" ref="imgList">
      <el-form label-width="160px">
        <el-form-item label="身份证人像面：">
          <div class="item-box" @click="viewPic(form.idCardImage)">
            <div class="img-box">
              <img :src="form.idCardImage">
            </div>
          </div>
        </el-form-item>
        <el-form-item label="身份证国徽面：">
          <div class="item-box" @click="viewPic(form.idCardBackImage)">
            <div class="img-box">
              <img :src="form.idCardBackImage">
            </div>
          </div>
        </el-form-item>
        <template v-if="isDriver">
          <el-form-item v-if="form.employmentCertImage && form.driverAuthStatus == 6" label="从业资格证：">
            <div class="item-box" @click="viewPic(form.employmentCertImage)">
              <div class="img-box">
                <img v-if="form.employmentCertImage" :src="form.employmentCertImage">
                <img v-else src="@/assets/images/empty.png">
              </div>
            </div>
          </el-form-item>
          <el-form-item label="驾驶证：">
            <div class="item-box" @click="viewPic(form.drivingLicencesImage)">
              <div class="img-box">
                <img :src="form.drivingLicencesImage">
              </div>
            </div>
          </el-form-item>
        </template>
      </el-form>
    </img-list>
    <LogList style="margin-top: 130px" v-if="selectIndex == 1" :listData="form.userVerifyLogDTO"></LogList>
    <el-dialog
      :visible.sync="isRejectDialogShow"
      title="驳回原因">
      <el-form :model="rejectForm">
        <el-form-item label="请选择驳回原因" label-width="120px">
          <el-checkbox-group v-model="rejectForm.region">
            <div v-for='item in rejectReasonList' :key="item.id">
              <el-checkbox :label="item.rejectReason" >{{ item.rejectReason }}</el-checkbox>
            </div>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="其他原因" label-width="120px">
          <el-input type="textarea"
            :rows="2"
            placeholder="请输入其他驳回原因"
            v-model="rejectForm.textarea">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isRejectDialogShow = false">取 消</el-button>
        <el-button type="primary" @click="reject">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ImgList from '@/components/ImgList/ImgList'
import dayjs from 'dayjs'
import { checkExpire } from '@/utils/date'
import LogList from '@/components/AuditLog/index.vue'
export default {
  components: {
    ImgList,
    LogList
  },
  data() {
    return {
      operation: null,
      selectIndex: 0,
      form: {},
      formRules: {
        companyName: [
          { required: true, message: '姓名不能为空', trigger: 'blur' }
        ],
        mobile: [
          { required: true, message: '手机号不能为空', trigger: 'blur' }
        ],
        idCard: [
          { required: true, message: '身份证号不能为空', trigger: 'blur' }
        ],
        idCardExpireDate: [
          {
            validator: (rule, value, cb) => {
              if (!this.isIdLong && !this.form.idCardExpireDate) {
                return cb('身份证有效期不能为空')
              } else {
                return cb()
              }
            },
            trigger: 'blur'
          }
        ],
        drivingLicencesNumber: [
          { required: true, message: '驾驶证号不能为空', trigger: 'blur' }
        ],
        drivingLicensesExpireDate: [
          {
            validator: (rule, value, cb) => {
              if (this.isDriverLisenceLong && !this.drivingLicensesExpireStartDate) {
                return cb('驾驶证有效期不能为空')
              } else if (!this.isDriverLisenceLong && (!this.drivingLicensesExpireDate || this.drivingLicensesExpireDate.length<2 || (this.drivingLicensesExpireDate.length == 2 && (!this.drivingLicensesExpireDate[1] || !this.drivingLicensesExpireDate[0])))) {
                return cb('驾驶证有效期不能为空')
              } else {
                return cb()
              }
            },
            trigger: 'blur'
          }
        ],
        drivenVehicleModel: [
          { required: true, message: '准驾车型不能为空', trigger: 'blur' }
        ],
        drivingLicensesIssueOrg: [
          { required: true, message: '驾驶证发证机关不能为空', trigger: 'blur' }
        ],
      },
      drivingLicensesExpireDate: [],
      drivingLicensesExpireStartDate: '',
      isIdLong: false,
      isDriverLisenceLong: false,
      tab: null,
      type: null,
      rejectReasonList: [],
      isLoading: false,
      isRejectDialogShow: false,
      rejectForm: {
        region: [],
        textarea: ''
      },
      isCertAbnormal: false,
      examineResult: {},
      idCardExpireDateStatus: -2,
      employmentCertExpireDateStatus: -2,
      drivingLicensesExpireDateStatus: -2,
       // 开始结束日期限制
       pickerOptionsStart: {
        disabledDate: time => {
          if (this.drivingLicensesExpireDate[1]) {
            return (
              time.getTime() >= new Date(this.drivingLicensesExpireDate[1]).getTime()
            );
          }
        }
      },
      // 结束日期限制
      pickerOptionsEnd: {
        disabledDate: time => {
          if (this.drivingLicensesExpireDate[0]) {
            return (
              time.getTime() <= new Date(this.drivingLicensesExpireDate[0]).getTime()-8.64e7
            );
          }
        }
      },
    }
  },
  activated() {
    let query = this.$route.query
    this.tab = query.tab //如果是司机车队长会传tab
    this.type = query.type
    this.id = query.id
    this.operation = query.operation
    
    this.getData()
    this.getRejectReason()

    this.$watch('form.idCardExpireDate', {
      deep: true,
      handler(v) {
        checkExpire(v)
          .then(res => this.idCardExpireDateStatus = res)
      }
    })
    this.$watch('form.employmentCertExpireDate', {
      deep: true,
      handler(v) {
        checkExpire(v)
          .then(res => this.employmentCertExpireDateStatus = res)
      }
    })
    this.$watch('drivingLicensesExpireDate', {
      deep: true,
      handler(v) {
        checkExpire(v[1])
          .then(res => this.drivingLicensesExpireDateStatus = res)
      }
    })
  },
  computed: {
    isDriverAndLeaderAuth() {
      return this.type === '10'
    },
    isDriver() {
      if (
        this.isDriverAndLeaderAuth && this.tab === '1' ||
        this.type === '3'
      ) {
        return true
      }
      return false
    },
    isShowPassBtn() {
      if (this.isDriver && (this.form.driverAuthStatus === '4' || this.form.driverAuthStatus === '6')) {
        return true
      }
      if (!this.isDriver && (this.form.carOwnerAuthStatus === '4' || this.form.carOwnerAuthStatus === '6')) {
        return true
      }
      return false
    },
    isShowRejectBtn() {
      if (this.isDriver && (this.form.driverAuthStatus === '4' || this.form.driverAuthStatus === '6' || this.form.driverAuthStatus === '2')) {
        return true
      }
      if (!this.isDriver && (this.form.carOwnerAuthStatus === '4' || this.form.carOwnerAuthStatus === '6' || this.form.carOwnerAuthStatus === '2')) {
        return true
      }
      return false
    }
  },
  methods: {
    changeFn(){
      this.$refs.form.clearValidate('drivingLicensesExpireDate');
    },
    handleTabChange() {
      this.getData()
      this.getRejectReason()
    },
    getData() {
      this.$get('/admin-center-server/commonUser/forUpdateInfo', {
        id: this.$route.query.id,
        type: Number(this.tab || '1') + 2 //3司机 4车队长
      })
        .then(res => {
          this.form = res
          if (res.idCardExpireDate === '9999-12-31') {
            this.isIdLong = true
          } else {
            this.isIdLong = false
          }
          if (res.drivingLicensesExpireDate === '9999-12-31') {
            this.isDriverLisenceLong = true
            this.drivingLicensesExpireStartDate = res.drivingLicensesStartDate
          } else {
            this.isDriverLisenceLong = false
            if(res.drivingLicensesStartDate && res.drivingLicensesExpireDate) {
              this.drivingLicensesExpireDate = [res.drivingLicensesStartDate, res.drivingLicensesExpireDate]
            } else {
              this.drivingLicensesExpireDate = ['', '']
            }
          }
          this.isCertAbnormal = res.employmentCertWarnFlag === '1' ? true : false
          this.getExamineResult(res)

          // 修改title
          document.title = '司机审核-' + res.name 
        })
    },

    getExamineResult(data) {
      let params = {}
      if (this.tab === '2') {
        params = {
          name: data.name,
          mobile: data.mobile,
          idCardNum: data.idCard,
          idCardExpireDate: data.idCardExpireDate,
          idCardImage: data.idCardImage,
          idCardBackImage: data.idCardBackImage
        }
      } else {
        params = {
          name: data.name,
          mobile: data.mobile,
          idCardNum: data.idCard,
          idCardExpireDate: data.idCardExpireDate,
          idCardImage: data.idCardImage,
          idCardBackImage: data.idCardBackImage,
          employmentCertImage: data.employmentCertImage,
          employmentCertExpireDate: data.employmentCertExpireDate,
          employmentCert: data.employmentCert,
          drivingLicensesStartDate: data.drivingLicensesStartDate,
          drivingLicensesIssueOrg: data.drivingLicensesIssueOrg,
          drivingLicensesExpireDate: data.drivingLicensesExpireDate,
          drivingLicencesNumber: data.drivingLicencesNumber,
          drivingLicencesImage: data.drivingLicencesImage,
          drivenVehicleModel: data.drivenVehicleModel
        }
      }

      const ruleUrl = this.tab === '2' ? "/admin-center-server/rule/engine/carLeaderRule" : "/admin-center-server/rule/engine/driverRule"
      
      this.$post(ruleUrl, params).then(
        res => {
          this.examineResult = res
        },
        error => {
          this.examineResult = {
            resultCode: '',
            hitResultList: []
          }
        }
      )
    },
    examineDisplayText(data) {
      let displayText = ''
      switch (data.isHit) {
        case 'miss':
          displayText = data.ruleName + ': 未命中'
          break
        case 'hit':
          displayText = data.ruleName + ': ' + data.hitMsg
          break
        case 'unExecuted':
          displayText = data.ruleName + ': 未执行'
          break
      }
      return displayText
    },
    viewPic(pic) {
      this.$refs.imgList.showPic(pic)
    },
    countDayDiff() {
      let now = dayjs(Date.now())
      if (this.drivingLicensesExpireDate[1]) {
        let date = dayjs(this.drivingLicensesExpireDate[1])
        return date.diff(now, 'day') + 1   
      }
      return '--'
    },
    getRejectReason() {
      //1司机 2车队长
      let scene = this.isDriver ? 1 : 2
      this.$post('/admin-center-server/verify/rejectReasonList', {
        scene
      })
        .then(res => {
          this.rejectReasonList = res
        })
    },
    getDriverauthapplychannel () {
      this.$http.get('/admin-center-server/verify/getVerifySumMsgByType', {
        params: {
          userType: 3
        }
      }).then(res => {
        let data = Number(res.data.data);
        let type = 30;
        this.getMenu(type, data)
      })
    },
    getMenu (type, num) {
      this.$store.state.permission.addRouters.map((item, index) => {
        if (item.meta) {
          if (item.meta.id === 22) {
            let changeNum = item.children[0].children;
            changeNum.map((item, index) => {
              if (!item.hidden && item.meta.id !== 24) {
                if (item.meta.id === 25 && type === 0) {
                  item.meta.num = num
                } else if (item.meta.id === 26 && type === 1) {
                  item.meta.num = num
                } else if (item.meta.id === 27 && type === 2) {
                  item.meta.num = num
                }
              }
            })
          } else if (item.meta.id === 1) {
            let clientNum = item.children[0].children;
            let brokerNum = item.children[1].children;
            let driverNum = item.children[2].children;
            clientNum.map((item, index) => {
              if (!item.hidden) {
                if (item.meta.id === 5 && type === 10) {
                  item.meta.num = num
                }
              }
            });
            brokerNum.map((item, index) => {
              if (!item.hidden) {
                if (item.meta.id === 10 && type === 20) {
                  item.meta.num = num
                }
              }
            });
            driverNum.map((item, index) => {
              if (!item.hidden) {
                if (item.meta.id === 13 && type === 30) {
                  item.meta.num = num
                }
              }
            })
          }
        }
      });
    },
    getParams() {
      let form = this.form
      let data = {
        id: this.id,
        name: form.companyName,
        mobile: form.mobile,
        idCard: form.idCard
      }
      if (this.isDriver) {
        Object.assign(data, {
          qualificationCertificate: form.qualificationCertificate,
          employmentCertExpireDate: form.employmentCertExpireDate,
          drivingLicencesNumber: form.drivingLicencesNumber,
          drivenVehicleModel: form.drivenVehicleModel,
          drivingLicensesIssueOrg: form.drivingLicensesIssueOrg,
          employmentCertExpireDate: form.employmentCertExpireDate
        })

        //从业资格证异常
        if (this.isCertAbnormal) {
          data.employmentCertWarnFlag = 1
          data.employmentCertWarnMemo = form.employmentCertWarnMemo
        } else {
          data.employmentCertWarnFlag = 0
        }

        if (this.isDriverLisenceLong) {
          data.drivingLicensesStartDate = this.drivingLicensesExpireStartDate
          data.drivingLicensesExpireDate = '9999-12-31 00:00:00'
        } else {
          data.drivingLicensesStartDate = this.drivingLicensesExpireDate && this.drivingLicensesExpireDate[0]
          data.drivingLicensesExpireDate = this.drivingLicensesExpireDate && this.drivingLicensesExpireDate[1]
        }
      }

      if (this.isIdLong) {
        data.idCardExpireDate = '9999-12-31 00:00:00'
      } else {
        data.idCardExpireDate = this.form.idCardExpireDate
      }
      return data
    },
    pass() {
      this.$refs.form.validate(valid => {
        if (!valid) return

        this.$confirm('确认通过审核')
          .then(() => {
            let data = this.getParams()

            if (this.isDriver) {
              Object.assign(data, {
                driverAuthStatus: 2,
                priority: 0
              })
            } else {
              Object.assign(data, {
                carOwnerPriority: 0,
                carOwnerAuthStatus: 2
              })
            }

            this.isLoading = true
            this.$post('/admin-center-server/verify', data)
              .then(res => {
                this.$message.success('操作成功')
                this.getDriverauthapplychannel()
                setTimeout(() => {
                  window.close()
                }, 1000)
              })
              .finally(() => {
                this.isLoading = false
              })
          })
      })
    },
    reject() {
      let rejectForm = this.rejectForm
      if (rejectForm.region.length === 0 && rejectForm.textarea === '') {
        this.$message.error('请选择或输入驳回原因');
        return
      }

      let selectReason = rejectForm.region.join(',')
      let rejectReason = ''
      if (selectReason !== '' && rejectForm.textarea !== '') {
        rejectReason = selectReason + ',' + rejectForm.textarea
      } else {
        if (selectReason === '') {
          rejectReason = rejectForm.textarea
        } else {
          rejectReason = selectReason
        }
      }

      let num
      let params = this.getParams()
      if (this.isDriver) {
        if (rejectForm.brokerAuthStatus == 6) {
          num = 7
        } else {
          num = 3
        }
        Object.assign(params, {
          driverAuthStatus: num,
          rejectReason: rejectReason,
          priority: 2
        })
      } else {
        if (rejectForm.authStatus == 6) {
          num = 7
        } else {
          num = 3
        }
        Object.assign(params, {
          carOwnerAuthStatus: num,
          rejectReason: rejectReason,
          carOwnerPriority: 2
        })
      }
      this.isLoading = true
      this.$post('/admin-center-server/verify', params)
        .then(() => {
          this.$message.success('操作成功')
          this.getDriverauthapplychannel()
          setTimeout(() => {
            window.close()
          }, 1000)
        })
        .finally(() => {
          this.isLoading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  margin: 10px;
  padding: 10px;
  background: #fff;
}
.audit-top {
  // margin: 30px -10px 0px;
  position: fixed;
  top: 142px;
  left: 210px;
  right: 25px;
  z-index: 99;
  // background-color: rgb(249, 249, 249);
  background-color: white;
  .top-content {
    background-color: rgb(249, 249, 249);
    margin:0px 1px;
  }
  .top-title {
    padding: 20px;
    font-size: 18px;
    font-weight: 800;
  }
  .top-sn {
    font-size: 18px;
    font-weight: 400;
  }

  .top-button {
    display: flex;
    .top-button-info {
      width: 108px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
    }
    .top-button-log {
      width: 108px;
      text-align: center;
      height: 30px;
      line-height: 30px;
      cursor: pointer;
    }

    .top-button-select {
      background-color: white;
    }
  }
}

.audit-bottom {
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 100;
  box-sizing: border-box;
  width: calc(100% - 180px);
  height: 80px;
  padding: 10px 60px 0px;
  background: #fff;
  display: flex;
  align-items: center;
  span {
    flex-grow: 1;
  }
}
.left {
  margin-top: 140px;
  padding-bottom: 40px;
  width: 40%;
}
.right {
  margin-top: 160px;
}
.auto-base {
  position: fixed;
    top: 93px;
    left: 210px;
    right: 20px;
    z-index: 100;
    padding-left: 5px;
    background-color: rgb(250, 250, 250);
}

::v-deep .el-collapse-item {
  background-color: rgb(250, 250, 250);
}
.auto {
  padding: 10px 0;
  height: 400px;
  background: rgb(250, 250, 250);
  overflow: scroll;
}
.auto-content {
  display: flex;
  align-items: center;
  margin-left: 10px;
  line-height: 50px;
  height: 50px;
}

.auto-display-text {
  margin-left: 35px;
  font-size: 14px;
  margin-top: 5px;
}

.auto-text {
  margin-left: 5px;
  font-size: 16px;
}
.audit {
  min-height: 800px;
  margin-top: 20px;
}
.form {
  margin-top: 20px;
  .el-input {
    width: 280px;
  }
}
.long {
  margin-left: 15px;
}
.expire-days {
  color: rgb(51, 51, 51);
}
.abnormal {
  margin-top: 15px;
  .el-checkbox {
    margin-right: 10px;
  }
}
.expire-text {
  color: #D92929;
}
.driving_licenses_expireDate {
  display: flex;
  .el-date-picker {
    width: 140px;
  }
}
</style>