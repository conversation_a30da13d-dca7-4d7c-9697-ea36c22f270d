<template>
    <!-- 为联调字段，获取的为相邻兄弟页面数据，仅供展示使用 ！！！！！！！！！！！！ -->
    <div class="app-container carsList">
        <div class="select-box">
            <div class="top-title">查询</div>
            <div class="select-info">
                <el-form
                    :inline="true"
                    :model="queryParams"
                    class="demo-form-inline"
                    label-width="100px"
                >
                    <el-form-item label="上报状态:">
                        <el-select
                            clearable
                            v-model="queryParams.reportType"
                            placeholder="请选择上报状态"
                        >
                            <el-option
                                v-for="(item, index) in typeList"
                                :key="index"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleQuery"
                            >查询</el-button
                        >
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="list-box">
            <div class="list-title">
                <div>运单列表</div>
            </div>
            <div class="list-main">
                <template>
                    <el-table
                        v-loading="loading"
                        :data="tableData"
                        border
                        style="width: 100%"
                        :max-height="tableHeight"
                        ref="table"
                        :cell-style="{
                            'text-align': 'center',
                        }"
                    >
                        <template v-for="(item, index) in tableColumn">
                            <el-table-column
                                v-if="item.prop != 'operation' && !item.hidden"
                                :key="index"
                                :label="item.label"
                                :prop="item.prop"
                                :width="item.width"
                            >
                            </el-table-column>
                            <el-table-column
                                v-if="item.prop == 'operation' && !item.hidden"
                                :key="index"
                                :label="item.label"
                                :prop="item.prop"
                                :width="item.width"
                                fixed="right"
                            >
                                <template slot-scope="scope">
                                    <el-button
                                        type="text"
                                        @click="handleUpdate(scope.row)"
                                        >修 改</el-button
                                    >
                                </template>
                            </el-table-column>
                        </template>
                    </el-table>
                    <div
                        class="page"
                        style="margin-top: 10px; text-align: right"
                    >
                        <el-pagination
                            @size-change="getList"
                            @current-change="getList"
                            :current-page.sync="queryParams.pageNumber"
                            :page-sizes="[20, 40, 60, 80, 100]"
                            :page-size.sync="queryParams.pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total"
                        ></el-pagination>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>
  
  <script>
export default {
    name: "regulatoryReporting",
    data() {
        return {
            queryParams: {
                reportType: undefined,
                pageNumber: 1,
                pageSize: 20,
            },
            loading: true,
            tableHeight: null, //表格的高度
            tableData: [],
            tableColumn: [
                {
                    label: "上报时间",
                    prop: "vehiclenumber",
                    width: "",
                    hidden: false,
                },
                {
                    label: "运单号",
                    prop: "licenseplatetypecode",
                    width: "",
                    hidden: false,
                },
                {
                    label: "上报状态",
                    prop: "operateName",
                    width: "100",
                    hidden: false,
                },
                {
                    label: "司机上报失败原因",
                    prop: "createddate",
                    width: "",
                    hidden: false,
                },
                {
                    label: "车辆上报失败原因",
                    prop: "lastmodifieddate",
                    width: "",
                    hidden: false,
                },
                {
                    label: "资金流水单失败原因",
                    prop: "vehiclenumber",
                    width: "",
                    hidden: false,
                },
                {
                    label: "运单上报失败原因",
                    prop: "vehiclenumber",
                    width: "",
                    hidden: false,
                },
                {
                    label: "操作",
                    prop: "operation",
                    width: "",
                    hidden: true,
                },
            ],
            total: null, //总数
            typeList: [
                {
                    value: 1,
                    label: "成功",
                },
                {
                    value: 2,
                    label: "失败",
                },
            ],
        };
    },

    /** 事件监听 */
    watch: {},

    /** 计算属性 */
    computed: {},

    /** 生命周期 -- 实例创建后调用 */
    created() {},

    /** 生命周期 -- 实例挂载后调用 */
    mounted() {
        this.init();
        this.tableHeight =
            window.innerHeight - this.$refs.table.$el.offsetTop - 170;
    },

    /** 生命周期 -- 被 keep-alive 缓存的组件激活时调用 */
    activated() {},

    /** 生命周期 -- 被 keep-alive 缓存的组件失活时调用 */
    deactivated() {},

    /** 生命周期 -- 实例销毁后调用 */
    destroyed() {},

    methods: {
        /** 页面初始化 */
        init() {
            this.getList();
        },

        /** 获取数据列表 **/
        getList() {
            this.loading = true;
            this.$get(
                "/admin-center-server/admin/db_car/list",
                this.queryParams
            ).then((res) => {
                this.loading = false;
                this.tableData = res.list;
                this.total = JSON.parse(res.total);
            });
        },

        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNumber = 1;
            this.getList(); //获取查询数据
        },
    },
};
</script>
  
<style rel="stylesheet/scss" lang="scss" scoped>
.el-form-item {
    margin-bottom: 0;
}

.carsList {
    .select-box {
        /*height: 260px;*/
        background-color: #ffffff;

        .top-title {
            font-size: 16px;
            font-weight: 700;
            height: 40px;
            line-height: 40px;
            padding-left: 10px;
            border-bottom: 1px solid #cccccc;
        }

        .select-info {
            padding-top: 30px;
            padding-bottom: 30px;
        }
    }

    .list-box {
        background-color: #ffffff;
        margin-top: 20px;
        padding: 10px;

        .list-title {
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            div {
                height: 38px;
                line-height: 38px;
            }
        }
        .page {
            text-align: right;
        }
        .list-main {
            width: 100%;
            border: 1px solid #cccccc;
            margin-top: 10px;
        }
    }
}
</style>
  