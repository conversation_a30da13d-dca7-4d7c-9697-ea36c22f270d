<template>
    <div class="app-container framework">
        <div class="select-box">
            <div class="top-title">
                筛选查询
            </div>
            <div class="select-info">
                <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="150px" size="mini">
                    <el-form-item label="部门名称:">
                        <el-input v-model="formInline.depName" placeholder="请输入部门名称"></el-input>
                    </el-form-item>
                    <el-form-item label="部门代码:">
                        <el-input v-model="formInline.depNo" placeholder="请输入部门代码"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onSubmit" size="mini"   icon="el-icon-search">查询</el-button>
                        <el-button
                                class="left"
                                icon="el-icon-delete"
                                size="mini"
                                type="danger"
                                @click="resetSubmit"
                        >清空筛选</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="list-box">
            <div class="list-title">
                <div></div>
                <div>
                    <el-button type="primary"  icon="el-icon-plus" @click="goAdd" size=mini>新增组织架构</el-button>
                </div>
            </div>
            <el-row>
                <el-col :span="4">
                    <div class="list-left">
                        <div class="left-title">
                            组织架构树
                        </div>
                        <div class="tree-box">
                            <el-tree
                                    :data="treeData"
                                    highlight-curren
                                    check-strictly
                                    ref="tree"
                                    :render-content="renderContent"
                                    @node-click="handleNodeClick">
                            </el-tree>
                        </div>
                    </div>
                </el-col>
                <el-col :span="20">
                    <div class="list-right">
                        <div class="right-title">
                            系统用户信息表
                        </div>
                        <div class="list-main">
                            <template>
                                <el-table
                                        :data="tableData"
                                        border
                                        style="width: 100%"
                                        height="600">
                                    <el-table-column
                                            label="序号"
                                            type=index
                                            width="50">
                                    </el-table-column>
                                    <el-table-column
                                            prop="depName"
                                            label="部门名称"
                                            width="100"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            prop="depNo"
                                            label="部门代码"
                                            width="150"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            prop="chargeName"
                                            label="负责人"
                                            width="150"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            prop="chargePost"
                                            label="负责人职位"
                                            width="150"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            prop="chargePhone"
                                            label="负责人电话"
                                            width="150"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            prop="depCreateTime"
                                            label="部门创建时间"
                                            width="150"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            prop="remarks"
                                            label="备注"
                                            width="150"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            prop="billModels"
                                            label="是否可进行开票"
                                            :formatter="isbillModels"
                                            width="150"
                                    >
                                    </el-table-column>
                                    <el-table-column
                                            fixed="right"
                                            label="操作"
                                            width="100">
                                        <template slot-scope="scope">
                                            <el-button @click="handleClick(scope.row)" type="text" size="small">编辑
                                            </el-button>
                                            <el-button type="text" size="small" @click="deleteRow(scope.row)">删除
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </template>
                        </div>
                        <div class="paging">
                            <div class="block">
                                <el-pagination
                                        @size-change="handleSizeChange"
                                        @current-change="handleCurrentChange"
                                        :current-page="currentPage"
                                        :page-sizes="[10, 20, 30, 40]"
                                        :page-size="10"
                                        layout="total, sizes, prev, pager, next, jumper"
                                        :total=total>
                                </el-pagination>
                            </div>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </div>
        <!-- 删除列表弹窗-->
        <el-dialog
                title="警告提示"
                :visible.sync="dialogVisible"
                width="30%"
                :before-close="handleClose">
            <div>
                <!-- <span style="color: red">树必须有一个被选中</span><br/> -->
                <span style="color: red">若删除该部门，则该部门下的所有系统用户则全部删除，是否确认删除？</span>
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button @click="dialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="deletcSure">确 定</el-button>
  </span>
        </el-dialog>
        <!-- 编辑列表弹窗-->
        <el-dialog
                title="提示"
                :visible.sync="dialogChange"

                :before-close="handleClose">
            <div>
                <div class="tip">
                    <div>编辑组织架构</div>
                    <div><em style="color: red">*</em>为必填项</div>
                </div>
                <div class="inner-box">
                    <el-form :model="formChange" :rules="rules" ref="formChange" label-width="200px" class="demo-ruleForm">
                        <el-form-item label='部门代码:' required>
                            <el-input v-model="formChange.depNo" style="width: 220px"  :disabled="true"></el-input>
                        </el-form-item>
                        <el-form-item label="部门名称:" required>
                            <el-input v-model="formChange.depName" style="width: 220px"
                                      placeholder="请输入部门名称"></el-input>
                        </el-form-item>
                        <el-form-item label="负责人:" required>
                            <el-input v-model="formChange.chargeName" style="width: 220px" placeholder="请输入负责人"></el-input>
                        </el-form-item>
                        <el-form-item label="负责人电话:" required>
                            <el-input v-model="formChange.chargePhone" style="width: 220px"
                                      placeholder="请输入负责人电话"></el-input>
                        </el-form-item>
                        <el-form-item label="负责人职位:" required>
                            <el-input v-model="formChange.chargePost" style="width: 220px"
                                      placeholder="请输入负责人职位"></el-input>
                        </el-form-item>
                        <el-form-item label="部门创建时间:">
                            <el-date-picker type="date"
                                placeholder="选择日期"
                                v-model="formChange.depCreateTime"
                                style="width: 220px"
                                format="yyyy 年 MM 月 dd 日"
                                value-format="yyyy-MM-dd"
                             ></el-date-picker>
                        </el-form-item>
                        <el-form-item label="备注:">
                            <el-input v-model="formChange.remarks" style="width: 220px"></el-input>
                        </el-form-item>
                        <el-form-item label="是否可进行开票:">
                            <el-switch
                              :value="formChange.billModels === '1' || formChange.billModels === true"
                              @change="handleSwitchChange"
                              active-color="#13ce66"
                              inactive-color="#ff4949"
                              active-text="是"
                             inactive-text="否">
                            </el-switch>
                        </el-form-item>
                        <el-form-item>
                            <el-button :loading="loading" type="primary" @click="submitForm" style="width: 220px">保存</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
<!--            <span slot="footer" class="dialog-footer">-->
<!--              <el-button @click="dialogChange = false">取 消</el-button>-->
<!--            </span>-->
        </el-dialog>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                loading:false,
                changeId:'',
                deleteId:'',
                currentPage: 1,
                pageSize: 10,
                total: 1,
                dialogVisible: false,
                dialogChange: false,
                treeId:'',
                formChange:{},
                formInline: {
                    depName: '',
                    depNo: '',

                },
                tableData: [],
                treeData: [],

                rules: {
                    name: [
                        {required: true, message: '请输入活动名称', trigger: 'blur'},
                        {message: '长度在 3 到 5 个字符', trigger: 'blur'}
                    ],
                    region: [
                        {required: true, message: '请选择车型', trigger: 'change'}
                    ],
                    zongzhong: [
                        {required: true, message: '填写总重', trigger: 'blur'},
                    ],
                    zaizhong: [
                        {required: true, message: '填写载重', trigger: 'blur'},
                    ]
                }
            }
        },
        methods: {
             /** 分页方法 **/
            handleSizeChange(val) {
                // console.log(`每页 ${val} 条`);
                this.pageSize = val;
                 this.currentPage = 1;
                this.getDataList()
            },
            handleCurrentChange(val) {
                // console.log(`当前页: ${val}`);
                this.currentPage = val;
                this.getDataList()
            },
            /** 是否可进行开票 **/
            isbillModels(row){
                if(row.billModels==='0'){
                    return '不可开票'
                }else if(row.billModels==='1'){
                    return '可开票'
                }
            },
            /** 架构树 添加图标  **/
            renderContent(h, { node, data, store }){
                   return(
                       <span>
                       <i class={data.icon}></i>
                       <span> {node.label}</span>
                       </span>
                   )
            },
            handleNodeClick(data) {
                let treeId = data.id;
                this.formInline.depName = data.label;
                this.treeId=treeId
                this.getDataList()
            },
            /** 确认删除 **/
            deletcSure(){
                let id  = this.deleteId;
                 this.$http.post('/admin-center-server/sys/deleteDepartment?id='+id).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                       this.$message.success('删除成功');
                        this.dialogVisible = false;
                        this.getDataList();
                        this.getTreeData();
                    }else {
                        this.$message.warning(data.message)
                    }
                })
            },
            /** 删除行 **/
            deleteRow(row) {
                 this.dialogVisible = true;
                let id = row.id;

                this.deleteId = id
            },
            /** 弹窗 上叉号关闭 支持异常回调**/
            handleClose(done) {
                this.dialogVisible = false;
                this.dialogChange = false
            },
            /** 编辑行 同新增参数 **/
            handleClick(row) {

                let id = row.id;
                this.changeId = id;
                this.$http.get('/admin-center-server/sys/getDepartmentInfo',{
                    params:{
                        id:id
                    }
                }).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                      this.dialogChange = true;

                      this.formChange={
                          depNo:data.data.depNo,
                          depName:data.data.depName,
                          chargeName:data.data.chargeName,
                          chargePhone:data.data.chargePhone,
                          chargePost:data.data.chargePost,
                          depCreateTime:data.data.depCreateTime,
                          remarks:data.data.remarks,
                          billModels:data.data.billModels
                      }
                    }

                })
            },
            /** 保存修改 **/
            submitForm() {
                this.loading=true;
                let form = this.formChange;
                if(form.billModels===false){
                    form.billModels='0'
                }else if(form.billModels===true){
                    form.billModels='1'
                }
                form.id=this.changeId;
                this.$http.post('/admin-center-server/sys/updateDepartment',form).then(res=>{
                      let data = res.data;
                      if(data.code ==='200'){
                           this.$message.success('修改成功');
                           this.loading=false;
                           this.dialogChange = false;
                           this.getDataList();
                           this.getTreeData()
                      }else {
                          this.loading=false;
                          this.$message.warning(data.message)
                      }
                })
            },


            onSubmit() {
                this.currentPage = 1;
                this.getDataList()
            },
            /** 清空搜索选项 **/
            resetSubmit() {
                this.formInline.depName='';
                 this.formInline.depNo='';
                this.getDataList();
            },
            goAdd() {
                if(this.treeId===''){
                    this.$message.warning('请选择一个组织架构')
                }else{
                   sessionStorage.setItem('curPid',this.treeId);
                   this.$router.push('addFramework')
                }

            },
            /** 获取部门列表 **/
            getDataList(){
                this.$http.get('/admin-center-server/sys/getDepartmentList',{
                    params:{
                        depName:this.formInline.depName,
                        depNo:this.formInline.depNo,
                        pageNumber: this.currentPage,
                        pageSize: this.pageSize,
                    }
                }).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                        this.tableData=data.data.list;
                        this.total = Number(data.data.total)
                    }
                })
            },
            /** 获取部门树 **/
            getTreeData(){
              this.$http.get('/admin-center-server/sys/getDepTree').then(res=>{
                  let data=res.data;

                  if(data.code==='200'){
                     let tree = data.data;
                     this.treeData=tree
                  }
              })
            },
            handleSwitchChange(v) {
                this.formChange.billModels = v
            }
        },
        activated(){
            this.getDataList()
            this.getTreeData()
        }
    }
</script>
<style>
.el-tree-node:focus > .el-tree-node__content {
    background-color: rgb(77,144,254) !important;
    color: #fff
  }
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
    .tip {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 40px 40px 0;
        font-size: 12px;

        em {
            margin-right: 5px;
        }
    }

    .inner-box {
        margin-left: 10%;
        width: 70%;

        .upload-box {
            width: 100%;
            height: 100%;
            position: relative;

            .icon-XZ {
                width: 92px;
                height: 92px;
                margin: 0 auto;
                background-size: 100% 100%;
            }

            .icon-word {
                width: 100%;
                height: 20px;
                line-height: 20px;
                font-size: 10px;
                position: absolute;
                bottom: 25px;
                left: 0px;
                color: #cccccc;
            }
        }
    }

    .framework {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
            }

            .select-info {
                padding-top: 30px;
                padding-bottom: 30px;
            }
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;
            overflow: hidden;

            .list-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                div {
                    height: 38px;
                    line-height: 38px;
                }
            }

            .list-left {
                .left-title {
                    font-size: 12px;
                    font-weight: 700;
                }

                .tree-box {
                    border: 1px solid #000000;
                    margin-top: 12px;
                }
            }

            .list-right {
                padding-left: 10px;

                .right-title {
                    font-size: 12px;
                    font-weight: 700;
                }

                .list-main {
                    width: 100%;
                    border: 1px solid #cccccc;
                    margin-top: 10px;
                }

                .paging {
                    margin-top: 10px;
                    float: right;
                }
            }

        }
    }
</style>
