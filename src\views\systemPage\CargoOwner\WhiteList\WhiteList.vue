<template>
  <div class="whiteList">
    <el-form :rules="rules" ref="form" label-width="250px">
      <el-form-item label="允许操作司机认证的司机和车辆：" prop="ownerList">
        <el-switch v-model="isWhiteOpen" @change="handleWhiteSwitchChange"></el-switch>
        <div v-if="isWhiteOpen">
          <el-select multiple v-model="selectedOwner" filterable class="select">
            <el-option v-for="item in ownerList" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button @click="save" type="primary">保存</el-button>
      </el-form-item>
    </el-form>

    <el-form :rules="platformRules" ref="platformForm" label-width="250px">
      <el-form-item label="允许使用平台油耗：" prop="ownerPlatformList">
        <el-switch v-model="isPlatformWhiteOpen" @change="handlePlatformWhiteSwitchChange"></el-switch>
        <div v-if="isPlatformWhiteOpen">
          <el-select multiple v-model="selectedPlatformOwner" filterable class="select">
            <el-option v-for="item in ownerList" :value="item.id" :label="item.name" :key="item.id"></el-option>
          </el-select>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button @click="savePlatform" type="primary">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      ownerList: [],
      selectedOwner: [], // 允许操作司机认证的司机和车辆
      isWhiteOpen: false,
      selectedPlatformOwner: [], // 允许使用平台油耗
      isPlatformWhiteOpen: false,
      rules: {
        ownerList: [
          { 
            validator: (rule, value, cb) => {
              if (this.isWhiteOpen && this.selectedOwner.length === 0) {
                cb('请选择')
              }
              cb()
            },
            trigger: 'blur'
          }
        ]
      },
      platformRules: {
        ownerPlatformList: [
          { 
            validator: (rule, value, cb) => {
              if (this.isPlatformWhiteOpen && this.selectedPlatformOwner.length === 0) {
                cb('请选择')
              }
              cb()
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created() {
    this.$get('/admin-center-server/businessSetting/getBusinessList')
      .then(res => {
        this.ownerList = res
        return this.$get('/admin-center-server/businessSetting/getBusinessWhiteList')
      })
      .then(res => {
        // 1.允许操作司机认证的司机和车辆
        if (res[0]) {
          this.selectedOwner = res[0].userIds ? res[0].userIds.split('&') : []
          this.isWhiteOpen = res[0].enable === '1' ? true : false
        } else {
          this.selectedOwner = []
          this.isWhiteOpen = false
        }
        // 2.允许使用平台油耗
        if (res[1]) {
          this.selectedPlatformOwner = res[1].userIds ? res[1].userIds.split('&') : []
          this.isPlatformWhiteOpen = res[1].enable === '1' ? true : false
        } else {
          this.selectedPlatformOwner = []
          this.isPlatformWhiteOpen = false
        }
      })
  },
  methods: {
    save() {
      this.$refs.form.validateField('ownerList', valid => {
        if (valid !== '') return
        let params = {
          functionCode: '1'
        }
        if (this.isWhiteOpen) {
          params.enable = '1'
          params.userIds = this.selectedOwner.join('&')
        } else {
          params.enable = '0'
        }
        this.$post('/admin-center-server/businessSetting/updateBusinessWhiteList', params)
          .then(() => {
            this.$message.success('保存成功')
          })
      })
    },
    handleWhiteSwitchChange() {
      //关闭时，移除必选验证
      if (!this.isWhiteOpen) {
        this.$refs.form.clearValidate('ownerList')
      } 
    },
    savePlatform() {
      this.$refs.platformForm.validateField('ownerPlatformList', valid => {
        if (valid !== '') return
        let params = {
          functionCode: '2'
        }
        if (this.isPlatformWhiteOpen) {
          params.enable = '1'
          params.userIds = this.selectedPlatformOwner.join('&')
        } else {
          params.enable = '0'
        }
        this.$post('/admin-center-server/businessSetting/updateBusinessWhiteList', params)
          .then(() => {
            this.$message.success('保存成功')
          })
      })
    },
    handlePlatformWhiteSwitchChange() {
      //关闭时，移除必选验证
      if (!this.isWhiteOpen) {
        this.$refs.form.clearValidate('ownerPlatformList')
      }
    }
  }
}
</script>

<style lang="scss">
.whiteList {
  padding: 20px;
  margin: 10px;
  background-color: #fff;
}
.select {
  width: 500px;
}
</style>