<template>
  <div>
    <div class="list-info">
      <el-form ref="form" label-width="110px" :inline="true" size="mini">
        <el-form-item label="保单号:">
          <el-input class="form-item-content-width" v-model="search.policyNo" placeholder="请输入保单号"></el-input>
        </el-form-item>
        <el-form-item label="运单号:">
          <el-input class="form-item-content-width" v-model="search.orderItemSn" placeholder="请输入运单号"></el-input>
        </el-form-item>
        <el-form-item label="司机姓名:">
          <el-input class="form-item-content-width" v-model="search.driverName" placeholder="请输入司机姓名"></el-input>
        </el-form-item>
        <el-form-item label="司机手机号:">
          <el-input class="form-item-content-width" v-model="search.driverPhone" placeholder="请输入司机手机号"></el-input>
        </el-form-item>
        <el-form-item label="车牌号:">
          <el-input class="form-item-content-width" v-model="search.carNumber" placeholder="请输入车牌号"></el-input>
        </el-form-item>
        <el-form-item label="投保状态:">
          <el-select
            class="form-item-content-width"
            v-model="search.status"
            placeholder="请选择"
            clearable
          >
            <el-option label="已投保" value="2"></el-option>
            <el-option label="已取消" value="5"></el-option>
            <el-option label="投保失败" value="9"></el-option>
            <el-option label="全部" value=""></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="投保方式:">
          <el-select
            class="form-item-content-width"
            v-model="search.insuranceChannel"
            placeholder="请选择"
            clearable
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="线上" value="0"></el-option>
            <el-option label="线下" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保司:">
          <el-input class="form-item-content-width" v-model="search.insuranceCompanyName" placeholder="请输入保司"></el-input>
        </el-form-item>
        <el-form-item label="投保货物:">
          <el-input class="form-item-content-width" v-model="search.goodName" placeholder="请输入投保货物"></el-input>
        </el-form-item>
        <el-form-item label="险种:">
          <el-select
            class="form-item-content-width"
            v-model="search.insuranceType"
            placeholder="请选择"
            clearable
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="基本险" value="0"></el-option>
            <el-option label="综合险" value="1"></el-option>
            <el-option label="单车货运险" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="投保时间:">
          <el-date-picker
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            v-model="createDate"
            :default-time="['00:00:00', '23:59:59']"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="取消投保时间:">
          <el-date-picker
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            v-model="cancelDate"
            :default-time="['00:00:00', '23:59:59']"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="doSearch">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="insure-amount">
      <el-button class="button" type="primary" @click="addInsure">添加保单</el-button>
      <span style="color:red">￥{{insurancePolicyListSum}}</span>
      当前列表“保单保费”合计：
    </div>
    <div class="list-box">
      <el-table
        v-loading="loading"
        :data="data"
        style="width: 100%"
        :max-height="tableHeight"
        ref="table"
        :cell-style="{ 'text-align': 'center', border: '0.5px solid #EAF0FB' }"
        :header-cell-style="{
          'text-align': 'center',
          border: '0.3px solid #EAF0FB',
          'background-color': '#F5F6F9',
          height: '60px',
        }"
      >
        <el-table-column fixed type="index" label="序号" width="50"></el-table-column>
        <el-table-column fixed label="保单号" prop="policyNo"></el-table-column>
        <el-table-column fixed label="保单保费(元)" prop="sumpremium"></el-table-column>
        <el-table-column fixed label="账户剩余保费(元)">
          <template slot-scope="scope">
            <template v-if="scope.row.residualpremium && scope.row.sumpremium">
              {{ Big(scope.row.residualpremium).minus(scope.row.sumpremium).toNumber() }}
            </template>
          </template>
        </el-table-column>
        <el-table-column fixed label="运单号" prop="sn">
          <template slot-scope="scope">
            <div class="helight-text"
            @click="$router.push('/transport/transportListDetail?orderItemId=' + scope.row.orderItemId)"
            >{{scope.row.sn}}</div>
          </template>
        </el-table-column>
        <el-table-column label="装货地" prop="placeOfLoad"></el-table-column>
        <el-table-column label="卸货地" prop="placeOfUnload"></el-table-column>
        <el-table-column label="司机姓名" prop="driverName">
          <template slot-scope="scope">
            <div class="helight-text"
            @click="$router.push('/driverAccount/accountDetails?id=' + scope.row.driverId + '&type=3')"
            >{{scope.row.driverName}}</div>
          </template>
        </el-table-column>
        <el-table-column label="司机手机号" prop="driverPhone"></el-table-column>
        <el-table-column label="保司" prop="insuranceCompanyName"></el-table-column>
        <el-table-column label="投保货物" prop="goodName"></el-table-column>
        <el-table-column label="险种" prop="insuranceType">
          <template scope="scope">
            <span v-if="scope.row.insuranceType == 0">基本险</span>
            <span v-else-if="scope.row.insuranceType == 1">综合险</span>
            <span v-else-if="scope.row.insuranceType == 2">单车货运险</span>
          </template>
        </el-table-column>
        <el-table-column label="车牌号" prop="carNumber">
          <template slot-scope="scope">
            <div class="helight-text"
            @click="$router.push('/carsList/carDetail?carId=' + scope.row.carId)">{{scope.row.carNumber}}</div>
          </template>
        </el-table-column>
        <el-table-column label="投保状态" prop="statusStr" fixed="right">
          <template slot-scope="scope">
            <div :class="{'red-text': scope.row.status == 9, 'gray-text': scope.row.status == 5}">{{scope.row.statusStr}}</div>
          </template>
        </el-table-column>
        <el-table-column label="投保方式" fixed="right" prop="insuranceChannelStr"></el-table-column>
        <el-table-column label="投保时间" fixed="right" prop="createdTime"></el-table-column>
        <el-table-column label="标记取消投保时间" fixed="right" prop="cancelInsureTime"></el-table-column>
        <el-table-column label="标记取消投保人" fixed="right" prop="cancelUserName"></el-table-column>
        <el-table-column fixed="right" label="操作" width="150">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status == 2"
              type="text"
              @click="openUrl(scope.row.policyOfInsuranceUrl)"
              >电子保单</el-button
            >
            <el-button
            v-if="scope.row.status == 2"
              type="text"
              @click="sign(scope.row)"
              >标记取消投保</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 10px; text-align: right">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="getList"
          :current-page.sync="page.pageNumber"
          :page-sizes="[10, 20, 40, 60, 80, 100]"
          :page-size="page.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
        ></el-pagination>
      </div>
    </div>
    <AddInsureDialog :show.sync="addInsureDialog" @addSuccess="getList"></AddInsureDialog>
  </div>
</template>

<script>
import { Big } from "big.js";
import AddInsureDialog from './components/addInsureDialog.vue';
export default {
  components: {
    AddInsureDialog
  },
  data() {
    return {
      search: {
        cancelInsureTimeEnd: "",
        cancelInsureTimeStart: "",
        carNumber: "",
        createdTimeEnd: "",
        createdTimeStart: "",
        driverName: "",
        driverPhone: "",
        policyNo: "",
        status: "",
        orderItemSn: "",
        goodName: '',
        insuranceCompanyName: '',
        insuranceType: '',
        insuranceChannel:''
      },
      isSearch: false,
      createDate: null,
      cancelDate: null,
      page: {
        pageNumber: 1,
        pageSize: 10,
      },
      data: [],
      total: 0,
      isAllocationShow: false,
      cardNumber: "",
      loading: true,
      tableHeight: null,
      addInsureDialog: false,
      insurancePolicyListSum: ''
    };
  },
  activated() {
    this.getList();
    this.tableHeight =
      window.innerHeight - this.$refs.table.$el.offsetTop - 170;
  },
  methods: {
    Big,
    getList() {
      this.loading = true;
      let params = { ...this.page };
      if (this.isSearch) {
        Object.assign(params, this.search);
        if (this.createDate !== null) {
          params.createdTimeStart = this.createDate[0];
          params.createdTimeEnd = this.createDate[1];
        } else {
          params.createdTimeEnd = null;
          params.createdTimeStart = null;
        }
        if (this.cancelDate !== null) {
          params.cancelInsureTimeStart = this.cancelDate[0];
          params.cancelInsureTimeEnd = this.cancelDate[1];
        } else {
          params.cancelInsureTimeStart = null;
          params.cancelInsureTimeEnd = null;
        }
      } else {
        params.status = this.search.status;
      }
      this.$post(
        "/admin-center-server/mlww/admin/getInsurancePolicyList",
        params
      ).then((res) => {
        this.data = res.list;
        this.total = Number(res.total);
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
      this.$post(
        "/admin-center-server/mlww/admin/getInsurancePolicyListSum",
        params
      ).then((res) => {
        let sum =Number(res).toFixed(2)
        this.insurancePolicyListSum = sum
      });
    },
    doSearch() {
      this.page.pageNumber = 1;
      this.isSearch = true;
      this.getList();
    },
    reset() {
      this.search = {
        cancelInsureTimeEnd: "",
        cancelInsureTimeStart: "",
        carNumber: "",
        createdTimeEnd: "",
        createdTimeStart: "",
        driverName: "",
        driverPhone: "",
        policyNo: "",
        status: "",
        orderItemSn: "",
        goodName: '',
        insuranceCompanyName: '',
        insuranceType: '',
        insuranceChannel: ''
      };
      this.createDate = null;
      this.cancelDate = null
      this.page.pageNumber = 1;
      this.isSearch = false;
      this.getList();
    },
    handleSizeChange(v) {
      this.page.pageSize = v;
      this.getList();
    },
    openUrl(url) {
      if (typeof url === 'string' && url !== '') {
        // location.href = url
        window.open(url)
      }
    },
    sign(row) {
      
      this.$confirm('标记取消投保前，请确保保险公司已取消本次投保。确定继续操作？', '提示', {
        type: 'warning'
      }).then(() => {
          this.$get('admin-center-server/mlww/admin/cancelInsure', {policyId: row.id}).then(
            res => {
              this.$message.success('操作成功')
              this.getList()
            }
          )
      })
    },
    addInsure() {
      this.addInsureDialog = true
    }
  },
};
</script>

<style lang="scss" scoped>
.insure-amount {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  .button {
    margin-left: 8px;
    margin-right: 20px;
  }
}
.list-info {
    padding: 10px;
    margin: 20px;
    background-color: white;
}
.list-box {
  background-color: #ffffff;
  margin: 20px;
  padding: 10px;

  .list-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    div {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
    }
  }

  .list-main {
    width: 100%;
    //   border: 1px solid #cccccc;
    margin-top: 10px;
  }

  .releaseMessage {
    margin-right: 20px;
  }

  .pagination {
    text-align: right;
    margin-top: 10px;
  }
}
.global-div-search {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  /* font-size: 14px; */
  /* color: #555; */
  position: relative;
}
.gray-status {
  color: #888;
}

.helight-text {
  color: #F6A018;
  cursor: pointer;
}

.red-text {
  color: #DD2042;
}

.gray-text {
  color: #888;
}
</style>