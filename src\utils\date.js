import { $post } from '@/utils/http2'
import dayjs from 'dayjs'
export function getCurrentTimesForElUIDefault() {
  let now = new Date()
  let year = now.getFullYear()
  let month = now.getMonth() + 1
  if (month < 10) month = '0' + String(month)
  let date = now.getDate()
  if (date < 10) day = '0' + String(day)
  return [`${year}-${month}-${date} 00:00:00`, `${year}-${month}-${date} 23:59:59`]
}

export function checkLess3Month(date1, date2) {
  //检测两个日期间隔是否小于三个月
  //同一年 结束月份 - 开始月份 或是 结束比开始晚一年 12 - 开始月份 + 结束月份
  //上述公式小于3 或是等于3时，结束日小于等于开始日
  date1 = new Date(date1)
  date2 = new Date(date2)
  let year1 = date1.getFullYear(),
    year2 = date2.getFullYear(),
    month1 = date1.getMonth() + 1,
    month2 = date2.getMonth() + 1,
    day1 = date1.getDate(),
    day2 = date2.getDate()

  let monthDiff
  if (year1 === year2) {
    monthDiff = month2 - month1
  } else if (year2 === year1 + 1) {
    monthDiff = 12 - month1 + month2
  } else {
    return false
  }

  if (monthDiff < 3 || (monthDiff === 3 && day1 >= day2)) {
    return true
  }
  return false
}

//-2不显示 -1 已过期 非负数 显示x天后到期
export function checkExpire(date, isMonth = false) {
  if (!date) {
    return Promise.resolve(-2)
  }
  date = dayjs(date)
  if (isMonth) {
    //设置为当月最后一天
    date = date.date(date.daysInMonth())
  }
  let now = dayjs(new Date).format('YYYY-MM-DD')
  let diff = date.diff(now, 'day')
  if (diff < 0) {
    return Promise.resolve(-1)
  }
  return new Promise(resolve => {
    //获取预警时间
    $post('/base-center-server/settings/get?group=Time&code=certEarlyWarnDays')
    .then(res => {
      let warnTime = Number(res.value)
      if (diff > warnTime) {
        resolve(-2)
      } else {
        resolve(diff)
      }
    })
  })
}