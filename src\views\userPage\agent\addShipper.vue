<template>
  <div>

    <el-form :model="ruleForm"
             :rules="rules"
             ref="ruleForm"
             label-width="150px"
             size="medium"
             class="demo-ruleForm">

      <el-form-item label="公司名称"
                    prop="shipName">
        <el-input v-model="ruleForm.name"
                  placeholder="请输入公司名称"></el-input>
      </el-form-item>
      <el-form-item label="手机号"
                    prop="shipTel">
        <el-input type="number"
                  v-model="ruleForm.tel"
                  placeholder="请输入手机号"></el-input>
      </el-form-item>

      <el-form-item label="登录密码"
                    prop="receiveName">
        <el-input v-model="ruleForm.pwd"
                  placeholder="请输入登录密码"></el-input>
      </el-form-item>
      <el-form-item label="统一社会信用代码"
                    prop="receiveTel">
        <el-input v-model="ruleForm.code"
                  placeholder="请输入统一社会信用代码"></el-input>
      </el-form-item>
      <el-form-item label="法人身份证照片:">
        <el-upload action="https://jsonplaceholder.typicode.com/posts/"
                   list-type="picture-card"
                   :on-preview="handlePictureCardPreview"
                   :on-remove="handleRemove">
          <div class="upload-box">
            <div class="icon-XZ"></div>
            <div class="icon-word">支持jpg、jpeg、png 格式不超过10M</div>
          </div>
        </el-upload>
        <el-dialog :visible.sync="dialogVisible"
                   size="tiny">
          <img width="100%"
               :src="dialogImageUrl"
               alt />
        </el-dialog>
      </el-form-item>
      <el-form-item label="营业执照:">
        <el-upload action="https://jsonplaceholder.typicode.com/posts/"
                   list-type="picture-card"
                   :on-preview="handlePictureCardPreview"
                   :on-remove="handleRemove">
          <div class="upload-box">
            <div class="icon-XZ"></div>
            <div class="icon-word">支持jpg、jpeg、png 格式不超过10M</div>
          </div>
        </el-upload>
        <el-dialog :visible.sync="dialogVisible"
                   size="tiny">
          <img width="100%"
               :src="dialogImageUrl"
               alt />
        </el-dialog>
      </el-form-item>

      <el-form-item label="营业期限至"
                    prop="date3">
        <el-date-picker v-model="ruleForm.date"
                        type="datetime"
                        placeholder="选择延迟付日期"
                        style="width: 100%;"> </el-date-picker>
      </el-form-item>
      <el-form-item label="是否启用账户">
        <el-switch v-model="ruleForm.type"></el-switch>
      </el-form-item>
      <el-form-item>
        <el-button type="primary"
                   @click="submitForm('ruleForm')">提交</el-button>

      </el-form-item>
    </el-form>

  </div>
</template>

<script>
export default {
  data () {
    return {
      ruleForm: {
        name: '',
        tel: '',
        pwd: '',
        code: '',
        type: '',
        date: ''
      },
      rules: {},
      dialogVisible: false,
      dialogImageUrl: "",
      //通讯录
    }  },
  methods: {
    handleRemove (file, fileList) {
      // console.log(file, fileList);
    },
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-ruleForm {
  width: 500px;
  margin-top: 20px;
}
.upload-box {
  width: 100%;
  height: 100%;
  /*border: 1px solid #00cb8a;*/
  position: relative;

  .icon-XZ {
    width: 92px;
    height: 92px;
    margin: 0 auto;
    background: url("../consignor/images/xiazai.png") no-repeat;
    background-size: 100% 100%;
  }

  .icon-word {
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 10px;
    position: absolute;
    bottom: 25px;
    left: 0px;
    color: #cccccc;
  }
}
</style>
