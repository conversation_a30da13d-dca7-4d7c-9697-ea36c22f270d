
<template>
  <div class="uploadTemp">
    <el-upload
      v-if="flag"
      class="avatar-uploader"
      action="#"
      :multiple="multiple"
      :before-upload="beforeAvatarUpload"
      :http-request="httpRequest"
      :show-file-list="false"
      :limit="imgLimit"
      :on-exceed="limitFn"
      v-loading="loading"
      :file-list="imgArr"
      drag
    >
      <i  class="el-icon-plus avatar-uploader-icon" ></i>
      <span class="limit-font">最多{{imgLimit}}张</span>
    </el-upload>
      <div class="imgContent1" :style="small?'width:calc(11% - 10px);':''" v-for="(item, index) in List" :key="index">
         <el-image
            :src="item"
            :class="small?'smallImg':'imgList'"
            :preview-src-list="List"
            lazy
          ></el-image>
        <div v-if="flag" class="closeBtn" @click="closeBtn(List, index)"></div>
    </div>
  </div>
</template>

<script>
import { upload as uploadFile } from '@/utils/file'
export default {
  props: {
    imgList: {},
    imgLimit: {
      default:9
    },
    authority: {
      default:false
    },
    once: {
      default: false,
    },
    ten: {
      default: false,
    },
    multiple:{
      default:true
    },
    small:{
      default:false
    }
  },
  model: {
    prop: "imgList",
    event: "successImg",
  },
  watch: {
    imgList: {
      handler(origin) {
        this.List = origin;
        if (origin.length > 0) {
          let arr = []
          origin.forEach((item) => {
            arr.push({name:'上传图片',url:item})
          });
          this.imgArr=arr
          console.log('imgArr',this.imgArr)
        }
      },
      immediate: true,
    },
    authority: {
      handler(origin) {
        this.flag = origin;
      },
      immediate: true,
    },
  },
  data() {
    return {
      flag: true,
      List: [],
      imgArr: [],
      loading:false
    };
  },
  methods: {
    limitFn(files, fileList){
      console.log('limitFn',files,fileList)
      if(files.length+fileList.length>this.imgLimit || (files.length + this.List.length)>this.imgLimit){
        // this.$message.error(`选择上传${files.length}张,最多上传${this.imgLimit-this.List.length}张图片`);
        this.$message.error(`选择上传${files.length}张,最多可上传${this.imgLimit}张图片`);
      }
    },
    // 文件上传处理
    httpRequest(options) {
      this.loading=true
      let that = this;
      if(that.List.length>this.imgLimit){
        this.$message.error(`最多上传${this.imgLimit}张图片`);
      }else{
        console.log('options',options)
        uploadFile(options.file).then((res) => {
          if (res && that.List.length<this.imgLimit) {
            that.loading = false;
            this.$emit('change', res.url)
          }else{
            this.$message.error(`最多上传${this.imgLimit}张图片`);
          } 
        });
      }
    },
    // 上传文件之前处理
    beforeAvatarUpload(file) {
      var testmsg = file.name
        .substring(file.name.lastIndexOf(".") + 1)
        .toLowerCase();
      const isJPG =
        testmsg === "jpg" || testmsg === "jpeg" || testmsg === "png";
      const isLimit = this.List.length < this.imgLimit || !this.imgLimit;
      if (!isJPG) {
        this.$message.error("请上传.png,.jpg,.jpeg格式的文件");
      }
      if (!isLimit) {
        this.$message.error("最多上传" + this.imgLimit + "张图片");
      } 
      console.log('this.List.length',this.List.length)
      return isJPG && isLimit;
    },
    //closeBtn 移除照片事件
    closeBtn(List, index) {
      console.log('List',List)
      List.splice(index, 1);
      this.$emit('change', '')
    },
  },
};
</script>
<style lang="scss" scoped>
.uploadTemp {
  position: relative;
  vertical-align: top;
  display: flex;
  padding-top: 10px;
  flex-wrap: wrap;
  flex-grow: 0;
  width: 450px;
  .contentSize {
    vertical-align: top;
    display: inline-block;

    margin-left: 20px;
  }
  .imgContent1 {
    display: inline-block;
    margin-right: 10px;
    position: relative;
    width: calc(20% - 10px);
    .imgList {
      width: 80px;
      height: 80px;
      border-radius: 8px;
    }
    .smallImg {
      width: 40px;
      height: 40px;
      border-radius: 8px;
    }
    .closeBtn {
      position: absolute;
      top: -10px;
      left: 60px;
      display: block;
      width: 35px;
      height: 35px;
      border-radius: 50%;
      // background-image: url("../../assets/images/close1.png");
      background-size: 100% 100%;
      cursor: pointer;
    }
  }
}

::v-deep .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 80px;
    height: 80px;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 80px;
    height: 80px;
    line-height: 80px;
    text-align: center;
  }
  .avatar-uploader {
    // width: 80px;
    width: calc(20% - 10px);
    height: 80px;
    display: block;
    margin-right: 10px;
    margin-bottom: 10px;
    position: relative;
  }
  .limit-font{
    position: absolute;
    width: 80px;
    left: 2px;
    bottom: -5px;
    color: #8c939d;
  }
  ::v-deep .el-upload-dragger{
    border:none;
    width:auto;
    height: auto;
  }
</style>
