<template>
  <div class="login-wrapper">
    <div class="wrapper">
      <div class="el-viewport" @keyup.enter="handleLogin">
        <div class="title">鸿飞达智慧物流管理后台</div>
        <div class="line"></div>
        <el-form ref="loginForm"
                :model="loginForm"
                class="login-form"
                auto-complete="on"
                label-position="left">

          <el-form-item prop="username">
            <i class="icon-zhanghao"></i>
            <el-input v-model="loginForm.nickName"
                      placeholder="请输入账号"
                      :οninput="loginForm.nickName=loginForm.nickName.replace(/[^\w\.\/]/ig,'')"
                      name="username"
                      type="text"
                      auto-complete="on"
                      class="account-input" />
          </el-form-item>

          <el-form-item prop="password">
            <i class="icon-mima"></i>
            <el-input v-model="loginForm.pwd"
                      :type="passwordType"
                      placeholder="请输入密码"
                      :οninput="loginForm.pwd=loginForm.pwd.replace(/[^\w\.\/]/ig,'')"
                      name="password"
                      maxlength="24"
                      auto-complete="on"
                      class="password-input" />
            <!-- <span class="show-pwd"
                  @click="showPwd">
              <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
            </span> -->
          </el-form-item>

          <el-button :loading="loading"
                    type="primary"
                    class="btn"
                    @click.native.prevent="handleLogin">
            {{ $t('login.logIn') }}
          </el-button>

        </el-form>
        <div class="copyright">
          <div>Copyright 2021-2022 版权所有 天津鸿飞达科技有限公司</div>
          <div>地址：天津市河西区中海财富中心20层</div>
          <a href="https://beian.miit.gov.cn/" target="_blank" class="mar8">津ICP备2021004152号</a>  <a href="https://beian.mps.gov.cn/#/query/webSearch" target="_blank" class="mar8">津公网安备12010302002621号</a>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
// import { validUsername } from '@/utils/validate'
// // import Particles from '@/components/Particles/index'
import base64 from 'Base64'
export default {
  name: 'Login',
  // components: {Particles},
  data () {
    // const validateUsername = (rule, value, callback) => {
    //   if (!validUsername(value)) {
    //     callback(new Error('Please enter the correct user name'))
    //   } else {
    //     callback()
    //   }
    // }
    // const validatePassword = (rule, value, callback) => {
    //   if (value.length < 6) {
    //     callback(new Error('The password can not be less than 6 digits'))
    //   } else {
    //     callback()
    //   }
    // }
    return {
      loginForm: {
        nickName: '',
        pwd: ''
      },
      // loginRules: {
      //   username: [{ required: true, trigger: 'blur', validator: validateUsername }],
      //   password: [{ required: true, trigger: 'blur', validator: validatePassword }]
      // },
      passwordType: 'password',
      loading: false,
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created () {
  },
  mounted () {
  },
  destroyed () {
  },
  methods: {
    handleLogin () {
      if(this.loginForm.pwd.length<6){
        this.$message.warning('密码长度最小6位')
      }else {
        this.$refs.loginForm.validate(valid => {
          if (valid) {
            this.loading = true;
            let loginForm = {
              ...this.loginForm,
              pwd: base64.btoa(this.loginForm.pwd)
            }
            this.$store.dispatch('LoginByUsername', loginForm).then(() => {
              this.loading = false;
              this.$router.push({ path: '/' })
            }).catch(() => {
              this.loading = false
            })
          } else {
              console.log('error submit!!');
            return false
          }
        })
      }
    }
  }
}
</script>

<style>
  .login-wrapper input{
    padding-left: 60px;
    height: 33px !important;
    border: none;
    border-bottom: 1px solid #cccccc;
    border-radius: 0;
    font-size: 16px;
  }
  .icon-zhanghao,
  .icon-mima {
    z-index: 10;
    position: absolute;
    left: 15px;
    top: 4px;
    font-size: 22px;
    color: #0c0c0c;
  }
</style>

<style rel="stylesheet/scss" lang="scss" scoped>
.login-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  background: url(../../assets/login.jpg) no-repeat;
  height: 100%;
  width: 100%;
  background-size: 100% 100%;
}
.wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 496px;
  height: 490px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 5px;
}

.el-viewport {
  background: #fff;
  width: 468px;
  height: 461px;
  border-radius: 5px;

  .title {
    padding-top: 57px;
    color: #666;
    font-size: 20px;
    line-height: 20px;
    margin-bottom: 15px;
    text-align: center;
  }
  .line {
    margin: 8px auto 0 auto;
    width: 58px;
    height: 4px;
    border-radius: 3px;
    background: #F2EDE7;
  }
  .el-input--medium .el-input__inner {
    height: 30px;
    border: none;
    outline: medium;
    border-bottom: 1px solid #ccc;
  }
  // .el-input input {

  // }
  .el-form {
    width: 366px;
    margin: 39px auto 0 auto;
  }

  .sub-title {
    font-size: 14px;
    color: #555555;
    display: block;
    line-height: 30px;
  }

  .btn {
    margin-top: 48px;
    width: 100%;
    height: 40px;
    font-size: 16px;
  }
  .login-btn {
    width: 100%;
    background: #0079ff;
    color: #fff;
    line-height: 20px;
    text-align: center;
    font-size: 14px;
    border-radius: 5px;
  }
  .copyright {
    margin-top: 46px;
    font-size: 12px;
    line-height: 12px;
    text-align: center;
    color: #888;
    > div {
      margin-bottom: 11px;
    }
  }
  .mar8{
    margin-left: 8px;
  }
}
</style>
