<template>
  <div class="car-audit">
    <div class="audit-top">
      <div class="top-content">
        <div class="auto-base">
          <el-collapse>
            <el-collapse-item>
              <template slot="title">
                <div style="font-size: 15px">系统自动审核结果：
                  <span v-if="examineResult.resultCode=='PASS'" class="auto-text" style="color: green;">审核通过</span>
                  <span v-if="examineResult.resultCode=='reject'" class="auto-text" style="color: red;"> 审核驳回</span>
                  <span v-if="examineResult.resultCode=='toStaff'" class="auto-text" style="color: #ED970F;">转人工</span>
                  </div>
              </template>
              <div class="auto">
                <div class="auto-display-text" v-for="(item, index) in examineResult.hitResultList" :key="index">
                  <i v-if="item.isHit === 'miss'" class="el-icon-check" style="color: green;"></i>
                  <i v-if="item.isHit === 'hit'" class="el-icon-close" style="color: red;"></i>
                  <i v-if="item.isHit === 'unExecuted'" class="el-icon-remove-outline" style="color: #ED970F;"></i>
                  {{item.ruleName}}
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
      
        </div>
        <div class="top-title">车辆审核 <span class="top-sn"> {{form.plateNumber}}</span></div>
        <div class="top-button">
          <div class="top-button-info" :class="{'top-button-select': selectIndex == 0}" @click="selectIndex = 0">基本信息</div>
          <div class="top-button-log" :class="{'top-button-select': selectIndex == 1}" @click="selectIndex = 1">审核日志</div>
        </div>
      </div>
    </div>
    <div class="container" v-show="selectIndex == 0">
      <div class="tab-title">车辆信息</div>
      <el-form :model="form" :rules="formRules" ref="form" label-width="140px" class="form">
        <div class="panel">
          <div v-if="!$store.state.user.userInfo2.hasStandardModeFlag" class="panel-title">
            <div>主车辆信息</div>
          </div>
          <div class="panel-main">
            <div class="panel-form">
              <el-form-item label="车牌号" prop="plateNumber">
                <el-input v-model="form.plateNumber"></el-input>
              </el-form-item>
              <el-form-item v-if="$store.state.user.userInfo2.hasStandardModeFlag" label="关联车辆" prop="plateNumber">
                <el-input v-model="form.bindCar"></el-input>
                <el-button @click="viewCarAuth" style="margin-left: 10px" type="text">查看认证信息</el-button>
              </el-form-item>
              <el-form-item label="车长车型" prop="typeAndLength" v-if="form.carModelName && !form.carModelName.includes('挂车')" key="car_type_length_input">
                <div class="car_type_length">
                  <el-select style="width: 138px" v-model="form.carTypeId"  @visible-change="selectCarTypes" placeholder="请选择车型">
                    <el-option v-for="item in carTypes" :key="item.id" :value="item.id" :label="item.name"></el-option>
                  </el-select>
                  <el-select v-if="hasCarLength" style="width: 138px; margin-left: 4px" v-model="form.carLength"  @visible-change="selectCarLength" placeholder="请选择车长">
                    <el-option v-for="item in carLengths" :key="item.id" :value="item.name" :label="item.name"></el-option>
                  </el-select>
                  <el-input v-else style="width: 138px; margin-left: 4px" v-model="form.carLength" placeholder="请输入车长"></el-input>
                </div>
                <div class="no_length" @click="hasCarLength=!hasCarLength">{{hasCarLength ? '没有可选长度？' : '选择车长'}}</div>
                
              </el-form-item>
              <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="是否挂靠" prop="affiliatedFlag">
                <el-select v-model="form.affiliatedFlag">
                  <el-option value="1" label="是"></el-option>
                  <el-option value="0" label="否"></el-option>
                </el-select>
                <div class="tip">非挂车必填</div>
              </el-form-item>
              <el-form-item label="行驶证所有人" prop="licenseOwner">
                <el-input v-model="form.licenseOwner"></el-input>
              </el-form-item>
              <el-form-item label="车辆类型" prop="carModelId">
                <CarTypeSelector v-model="form.carModelId" @changeName="name => form.carModelName = name" class="car-type-selector"></CarTypeSelector>
              </el-form-item>
              <el-form-item v-if="$store.state.user.userInfo2.hasStandardModeFlag" label="住址">
                <el-input v-model="form.xszAddress"></el-input>
              </el-form-item>
              <el-form-item label="使用性质" prop="licensesUseCharacter">
                <el-input v-model="form.licensesUseCharacter"></el-input>
                <div class="tip">总质量4.5吨及以下普通货运车辆必填</div>
              </el-form-item>
              <el-form-item label="车辆品牌型号" prop="cypp">
                <el-input v-model="form.cypp"></el-input>
              </el-form-item>
              <el-form-item label="车辆识别代号" prop="licensesVin">
                <el-input v-model="form.licensesVin"></el-input>
                <div class="tip">总质量4.5吨及以下普通货运车辆必填</div>
              </el-form-item>
              <el-form-item label="发证机关" prop="licensesIssueOrg">
                <el-input v-model="form.licensesIssueOrg"></el-input>
                <div class="tip">总质量4.5吨及以下普通货运车辆必填</div>
              </el-form-item>
              <el-form-item v-if="$store.state.user.userInfo2.hasStandardModeFlag" label="发动机号码">
                <el-input v-model="form.fdjhm"></el-input>
              </el-form-item>
              <el-form-item label="注册日期:" prop="licensesRegisterDate">
                <el-date-picker
                  v-model="form.licensesRegisterDate"
                  type="date"
                  class="form-input"
                  value-format="yyyy-MM-dd"></el-date-picker>
                  <div class="tip">总质量4.5吨及以下普通货运车辆必填</div>
              </el-form-item>
              <el-form-item label="发证日期:" prop="licensesIssueDate">
                <el-date-picker
                  v-model="form.licensesIssueDate"
                  type="date"
                  class="form-input"
                  value-format="yyyy-MM-dd"></el-date-picker>
                  <div class="tip">总质量4.5吨及以下普通货运车辆必填</div>
              </el-form-item>
              <el-form-item label="行驶证档案编号" porp="licenseNumber">
                <el-input v-model="form.licenseNumber"></el-input>
              </el-form-item>
              <el-form-item label="总质量" prop="sumCapacityTonnage">
                <el-input v-model="form.sumCapacityTonnage">
                  <template #append>kg</template>
                </el-input>
                <div class="tip">非牵引车必填</div>
              </el-form-item>
              <el-form-item label="核定载质量" prop="capacityTonnage">
                <el-input v-model="form.capacityTonnage">
                  <template #append>kg</template>
                </el-input>
                <div class="tip">非牵引车必填</div>
              </el-form-item>
              <el-form-item label="整备质量" prop="curbWeight">
                <el-input v-model="form.curbWeight">
                  <template #append>kg</template>
                </el-input>
                <!-- <div class="tip">牵引车必填</div> -->
              </el-form-item>
              <el-form-item label="准牵引总质量" prop="tractionMass">
                <el-input v-model="form.tractionMass">
                  <template #append>kg</template>
                </el-input>
                <div class="tip">牵引车必填</div>
              </el-form-item>
              <el-form-item prop="size" class="size-form">
                <template #label><span class="required-asterisk">*</span> 外廓尺寸:</template>
                <el-input v-model="form.carLong" class="form-input-size" placeholder="长"></el-input><span class="size-text">*</span>
                <el-input v-model="form.carWeight" class="form-input-size" placeholder="宽"></el-input><span class="size-text">*</span>
                <el-input v-model="form.carHigh" class="form-input-size" placeholder="高"></el-input><span class="size-text">mm</span>
              </el-form-item>
              <el-form-item label="行驶证有效期:" prop="licenseExpire">
                <el-date-picker
                  v-model="form.licenseExpire"
                  type="date"
                  class="form-input"
                  value-format="yyyy-MM-dd"
                  :picker-options='mainLicenseExpirePickerOptions'></el-date-picker>
                  <div class="expire-text">
                    <template v-if="licenseExpireStatus === -1">（已过期）</template>
                    <template v-else-if="licenseExpireStatus !== -2">（{{ licenseExpireStatus }}天后到期）</template>
                  </div>
              </el-form-item>
              <el-form-item label="能源类型" prop="energyType">
                <el-select v-model="form.energyType">
                  <el-option v-for="item in energyList" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="车牌颜色" prop="plateColor">
                <el-select v-model="form.plateColor">
                  <el-option 
                  v-for="item in plateColorList"
                  :key="item.plateColorCode"
                  :label="item.plateColorMessage"
                  :value="item.plateColorCode"
                  ></el-option>
                </el-select>
                <div class="tip">非挂车必填</div>
              </el-form-item>
              <el-form-item label="道路运输证号" prop="shippingCert">
                <el-input v-model="form.shippingCert"></el-input>
                <div class="tip">牵引车，总质量4.5吨以上普通货车必填</div>
                <div class="abnormal">
                  <el-checkbox v-model="isShippingAbonormal">存在异常</el-checkbox>
                  <el-select v-if="isShippingAbonormal" v-model="form.shippingCertWarnMemo">
                    <el-option value="网站无相关信息"></el-option>
                    <el-option value="网站查询过期"></el-option>
                    <el-option value="从业资格证号与网站不一致"></el-option>
                  </el-select>
                </div>
              </el-form-item>
              <el-form-item label="道路运输证有效期" prop="shippingCertExpire">
                <el-date-picker
                  v-model="form.shippingCertExpire"
                  type="date"
                  class="form-input"
                  value-format="yyyy-MM-dd"></el-date-picker>
                  <div class="expire-text">
                    <template v-if="shippingCertExpireStatus === -1">（已过期）</template>
                    <template v-else-if="shippingCertExpireStatus !== -2">（{{ shippingCertExpireStatus }}天后到期）</template>
                  </div>
              </el-form-item>
              <el-form-item v-if="form.carModelName && !form.carModelName.includes('挂车')" label="从业资格证号：" prop="qualificationCertificate">
                <el-input v-model="form.employmentCert"></el-input>
                <div class="abnormal">
                  <el-checkbox v-model="isCertAbnormal">存在异常</el-checkbox>
                  <el-select v-if="isCertAbnormal" v-model="form.employmentCertWarnMemo">
                    <el-option value="网站无相关信息"></el-option>
                    <el-option value="网站查询过期"></el-option>
                    <el-option value="从业资格证号与网站不一致"></el-option>
                  </el-select>
                </div>
              </el-form-item>
              <el-form-item v-if="form.carModelName && !form.carModelName.includes('挂车')" label="从业资格证有效期：" prop="employmentCertExpireDate">
                <el-date-picker v-model="form.employmentCertExpireDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                <div class="expire-text">
                  <template v-if="employmentCertExpireDateStatus === -1">（已过期）</template>
                  <template v-else-if="employmentCertExpireDateStatus !== -2">（{{ employmentCertExpireDateStatus }}天后到期）</template>
                </div>
              </el-form-item>
              <div class="sub-title">以下是申请人信息：</div>
              <el-form-item label="姓名">
                {{ form.applyUserName }}
              </el-form-item>
              <el-form-item label="联系方式">
                {{ form.applyUserMobile }}
              </el-form-item>
              <el-form-item label="身份证号">
                {{  form.applyUserIdCard }}
              </el-form-item>
              <el-form-item label="驾驶证">
                <ImageUploader type="viewer" :defaultUrl="form.applyUserDrivingLicenc"></ImageUploader>
              </el-form-item>
            </div>
            <div class="panel-view">
              <img-list ref="imgList" :static="true">
                <el-form label-width="160px">
                  <el-form-item label="行驶证正页：">
                    <div class="item-box" @click="showPic(form.licenseImage)">
                      <div class="img-box">
                        <img :src="form.licenseImage" v-if="form.licenseImage">
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item label="行驶证副页：">
                    <div class="item-box" @click="showPic(form.licenseBackImage)">
                      <div class="img-box">
                        <img :src="form.licenseBackImage" v-if="form.licenseBackImage">
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item label="行驶证有效期页：">
                    <div class="item-box" @click="showPic(form.licenseExpireImage)">
                      <div class="img-box">
                        <img :src="form.licenseExpireImage" v-if="form.licenseExpireImage">
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item label="车辆道路运输证：">
                    <div class="item-box" @click="showPic(form.shippingCertImage)">
                      <div class="img-box">
                        <img :src="form.shippingCertImage" v-if="form.shippingCertImage">
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="车主声明：">
                    <div class="item-box" @click="showPic(form.statementImage)">
                      <div class="img-box">
                        <img :src="form.statementImage" v-if="form.statementImage">
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item label="人车合照：">
                    <div class="item-box" @click="showPic(form.carImage)">
                      <div class="img-box">
                        <img :src="form.carImage" v-if="form.carImage">
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item v-if="form.carModelName && !form.carModelName.includes('挂车')" label="从业资格证：">
                    <div class="item-box" @click="showPic(form.employmentCertImage)">
                      <div class="img-box">
                        <img :src="form.employmentCertImage" v-if="form.employmentCertImage">
                      </div>
                    </div>
                  </el-form-item>
                </el-form>
              </img-list>
            </div>
          </div>
        </div>
        <div v-for="(item, index) in form.trailers" class="panel">
          <div class="panel-title">
            <div>挂车{{ index + 1 }}信息</div>
          </div>
          <div class="panel-main">
            <div class="panel-form">
              <el-form-item :prop="'trailers.' + index + '.plateNumber'" label="车牌号" :rules="[{ required: true, message: '车牌号不能为空', trigger: 'blur' }]">
                <el-input v-model="item.plateNumber"></el-input>
              </el-form-item>
              <el-form-item :prop="'trailers.' + index + '.licenseOwner'" label="行驶证所有人" :rules="[{ required: true, message: '行驶证所有人不能为空', trigger: 'blur' }]">
                <el-input v-model="item.licenseOwner"></el-input>
              </el-form-item>
              <el-form-item :prop="'trailers.' + index + '.carModelId'" label="车辆类型" :rules="[{ required: true, message: '车辆类型不能为空', trigger: ['change', 'blur'] }]">
                <CarTypeSelector v-model="item.carModelId"  @changeName="name => item.carModelName = name" class="car-type-selector"></CarTypeSelector>
              </el-form-item>
              <el-form-item :prop="'trailers.' + index + '.licensesUseCharacter'" label="使用性质" :rules="itemLicensesUseCharacterRules(item)">
                <el-input v-model="item.licensesUseCharacter"></el-input>
                <div class="tip">总质量4.5吨及以下普通货运车辆必填</div>
              </el-form-item>
              <el-form-item label="车辆品牌型号">
                <el-input v-model="item.cypp"></el-input>
              </el-form-item>
              <el-form-item :prop="'trailers.' + index + '.licensesVin'" label="车辆识别代号" :rules="itemLicensesVinRules(item)">
                <el-input v-model="item.licensesVin"></el-input>
                <div class="tip">总质量4.5吨及以下普通货运车辆必填</div>
              </el-form-item>
              <el-form-item :prop="'trailers.' + index + '.licensesIssueOrg'" label="发证机关" :rules="itemLicensesIssueOrgRules(item)">
                <el-input v-model="item.licensesIssueOrg"></el-input>
                <div class="tip">总质量4.5吨及以下普通货运车辆必填</div>
              </el-form-item>
              <el-form-item :prop="'trailers.' + index + '.licensesRegisterDate'" label="注册日期:" :rules="itemLicensesRegisterDateRules(item)">
                <el-date-picker
                  v-model="item.licensesRegisterDate"
                  type="date"
                  class="form-input"
                  value-format="yyyy-MM-dd"></el-date-picker>
                <div class="tip">总质量4.5吨及以下普通货运车辆必填</div>
              </el-form-item>
              <el-form-item :prop="'trailers.' + index + '.licensesIssueDate'" label="发证日期:" :rules="itemLicensesIssueDateRules(item)">
                <el-date-picker
                  v-model="item.licensesIssueDate"
                  type="date"
                  class="form-input"
                  value-format="yyyy-MM-dd"></el-date-picker>
                <div class="tip">总质量4.5吨及以下普通货运车辆必填</div>
              </el-form-item>
              <el-form-item :prop="'trailers.' + index + '.licenseNumber'" label="行驶证档案编号" :rules="[{ required: true, message: '行驶证档案编号不能为空', trigger: 'blur' }]">
                <el-input v-model="item.licenseNumber"></el-input>
              </el-form-item>
              <el-form-item :prop="'trailers.' + index + '.sumCapacityTonnage'" label="总质量" :rules="[{ required: true, message: '总质量不能为空', trigger: 'blur' }]">
                <el-input v-model="item.sumCapacityTonnage">
                  <template #append>kg</template>
                </el-input>
              </el-form-item>
              <el-form-item :prop="'trailers.' + index + '.capacityTonnage'" label="核定载质量" :rules="[{ required: true, message: '核定载质量不能为空', trigger: 'blur' }]">
                <el-input v-model="item.capacityTonnage">
                  <template #append>kg</template>
                </el-input>
              </el-form-item>
              <el-form-item :prop="'trailers.' + index + '.size'" label="外廓尺寸:" prop="size" class="size-form" :rules="itemSizeRules(item)">
                <template #label><span class="required-asterisk">*</span> 外廓尺寸:</template>
                <el-input v-model="item.carLong" class="form-input-size" placeholder="长"></el-input><span class="size-text">*</span>
                <el-input v-model="item.carWeight" class="form-input-size" placeholder="宽"></el-input><span class="size-text">*</span>
                <el-input v-model="item.carHigh" class="form-input-size" placeholder="高"></el-input><span class="size-text">mm</span>
              </el-form-item>
              <el-form-item :prop="'trailers.' + index + '.licenseExpire'" label="行驶证有效期" :rules="[{ required: true, message: '行驶证有效期不能为空', trigger: 'blur' }]">
                <el-date-picker
                  v-model="item.licenseExpire"
                  type="month"
                  class="form-input"
                  value-format="yyyy-MM"
                  :picker-options='mainLicenseExpirePickerOptions'></el-date-picker>
              </el-form-item>
              <el-form-item :prop="'trailers.' + index + '.energyType'" label="能源类型" :rules="[{ required: true, message: '能源类型不能为空', trigger: 'blur' }]">
                <el-select v-model="item.energyType">
                  <el-option v-for="item in energyList" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="panel-view">
              <img-list ref="imgList2" :static="true">
                <el-form label-width="160px">
                  <el-form-item label="行驶证正页：">
                    <div class="item-box" @click="showPic2(index, item.licenseImage)">
                      <div class="img-box">
                        <img :src="item.licenseImage">
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item label="行驶证副页：">
                    <div class="item-box" @click="showPic2(index, item.licenseBackImage)">
                      <div class="img-box">
                        <img :src="item.licenseBackImage">
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item label="行驶证有效期页：">
                    <div class="item-box" @click="showPic2(index, item.licenseExpireImage)">
                      <div class="img-box">
                        <img :src="item.licenseExpireImage">
                      </div>
                    </div>
                  </el-form-item>
                </el-form>
              </img-list>
            </div>
          </div>
        </div>
      </el-form>
    </div>
    <!-- <div class="container">
      <div class="tab-title">车辆审核记录</div>
      <el-table :data="form.carAuthLogs">
        <el-table-column prop="createddate" label="创建日期"></el-table-column>
        <el-table-column prop="statusParams" label="车辆审核状态"></el-table-column>
        <el-table-column prop="operator" label="操作员"></el-table-column>
        <el-table-column prop="memo" label="车辆审核备注"></el-table-column>
      </el-table>
    </div> -->
    <LogList style="margin-top: 92px" v-show="selectIndex == 1" :listData="carAuthLogs"></LogList>
    <div class="audit">
      <div class="audit-status">车辆审核状态：<span>{{form.statusDesc}}</span></div>
      <div class="audit-btns">
        <el-button @click="audit" :loading="loading" type="primary">通过审核</el-button>
        <el-button @click="isRejectShow = true" :loading="loading">驳回</el-button>
        <!-- <el-button @click="audit(true)" :loading="loading" type="success">优先通过</el-button> -->
      </div>
    </div>
    <el-dialog
      :visible.sync="isRejectShow"
      title="请选择驳回原因"
      class="reject-dialog"
      width="500px">
      <el-checkbox-group v-model="selectedRejectReason">
        <div v-for="item in reasonList" :key="item.id" class="reason-item">
          <el-checkbox :label="item.reason">
            {{ item.reason }}
          </el-checkbox>
        </div>
      </el-checkbox-group>
      <div class="reason-other">其他原因</div>
      <el-input v-model="otherReason" type="textarea"></el-input>
      <template #footer>
        <el-button @click="isRejectShow = false">取消</el-button>
        <el-button type="primary" @click="reject">确认驳回</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import ImgList from '@/components/ImgList/ImgList'
import CarTypeSelector from './components/CarTypeSelector'
import formRulesMixin from './carsEmpowerExamineRules'
import { checkExpire } from '@/utils/date'
import LogList from '@/components/AuditLog/index.vue'

export default {
  mixins: [formRulesMixin],
  components: {
    ImgList,
    CarTypeSelector,
    LogList
  },
  data() {
    return {
      loading: false,
      selectIndex: 0,
      carAuthLogs: [],
      form: {},
      mainLicenseExpirePickerOptions: {
        disabledDate(now) {
          return now.getTime() <= Date.now()
        }
      },
      carModelText: '',
      energyList: [
        { value: 'A', label: '汽油' },
        { value: 'B', label: '柴油' },
        { value: 'C', label: '电' },
        { value: 'D', label: '混合油' },
        { value: 'E', label: '天然气' },
        { value: 'F', label: '液化石油气' },
        { value: 'L', label: '甲醇' },
        { value: 'M', label: '乙醇' },
        { value: 'N', label: '太阳能' },
        { value: 'O', label: '混合动力' },
        { value: 'Y', label: '无' },
        { value: 'Z', label: '其他' },
      ],
      reasonList: [],
      isRejectShow: false,
      selectedRejectReason: [],
      otherReason: '',
      isShippingAbonormal: false,
      plateColorList: [],
      licenseExpireStatus: null,
      shippingCertExpireStatus: null,
      carTypes: [],
      carLengths: [],
      hasCarLength: true,
      examineResult:{}, //自动审核结果
      isCertAbnormal: false,
      employmentCertExpireDateStatus: -2
    }
  },
  activated() {
    this.getData()
    this.getReasonList()
    this.getPlateColorList()

    this.$watch('form.licenseExpire', {
      deep: true,
      handler(v) {
        checkExpire(v)
          .then(res => this.licenseExpireStatus = res)
        
      }
    })

    this.$watch('form.shippingCertExpire', {
      deep: true,
      handler(v) {
        checkExpire(v)
          .then(res => this.shippingCertExpireStatus = res)
        
      }
    })

    this.$watch('form.employmentCertExpireDate', {
      deep: true,
      handler(v) {
        checkExpire(v)
          .then(res => this.employmentCertExpireDateStatus = res)
      }
    })
  },
  computed: {
    isMainTractor() {
      return typeof this.form.carModelName === 'string' && this.form.carModelName.includes('牵引车')
    },
    isTrailer() {
      return typeof this.form.carModelName === 'string' && this.form.carModelName.includes('挂车')
    }
  },
  methods: {
    getData() {
      this.$get('/admin-center-server/car/driving/DrivingDesc', {
        drivingId: this.$route.query.carId
      })
        .then(res => {
          this.form = res
          if (this.form.sumCapacityTonnage) this.form.sumCapacityTonnage = parseInt(this.form.sumCapacityTonnage)
          if (this.form.capacityTonnage) this.form.capacityTonnage = parseInt(this.form.capacityTonnage)
          if (this.form.curbWeight) this.form.curbWeight = parseInt(this.form.curbWeight)
          if (this.form.tractionMass) this.form.tractionMass = parseInt(this.form.tractionMass)
          this.isShippingAbonormal = res.shippingCertWarnFlag === '1' ? true : false
          this.isCertAbnormal = res.employmentCertWarnFlag === '1' ? true : false
          if (res.carAuthLogs) {
            this.carAuthLogs = res.carAuthLogs.map((item) => {
              return {
                createdDate: item.createddate,
                authStatusStr: item.statusParams,
                operator: item.operator,
                rejectReason: item.memo
              }
            })
          }
          this.getCarRule()
          document.title = '车辆审核' + '-' + res.plateNumber
          this.$get("/admin-center-server/carmodel/getCarModelListByCategory?category=2").then(res => {
            this.carTypes = res
          });
          this.$get("/admin-center-server/carmodel/getCarModelListByCategory?category=1").then(res => {
            this.carLengths = res
            let lengths = res.map(item => item.name)
            this.hasCarLength = lengths.includes(this.form.carLength)
          });
        })
    },
    getPlateColorList() {
      this.$get('/admin-center-server/car/driving/getPlateColorList').then(
        res => {
          this.plateColorList = res
        }
      )
    },
    showPic(pic) {
      this.$refs.imgList.showPic(pic)
    },
    showPic2(index, pic) {
      this.$refs.imgList2[index].showPic(pic)
    },
    selectCarTypes() {
      
    },
    selectCarLength() {
    
    },
    //检测是否大于
    checkHeavierThan(value) {
      //只有在填写了值，并且大于4500时返回true，也就是说没有填时将返回false
      if (value !== null && value !== '' && value > 4500) {
        return true
      }
      return false
    },
    //检测是否小于等于
    checkLighterThan(value) {
      if (value !== null && value !== '' && value <= 4500) {
        return true
      }
      return false
    },
    isTractor(name) {
      return typeof name === 'string' && name.inclues('牵引车')
    },
    getReasonList() {
      this.$get('/admin-center-server/reject/reason/list', {
        pageNumber: 1,
        pageSize: 2000
      })
        .then(res => {
          this.reasonList = res.list
        })
    },
    auditApi(params) {
      //总重量和核定载质量单位传吨
      if (params.sumCapacityTonnage) {
        params.sumCapacityTonnage = Number(params.sumCapacityTonnage) / 1000
      } else {
        params.sumCapacityTonnage = null
      }
      if (params.capacityTonnage) {
        params.capacityTonnage = Number(params.capacityTonnage) / 1000
      } else {
        params.capacityTonnage = null
      }
      return this.$post('/admin-center-server/car/driving/review', params)
    },
    audit(isPrecedence = false) {
      if(this.form.licensesUseCharacter){
        if(this.form.licensesUseCharacter.includes('客运')){
          this.$message.error('操作失败，原因：车辆使用性质不符合平台要求')
          return
        }
      }
      this.$refs.form.validate(valid => {
        if (!valid){
          this.$moveToErr()
          return
        }
        this.$confirm('确定要通过审核吗')
          .then(() => {
            this.loading = true
            let params = {
              carReviewUpdater: this.form,
              id: this.$route.query.carId,
              priority: isPrecedence ? 1 : 0,
              status: 1,
              trailers: this.form.trailers
            }
            params.carReviewUpdater.shippingCertWarnFlag = this.isShippingAbonormal ? '1' : '0'
            params.carReviewUpdater.employmentCertWarnFlag = this.isCertAbnormal ? '1' : '0'
            //根据后端要求
            params.carReviewUpdater.id = params.carReviewUpdater.carId

            if (this.$store.state.user.userInfo2.hasStandardModeFlag) {
              params.carReviewUpdater.affiliatedFlag = '0'
            }
            this.auditApi(params)
              .then(() => {
                this.getData()
                this.getdrivingapplychannel()
                setTimeout(() => {
                  window.close()
                }, 1000)
              })
              .finally(() => {
                this.loading = false
              })
          })
      })
    },
    reject() {
      if(this.selectedRejectReason.length===0 && this.otherReason===''){
        this.$message.error('请选择或输入驳回原因');
        return false
      }
      this.loading = true
      let params = {
        carReviewUpdater: this.form,
        trailers: this.form.trailers,
        id: this.$route.query.carId,
        priority: 0,
        status: 2,
        authOtherMemo: this.otherReason,
        authMemo: this.selectedRejectReason.join(',')
      }
      //根据后端要求
      params.carReviewUpdater.id = params.carReviewUpdater.carId

      if (this.$store.state.user.userInfo2.hasStandardModeFlag) {
        params.carReviewUpdater.affiliatedFlag = '0'
      }
      this.auditApi(params)
        .then(() => {
          this.getData()
          this.getdrivingapplychannel()
          setTimeout(() => {
            window.close()
          }, 1000)
        })
        .finally(() => {
          this.loading = false
        })
    },
    //驾驶审核页面
    getdrivingapplychannel() {
      this.$http.get('/admin-center-server/car/auth/manager/getSumMsgByType', {
        params: {
          type: ''
        }
      }).then(res => {
        let data = Number(res.data.data)
        let type = 1;
        this.getMenu(type, data)
      })
    },
    getMenu(type, num) {
      this.$store.state.permission.addRouters.map((item, index) => {
          if (item.meta) {
              if (item.meta.id === 22) {
                  let changeNum = item.children[0].children;
                  changeNum.map((item, index) => {
                      if (!item.hidden && item.meta.id !== 24) {
                          if (item.meta.id === 25 && type === 0) {
                              item.meta.num = num
                          } else if (item.meta.id === 26 && type === 1) {
                              item.meta.num = num
                          } else if (item.meta.id === 27 && type === 2) {
                              item.meta.num = num
                          }
                      }
                  })
              } else if (item.meta.id === 1) {
                  let clientNum = item.children[0].children;
                  let brokerNum = item.children[1].children;
                  let driverNum = item.children[2].children;
                  clientNum.map((item, index) => {
                      if (!item.hidden) {
                          if (item.meta.id === 5 && type === 10) {
                              item.meta.num = num
                          }
                      }
                  });
                  brokerNum.map((item, index) => {
                      if (!item.hidden) {
                          if (item.meta.id === 10 && type === 20) {
                              item.meta.num = num
                          }
                      }
                  });
                  driverNum.map((item, index) => {
                      if (!item.hidden) {
                          if (item.meta.id === 13 && type === 30) {
                              item.meta.num = num
                          }
                      }
                  })
              }
          }
      });
    },
    viewCarAuth() {
      let routerData = this.$router.resolve({
          path:'/carsList/carDetail',
          query:{
            carId: this.form.bindCarDrivingId
          }
      })
      window.open(routerData.href)
    },
    // 车辆审核调用规则引擎
    getCarRule(){
      let data ={
        carImage: this.form.carImage,
        drivingLicencesImage: this.form.applyUserDrivingLicenc,
        employmentCert: this.form.employmentCert,
        employmentCertExpireDate: this.form.employmentCertExpireDate,
        employmentCertImage: this.form.employmentCertImage,
        idCardNum: this.form.applyUserIdCard,
        licenseBackImage: this.form.licenseBackImage,
        licenseExpireImage: this.form.licenseExpireImage,
        licenseImage: this.form.licenseImage,
        shippingCertImage: this.form.shippingCertImage,
        statementImage: this.form.statementImage,
        name: this.form.applyUserName
      }
      this.$post('/admin-center-server/rule/engine/carRule',data).then(res => {
        this.examineResult = res || {}
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.car-audit {
  padding-bottom: 110px;
}
.audit-top {
  // background-color: rgb(249, 249, 249);
  background-color: white;
  position: fixed;
  top: 93px;
  left: 210px;
  right: 25px;
  z-index: 99;
  .top-content {
    background-color: rgb(249, 249, 249);
    margin:0px 1px;
  }
  .top-title {
    padding: 20px;
    font-size: 18px;
    font-weight: 800;
  }
  .top-sn {
    font-size: 18px;
    font-weight: 400;
  }

  .top-button {
    display: flex;
    .top-button-info {
      width: 108px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
    }
    .top-button-log {
      width: 108px;
      text-align: center;
      height: 30px;
      line-height: 30px;
      cursor: pointer;
    }

    .top-button-select {
      background-color: white;
    }
  }
}
.container {
  margin: 92px 10px 10px;
  padding: 20px 10px;
  background: #fff;
}
.car_type_length {
  display: flex;

}
.no_length {
  text-align: right;
  color: #ED970F;
  width: 280px;
  height: 20px;
  line-height: 20px;
  font-size: 14px;
  cursor: pointer;
}
.tab-title {
  position: relative;
  height: 30px;
  margin-bottom: 10px;
  line-height: 30px;
  padding-left: 10px;
  font-size: 14px;
  &::after {
    content: '';
    position: absolute;
    top: 7px;
    left: 0;
    width: 4px;
    height: 14px;
    background-color: #F6A018;
    border-radius: 2px;
  }
}
.panel-title {
  position: relative;
  margin-top: 10px;
  margin-bottom: 20px;
  color: #333;
  font-size: 14px;
  line-height: 14px;
  text-align: center;
  div {
    position: relative;
    z-index: 10;
    display: inline-block;
    padding: 0 10px;
    background-color: #fff;
    color: rgba(51, 51, 51, 0.7);
  }
  &::after {
    content: '';
    position: absolute;
    width: 100%;
    left: 0;
    top: 6px;
    border-top: 1px solid #d8dce3;
  }
}
.panel-main {
  display: flex;
}
.panel-form {
  flex-grow: 1;
}
.panel-view {
  flex-grow: 1;
}
.form {
  .el-input,
  .el-select {
    width: 280px;
  }
}
.tip {
  margin-left: 10px;
  display: inline-block;
  color: rgb(221, 32, 66);
}
.car-type-selector {
  width: 280px;
}
.size-form .el-input {
  width: 70px;
}
.size-text {
  margin: 0 5px;
}
.abnormal {
  margin-top: 10px;
  .el-checkbox {
    margin-right: 10px;
  }
}
.sub-title {
  color: #ED970F;
  font-size: 14px;
}
.audit {
  position: fixed;
  bottom: 0;
  right: 0;
  width: calc(100% - 200px);
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
}
.audit-status {
  margin-left: 20px;
  span {
    color: red;
  }
}
.audit-btns {
  margin-right: 20px;
}
.reason-item {
  line-height: 30px;
}
.reason-other {
  margin: 10px 0;
  color: rgb(51, 174, 240);
}
.expire-text {
  color: #D92929;
}

</style>