<template>
  <div class="container" :class="{ 'container-audit': type === 'audit'}">
    <template v-if="type === 'audit'">
      <div class="auto-base">
        <el-collapse>
          <el-collapse-item>
            <template slot="title">
              <div style="font-size: 15px">系统自动审核结果：
              </div>
            </template>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div class="audit-bottom" v-if="type === 'audit'">
        <span>认证状态：{{ form.transportationCapacitySupplierStatusName }}</span>
        <el-button @click="audit(true)" :loading="isLoading" type="primary">通过审核</el-button>
        <el-button @click="openReject" :loading="isLoading" type="danger">驳回</el-button>
      </div>
      <div class="audit-top">
        <div class="top-content">
          <div class="top-title">运力承运商身份信息： <span class="top-sn"> {{form.name}}</span></div>
          <div class="top-button">
            <div class="top-button-info" :class="{'top-button-select': selectIndex == 0}" @click="selectIndex = 0">基本信息</div>
            <div class="top-button-log" :class="{'top-button-select': selectIndex == 1}" @click="selectIndex = 1">审核日志</div>
          </div>
        </div>
      </div>
    </template>
    <div v-if="selectIndex === 0" class="main">
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-width="140px"
        class="form">
        <el-form-item label="法人认证：" prop="legalPersonCertificationFlag">
          <el-select v-model="form.legalPersonCertificationFlag" :disabled="type === 'audit'">
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="form.legalPersonCertificationFlag === '1' ? '法人姓名：' : '代办人姓名：'" prop="idCardName">
          <el-input v-model="form.idCardName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item :label="form.legalPersonCertificationFlag === '1' ? '法人身份证号码：' : '代办人身份证号码：'" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="紧急联系人电话：" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="公司名称：" prop="companyName">
          <el-input v-model="form.companyName" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="社会信用代码：" prop="organizationCode">
          <el-input v-model="form.organizationCode" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="道路运输经营许可证号：" prop="roadTransportOperationLicenseNumber">
          <el-input v-model="form.roadTransportOperationLicenseNumber" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="道路运输经营许可证有效期止：" prop="roadTransportOperationLicenseDate">
          <el-date-picker v-model="form.roadTransportOperationLicenseDate" value-format="yyyy-MM-dd" placeholder="请选择"></el-date-picker>
          <div class="expire-text">
            <template v-if="roadTransportOperationLicenseDateStatus === -1">（已过期）</template>
            <template v-else-if="roadTransportOperationLicenseDateStatus !== -2">（{{ roadTransportOperationLicenseDateStatus }}天后到期）</template>
          </div>
        </el-form-item>
        <template v-if="form.legalPersonCertificationFlag === '0'">
          <el-form-item label="法人姓名：" prop="corporationName">
            <el-input v-model="form.corporationName" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="法人身份证号码：" prop="corporationIdCard">
            <el-input v-model="form.corporationIdCard" placeholder="请输入"></el-input>
          </el-form-item>
        </template>
        <el-form-item v-if="type === 'edit'">
          <el-button @click="save" type="primary">保存</el-button>
        </el-form-item>
      </el-form>
      <ImgList class="img-list" ref="imgList">
        <el-form label-width="160px">
          <el-form-item label="营业执照：">
            <ImageUploader2
              :defaultUrl="form.licenseImage"
              @change="licenseImageOcr"
              :type="uploaderType"></ImageUploader2>
          </el-form-item>
          <el-form-item label="道路运输经营许可证：">
            <ImageUploader2
              :defaultUrl="form.roadTransportOperationLicenseImage"
              @change="roadTransportOperationLicenseImageOcr"
              :type="uploaderType"></ImageUploader2>
          </el-form-item>
          <el-form-item v-if="form.legalPersonCertificationFlag === '0'" label="法人身份证：">
            <ImageUploader2
              :defaultUrl="form.corporationIdCardImage"
              @change="corporationIdCardImageOcr"
              :type="uploaderType"></ImageUploader2>
          </el-form-item>
          <el-form-item :label="form.legalPersonCertificationFlag === '0' ? '代办人身份证：' : '法人身份证：'">
            <ImageUploader2
              :defaultUrl="form.idCardImage"
              @change="idCardImageOcr"
              :type="uploaderType"></ImageUploader2>
          </el-form-item>
          <el-form-item v-if="form.legalPersonCertificationFlag === '0'" label="授权委托书：">
            <ImageUploader2
              :defaultUrl="form.powerOfAttorney"
              @change="url => $set(form, 'powerOfAttorney', url)"
              :type="uploaderType"></ImageUploader2>
          </el-form-item>
        </el-form>
      </ImgList>
    </div>
    <div v-else>
      <LogList :listData="form.userVerifyLogDTO"></LogList>
    </div>
    <el-dialog
      :visible.sync="isRejectDialogShow"
      title="驳回原因">
      <el-form :model="rejectForm">
        <el-form-item label="请选择驳回原因" label-width="120px">
          <el-checkbox-group v-model="rejectForm.region">
            <div v-for='item in rejectReasonList' :key="item.id">
              <el-checkbox :label="item.rejectReason" >{{ item.rejectReason }}</el-checkbox>
            </div>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="其他原因" label-width="120px">
          <el-input type="textarea"
            :rows="2"
            placeholder="请输入其他驳回原因"
            v-model="rejectForm.textarea">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isRejectDialogShow = false">取 消</el-button>
        <el-button type="primary" @click="audit(false)">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ImgList from '@/components/ImgList/ImgList'
import { checkExpire } from '@/utils/date'
import LogList from '@/components/AuditLog/index.vue'
export default {
  components: { ImgList, LogList },
  data() {
    return {
      selectIndex: 0,
      form: {},
      rules: {
        legalPersonCertificationFlag: [
          { required: true, message: '请选择' }
        ],
        idCardName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        idCard: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        contactPhone: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        companyName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        organizationCode: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        roadTransportOperationLicenseDate: [
          { validator: (rule, value, cb) => {
            if (this.form.roadTransportOperationLicenseNumber && !this.form.roadTransportOperationLicenseDate ) {
              cb('请输入')
            }
            cb()
          } }
        ],
        corporationName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        corporationIdCard: [
          { required: true, message: '请输入', trigger: 'blur' }
        ]
      },
      type: '', //审核audit 编辑edit 详情detail
      uploaderType: '',
      isLoading: false,
      isRejectDialogShow: false,
      rejectForm: {
        region: [],
        textarea: ''
      },
      rejectReasonList: [],
      roadTransportOperationLicenseDateStatus: -2
    }
  },
  mounted() {
    if (this.type === 'edit' || this.type === 'detail') {
      let title = this.type === 'edit' ? '司机编辑' : '账户详情'
      this.$store.commit('UPDATE_VISITED_VIEW', {
        title,
        path: this.$route.path
      })
    }
  },
  created() {
    this.getData()
    
    this.type = this.$route.query.type
    if (this.type === 'audit') {
      this.uploaderType = 'viewer'
      this.$post('/admin-center-server/verify/rejectReasonList', {
        scene: '5'
      })
        .then(res => {
          this.rejectReasonList = res
        })
    } else if (this.type === 'detail') {
      this.uploaderType = 'viewer'
    } else if (this.type === 'edit') {
      this.uploaderType = 'upload'
    }

    this.$watch('form.roadTransportOperationLicenseDate', {
      deep: true,
      handler(v) {
        checkExpire(v)
          .then(res => this.roadTransportOperationLicenseDateStatus = res)
      }
    })
  },
  methods: {
    getData() {
      this.$get('/admin-center-server/commonUser/forUpdateInfo', {
        type: 5,
        id: this.$route.query.id
      })
        .then(res => {
          this.form = res
          document.title = '运力供应商审核-' + res.name 
        })
    },
    licenseImageOcr(url) {
      this.$set(this.form, 'licenseImage', url)
      if (!url) return
      this.$post('/order-center-server/ocr/ocrImage', {
        type: 4,
        url
      })
        .then(res => {
          this.$set(this.form, 'companyName', res.businessLicenseRes.companyName)
        })
    },
    roadTransportOperationLicenseImageOcr(url) {
      this.$set(this.form, 'roadTransportOperationLicenseImage', url)
      if (!url) return
      this.$post('/order-center-server/ocr/ocrImage', {
        type: 6,
        url
      })
        .then(res => {
          this.$set(this.form, 'roadTransportOperationLicenseNumber', res.roadTransportCertificateRes.permitNumber)
        })
    },
    idCardImageOcr(url) {
      this.$set(this.form, 'idCardImage', url)
      if (!url) return
      this.$post('/order-center-server/ocr/ocrImage', {
        type: 1,
        url,
        side: 'front'
      })
        .then(res => {
          this.$set(this.form, 'idCardName', res.ocrIdCardRes.name)
          this.$set(this.form, 'idCard', res.ocrIdCardRes.idCard)
        })
    },
    corporationIdCardImageOcr(url) {
      this.$set(this.form, 'corporationIdCardImage', url)
      if (!url) return
      this.$post('/order-center-server/ocr/ocrImage', {
        type: 1,
        url,
        side: 'front'
      })
        .then(res => {
          this.$set(this.form, 'corporationName', res.ocrIdCardRes.name)
          this.$set(this.form, 'corporationIdCard', res.ocrIdCardRes.idCard)
        })
    },
    viewPic(pic) {
      this.$refs.imgList.showPic(pic)
    },
    openReject() {
      this.isRejectDialogShow = true
    },
    getParams() {
      return {
        legalPersonCertificationFlag: this.form.legalPersonCertificationFlag,
        idCardName: this.form.idCardName,
        idCard: this.form.idCard,
        contactPhone: this.form.contactPhone,
        companyName: this.form.companyName,
        roadTransportOperationLicenseNumber: this.form.roadTransportOperationLicenseNumber,
        organizationCode: this.form.organizationCode,
        roadTransportOperationLicenseDate: this.form.roadTransportOperationLicenseDate,
        corporationName: this.form.corporationName,
        corporationIdCard: this.form.corporationIdCard,
        corporationIdCardImage: this.form.corporationIdCardImage,
        roadTransportOperationLicenseImage: this.form.roadTransportOperationLicenseImage,
        licenseImage: this.form.licenseImage,
        idCardImage: this.form.idCardImage,
        powerOfAttorney: this.form.powerOfAttorney,
        userId: this.$route.query.id
      }
    },
    audit(isPass) {
      let validatePromise = Promise.resolve()
      if (isPass) validatePromise = this.$refs.form.validate()
      validatePromise
        .then(valid => {
          if (isPass && !valid) return
          let p = isPass ? this.$confirm('确认通过审核') : Promise.resolve()
          p.then(() => {
            let params = {
              ...this.getParams()
            }
            if (isPass) {
              params.transportationCapacitySupplierStatus = '2'
            } else {
              params.transportationCapacitySupplierStatus = '3'
              let rejectReasonList = [...this.rejectForm.region]
              if (this.rejectForm.textarea) rejectReasonList.push(this.rejectForm.textarea)
              params.rejectReason = rejectReasonList.join(',')
            }
            this.$post('/admin-center-server/transportationCapacitySupplierExamine', params)
              .then(res => {
                this.$message.success('操作成功')
                setTimeout(() => {
                  window.close()
                }, 1000)
              })
          })
        })
    },
    save() {
      this.$refs.form.validate()
        .then(valid => {
          if (!valid) return
          let params = {
            ...this.getParams()
          }
          this.$post('/admin-center-server/commonUser/updateTransportationCapacitySupplier', params)
            .then(() => {
              this.$message.success('操作成功')
              this.$router.go(-1)
            })
        })
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  margin: 10px;
  padding: 10px;
  padding-bottom: 80px;
  background: #fff;
}
.container-audit {
  padding-top: 140px;
  .img-list {
    margin-top: 97px;
  }
}
.audit-top {
  // margin: 30px -10px 0px;
  position: fixed;
  top: 142px;
  left: 210px;
  right: 25px;
  z-index: 99;
  // background-color: rgb(249, 249, 249);
  background-color: white;
  .top-content {
    background-color: rgb(249, 249, 249);
    margin:0px 1px;
  }
  .top-title {
    padding: 20px;
    font-size: 16px;
  }
  .top-sn {
    font-size: 18px;
    font-weight: 400;
  }

  .top-button {
    display: flex;
    .top-button-info {
      width: 108px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
    }
    .top-button-log {
      width: 108px;
      text-align: center;
      height: 30px;
      line-height: 30px;
      cursor: pointer;
    }

    .top-button-select {
      background-color: white;
    }
  }
}

.audit-bottom {
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 100;
  box-sizing: border-box;
  width: calc(100% - 180px);
  height: 80px;
  padding: 10px 60px 0px;
  background: #fff;
  display: flex;
  align-items: center;
  span {
    flex-grow: 1;
  }
}
.auto-base {
  position: fixed;
    top: 93px;
    left: 210px;
    right: 20px;
    z-index: 100;
    padding-left: 5px;
    background-color: rgb(250, 250, 250);
}
.main {
  margin-top: 20px;
}
.form {
  width: 40%;
  .el-select,
  .el-date-editor {
    width: 100%;
  }
  ::v-deep .el-form-item__label {
    line-height: 16px;
  }
}
.expire-text {
  color: #D92929;
}
</style>