// $baseColor:#324157;
// //sidebar
// $menuText:#bfcbd9;
// $menuActiveText:#409EFF;
// $subMenuActiveText:#f4f4f5; //https://github.com/ElemeFE/element/issues/12951

// $menuBg:#304156;
// $menuHover:#263445;

// $subMenuBg:#1f2d3d;
// $subMenuHover:#001528;

$sideBarWidth: 200px;
// the :export directive is the magic sauce for webpack
:export {
    // menuText: $menuText;
    // menuActiveText: $menuActiveText;
    // subMenuActiveText: $subMenuActiveText;
    // menuBg: $menuBg;
    // menuHover: $menuHover;
    // subMenuBg: $subMenuBg;
    // subMenuHover: $subMenuHover;
    sideBarWidth: $sideBarWidth;
}