import axios from 'axios'
import Vue from 'vue'
import { Message } from 'element-ui'
import { getToken } from '@/utils/auth'
import store from '../store'
import { grayFn } from './index.js'
export const instance = axios.create({
  baseURL: process.env.VUE_APP_BASE_API
})
instance.interceptors.request.use(
  config => {
    // 灰度标识
    if(grayFn()){
      config.headers['Env'] = 'gray'
    }
    config.headers['AuthMgr'] = getToken()
    return config
  }
)
const logout = () => {
  store.dispatch('LogOut').then(()=>{
    location.reload()
  })
}
const handleResponse = res => {
  let data = res.data
  if (data.code === '200' || data.code === 200) {
    return data.data
  } else {
    Message.error(data.message)
    return Promise.reject()
  }
}
const handleError = err => {
  if (!err) {
    return
  }
  let data = err.response.data
  if (data && data.code === 403) {
    Message.error(data.message)
    setTimeout(() => {
      logout()
    }, 1000)
  } else {
    Message.error('服务器错误')
  }
  return err
}
export const $post = (url, data, config) => {
  return instance.post(url, data, config)
    .then(res => {
      return handleResponse(res)
    })
    .catch(err => {
      throw handleError(err)
    })
}

export const $get = (url, data, config) => {
  // 登录页也加上灰度标识
  if(grayFn()){
    config = config || {}
    config.headers = config.headers || {}
    config.headers['Env'] = 'gray'
  }
  return instance.get(url, {
    params: data,
    ...config
  })
    .then(res => {
      return handleResponse(res)
    })
    .catch(err => {
      throw handleError(err)
    })
}
export const $httpMap = axios.create({
  baseURL: 'https://restapi.amap.com',
  timeout: 10000
})
Vue.prototype.$get = $get
Vue.prototype.$post = $post
Vue.prototype.$httpMap = $httpMap