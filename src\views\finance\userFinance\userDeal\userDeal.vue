<template>
  <div class="app-container userDeal">
    <div class="base">
      当前平台主体：{{ currentBase.baseName }}<i @click="changeBase" class="el-icon-sort"></i>
    </div>
    <div class="content">
    <el-button v-if="activeName!='first'&&activeName!='seventh'" type="primary" class="imgBtn" @click="showImg">{{activeNameText}}收支示意图</el-button>
    <el-image class="imgShow" ref="bigImg" :preview-src-list="srcList" :src="require('@/assets/images/warn.png')"></el-image>
      <el-tabs v-model="activeName">
        <el-tab-pane label="平台账户" name="first"><PlaformDeal :baseId="currentBase.id"  /></el-tab-pane>
        <!-- <el-tab-pane label="用户账户" name="second"><CustomerDeal :baseId="currentBase.id"/></el-tab-pane> -->
        <el-tab-pane label="货主账户" name="third"><FlowRecord :subsidyList='subsidyList' :allTransactionType='allTransactionType'  :baseId="currentBase.id" type='1' /></el-tab-pane>
        <el-tab-pane label="司机/个人车队长账户" name="fourth"><FlowRecord :subsidyList='subsidyList' :allTransactionType='allTransactionType'  :baseId="currentBase.id" type='2'/></el-tab-pane>
        <el-tab-pane label="供应商账户" name="fifth"><FlowRecord :subsidyList='subsidyList' :allTransactionType='allTransactionType'  :baseId="currentBase.id" type='3' /></el-tab-pane>
        <el-tab-pane label="渠道账户" name="sixth"><FlowRecord :subsidyList='subsidyList' :allTransactionType='allTransactionType'  :baseId="currentBase.id" type='4' /></el-tab-pane>
        <el-tab-pane label="手动转账" name="seventh"><FlowRecord :subsidyList='subsidyList'  :allTransactionType='allTransactionType' :baseId="currentBase.id" type='5' /></el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import PlaformDeal from "./platformDeal.vue";
import CustomerDeal from "./customerDeal.vue";
import FlowRecord from "./flowRecord.vue";
export default {
  components: {
    PlaformDeal,
    // CustomerDeal,
    FlowRecord
  },
  data() {
    return {
      activeName: "first",
      baseInfo: {},
      currentBase: {},
      payTypeList: [],
      allTransactionType:[],
      subsidyList:[],
      srcList: [
        require('@/assets/images/warn.png')
      ],
      allImg: {
        'third': require('@/assets/images/note/huozhu.png'),
        'fourth': require('@/assets/images/note/siji.png'),
        'fifth': require('@/assets/images/note/gongyingshang.png'),
        'sixth': require('@/assets/images/note/qudao.png')
      }
      
    };
  },
  computed:{
    //页签名字
    
  },
  methods: {
    changeBase() {
      let baseInfo = this.baseInfo;
      let currentIndex = baseInfo.findIndex(
        (v) => v.id === this.currentBase.id
      );
      let toggleIndex = null;
      if (currentIndex === baseInfo.length - 1) {
        toggleIndex = 0;
      } else {
        toggleIndex = currentIndex + 1;
      }
      this.currentBase = baseInfo[toggleIndex];
      localStorage.setItem("UserCurrentBaseId", this.currentBase.id);

      setTimeout(() => {
        this.$message.success("切换成功");
      }, 1000);
    },
        // 获取交易类型
    getAllTransactionType() {
      this.$get(
        "/admin-center-server/transaction/flow/queryUserTransactionType"
      ).then((res) => {
        this.allTransactionType = res
      });

    },
    // 获取下游补贴类型
    getSubsidyDownList(){
      this.$get('/order-center-server/order/dict/findDictByType?dictType=subsidyDown').then(res=>{
        this.subsidyList = res
      })
    },
    // 点击预览
    showImg() {
      this.$refs.bigImg.showViewer = true
      this.srcList = [this.allImg[this.activeName]]
    }
  },
  created() {    
    this.getAllTransactionType()
    this.getSubsidyDownList()
  },
  mounted() {
    // 获取url参数 accountType 
    this.activeName = this.$route.query.accountType || 'first'
  },
  activated() {
    let baseInfo = (this.baseInfo = this.$store.state.user.baseInfo);
    let currentBaseId = localStorage.getItem("UserCurrentBaseId");
    if (currentBaseId) {
      this.currentBase = baseInfo.find((v) => v.id == currentBaseId);
    } else {
      this.currentBase = baseInfo.find((v) => v.defaultFlag);
      localStorage.setItem("UserCurrentBaseId", this.currentBase.id);
    }
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.userDeal {
  .content{
    position: relative;
      .imgBtn {
        position: absolute;
        right: 20px;
        top: 20px;
        z-index: 5;
      }
      ::v-deep.imgShow {
        position: absolute;
        right: -999px;
        .el-image-viewer__mask {
          background-color: #fff;
          opacity: 1;
        }
      }
  }

  .base {
    margin-bottom: 10px;
    font-size: 14px;
    i {
      transform: rotate(90deg);
      color: #f6a018;
      cursor: pointer;
    }
  }
  .content {
    background-color: white;
    padding: 20px;
  }
}
</style>
