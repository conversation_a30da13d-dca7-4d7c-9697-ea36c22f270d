<template>
  <div class="app-container userDeal">
    <div class="base">
      当前平台主体：{{ currentBase.baseName }}<i @click="changeBase" class="el-icon-sort"></i></div>
    <div class="content">
      <el-tabs v-model="activeName">
        <el-tab-pane label="平台账户" name="first"><PlaformDeal :baseId="currentBase.id"/></el-tab-pane>
        <el-tab-pane label="用户账户" name="second"><CustomerDeal :baseId="currentBase.id"/></el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import PlaformDeal from "./platformDeal.vue";
import CustomerDeal from "./customerDeal.vue";
export default {
  components: {
    PlaformDeal,
    CustomerDeal
  },
  data() {
    return {
      activeName: "first",
      baseInfo: {},
      currentBase: {},
      payTypeList: [],
    };
  },
  methods: {
    changeBase() {
      let baseInfo = this.baseInfo;
      let currentIndex = baseInfo.findIndex(
        (v) => v.id === this.currentBase.id
      );
      let toggleIndex = null;
      if (currentIndex === baseInfo.length - 1) {
        toggleIndex = 0;
      } else {
        toggleIndex = currentIndex + 1;
      }
      this.currentBase = baseInfo[toggleIndex];
      localStorage.setItem("UserCurrentBaseId", this.currentBase.id);

      setTimeout(() => {
        this.$message.success("切换成功");
      }, 1000);
    },
  },
  activated() {
    let baseInfo = (this.baseInfo = this.$store.state.user.baseInfo);
    let currentBaseId = localStorage.getItem("UserCurrentBaseId");
    if (currentBaseId) {
      this.currentBase = baseInfo.find((v) => v.id == currentBaseId);
    } else {
      this.currentBase = baseInfo.find((v) => v.defaultFlag);
      localStorage.setItem("UserCurrentBaseId", this.currentBase.id);
    }
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.userDeal {
  .base {
    margin-bottom: 10px;
    font-size: 14px;
    i {
      transform: rotate(90deg);
      color: #f6a018;
      cursor: pointer;
    }
  }
  .content {
    background-color: white;
    padding: 20px;
  }
}
</style>
