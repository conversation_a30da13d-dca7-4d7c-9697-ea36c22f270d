<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">司机车主冻结金额查询</div>
      <div class="select-info">
        <el-form :inline="true"
                 :model="formInline"
                 class="demo-form-inline"
                 size="mini"
                 label-width="120px">
          <el-form-item label="司机／车主名称:">
            <el-input v-model="formInline.name"
                      placeholder="请输入司机／车主名称"></el-input>
          </el-form-item>
          <el-form-item label="手机号:">
            <el-input v-model="formInline.tel"
                      oninput="value=value.replace(/[^\d]/g,'')"
                      maxlength="11"
                      placeholder="请输入手机号"></el-input>
          </el-form-item>
          <el-form-item label="交易交易编号:">
            <el-input v-model="formInline.serial"
                      placeholder="请输入交易编号"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button class="left"
                       icon="el-icon-search"
                       @click="onSubmit">查询</el-button>
          </el-form-item>
        </el-form>

      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>司机车主冻结金额列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table ref="multipleTable"
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%"
                    :height="tableHeight"
                    @selection-change="handleSelectionChange"
                    border>

            <el-table-column type="index"
                             label="序号"
                             width="55">
            </el-table-column>
            <el-table-column show-overflow-tooltip
                             v-for="item in tableLabel"
                             :label="item.label"
                             :width="item.width ? item.width : ''">
              <template slot-scope="scope">
                <span style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page="pageNumber"
                         :page-sizes="[10,20, 40, 60, 80,100]"
                         :page-size="pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total"
                         class="pagination"></el-pagination>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
const list = '/admin-center-server/taxFrozen/queryDriverFrozenPageList'//列表
export default {
  name: "CarsList",
  data () {
    return {
      tableHeight: null, //表格的高度
      tableLabel: [
        {
          prop: 'businessSn',
          label: '交易编号',
          width: 200
        },
        {
          prop: 'userName',
          label: '用户名称'
        },
        {
          prop: 'mobile',
          label: '手机号'
        },
        {
          prop: 'quarterlyIncomeMoney',
          label: '季度收入总额（元）'
        },
        {
          prop: 'frozenMoney',
          label: '冻结金额（元）'
        },
        {
          prop: 'deductionMoney',
          label: '冻结金额扣款（元）'
        }
      ],
      pageSize: 10,
      total: 0,
      pageNumber: 1,
      //目前查询时候还差个id查询
      formInline: {
        name: "",
        tel: "",
        serial: '',
      },
      tableData: []
    };
  },
  methods: {
    onSubmit () {
      this.pageNumber = 1
      this.getData()
    },
    editfn (row) {
      this.$router.push("/consignorAccount/addShipper?urlType=1&id=" + row.id);
    },
    goDetail (row) {
      // this.$router.push("/consignorAccount/accountDetails?id=" + row.id);
      this.$router.push("/paying/withholding_record?id=" + row.id);
    },
    addnewfn () { this.$router.push("/consignorAccount/addShipper?urlType=1"); },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },

    handleSizeChange (val) {
      this.pageNumber = 1
      this.pageSize = val
      this.getData()
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange (val) {
      this.pageNumber = val
      this.getData()
      console.log(`当前页: ${val}`);
    },
    getData () {
      var v = this.formInline;
      var url = list + `?businessSn=${v.serial}&mobile=${v.tel}&userName=${v.name}&pageNumber=${this.pageNumber}&pageSize=${this.pageSize}`
      this.$http.get(url).then(res => {
        var data = res.data.data
        this.tableData = data.list
        this.total = Number(data.total)
      })
    }
  },
  activated () {
    this.getData()
    this.tableHeight =
      window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 160;

  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
