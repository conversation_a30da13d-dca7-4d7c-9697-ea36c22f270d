<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">
        <div>筛选查询</div>
        <div class="button">
          <el-button
            class="left"
            icon="el-icon-search"
            size="mini"
            type="primary"
            @click="onSubmit"
          >查询</el-button>
          <el-button
            class="left"
            icon="el-icon-delete"
            size="mini"
            type="danger"
            @click="recet"
          >清空筛选</el-button>
        </div>
      </div>
      <div class="select-info">
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="120px">
          <el-form-item label="活动名称:">
            <el-input v-model.trim="formInline.activityName" placeholder="请输入活动名称" maxlength="50"></el-input>
          </el-form-item>

          <el-form-item label="活动状态:">
            <el-select v-model="formInline.activityStatus" placeholder="请选择" clearable>
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          label-width="150px"
          style="margin-top: 30px"
        ></el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
        <div>
          <el-button class="releaseMessage" @click="addEvent">添加活动</el-button>

          <el-select v-model="sort" placeholder="请选择" style="width: 100px">
            <el-option
              v-for="item in sortoptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="list-main">
        <template>
          <el-table
            ref="multipleTable"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            border
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column type="index" label="编号" width="55"></el-table-column>
            <el-table-column prop="activityName" label="活动名称"></el-table-column>
            <el-table-column prop="beginDate" label="开始时间"></el-table-column>
            <el-table-column prop="endDate" label="结束时间"></el-table-column>
            <el-table-column prop="activityStatusEnum" label="状态"></el-table-column>
            <el-table-column prop="online" label="下架/上架">
              <template scope="scope">
                <el-switch
                  on-text="是"
                  off-text="否"
                  on-color="#5B7BFA"
                  off-color="#dadde5"
                  v-model="scope.row.online"
                  @change="change(scope.$index,scope.row)"
                ></el-switch>
              </template>
            </el-table-column>

            <el-table-column fixed="right" label="操作" width="160">
              <template slot-scope="scope">
                <el-button @click="goDetail(scope.row)" type="text" size="small">查看</el-button>
                <el-button type="text" size="small" @click="editorDetail(scope.row)">编辑</el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="release(scope.row)"
                  v-if="scope.row.activityStatus == '0' "
                >发布</el-button>
                <el-button type="text" size="small" @click="deleteItem(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="flex">
            <div class="delAll">
              <el-button @click="deleteAll">批量删除</el-button>
            </div>
            <div class="pageSize">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="formInline.pageNumber"
                :page-sizes="[20, 40, 60, 80,100]"
                :page-size="formInline.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                style="margin: 10px auto"
              ></el-pagination>
            </div>
          </div>
        </template>
        <!-- 查看的弹窗 -->
        <el-dialog title="查看消息" :visible.sync="dialogTableVisible" class="messageDetail">
          <p>{{title}}</p>
          <h4>{{time}}</h4>
          <h5 class="content" v-html="content">{{content}}</h5>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CarsList",
  comments: {},
  data() {
    return {
      dialogTableVisible: false,
      sort: "desc", //设置降序默认

      time: "",
      title: "",
      content: "",
      options: [
        {
          value: "0",
          label: "未开始"
        },
        {
          value: "1",
          label: "进行中"
        },
        {
          value: "2",
          label: "已结束"
        }
      ],
      sortoptions: [
        {
          value: "desc",
          label: "按时间降序"
        },
        {
          value: "asc",
          label: "按时间升序"
        }
      ],

      formInline: {
        activityName: "", //活动名称
        activityStatus: "", // 活动状态
        pageNumber: 1,
        pageSize: 20
      },
      tableData: [],
      total: null,
      multipleSelection: [] //多选的值
    };
  },
  activated() {
    this.getDataList(); //获取数据列表
  },
  methods: {
    /* 查询 */
    onSubmit() {
      this.formInline.pageNumber =1;
      this.getDataList();
    },
    /* 重置 */
    recet() {
      this.formInline = {
        activityName: "", //活动名称
        activityStatus: "", // 活动状态
        pageNumber: 1,
        pageSize: 20
      };
      this.getDataList();
    },
    goDetail(row) {
      console.log(row);
      console.log(row.activityName, "active----");
      var that = this;
      that.dialogTableVisible = true;
      that.title = row.activityName;
      that.content = row.content;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    /* 选取每页多少条数据 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.formInline.pageNumber;
      this.formInline.pageSize = JSON.parse(`${val}`);
      console.log(this.formInline.pageSize);
      this.getDataList();
    },
    /* 翻页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.formInline.pageNumber = JSON.parse(`${val}`);
      this.getDataList();
    },
    /* 表格中的开关 */
    change: function(index, row) {
      console.log(index, row);

      let ids = row.id;
      console.log(ids, "------");
      let isOnline = row.online;
      var postData = {
        ids: ids,
        isOnline: isOnline
      };
      this.$http
        .post(
          "/admin-center-server/avtivity/updateIsOnline",
          this.$qs.stringify(postData)
        )
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 添加活动 */
    addEvent() {
      this.$router.push({
        path: "addEvent",
        query: {
          flag: 1 //添加活动
        }
      });
    },
    /* 编辑每一条的活动详情 */
    editorDetail(row) {
      this.$router.push({
        path: "addEvent",
        query: {
          content: row.content,
          title: row.activityName, //活动标题
          flag: 2, //表示编辑
          id: row.id,
          activityImageUrl: row.activityImageUrl,
          activityType: row.activityType, // 0:站内活动 ，1:网页活动
          path: row.path, //网页链接
          beginDate: row.beginDate, //开始日期
          endDate: row.endDate //结束日期
        }
      });
    },

    /* 删除某行数据 */
    deleteItem(row) {
      let id = row.id;
      console.log(row.id, "-----id");
      let ids = [];
      ids[0] = Number(row.id);

      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http
            .post("/admin-center-server/avtivity/deleteByActivityIds", ids)
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
              }
              this.getDataList();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    /* 批量删除 */
    deleteAll() {
      var deleteAllData = this.multipleSelection; //批量删除的数据
      let ids = deleteAllData.map(obj => {
        return obj.id;
      });
      console.log(ids, "----");
      this.dialogVisible = true;
      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http
            .post("/admin-center-server/avtivity/deleteByActivityIds", ids)
            .then(res => {
              let data = res.data;
              if (data.code == "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
              }
              this.getDataList();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    /* 获取数据 */
    getDataList() {
      var postData = {
        order: this.sort,
        activityName: this.formInline.activityName, //活动名称
        activityStatus: this.formInline.activityStatus, // 活动状态
        pageNumber: this.formInline.pageNumber,
        pageSize: this.formInline.pageSize
      };
      console.log(postData, "postData");
      this.$http
        .post("/admin-center-server/avtivity/queryActivityList", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            console.log(data);
            this.tableData = data.data.list;
            this.total = JSON.parse(data.data.total);
            console.log(this.tableData, "tableData----");
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 发布 */
    release(row) {
      console.log(row.endDate, "---endDate");
      console.log(row.id, "----id");
      let postData = {
        id: row.id,
        endDate: row.endDate
      };
      console.log(postData, "postData");

      this.$http
        .post(
          "/admin-center-server/avtivity/releaseActivity",
          this.$qs.stringify(postData)
        )
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "发布成功!"
            });
            this.getDataList(); //获取数据列表
          } else {
            this.$message.warning(data.message);
          }
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.el-form-item {
  margin-bottom: 0;
}
.messageDetail p {
  text-align: center;
  font-weight: 600;
  font-size: 20px;
}
.messageDetail h4 {
  font-size: 16px;
  text-align: center;
  font-weight: 200;
}
.messageDetail h5 {
  font-size: 16px;
  line-height: 30px;
}
.top-title {
  font-size: 16px;
  font-weight: 700;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  border-bottom: 1px solid #cccccc;
  display: flex;
  justify-content: space-between;

  .button {
    margin-right: 20px;
  }
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }
    .page {
      text-align: right;
    }
    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
    .messageDetail p {
      text-align: center;
      font-weight: 600;
      font-size: 20px;
    }
    .messageDetail h4 {
      font-size: 16px;
      text-align: center;
      font-weight: 200;
    }
    .messageDetail h5 {
      font-size: 16px;
      line-height: 30px;
    }
  }
}
</style>
