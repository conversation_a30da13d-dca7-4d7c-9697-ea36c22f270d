<template>
  <div class="app-container addCar">
    <div class="tip">
      <div>添加广告位</div>
      <div>
        <em style="color: red">*</em>为必填项
      </div>
    </div>
    <div class="inner-box">
      <el-form :model="ruleForm" ref="ruleForm" label-width="200px" class="demo-ruleForm">
        <el-form-item label="广告名称" required>
          <el-input
            v-model="ruleForm.name"
            style="width: 220px"
            placeholder="请输入广告名称"
            maxlength="50"
          ></el-input>
        </el-form-item>

        <el-form-item label="广告位置" required>
          <el-select v-model="ruleForm.positionType" placeholder="请选择">
            <el-option v-for="item in positons" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="高度" required>
          <el-input
            v-model.number="ruleForm.height"
            style="width: 220px"
            placeholder="请输入广告高度"
             @keyup.native="numHeight"
             maxlength="8"
          ></el-input>
        </el-form-item>
        <el-form-item label="宽度" required>
          <el-input
            v-model.number="ruleForm.width"
            style="width: 220px"
            placeholder="请输入广告宽度"
            @keyup.native="numWidth"
            maxlength="8"
          ></el-input>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="ruleForm.description" style="width: 220px" placeholder="请输入广告名称"></el-input>
        </el-form-item>
        <el-form-item label="模版" required>
          <el-input
            type="textarea"
            placeholder="请输入内容"
            v-model="ruleForm.template"
            maxlength="300"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button @click="commitInfo" v-if="typeFlag ==1" type="primary">提交</el-button>
          <el-button @click="editorInfo" v-if="typeFlag ==2" type="primary" >提交</el-button>
          <el-button @click="goBack">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      ruleForm: {
        name: "", //广告名称
        positionType: "", //广告位置
        height: "", //高度
        width: "", //宽度
        description: "", //描述
        template: "" //备注
      },
      rules: {
        name: [
          { required: true, message: "请输入活动名称", trigger: "blur" },
          { message: "长度在 3 到 5 个字符", trigger: "blur" }
        ]
      },

      positons: [
        {
          id: "0",
          name: "banner-货主端"
        },
        {
          id: "1",
          name: "banner—司机端"
        },
        {
          id: "2",
          name: "banner—调度员端"
        },
        {
          id: "3",
          name: "启动页—货主端"
        },
        {
          id: "4",
          name: "启动页—司机端"
        },
        {
          id: "5",
          name: "启动页—调度员端"
        },
        {
          id: "6",
          name: "弹出广告—客户端"
        },
        {
          id: "7",
          name: "弹出广告—司机端"
        },
        {
          id: "8",
          name: "弹出广告—调度员端"
        }

      ],
      value: "",
      typeFlag: ""
    };
  },
  activated() {
    /* 添加 */
    this.typeFlag = this.$route.query.typeFlag;
    if (this.typeFlag == 1) {
      //新增
    } else if (this.typeFlag == 2) {
      //修改
      this.getInfo(); //回显
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          alert("submit!");
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },

    // ruleForm: {
    //         name: "", //广告名称
    //         positionType: "", //广告位置
    //         height: "", //高度
    //         width: "", //宽度
    //         description: "", //描述
    //         template: "" //备注
    //       },
    /*添加的提交*/
    commitInfo() {
      let postData = this.ruleForm;
      console.log(postData, "postData-------");

      if (this.ruleForm.name == "") {
        this.$message({
          type: "warning",
          message: "请填写广告名称"
        });
        return;
      }
      if (this.ruleForm.positionType == "") {
        this.$message({
          type: "warning",
          message: "请选择广告位置"
        });
        return;
      }
      if (this.ruleForm.height == "") {
        this.$message({
          type: "warning",
          message: "请填写高度"
        });
        return;
      }
      if (this.ruleForm.width == "") {
        this.$message({
          type: "warning",
          message: "请填写宽度"
        });
        return;
      }
      if (this.ruleForm.template == "") {
        this.$message({
          type: "warning",
          message: "请填写模版文字"
        });
        return;
      }

      this.$http
        .post("/admin-center-server/ad/addAdposition", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "添加成功!"
            });
            this.$router.go(-1);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 编辑 */
    editorInfo() {
      if (this.ruleForm.name == "") {
        this.$message({
          type: "warning",
          message: "请填写广告名称"
        });
        return;
      }
      if (this.ruleForm.positionType == "") {
        this.$message({
          type: "warning",
          message: "请选择广告位置"
        });
        return;
      }
      if (this.ruleForm.height == "") {
        this.$message({
          type: "warning",
          message: "请填写高度"
        });
        return;
      }
      if (this.ruleForm.width == "") {
        this.$message({
          type: "warning",
          message: "请填写宽度"
        });
        return;
      }
      if (this.ruleForm.template == "") {
        this.$message({
          type: "warning",
          message: "请填写模版文字"
        });
        return;
      }

      let postData = this.ruleForm;
      postData.id = this.$route.query.id;

      this.$http
        .post("/admin-center-server/ad/updateAdposition", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "添加成功!"
            });
            this.$router.go(-1);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 回显 */
    getInfo() {
      let id = this.$route.query.id;
      this.$http
        .get("/admin-center-server/ad/getAdpositionDesc?id=" + id)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.ruleForm = {
              name: data.data.name,
              positionType: data.data.positionType, //广告位置
              height: data.data.height, //高度
              width: data.data.width, //宽度
              description: data.data.description, //描述
              template: data.data.template //备注
            };
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 返回 */
    goBack() {
      this.$router.go(-1);
    },

    /* 校验 */
     numWidth(){
        this.ruleForm.width=this.ruleForm.width.replace(/[^\.\d]/g,'');
        this.ruleForm.width=this.ruleForm.width.replace('.','');
      },
     numHeight(){
        this.ruleForm.height=this.ruleForm.height.replace(/[^\.\d]/g,'');
        this.ruleForm.height=this.ruleForm.height.replace('.','');
      }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.hide .el-upload--picture-card {
  display: none;
}
.addCar {
  background-color: #ffffff;
  .tip {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 40px 40px 0;
    font-size: 12px;
    em {
      margin-right: 5px;
    }
  }
  .inner-box {
    margin-left: 10%;
    width: 70%;
    .upload-box {
      width: 100%;
      height: 100%;
      position: relative;
      .icon-XZ {
        width: 92px;
        height: 92px;
        margin: 0 auto;
        background: url("../../../../assets/xiazai.png") no-repeat;
        background-size: 100% 100%;
      }
      .icon-word {
        width: 100%;
        height: 20px;
        line-height: 20px;
        font-size: 10px;
        position: absolute;
        bottom: 25px;
        left: 0px;
        color: #cccccc;
      }
    }
  }
}

.editor {
  margin-top: 30px;
}

.myQuillEditor {
  height: 400px;
}
.editCommit {
  margin: 100px;
}
</style>
