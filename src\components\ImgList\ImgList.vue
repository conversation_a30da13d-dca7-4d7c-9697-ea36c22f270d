<template>
  <div class="img-list" :class="{ static: static }" ref="imgList">
    <div class="wrapper">
      <slot></slot>
    </div>
    <el-image-viewer v-if="showedPic"
      :on-close="handlePicClose"
      :url-list="[showedPic]"/>
  </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  components: {
    ElImageViewer
  },
  props: {
    static: false //false 绝对定位在右上角 true 默认文档流
  },
  data() {
    return {
      showedPic: null
    }
  },
  methods: {
    showPic(pic) {
      this.showedPic = pic
      this.$nextTick(() => {
        let pic = document.querySelector('.el-image-viewer__wrapper')
        if (!pic) return
        pic.style.position = 'absolute'
        this.$refs.imgList.appendChild(pic)
      })
    },
    handlePicClose() {
      this.showedPic = null
    }
  }
}
</script>

<style scoped lang="scss">
.img-list {
  overflow: hidden;
  position: absolute;
  right: 3%;
  top: 40px;
  height: calc(100% - 150px);
  width: 50%;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  background: #fff;
  .wrapper {
    height: 100%;
    overflow-y: auto;
  }
}
.img-list.static {
  position: relative;
  width: 100%;
}
.img-list ::v-deep {
  .item-box {
    position: relative;
  }
  .img-box {
    position: relative;
    width: 270px;
    height: 174px;
    padding: 14px 11px;
    border: 2px dashed #DCDFE6;
    cursor: pointer;
    > img {
      display: block;
      height: 100%;
      width: 100%;
    }
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url(~@/assets/images/big.png) no-repeat center center;
    }
  }
  .img-box-empty {
    &::before {
      content: none;
    }
    > img {
      width: auto;
      margin: 0 auto;
    }
  }
}
</style>