<template>
    <!-- 车主、司机 -->
    <div class="app-container carsList">
        <div class="select-box">
            <div class="top-title">
            <div>
                税务查询>代扣代缴
            </div>
                <div>
                    <el-button class="left"
                               @click="onSubmit" size="mini" :loading="loading">导出
                    </el-button>
                    <el-button class="left"
                               :disabled="canUpLoad"
                               @click="uploadFn" type="primary" icon="el-icon-download" size="mini" :loading="loadLoading">
                        下载
                    </el-button>
                </div>
            </div>
        </div>
        <div class="list-box">
            <div class="list-title">
                <div>数据列表</div>
            </div>
            <div class="list-main">
                <template>
                    <el-table ref="multipleTable"
                              :data="tableData"
                              :height="tableHeight"
                              tooltip-effect="dark"
                              style="width: 100%"
                              @selection-change="handleSelectionChange"
                              border>
                        <el-table-column type="index"
                                         label="序号"
                                         width="55">
                        </el-table-column>
                        <el-table-column show-overflow-tooltip
                                         v-for="item in tableLabel"
                                         :label="item.label"
                                         :width='item.width?item.width:""'>

                            <template slot-scope="scope">
                <span style="margin-left: 10px;cursor:pointer;"
                      v-if="item.label=='用户名称'"
                      @click="goDriverDetailsfn(scope.row)">{{ scope.row[item.prop] }}</span>
                                <span style="margin-left: 10px;cursor:pointer;"
                                      v-else-if="item.label=='运单号'"
                                      @click="goWaybillDetailsfn(scope.row)">{{ scope.row[item.prop] }}</span>
                                <span v-else
                                      style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
                            </template>

                        </el-table-column>

                    </el-table>
                    <el-pagination @size-change="handleSizeChange"
                                   @current-change="handleCurrentChange"
                                   :current-page="pageNumber"
                                   :page-sizes="[10,20, 40, 60, 80,100]"
                                   :page-size="pageSize"
                                   layout="total, sizes, prev, pager, next, jumper"
                                   :total="total"
                                   class="pagination"></el-pagination>

                </template>
            </div>
        </div>
    </div>
</template>
<script>
    // 角色类型 目前没有 只有userType
    // 货物名称没有  目前有货物类型 cargoType
    // 托运人名称没有 目前有发货人姓名 consigneeName
    //跳转司机详情、运单详情
    const list = '/admin-center-server/taxFrozen/queryTaxOrderItemPageList'//列表
    const exportTax = '/order-center-server/exportTaxOrderItem'//导出
    const forUpdateInfo = '/admin-center-server/commonUser/forUpdateInfo'//用户基本信息
    export default {
        name: "CarsList",
        data() {
            return {
                canUpLoad: false,
                loading: false,
                loadLoading: false,
                tableHeight: null, //表格的高度
                id: '',
                total: 0,
                revenueType: '',
                month: '',
                year: '',
                moneyType: '',
                pageNumber: 1,
                pageSize: 20,
                paymentAmount:'',//付款金额合计 （2020.4.18 景伟让加的）
                tableLabel: [
                    {
                        prop: 'userName',
                        label: '用户名称',
                        width: 120
                    },
                    {
                        prop: 'idCard',
                        label: '身份证号／企业信用代码',
                        width: 200
                    },
                    {
                        prop: 'userTypeText',
                        label: '角色类型'
                    },
                    {
                        prop: 'mobile',
                        label: '手机号',
                        width: 120
                    },
                    {
                        prop: 'orderItemSn',
                        label: '运单号',
                        width: 210
                    },
                    {
                        prop: 'plateNumber',
                        label: '车牌号',
                        width: 120
                    },
                    {
                        prop: 'deliveryPlace',
                        label: '起运地',
                        width: 160
                    },
                    {
                        prop: 'receivePlace',
                        label: '到达地',
                        width: 160
                    },
                    {
                        prop: 'paymentDate',
                        label: '付款时间',
                        width: 180
                    },
                    {
                        prop: 'paymentAmount',
                        label: '付款金额（元）',
                        width: 120
                    },
                    // {
                    //     prop: 'vat',
                    //     label: '增值税'
                    // },
                    // {
                    //     prop: 'threeTaxTotal',
                    //     label: '城建税及两项附加',
                    //     width: 160
                    // },
                    // {
                    //     prop: 'individualIncomeTax',
                    //     label: '个人所得税',
                    //     width: 160
                    // },
                    {
                        prop: 'cargoType',
                        label: '货物名称'
                    },
                    {
                        prop: 'consigneeName',
                        label: '托运人名称',
                        width: 120
                    },
                    {
                        prop: 'consignerName',
                        label: '收货人名称',
                        width: 120
                    }
                ],
                formInline: {},
                tableData: []
            };
        },
        methods: {
            //导出
            onSubmit() {
                this.loading = true;
                if (this.moneyType != 0) {
                    let sendData = {
                        businessKey:1,
                        moneyType:this.moneyType,
                        month:JSON.parse(this.month).toString(),
                        revenueType:this.revenueType,
                        userType:this.type,
                        year:this.year,
                    };
                    this.$http.post('/statistics-center-server/tax/taxOrderItemExport',sendData).then(res => {
                        let data = res.data;
                        if(data.code==='200'){
                            this.$message.success(data.message);
                            this.loading = false;
                        }else {
                            this.$message.warning(data.message);
                            this.loading = false;
                        }
                    });
                } else {
                    this.$message.warning('请选择收入金额');
                    this.loading = false;
                }
            },
            /** 下载 **/
            uploadFn(){
                this.loadLoading=true;
                this.$http.get('/statistics-center-server/tax/downTaxOrderItemExport',{
                    params:{
                        businessKey:1
                    }
                }).then(res=>{
                    let data = res.data;
                    if(data.code===200){
                        if(data.data===''){
                            this.$message.warning('暂无下载任务');
                            this.loadLoading=false;
                        }else {
                            let url = data.data;
                            window.location.href=url;
                            this.loadLoading=false;
                        }
                    }else {
                        this.$message.warning(data.message)
                    }
                })
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            //跳转司机详情
            goDriverDetailsfn(row) {
                if (this.type == 3) {
                    var userId = row.userId
                    this.$http.get(forUpdateInfo + `?id=${userId}`).then(res => {
                        var data = res.data.data
                        var type = ''
                        if (data.carOwnerAuthStatus != 2 && data.driverAuthStatus == 2) {
                            type = 1
                        }
                        if (data.carOwnerAuthStatus == 2 && data.driverAuthStatus == 2 && data.carAuthType == 1) {
                            type = 2
                        }
                        if (data.carOwnerAuthStatus == 2 && data.driverAuthStatus != 2 && data.carAuthType == 1) {
                            type = 3
                        }
                        if (data.carOwnerAuthStatus == 2 && data.driverAuthStatus != 2 && data.carAuthType == 2) {
                            type = 4
                        }
                        this.$router.push("/driverAccount/accountDetails?id=" + userId + '&type=' + type);
                    })
                } else if (this.type == 4) {
                    this.$router.push("/consignorAccount/accountDetails?id=" + row.userId);
                } else if (this.type == 2) {
                    //brokerAuthType 目前没有
                    if (row.brokerAuthType == '个人') {
                        row.driverType = '1'
                    } else {
                        row.driverType = '2'
                    }
                    this.$router.push("/agentAccount/accountDetails?id=" + row.userId + '&type=' + row.driverType);
                }

            },
            handleSizeChange(val) {
                this.pageNumber = 1
                this.pageSize = val
                this.getData()
            },
            handleCurrentChange(val) {
                this.pageNumber = val
                this.getData()
            },
            //跳转运单详情
            goWaybillDetailsfn(row) {
                this.$router.push({
                    path: "/transport/transportListDetail",
                    query: {
                        orderItemId: row.orderItemId,//运单id
                        status: row.status,//订单状态
                        freezeStatus: row.freezeStatus//0未冻结1冻结
                    }
                });
            },
            getData() {
                var data = {
                    "month": JSON.parse(this.month),
                    "pageNumber": this.pageNumber,
                    "pageSize": this.pageSize,
                    "revenueType": this.revenueType,
                    "userId": this.id,
                    "userType": this.type,
                    "year": this.year,
                    'paymentAmount':this.paymentAmount,
                    'baseId': this.$route.query.baseId
                }
                this.$http.post(list, data).then(res => {
                    var data = res.data.data
                    for (var i = 0; i < data.list.length; i++) {
                        if (data.list[i].userType == 2) {
                            data.list[i].userTypeText = '调度员'
                        } else if (data.list[i].userType == 3) {
                            data.list[i].userTypeText = '司机'
                        } else {
                            data.list[i].userTypeText = '车队长'
                        }
                    }
                    this.tableData = data.list
                    this.total = Number(data.total)
                })
            },

            /** 页面进入  判断是否存在下载任务 **/
            enterCheck () {
                this.$http.get('/statistics-center-server/tax/downTaxOrderItemExport',{
                    params:{
                        businessKey:1
                    }
                }).then(res => {
                    let data = res.data;
                    if(data.data===''){
                        this.canUpLoad = true;
                    }else {
                        this.canUpLoad = false;
                    }
                });
            },

        },
        activated() {
            this.enterCheck();
            this.id = this.$route.query.id;
            this.paymentAmount = this.$route.query.totalPaymentAmount;
            this.revenueType = this.$route.query.revenueType;
            this.year = this.$route.query.year;
            this.type = this.$route.query.type;
            this.month = this.$route.query.month;
            this.moneyType = this.$route.query.moneyType;
            if (this.type == 3) {
                this.tableLabel = [
                    {
                        prop: 'userName',
                        label: '用户名称',
                        width: 120
                    },
                    {
                        prop: 'idCard',
                        label: '身份证号／企业信用代码',
                        width: 180
                    },
                    {
                        prop: 'userTypeText',
                        label: '角色类型'
                    },
                    {
                        prop: 'mobile',
                        label: '手机号',
                        width: 120

                    },
                    {
                        prop: 'orderItemSn',
                        label: '运单号',
                        width: 210
                    },
                    {
                        prop: 'deliveryPlace',
                        label: '起运地',
                        width: 160
                    },
                    {
                        prop: 'receivePlace',
                        label: '到达地',
                        width: 160
                    },
                    {
                        prop: 'paymentDate',
                        label: '付款时间',
                        width: 180
                    },
                    {
                        prop: 'paymentAmount',
                        label: '付款金额（元）',
                        width: 120
                    },
                    // {
                    //     prop: 'vat',
                    //     label: '增值税'
                    // },
                    // {
                    //     prop: 'threeTaxTotal',
                    //     label: '城建税及两项附加',
                    //     width: 160
                    // },
                    // {
                    //     prop: 'individualIncomeTax',
                    //     label: '个人所得税',
                    //     width: 160
                    // },
                    {
                        prop: 'cargoType',
                        label: '货物名称'
                    },
                    {
                        prop: 'consigneeName',
                        label: '托运人名称',
                        width: 120
                    },
                    {
                        prop: 'consignerName',
                        label: '收货人名称',
                        width: 120
                    }
                ]
            }
            this.getData();
            // this.tableHeight =
            //     window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 160;

        }
    };
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
    .carsList {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 14px;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                padding-right: 10px;
                border-bottom: 1px solid #cccccc;
                display: flex;
                flex-flow: row nowrap;
                justify-content: space-between;
            }

            .select-info {
                padding-top: 10px;
                padding-bottom: 10px;
            }
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;

            .list-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                div {
                    font-size: 14px;
                    height: 30px;
                    line-height: 30px;
                }
            }

            .list-main {
                width: 100%;
                //   border: 1px solid #cccccc;
                margin-top: 10px;
            }

            .releaseMessage {
                margin-right: 20px;
            }

            .pagination {
                text-align: right;
                margin-top: 10px;
            }
        }
    }
</style>
