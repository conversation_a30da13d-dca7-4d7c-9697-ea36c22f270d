<template>
    <div class="app-container detailedList">
        <div class="select-box">
            <div class="top-title">结算单列表点击清单</div>
<!--            开票金额: <span style="color: red">{{allInvoiceMoney}}</span> 元；-->
            <div class="curTotalInfo">当前搜索结果总计：开票吨数 <span style="color: red">{{allTon}}</span> 吨；结算金额 <span
                    style="color: red">{{allMoney}}</span>；</div>
        </div>
        <div class="list-box">
            <el-row>
                <div class="list-right">
                    <div class="right-title">
                        <div>数据列表</div>
                        <div>
                            <el-button v-if="showFlag==='0'" size="mini" type="primary" @click="changeMantissa">
                                <i class="el-icon-edit-outline"></i>
                                尾数调整
                            </el-button>
                            <el-button size="mini" type="primary" @click="getTicketInfo">
                                <i class="el-icon-view"></i>
                                查看开票信息
                            </el-button>
                        </div>
                    </div>
                    <div class="list-main">
                        <template>
                            <el-table :data="tableData" border style="width: 100%">
                                <el-table-column prop="num" label="序号" type="index" width="50"></el-table-column>
                                <el-table-column show-overflow-tooltip prop="sn" label="清单号"
                                                 width="220px"></el-table-column>
                                <el-table-column show-overflow-tooltip prop="finalStatementSn" label="结算单号"
                                                 width="220px"></el-table-column>
                                <el-table-column prop="amount" label="结算金额(元)" width="120"></el-table-column>
                                <el-table-column prop="taxAmount" label="补充运费(元)" width="120"></el-table-column>
                                <el-table-column prop="waitPayMoney" label="开票金额(元)" width="120"></el-table-column>

                                <el-table-column show-overflow-tooltip prop="transportAmount" label="结算运费(元)" width="120"></el-table-column>
                                <el-table-column show-overflow-tooltip prop="gasAmount" label="结算油气(元)" width="120"></el-table-column>

                                <el-table-column prop="ton" label="开票吨数(吨)" width="120"></el-table-column>
                                <el-table-column :formatter="invoiceStatus" prop="invoice"
                                                 label="清单状态"></el-table-column>
                                <el-table-column show-overflow-tooltip prop="createdDate"
                                                 label="创建日期"></el-table-column>
                                <el-table-column fixed="right" label="操作" width="180">
                                    <template slot-scope="scope">
                                        <el-button @click="handleClick(scope.row)" type="text" size="small">查看
                                        </el-button>
                                        <el-button v-if="scope.row.invoice==='0'" style="color:red" type="text"
                                                   size="small" @click="deleteRow(scope.row)">删除
                                        </el-button>
                                        <el-button :disabled="canExport" type="text" size="small"
                                                   @click="exportRow(scope.row)">导出
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </div>
                    <div class="paging">
                        <div class="block">
                            <el-pagination
                                    @size-change="handleSizeChange"
                                    @current-change="handleCurrentChange"
                                    :current-page="pageNumber"
                                    :page-sizes="[10, 20, 30, 40,50,100,200]"
                                    :page-size="pageSize"
                                    layout="total, sizes, prev, pager, next, jumper"
                                    :total="total"
                            ></el-pagination>
                        </div>
                    </div>
                </div>
            </el-row>
        </div>
        <!-- 删除列表弹窗-->
        <el-dialog
                title="删除确认"
                :visible.sync="dialogVisible"
                width="30%"
                :before-close="handleClose">
            <div style="height: 40px;">
                <i class="el-icon-info" style="color: red;"></i> <span style=" margin-left: 10px;
">是否确认删除清单？</span>
            </div>
            <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="suerDeleteRow">确 定</el-button>
      </span>
        </el-dialog>
        <!--开票信息-->
        <el-dialog
                title="开票信息"
                :visible.sync="listInfoMask"
                :before-close="handleClose">
            <div class="ticket-form">
                <div style="color: black;font-weight: 600">发票详情</div>
                <el-form ref="form" :model="formMask" label-width="120px" size="mini" style="margin-top: 10px">
                    <el-form-item label="抬头类型:" required>
                        {{formMask.headType}}
                    </el-form-item>
                    <el-form-item label="发票抬头:" required>
                        {{formMask.headName}}
                    </el-form-item>
                    <el-form-item label="税号:" required>
                        <el-input :disabled="tax" v-model="formMask.dutyNum"
                                  :οnkeyup="formMask.dutyNum=formMask.dutyNum.replace(/\s+/g,'')"
                                  style="width: 300px"></el-input>
                        <el-button style="margin-left: 20px;color: #00cb8a;" type="text" @click="tax=false">修改
                        </el-button>
                    </el-form-item>
                </el-form>
                <div style="color: black;font-weight: 600;margin-top: 20px">更多内容</div>
                <el-form ref="form" :model="formMask" label-width="120px" size="mini" style="margin-top: 10px">
                    <el-form-item label="地址和电话:" required>
                        <el-input v-model="formMask.addressPhone" style="width: 300px"></el-input>
                    </el-form-item>
                    <el-form-item label="开户行和账号:" required>
                        <el-input v-model="formMask.bankAccount" style="width: 300px"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="changeTicketInfo">确 定</el-button>
  </span>
        </el-dialog>
        <!--尾数调整-->
        <el-dialog
                title="提示"
                :visible.sync="Mantissa"
                :before-close="handleClose">
            <div>
                <el-form ref="formMantissa" :model="formMantissa" label-width="150px" size="mini"
                         style="margin-top: 10px">
                    <el-form-item label="当前总金额(含税):">
                        <el-input v-model="formMantissa.curtotalItems" style="width: 300px">
                            <template slot="append">元</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="调整总金额:">
                        <el-input :disabled="mantissaFlag" v-model="formMantissa.totalItems" style="width: 300px">
                            <template slot="append">元</template>
                        </el-input>
                        <el-button style="margin-left: 20px;color: #00cb8a;" type="text" @click="mantissaFlag=false">
                            修改
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="saveMantissa">确 定</el-button>
  </span>
        </el-dialog>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                allInvoiceMoney:'',
                showFlag: '',
                tax: true,
                mantissaFlag: true,
                formMantissa: {
                    totalItems: '',
                    curtotalItems: '',
                },
                formMask: {
                    headType: '',
                    headName: '',
                    dutyNum: '',
                    addressPhone: '',
                    bankAccount: '',
                },
                listInfoMask: false,
                dialogVisible: false,
                Mantissa: false,
                canExport: false,
                allMoney: '',
                allTon: '',
                currentPage: 1,
                tableData: [],
                pageNumber: 1,
                pageSize: 20,
                total: 1,
            };
        },
        methods: {
            /** 调整尾数 **/
            changeMantissa() {
                let finalStatementId = this.$route.query.id;
                this.$http.get('/admin-center-server/app/invoice_schedule/changeInfo', {
                    params: {
                        finalStatementId: finalStatementId
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.Mantissa = true;
                        this.formMantissa = {
                            totalItems: data.data.totalAmounts,
                            curtotalItems: data.data.totalAmounts,
                        };
                    } else if (data.code === '424') {
                        this.$message.warning(data.message)
                    } else {
                        this.$message.error(data.message)
                    }

                })
            },
            /** 确认尾数调整 **/
            saveMantissa() {
                let finalStatementId = this.$route.query.id;
                let form = {
                    totalAmounts: this.formMantissa.curtotalItems,
                    changeTotalAmounts: this.formMantissa.totalItems,
                    finalStatementId: finalStatementId
                };
                this.$http.post('/admin-center-server/app/invoice_schedule/changeMoney', form).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.$message.success(data.message);
                        this.mantissaFlag = true;
                        this.Mantissa = false;
                    } else {
                        this.mantissaFlag = true;
                        this.$message.warning(data.message)
                    }
                })
            },
            /** 获取开票信息 **/
            getTicketInfo() {
                let userId = this.$route.query.ownerId;
                this.$http.get('/admin-center-server/bill/getBillInfo', {
                    params: {
                        userId: userId
                    }
                }).then(res => {
                    let data = res.data;
                    this.listInfoMask = true;
                    if (data.code === '200') {
                        this.formMask = data.data;
                    } else {
                        this.$message.warning(data.message)
                    }
                })
            },
            /** 修改开票信息 **/
            changeTicketInfo() {
                let form = this.formMask;
                if (this.formMask.dutyNum === '' || this.formMask.dutyNum.length < 1 || this.formMask.dutyNum === '    ') {
                    this.$message.warning('请填写税号')
                } else {
                    this.$http.post('/admin-center-server/bill/create', form).then(res => {
                        let data = res.data;
                        if (data.code === '200') {
                            this.$message.success(data.message);
                            this.listInfoMask = false;
                            this.tax = true;
                        } else {
                            this.$message.warning(data.message);
                            this.tax = true;
                        }
                    })
                }
            },
            /** 删除行 **/
            deleteRow(row) {
                this.dialogVisible = true;
                let id = row.id;
                this.deleteId = id;
            },
            /** 确认删除 **/
            suerDeleteRow() {
                let id = this.deleteId;
                this.$http.post('admin-center-server/app/invoice_schedule/delete?invoiceScheduleId=' + id).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.$message.success(data.message);
                        this.dialogVisible = false;
                        this.getDataList();
                    } else {
                        this.$message.warning(data.message)
                    }
                })
            },
            /** 弹窗 上叉号关闭**/
            handleClose(done) {
                this.dialogVisible = false;
                this.Mantissa = false;
                this.listInfoMask = false;
                this.tax = true;
                this.mantissaFlag = true;
            },
            /** 清单状态 **/
            invoiceStatus(row) {
                if (row.invoice === "0") {
                    return "待确认";
                } else if (row.invoice === "1") {
                    return "待结算";
                } else if (row.invoice === "2") {
                    return "已结算";
                } else if (row.invoice == null) {
                    return "暂无";
                }
            },
            /** 导出行 **/
            exportRow(row) {
                this.canExport = true;
                let id = row.id;
                this.$http.get('/admin-center-server/app/invoice_schedule/exportSchedules', {
                    params: {
                        invoiceScheduleId: id
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === 200) {
                        let uploadUrl = data.data;
                        window.location.href = uploadUrl;
                        this.canExport = false;
                    } else {
                        this.$message.warning(data.message);
                        this.canExport = false;
                    }
                })
            },

            handleSizeChange(val) {
                this.pageSize = val;
                this.pageNumber = 1;
                this.getDataList();
            },
            handleCurrentChange(val) {
                this.pageNumber = val;
                this.getDataList();
            },
            getDataList() {
                let id = this.$route.query.id;
                this.$http
                    .get("/admin-center-server/app/invoice_schedule/list", {
                        params: {
                            finalStatementId: id,
                            pageNumber: this.pageNumber,
                            pageSize: this.pageSize,
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            // this.tableData = data.data.list;
                            this.total = Number(data.data.total);
                            let useList = data.data.list;
                            useList.map((item, index) => {
                                if (item.taxAmount === null) {
                                    item.taxAmount = 0;
                                }
                                if (item.amount === null) {
                                    item.amount = 0;
                                }
                            });
                            useList.map((item, index) => {
                                item.waitPayMoney = this.plus(item.amount, item.taxAmount)
                            });
                            this.tableData = useList;


                            if (data.data.total === '0') {
                                this.showFlag = false;
                            }
                        } else {
                            this.$message.error(data.message)
                        }
                    });
            },
            handleClick(row) {
                let id = row.id;
                let finalStatementSn = row.finalStatementSn;
                let sn = row.sn;
                let businessName = row.businessName;
                let amount = row.amount;
                let ton = row.ton;
                this.$router.push({
                    path: 'detailedListLook',
                    query: {
                        id: id,
                        businessName: businessName,
                        finalStatementSn: finalStatementSn,
                        sn: sn,
                        amount: amount,
                        ton: ton,
                    }
                })
            },
            getTitleInfo() {
                let id = this.$route.query.id;
                this.$http.get('/admin-center-server/app/invoice_schedule/getCounts', {
                    params: {
                        finalStatementId: id
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.allMoney = data.data.allMoney;
                        this.allTon = data.data.allTon;
                        this.allInvoiceMoney = data.data.allInvoiceMoney;
                    } else {
                        this.$message.warning(data.message)
                    }
                })
            },
            /** 解决浮点数计算 **/
            times(num1, num2) {
                const num1String = num1.toString();
                const num2String = num2.toString();
                const num1Digits = (num1String.split('.')[1] || '').length;
                const num2Digits = (num2String.split('.')[1] || '').length;
                const baseNum = Math.pow(10, num1Digits + num2Digits);
                return Number(num1String.replace('.', '')) * Number(num2String.replace('.', '')) / baseNum
            },
            plus(num1, num2) {
                const num1Digits = (num1.toString().split('.')[1] || '').length;
                const num2Digits = (num2.toString().split('.')[1] || '').length;
                const baseNum = Math.pow(10, Math.max(num1Digits, num2Digits));
                return (this.times(num1, baseNum) + this.times(num2, baseNum)) / baseNum
            },
        },
        activated() {
            this.getDataList();
            this.getTitleInfo();
            this.showFlag = this.$route.query.invoiceStatus
        }
    };
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .tip {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 40px 40px 0;
        font-size: 12px;

        em {
            margin-right: 5px;
        }
    }

    .inner-box {
        margin-left: 10%;
        width: 70%;

        .upload-box {
            width: 100%;
            height: 100%;
            position: relative;

            .icon-XZ {
                width: 92px;
                height: 92px;
                margin: 0 auto;
                background-size: 100% 100%;
            }

            .icon-word {
                width: 100%;
                height: 20px;
                line-height: 20px;
                font-size: 10px;
                position: absolute;
                bottom: 25px;
                left: 0px;
                color: #cccccc;
            }
        }
    }

    .detailedList {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
            }

            .select-info {
                padding-top: 20px;
            }

            .curTotalInfo {
                padding-left: 20px;
                height: 30px;
                line-height: 30px;
                font-size: 12px;
                color: #999999;
            }
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;
            overflow: hidden;

            .list-right {
                padding-left: 10px;

                .right-title {
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;

                    div {
                        height: 38px;
                        line-height: 38px;
                    }
                }

                .list-main {
                    width: 100%;
                    border: 1px solid #cccccc;
                    margin-top: 10px;
                }

                .paging {
                    margin-top: 10px;
                    float: right;
                }
            }
        }
    }
</style>
