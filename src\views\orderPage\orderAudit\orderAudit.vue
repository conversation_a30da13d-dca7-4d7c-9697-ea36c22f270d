<template>
  <div class="app-container">
    <div class="select-box">
      <div class="top-title">筛选查询</div>
      <div class="select-info">
        <el-form :inline="true" size="mini" label-width="110px">
          <el-form-item label="订单类型">
            <el-select class="form-item-content" v-model="searchForm.type" placeholder="请选择" clearable>
              <el-option label="平台单" value="0"></el-option>
              <el-option label="调度单" value="1"></el-option>
              <el-option label="货主单" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="订单编号">
            <el-input class="form-item-content" v-model="searchForm.sn" placeholder="请输入订单编号"></el-input>
          </el-form-item>
          <el-form-item label="创建人">
            <el-input class="form-item-content" v-model="searchForm.orderCreateUserName" placeholder="请输入创建人姓名"></el-input>
          </el-form-item>
          <el-form-item label="业务类型:">
            <el-select class="form-item-content" v-model="searchForm.businessTypeCode" placeholder="请选择">
              <el-option v-for="item in businessTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="客户名称">
            <el-input class="form-item-content" v-model="searchForm.businessName" placeholder="请输入客户名称"></el-input>
          </el-form-item>
          <el-form-item label="客户手机号">
            <el-input class="form-item-content" v-model="searchForm.businessPhone" placeholder="请输入客户手机号"></el-input>
          </el-form-item>
          <el-form-item label="创建日期">
            <el-date-picker
              v-model="createdDate"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              range-separator="至"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button @click="doSearch" type="primary">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>订单列表</div>
        <el-button @click="rejectReason" size="small" icon="el-icon-s-order" type="danger">驳回原因管理</el-button>
      </div>
      <div class="list-main">
        <el-table 
        :data="data" 
        border 
        :height="tableHeight"
        cell-class-name="table_cell_gray"
        header-cell-class-name="table_header_cell_gray">
          <el-table-column type="index" label="序号" width="50"></el-table-column>
          <el-table-column prop="sn" label="订单编号" width="200"></el-table-column>
          <el-table-column prop="businessName" label="客户名称" width="240"></el-table-column>
          <el-table-column prop="projectName" label="所属项目">
            <template slot-scope="scope">
              {{scope.row.projectName ? scope.row.projectName : '-'}}
            </template>
          </el-table-column>
          <el-table-column label="货物名称" width="200">
            <template slot-scope="scope">
              {{ scope.row.cargoTypeClassificationValue + ' / ' + scope.row.cargoType }}
            </template>
          </el-table-column>
          <el-table-column prop="paymentTypeEnum" label="支付方式"></el-table-column>
          <el-table-column prop="amount" label="司机运费" width="120"></el-table-column>
          <el-table-column prop="onlineInsuranceValue" label="是否投保" width="120"></el-table-column>
          <el-table-column prop="statusEnum" label="订单状态"></el-table-column>
          <el-table-column prop="consignerName" label="发货人"></el-table-column>
          <el-table-column prop="consignerPhone" label="发货人手机号" width="120"></el-table-column>
          <el-table-column prop="consigneeName" label="收货人"></el-table-column>
          <el-table-column prop="consigneePhone" label="收货人手机号" width="120"></el-table-column>
          <el-table-column prop="orderCreateUserName" label="创建人" width="130"></el-table-column>
          <el-table-column prop="createdDate" label="创建时间" width="130"></el-table-column>
          <el-table-column fixed="right" label="操作" width="100">
              <template slot-scope="scope">
                <el-button @click="audit(scope.row)" type="text" size="small">审核</el-button>
              </template>
            </el-table-column>
        </el-table>
        <div class="page">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page.pageNumber"
            :page-sizes="[20, 40, 60, 80, 100]"
            :page-size="page.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      businessTypeList: [],
      searchForm: {},
      createdDate: null,
      page: {
        pageNumber: 1,
        pageSize: 20
      },
      total: 0,
      data: [],
      tableHeight: null
    }
  },
  methods: {
    doSearch() {
      if (this.createdDate !== null) {
        this.searchForm.createdDate = this.createdDate[0]
        this.searchForm.completeDate = this.createdDate[0]
      } else {
        delete this.searchForm.createdDate
        delete this.searchForm.completeDate
      }
      this.getList()
    },
    resetSearch() {
      this.searchForm = {}
      this.createdDate = null
      this.getList()
    },
    getList() {
      let params = {
        ...this.page,
        ...this.searchForm
      }
      this.$post('/admin-center-server/order/queryOrderExamineList', params)
        .then(res => {
          this.total = Number(res.total)
          this.data = res.list
        })
    },
    handleSizeChange(v) {
      this.page.pageNumber = 1
      this.page.pageSize = v
      this.getList()
    },
    handleCurrentChange(v) {
      this.page.pageNumber = v
      this.getList()
    },
    audit(row) {
      let routerData = this.$router.resolve({
        path: '/order/orderAuditDetail',
        query: {
          orderBusinessId: row.orderBusinessId,
          status: row.status,
          statusEnum: row.statusEnum,
          type: row.type
        }
      })
      let auditWindow = window.open(routerData.href)
      this.openTimers = this.openTimers || []
      let openTimer = setInterval(() => {
        if (auditWindow.closed) {
        this.getList()
        clearInterval(openTimer)
        }
      }, 200)
      this.openTimers.push(openTimer)
    },
    rejectReason() {
      this.$router.push('/order/orderRejectReason')
    }
  },
  activated() {
    this.tableHeight = window.innerHeight - 460

    this.$get('/admin-center-server/order/dict/findDictByType', {
      dictType: "businessType"
    })
      .then(res => {
        this.businessTypeList = res
      })
    
    this.getList()
  },
  deactivated() {
    this.openTimers&&this.openTimers.forEach(v => clearInterval(v))
  }
}
</script>

<style lang="scss" scoped>
.form-item-content {
  width: 180px;
}
.select-box {
  background-color: #ffffff;

  .top-title {
    font-size: 16px;
    font-weight: 700;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    border-bottom: 1px solid #cccccc;
  }

  .select-info {
    padding-top: 30px;
    padding-bottom: 30px;
  }
}
.list-box {
  background-color: #ffffff;
  margin-top: 20px;
  padding: 10px;

  .list-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    div {
      height: 38px;
      line-height: 38px;
    }
  }
  .page {
    margin: 10px auto;
    text-align: right;
  }
  .list-main {
    width: 100%;
    border: 1px solid #cccccc;
    margin-top: 10px;
  }
}
</style>