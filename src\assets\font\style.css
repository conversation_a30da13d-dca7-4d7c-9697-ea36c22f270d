@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?2ws8m8');
  src:  url('fonts/icomoon.eot?2ws8m8#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?2ws8m8') format('truetype'),
    url('fonts/icomoon.woff?2ws8m8') format('woff');
    /* url('fonts/icomoon.svg?2ws8m8#icomoon') format('svg'); */
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-mima:before {
  content: "\e900";
}
.icon-yanzhengma:before {
  content: "\e901";
}
.icon-zhanghao:before {
  content: "\e902";
}
.icon-nav-caiwu:before {
  content: "\e903";
}
.icon-nav-cheliang:before {
  content: "\e904";
}
.icon-nav-dingdan:before {
  content: "\e905";
}
.icon-nav-shouqi:before {
  content: "\e906";
}
.icon-nav-shouye:before {
  content: "\e907";
}
.icon-nav-shuju:before {
  content: "\e908";
}
.icon-nav-xiaoshou:before {
  content: "\e909";
}
.icon-nav-xiaoxi:before {
  content: "\e90a";
}
.icon-nav-xitong:before {
  content: "\e90b";
}
.icon-nav-yonghu:before {
  content: "\e90c";
}
.icon-nav-yujing:before {
  content: "\e90d";
}
.icon-nav-yundan:before {
  content: "\e90e";
}
.icon-nav-yunying:before {
  content: "\e90f";
}
.icon-nav-zhankai:before {
  content: "\e910";
}
