<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">筛选查询</div>
      <div class="select-info">
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="100px">
          <el-form-item label="标题:">
            <el-input v-model="formInline.title" placeholder="标题关键字" size="small"></el-input>
          </el-form-item>

          <el-form-item label="客户端:">
            <el-select
              v-model="formInline.categoryLocation"
              placeholder="请选择端"
              clearable
              size="small"
            >
              <el-option label="调度员端" value="0"></el-option>
              <el-option label="货主端" value="1"></el-option>
              <el-option label="司机端" value="2"></el-option>
              <el-option label="公共" value="3"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="内容分类">
            <el-select v-model="formInline.typeName" placeholder="请选择内容分类" size="small" clearable>
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.maxName"
                :value="item.typeName"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-button type="primary" @click="query" size="small">查询</el-button>
          <el-form-item></el-form-item>
        </el-form>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          label-width="150px"
          style="margin-top: 30px"
        ></el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
        <div>
          <el-button class="releaseMessage" @click="classify">分类管理</el-button>
          <el-button class="releaseMessage" @click="addArticle">添加文章</el-button>
        </div>
      </div>
      <div class="list-main">
        <template>
          <el-table
            ref="multipleTable"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            border
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="title" label="标题"></el-table-column>
            <el-table-column prop="typeName" label="分类"></el-table-column>

            <el-table-column prop="relesaeUser" label="发布用户"></el-table-column>
            <el-table-column prop="releaseDate" label="发布时间"></el-table-column>
            <el-table-column prop="categoryLocationEnum" label="客户端"></el-table-column>
            <el-table-column prop="isShow" label="显示">
              <template scope="scope">
                <el-switch
                  on-text="是"
                  off-text="否"
                  on-color="#5B7BFA"
                  off-color="#dadde5"
                  v-model="scope.row.isShow"
                  @change="change(scope.$index,scope.row)"
                ></el-switch>
              </template>
            </el-table-column>

            <el-table-column fixed="right" label="操作" width="160">
              <template slot-scope="scope">
                <el-button @click="goDetail(scope.row)" type="text" size="small">查看</el-button>
                <el-button type="text" size="small" @click="editorInfo(scope.row)">编辑</el-button>
                <el-button type="text" size="small" @click="delItem(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="flex">
            <div class="delAll">
              <el-button @click="deleteAll">批量删除</el-button>
            </div>
            <div class="pageSize">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="formInline.pageNumber"
                :page-sizes="[20, 40, 60, 80,100]"
                :page-size="formInline.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                style="margin: 10px auto"
              ></el-pagination>
            </div>
          </div>
        </template>

        <!-- 查看的弹窗 -->
        <el-dialog title="查看消息" :visible.sync="dialogTableVisible" class="messageDetail">
          <p>{{title}}</p>
          <h4>{{time}}</h4>
          <h5 class="content" v-html="content"></h5>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      formInline: {
        title: "", //标题
        categoryLocation: "", //客户端
        typeName: "", //内容分类
        pageNumber: 1,
        pageSize: 20,
        order:'desc'
      },
      tableData: [],
      multipleSelection: [],
      total: null,
      options: [], //内容分类下拉框
      categoryLocation: "",

      dialogTableVisible:false, //查看的弹窗
      title:'', //文章标题
      time:'', //发布时间
      content:'' , //文章内容

    };
  },
  activated() {
    this.getDataList();

    this.getSelectData(); //获取内容分类下拉列表
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    /* 选取每页多少条数据 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.formInline.pageNumber =1;
      this.formInline.pageSize = JSON.parse(`${val}`);
      console.log(this.formInline.pageSize);
      this.getDataList();
    },
    /* 翻页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.formInline.pageNumber = JSON.parse(`${val}`);
      console.log(this.formInline.pageNumber);
      this.getDataList();
    },
    /* 获取数据 */
    getDataList() {
      var postData = this.formInline;
      console.log(postData, "postData");
      this.$http
        .post("/admin-center-server/articel/queryCusArticelList", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            console.log(data);
            this.tableData = data.data.list;
            this.total = JSON.parse(data.data.total);
            console.log(this.tableData, "tableData----");
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 表格中的开关 */
    change: function(index, row) {
      console.log(index, row);
      var postData = {
        ids: Number(row.id),
        isShow: row.isShow
      };
      this.$http
        .post(
          "/admin-center-server/articel/updateByCusArticelIds",
          this.$qs.stringify(postData)
        )
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.getDataList();
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 单个删除功能 */
    delItem(row) {
      console.log(row.id, "------------------");
      let id = row.id;
      console.log(row.id, "-----id");
      let ids = [];
      ids[0] = Number(row.id);

      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http
            .post("/admin-center-server/articel/deleteByCusArticelIds", ids)
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
              }
              this.getDataList();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },

    /* 批量删除 */
    deleteAll() {
      var deleteAllData = this.multipleSelection; //批量删除的数据
      let ids = deleteAllData.map(obj => {
        return obj.id;
      });
      console.log(ids, "----");
      this.dialogVisible = true;
      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http
            .post("/admin-center-server/articel/deleteByCusArticelIds", ids)
            .then(res => {
              let data = res.data;
              if (data.code == "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
              }
              this.getDataList();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    /* 分类管理 */
    classify() {
      this.$router.push({
        path: "classify"
      });
    },
    /* 添加文章 */
    addArticle() {
      this.$router.push({
        path: "addArticle",
        query: {
          typeFlag: 1
        }
      });
    },
    /* 查询 */
    query() {
      this.formInline.pageNumber=1
      this.getDataList();
    },
    /* 编辑 */
    editorInfo(row) {
      this.$router.push({
        path: "addArticle",
        query: {
          typeFlag: 2, // 编辑
          id: row.id
          // isShow:row.isShow, //是否显示
          // cusId:row.cusId,
          // title:row.title,
          // sort:row.sort,
          // isTop:row.isTop
        }
      });
    },

    /* 下拉选列表的数据 */
    getSelectData() {
      this.$http
        .post("/admin-center-server/articel/getCusArticelSortByCategory")
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.options = data.data;
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 查看文章 */
    goDetail(row){
      console.log(row);
      this.dialogTableVisible = true;
      this.title = row.title;
      this.time = row.releaseDate;


    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.el-form-item {
  margin-bottom: 0;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
    .messageDetail p {
      text-align: center;
      font-weight: 600;
      font-size: 20px;
    }
    .messageDetail h4 {
      font-size: 16px;
      text-align: center;
      font-weight: 200;
    }
    .messageDetail h5 {
      font-size: 16px;
      line-height: 30px;
    }
  }
}
</style>
