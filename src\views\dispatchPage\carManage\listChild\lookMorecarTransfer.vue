<template>
    <div class="app-container">
        <div class="list-main">
            <div class="title">车辆转让记录</div>
            <div class="list-table">
                <template>
                    <el-table
                            :data="tableData"
                            style="width: 100%"
                            border>
                        <el-table-column
                                prop="userName"
                                label="转让车队长名称"
                               >
                        </el-table-column>
                        <el-table-column
                                prop="userMobile"
                                label="转让车队长手机号"
                                >
                        </el-table-column>
                        <el-table-column
                                prop="toUserName"
                                label="被转让车队长名称">
                        </el-table-column>
                        <el-table-column
                                prop="toUserNameMobile"
                                label="被转让车队长手机号">
                        </el-table-column>
                        <el-table-column
                                prop="createDate"
                                label="转让时间">
                        </el-table-column>
                    </el-table>
                </template>
            </div>
            <div class="pagination" style="float: right;margin-top: 20px">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 40]"
                        :page-size=pageSize
                        layout="total, sizes, prev, pager, next, jumper"
                        :total=total>
                </el-pagination>
            </div>
        </div>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                tableData: [],
                currentPage: 1,
                pageSize: 20,
                total: 0,
            }
        },
        methods: {
            /** 分页方法 **/
            handleSizeChange(val) {
                this.pageSize = val;
                this.getListData()
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getListData()
            },
            getListData() {
                let carId = this.$route.query.carId;
                this.$http.get('/admin-center-server/app/mycar/manager/zc_car_history', {
                    params: {
                        pageNumber: this.currentPage,
                        pageSize: this.pageSize,
                        carId: carId
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.tableData = data.data.list;
                        this.total = Number(data.data.total)
                    }
                })
            },
        },
        activated() {
            this.getListData()
        }
    }
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .list-main {
        background-color: white;
        overflow: hidden;

        .title {
            font-size: 16px;
            font-weight: 700;
            height: 40px;
            line-height: 40px;
            padding-left: 10px;
        }

        .list-table {
            padding: 10px;
        }
    }
</style>
