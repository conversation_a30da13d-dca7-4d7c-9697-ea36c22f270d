<template>
    <div class="app-container userWallet">
        <div class="list-box">
            <div class="title">钱包明细</div>
            <div class="select-info">
                <el-form
                        :inline="true"
                        :model="formInline"
                        class="demo-form-inline"
                        label-width="100px"
                        size="mini"
                >
                    <el-form-item label="用户名称:">
                        <el-input v-model="formInline.name" :οnkeyup="formInline.name=formInline.name.replace(/\s/g, '')" placeholder="请输入用户名称或手机号"></el-input>
                    </el-form-item>
                    <el-form-item label="用户类型:">
                        <el-select v-model="formInline.type" placeholder="不限">
                            <el-option label="不限" value></el-option>
                            <el-option label="货主" value="1"></el-option>
                            <el-option v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="调度员" value="2"></el-option>
                            <el-option v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="司机(包括车队长)" value="3"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="钱包状态:">
                        <el-select v-model="formInline.status" placeholder="不限">
                            <el-option label="不限" value></el-option>
                            <el-option label="正常" value="1"></el-option>
                            <el-option label="冻结" value="2"></el-option>
                            <el-option label="禁止提现" value="3"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button icon="el-icon-search" type="primary" @click="onSubmit">查询</el-button>
                        <el-button icon="el-icon-delete" type="danger" @click="clearForm">清空筛选</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="list-main">
                <template>
                    <el-table 
                    :data="tableData" 
                    border 
                    style="width: 100%"
                    cell-class-name="table_cell_gray"
                    header-cell-class-name="table_header_cell_gray">
                        <el-table-column label="序号" type="index" width="50"></el-table-column>
                        <el-table-column prop="mobile" label="用户手机号" width="120"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="userName" label="用户名称"
                                         width="200"></el-table-column>
                        <el-table-column show-overflow-tooltip :formatter="tabUserType" prop="userType" label="用户类型"
                                         width="160"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="baseName" label="合作平台主体"
                                         width="200"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="subAcctNo" label="平安子账户号"
                                         width="200"></el-table-column>
                        <el-table-column :formatter="walletStatus" prop="status" label="钱包状态"
                                         width="80"></el-table-column>
                        <el-table-column prop="sumAmount" label="账户总额(元)" width="150"></el-table-column>

                        <el-table-column prop="oilAmount" label="油卡金额(元)" width="150"></el-table-column>

                        <el-table-column prop="availableAmount" label="可使用金额(元)" width="150"></el-table-column>
                        <el-table-column prop="noAmountWithdraw" label="不可提现金额(元)" width="150"></el-table-column>
                        <el-table-column prop="cashAmount" label="可提现金额(元)" width="150"></el-table-column>
                        <el-table-column prop="lockAmount" label="冻结运单金额(元)" width="150"></el-table-column>
                        <el-table-column prop="operationTime" label="操作日期" width="100"></el-table-column>
                        <el-table-column fixed="right" label="操作" width="100">
                            <template slot-scope="scope">
                                <el-button @click="rejectReason(scope.row)" type="text" size="small">设置钱包状态</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </div>
            <div class="paging">
                <div class="block">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 30, 40]"
                            :page-size="pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total"
                    ></el-pagination>
                </div>
            </div>
        </div>
        <!--设置用户钱包状态-->
        <el-dialog
                title="设置用户钱包状态"
                :visible.sync="dialogVisible"
                width="545px"
        >
            <div class="mask-box">
                <div class="wallet-status-warn">钱包状态更改后，用户名下所有钱包将同步调整</div>
                <el-form :model="ruleForm" ref="ruleForm" label-width="120px" class="demo-ruleForm">
                    <el-form-item label="用户类型:" required>
                        <el-input
                                :disabled="true"
                                v-model="ruleForm.dtype"
                                placeholder="用户类型"
                                style="width: 300px"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="用户名称:" required>
                        <el-input
                                :disabled="true"
                                v-model="ruleForm.name"
                                placeholder="用户名称"
                                style="width: 300px"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="用户手机号:" required>
                        <el-input
                                :disabled="true"
                                v-model="ruleForm.mobile"
                                placeholder="用户手机号"
                                style="width: 300px"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="钱包状态:" required>
                        <el-radio-group v-model="ruleForm.resource">
                            <el-radio label="1">正常</el-radio>
                            <el-radio label="2">冻结</el-radio>
                            <el-radio label="3">禁止提现</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="sureChange">确 定</el-button>
      </span>
        </el-dialog>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                title: '提现',
                showSet: true,
                setPwd: false,
                changePwd: false,
                hasPwd: false,
                formCash: {
                    withdrawalAmount: '',
                    passWord: '',
                },
                formSetPWD: {
                    loginPassWord: '',
                    firstWalletPassword: '',
                    secondWalletPassword: '',
                },
                formResetPWD: {
                    oldPassWord: '',
                    newPassWord: '',
                },
                gridData: [],
                total: 1,
                totalInner: 1,
                pageSize: 20,
                currentPage: 1,
                currentPageInner: 1,
                pageSizeInner: 20,

                loading: false,
                dialogVisible: false,
                budgetMask: false,
                formInline: {
                    name: "",
                    type: "",
                    status: ""
                },
                innerForm: {
                    cashType: "",
                    type: "",
                    transactionSn: ""
                },
                tableData: [],
                ruleForm: {
                    dtype: "",
                    name: "",
                    mobile: "",
                    resource: "",
                    baseName: ''
                },
                changeId: "",
                isWithdrawLoading: false
            };
        },
        methods: {
            tabUserType(row) {
                if (row.userType === "1") {
                    return "货主";
                } else if (row.userType === "2") {
                    return "调度员";
                } else if (row.userType === "3") {
                    return "司机(包括车队长)";
                }
            },
            walletStatus(row) {
                if (row.status === "1") {
                    return "正常";
                } else if (row.status === "2") {
                    return "冻结";
                } else if (row.status === "3") {
                    return "禁止提现";
                }
            },
            onSubmit() {
                this.currentPage = 1;
                this.getDataList();
            },
            rejectReason(row) {
                this.dialogVisible = true;
                let id = row.userSn;
                this.changeId = id;
                this.$http
                    .get("/admin-center-server/finance/queryByUserId", {
                        params: {
                            userSn: id
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.ruleForm = {
                                name: data.data.name,
                                mobile: data.data.mobile,
                                resource: data.data.status,
                                baseName: data.data.baseName
                            };
                            let dtype = data.data.dtype;
                            if (dtype === "1") {
                                this.ruleForm.dtype = "货主";
                            } else if (dtype === "2") {
                                this.ruleForm.dtype = "调度员";
                            } else if (dtype === "3") {
                                this.ruleForm.dtype = "司机(包括车队长)";
                            }
                        }
                    });
            },
            /** 修改钱包三个状态提示语 **/
            sureChange(done) {
                if (this.ruleForm.resource === "3") {
                    this.$confirm(
                        "确认将当前用户钱包状态设为禁止提现吗？设置后该用户将不可进行提现操作"
                    )
                        .then(_ => {
                            this.changeStatusSend();
                            this.dialogVisible = false;
                        })
                        .catch(_ => {
                        });
                } else if (this.ruleForm.resource === "2") {
                    this.$confirm(
                        "确认将当前用户钱包状态冻结吗？设置后该用户将不可使用钱包功能。"
                    )
                        .then(_ => {
                            this.changeStatusSend();
                            this.dialogVisible = false;
                        })
                        .catch(_ => {
                        });
                } else {
                    this.changeStatusSend();
                    this.dialogVisible = false;
                }
            },
            /** 设置钱包状态 **/
            changeStatusSend() {
                this.$http
                    .get("/admin-center-server/finance/settingStatus", {
                        params: {
                            userSn: this.changeId,
                            status: this.ruleForm.resource
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.getDataList();
                        }
                    });
            },
            /** 清空筛选 **/
            clearForm() {
                this.formInline = {
                    name: "",
                    type: "",
                    status: ""
                };
                this.getDataList();
            },
            /** 分页方法 **/
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1;
                this.getDataList();
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getDataList();
            },
            getDataList() {
                this.$http
                    .get("/admin-center-server/finance/list", {
                        params: {
                            pageNumber: this.currentPage,
                            pageSize: this.pageSize,
                            name: this.formInline.name,
                            type: this.$store.state.user.userInfo2.hasStandardModeFlag ? '1' : this.formInline.type,
                            status: this.formInline.status
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.tableData = data.data.list;
                            this.total = Number(data.data.total);
                        }
                    });
            },
            /** 内表导出 **/
            innerExport() {
                this.loading = true;
                let str = this.gridData;
                let ids = str.map((item, index) => {
                    return item.id
                });
                this.$http.post('/admin-center-server/finance/record/export', ids).then(res => {
                    let data = res.data;
                    if (data.code == 200) {
                        this.$message.success('正在导出，请等待');
                        let url = data.data;
                        window.location.href = url;
                        this.loading = false;
                    } else {
                        this.$message.warning(data.message);
                        this.loading = false;
                    }
                })
            }
        },
        activated() {
            this.getDataList();
        },
    };
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .title {
        position: relative;
        padding-left: 12px;
        font-size: 14px;
        &::after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 4px;
            height: 14px;
            border-radius: 2px;
            background: #F6A018;
        }
    }
    .info {
        padding: 20px;
        background: #fff;
    }
    .info-head {
        display: flex;
        justify-content: space-between;
    }
    .info-refresh {
        cursor: pointer;
    }
    @keyframes refresh {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    .refreshing {
        animation: refresh 1s linear infinite;
    }
    .info-wrapper {
        display: flex;
        margin-top: 35px;
    }
    .info-btns {
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        .el-button {
            margin-left: 0;
            margin-bottom: 15px;
            color: #f6a018;
            background-color: #fff;
        }
    }
    .item-right {
        margin-left: 2%;
        h3 {
            margin: 0;
            padding: 10px 0;
            font-size: 16px;
            color: #333;
            font-weight: normal;
        }
        div  {
            color: #333;
            font-size: 28px;
            word-break: break-all;
        }
    }
    .userWallet {
        .wallet-titleInfo {
            display: flex;
            flex-grow: 1;

            .infoItem {
                position: relative;
                flex-grow: 1;
                flex-shrink: 0;
                justify-content: center;
                width: 0;
                display: flex;
                text-align: left;
                &:not(:last-child)::after {
                    content: '';
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 1px;
                    height: 80px;
                    background: #DCDFE6;
                }
            }
        }

        .select-box {
            margin-top: 10px;
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
            }

        }
        .select-info {
            padding-top: 30px;
            padding-bottom: 30px;
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 20px;
            overflow: hidden;

            .list-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                div {
                    height: 38px;
                    line-height: 38px;
                }
            }

            .list-main {
                width: 100%;
                border: 1px solid #cccccc;
                margin-top: 10px;
            }

            .paging {
                margin-top: 10px;
                float: right;
            }
        }
    }

    .mask-box {
        width: 500px;
        padding-top: 20px;
    }
    .wallet-status-warn {
        margin-left: 35px;
        margin-top: -30px;
        margin-bottom: 20px;
        color: #DD2042;
    }
</style>
