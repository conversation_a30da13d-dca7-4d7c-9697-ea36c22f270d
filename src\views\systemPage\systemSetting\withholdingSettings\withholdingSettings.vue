<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">代扣代缴设置</div>
      <div class="select-info">
        <el-form :inline="true"
                 :model="formInline"
                 class="demo-form-inline"
                 size="mini"
                 label-width="90px">
          <el-form-item>
            <el-button class="left"
                       icon="el-icon-plus"
                       @click="addSubmit">添加</el-button>
          </el-form-item>
        </el-form>

      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table ref="multipleTable"
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%"
                    :height="tableHeight"
                    @selection-change="handleSelectionChange"
                    border>

            <el-table-column type="index"
                             label="序号"
                             width="55">
            </el-table-column>
            <el-table-column show-overflow-tooltip
                             v-for="item in tableLabel"
                             :label="item.label"
                             :width="item.width ? item.width : ''">
              <template slot-scope="scope">
                <span style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
              </template>
            </el-table-column>

            <el-table-column fixed="right"
                             width='100'
                             label="操作">
              <template slot-scope="scope">
                <el-button class="btn-1"
                           @click="goDetail(scope.row)"
                           type="text"
                           size="small">修改</el-button>
                <el-button size="small"
                           type="text"
                           @click='switchfn(scope.row)'>{{scope.row.switchText}}</el-button>
                <!-- <el-switch @change='switchfn(scope.row)'
                           v-model="scope.row.disable">
                </el-switch> -->
              </template>
            </el-table-column>
          </el-table>

          <!-- <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page="pageNumber"
                         :page-sizes="[10,20, 40, 60, 80,100]"
                         :page-size="pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total"
                         class="pagination"></el-pagination> -->
        </template>
      </div>
    </div>
  </div>
</template>
<script>
const list = '/admin-center-server/tax/tate/select'//列表

const updata = '/admin-center-server/tax/tate/update'//开关
export default {
  name: "CarsList",
  data () {
    return {
      tableHeight: null, //表格的高度
      value: true,
      tableLabel: [
        {
          prop: 'roles',
          label: '角色',
          width: 200
        },
        {
          prop: 'priceText',
          label: '收入（万元）',
          width: 120
        },
        {
          prop: 'calculateTaxPointBase',
          label: '计税依据基数',
          width: 120
        },
        {
          prop: 'valueAddedTax',
          label: '增值税'
        },
        {
          prop: 'buildingTax',
          label: '城建税'
        },
        {
          prop: 'educationSurcharge',
          label: '教育附加费',
          width: 120
        },
        {
          prop: 'localEducationSurcharge',
          label: '地方教育附加税',
          width: 140
        },
        {
          prop: 'personalIncomeTax',
          label: '个人所得税',
          width: 120
        }
      ],
      pageSize: 10,
      total: 0,
      pageNumber: 1,
      formInline: {
      },
      tableData: []
    };
  },
  methods: {
    switchfn (row) {
      var that = this
      this.$confirm('确认' + row.switchText + '代扣代缴?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$http.get(updata + '?disable=' + !row.disable + '&updateId=' + row.id).then(res => {
          if (res.data.code == '200') {
            that.$message({
              message: '操作成功',
              type: 'success'
            });
            setTimeout(function () {
              that.pageNumber = 1
              that.getData()
            }, 3000);
          } else {
            this.$message.error(res.data.message);
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });


      })
    },
    addSubmit () {
      this.$router.push("/withholdingSettings/modify?type=add");
      // var data = {
      //   "buildingTax": '',
      //   "calculateTaxPointBase": '',
      //   "educationSurcharge": '',
      //   "id": '',
      //   "income": '',
      //   "isDelete": false,
      //   "localEducationSurcharge": '',
      //   "personalIncomeTax": '',
      //   "roles": "",
      //   "valueAddedTax": ''
      // }
      // var that = this
      // this.$http.post(add, data).then(res => {
      //   if (res.data.code == '200') {
      //     this.$message({
      //       message: '添加成功',
      //       type: 'success'
      //     });
      //     setTimeout(function () {
      //       that.pageNumber = 1
      //       that.getData()
      //     }, 3000);
      //   } else {
      //     this.$message.error(res.data.message);
      //   }
      // })

    },
    editfn (row) {
      this.$router.push("/consignorAccount/addShipper?urlType=1&id=" + row.id);
    },
    goDetail (row) {
      localStorage.setItem('modifyData', JSON.stringify(row));
      this.$router.push("/withholdingSettings/modify?type=edit");
    },
    addnewfn () { this.$router.push("/consignorAccount/addShipper?urlType=1"); },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },

    handleSizeChange (val) {
      this.pageNumber = 1
      this.pageSize = val
      this.getData()
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange (val) {
      this.pageNumber = val
      this.getData()
      console.log(`当前页: ${val}`);
    },
    getData () {
      this.$http.get(list).then(res => {
        var data = res.data.data
        for (var i = 0; i < data.length; i++) {
          if (data[i].disable) {
            data[i].switchText = '开启'
          } else {
            data[i].switchText = '关闭'
          }
          if (data[i].income > 0) {
            console.log(data[i].income)
            if (data[i].comparisonSymbol == 4) {
              data[i].priceText = '<=' + data[i].income
            } else {
              data[i].priceText = '>' + data[i].income
            }
          } else {
            data[i].priceText = data[i].income
          }

        }
        this.tableData = data
      })
    }
  },
  activated () {
    this.getData()
    this.tableHeight =
      window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 160;

  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .btn-1 {
    margin-right: 10px;
  }
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
