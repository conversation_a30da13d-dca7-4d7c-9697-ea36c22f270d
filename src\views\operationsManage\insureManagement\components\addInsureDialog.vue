<template>
  <el-dialog title="添加保单" :visible.sync="visible">
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="110px"
      class="demo-ruleForm"
    >
      <el-form-item label="运单号" prop="orderItemSn">
        <el-input v-model="ruleForm.orderItemSn" placeholder="请输入运单号"></el-input>
      </el-form-item>
      <el-form-item label="保单号" prop="policyNo">
        <el-input v-model="ruleForm.policyNo" placeholder="请输入保单号"></el-input>
      </el-form-item>
      <el-form-item label="保费" prop="sumpremium">
        <el-input v-model="ruleForm.sumpremium" placeholder="请输入保费">
          <template slot="append">元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="投保货物" prop="conveyanceId">
        <el-select 
        style="width: 100%"
        v-model="ruleForm.conveyanceId" 
        filterable
        placeholder="请选择投保货物">
          <el-option
            v-for="item in cargoList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="投保货值" prop="goodsValue">
        <el-input v-model="ruleForm.goodsValue" placeholder="请输入投保货值">
          <template slot="append">万元/车</template>
        </el-input>
      </el-form-item>
      <el-form-item label="保司" prop="insuranceCompanyCode">
        <el-select 
        style="width: 100%"
        v-model="ruleForm.insuranceCompanyCode" 
        @visible-change="querySearchCompany"
        placeholder="请选择保司">
          <el-option
            v-for="item in companyList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="险种" prop="type">
        <el-radio-group v-model="ruleForm.type">
          <el-radio label="0">基本险</el-radio>
          <el-radio label="1">综合险</el-radio>
          <el-radio label="2">单车货运险</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="投保时间" prop="insuranceTime">
        <el-date-picker
          type="datetime"
          placeholder="请选择投保时间"
          v-model="ruleForm.insuranceTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 100%"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="电子保单" prop="policyOfInsuranceUrl">
        <el-upload
            :show-file-list="true"
            action=""
            :http-request="handleFileUpload"
            :limit="1"
            :on-exceed="handleExceed"
            :on-remove="handleFileRemove"
            accept=".doc,.docx,.pdf"
            ref="upload"
            drag>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传文件</div>
          </el-upload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="resetForm('ruleForm')">取 消</el-button>
      <el-button type="primary" @click="submitForm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { upload } from '@/utils/file'

export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    visible: {
      set(val) {
        this.$emit("update:show", val);
      },
      get() {
        return this.show;
      },
    },
  },
  data() {
    return {
      waybillItems: [],
      ruleForm: {
        orderItemSn: "",
        policyNo: "",
        sumpremium: "",
        conveyanceId: "",
        goodsValue: "",
        insuranceCompanyCode: "",
        type: "",
        insuranceTime: "",
        policyOfInsuranceUrl: "",
      },
      rules: {
        orderItemSn: [{ required: true, message: "请输入运单号", trigger: "blur" }],
        policyNo: [
          { required: true, message: "请输入保单号", trigger: "blur" },
        ],
        sumpremium: [
          { required: true, message: "请输入保费", trigger: "blur" },
          {
            validator(rule, value, cb) {
              let num = Number(value)
              if (Number.isNaN(num) || num <= 0) {
                cb('必须为正数，最多两位小数')
              }
              if (!Number.isInteger(num)) {
                let decimalDigits = value.split('.')[1]
                if (decimalDigits.length > 2) {
                  cb('必须为正数，最多两位小数')
                }
              }
              cb()
            },
            trigger: 'blur'
          }
        ],
        conveyanceId: [
          { required: true, message: "请选择投保货物", trigger: "blur" },
        ],
        goodsValue: [
          { required: true, message: "请输入投保货值", trigger: "blur" },
          {
            validator(rule, value, cb) {
              let num = Number(value)
              if (Number.isNaN(num) || num <= 0) {
                cb('必须为正数，最多两位小数')
              }
              if (!Number.isInteger(num)) {
                let decimalDigits = value.split('.')[1]
                if (decimalDigits.length > 2) {
                  cb('必须为正数，最多两位小数')
                }
              }
              cb()
            },
            trigger: 'blur'
          }
        ],
        insuranceTime: [
          {
            required: true,
            message: "请选择投保时间",
            trigger: "change",
          },
        ],
        insuranceCompanyCode: [
          { required: true, message: "请选择保司", trigger: "change" },
        ],
        type: [
          { required: true, message: "请选择险种", trigger: "change" },
        ],
        policyOfInsuranceUrl: [{ required: true, message: "请上传电子保单", trigger: "blur" }],
      },
      showImageUrl: "",
      fileList: [],
      cargoList: [],
      companyList: []
    };
  },
  created() {
    this.querySearchCargo();
  },
  methods: {
    querySearchCargo() {
      this.$http.get("/admin-center-server/order/dict/findDictByType?dictType=insuranceGoodType").then(res => {
        let data = res.data;
        if (data.code == "200") {
          this.cargoList = data.data;
        } else {
          this.$message.warning(data.message);
        }
      });
    },
    querySearchCompany() {
      this.$http.get("/admin-center-server/order/dict/findDictByType?dictType=insuranceCompany").then(res => {
        let data = res.data;
        if (data.code == "200") {
          this.companyList = data.data;
        } else {
          this.$message.warning(data.message);
        }
      });
    },
    handleSelectCargo(item) {
      console.log(item);
    },
    changeImage(url) {
      this.ruleForm.url = url
    },
    handleFileUpload(e) {
      return upload(e.file)
        .then(res => {
          console.log(res)
          // this.$set(this.ruleForm,'policyOfInsuranceUrl',res.url)
          this.ruleForm.policyOfInsuranceUrl = res.url
          console.log(this.ruleForm.policyOfInsuranceUrl)
          this.$refs.ruleForm.validateField('policyOfInsuranceUrl')
        })
    },
    handleExceed() {
      this.$message.error('文件已上传')
    },
    handleFileRemove() {
      this.ruleForm.policyOfInsuranceUrl = ''
      this.$refs.ruleForm.validateField('policyOfInsuranceUrl')
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            ...this.ruleForm
          }
          this.companyList.forEach(item => {
            if (item.value == data.insuranceCompanyCode) {
              data.insuranceCompanyName = item.label
            }
          })
          this.$http.post("/admin-center-server/mlww/admin/addInsurancePolicy",data).then(res => {
            if (res.data.code == "200") {
              this.$message.success("添加成功");
              this.$emit("addSuccess");
              this.resetForm(formName);
            } else {
              this.$message.error(res.data.message);
            }
          });
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.visible = false
      this.ruleForm= {
        orderItemSn: "",
        policyNo: "",
        sumpremium: "",
        conveyanceId: "",
        goodsValue: "",
        insuranceCompanyCode: "",
        type: "",
        insuranceTime: "",
        policyOfInsuranceUrl: "",
      }
      this.$refs[formName].resetFields();
      this.$refs.upload.clearFiles()
    },
  },
};
</script>