<template>
  <div>
    <div class="detail-panel">
      <el-row :gutter="10">
        <el-col :span="12">
          <div class="detail-title">装卸货信息</div>
          <div>
            <div class="dispatchInfo-all">
              <div class="dispatchInfo-item">
                <div class="dispatchInfo-left">
                  <div class="label-all">
                    <div class="zhuang-div">装</div>
                    <div class="line2"></div>
                  </div>
                </div>
                <div class="dispatchInfo-right">
                  <div class="dispatchInfo-right-top">
                    <div>
                      {{loadDetail.addressPlace && loadDetail.addressPlace + "-"}}
                      {{ loadDetail.addressName }}
                    </div>
                  </div>
                  <div class="dispatchInfo-right-bottom">
                    <div><i class="el-icon-user"></i> 发货人:{{tableData[0].consignerName}} / {{ tableData[0].consignerPhone }}
                      <span v-if="tableData[0].planLoadingTime" style="margin-left: 10px" >
                        <i class="el-icon-time"></i>
                        {{ formatDate(tableData[0].planLoadingTime) }} 
                        <span style="color: red">{{formatTime(tableData[0].planLoadingTime)}}</span>
                        装货
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="dispatchInfo-item" v-for="(item, index) in stopOverDetails" :key="index" >
                <div class="dispatchInfo-left">
                  <div class="label-all">
                    <div class="jing-div">经</div>
                    <div class="line2"></div>
                  </div>
                </div>
                <div class="dispatchInfo-right">
                  <div class="dispatchInfo-right-top nomarbottom">
                    <div>{{ item.addressName }}</div>
                  </div>
                </div>
              </div>
              <div class="dispatchInfo-item">
                <div class="dispatchInfo-left">
                  <div class="label-all">
                    <div class="xie-div">卸</div>
                  </div>
                </div>
                <div class="dispatchInfo-right">
                  <div class="dispatchInfo-right-top">
                    <div>
                      {{ unloadDetail.addressPlace && unloadDetail.addressPlace + "-" }}
                      {{ unloadDetail.addressName }}
                    </div>
                  </div>
                  <div class="dispatchInfo-right-bottom">
                    <div> 
                      <i class="el-icon-user"></i> 
                      收货人:{{ tableData[0].consigneeName }} / {{ tableData[0].consigneePhone }}
                      <span v-if="tableData[0].planUnLoadingTime" style="margin-left: 10px" >
                        <i class="el-icon-time"></i>
                        {{ formatDate(tableData[0].planUnLoadingTime) }} 
                        <span style="color: red">{{formatTime(tableData[0].planUnLoadingTime)}}</span>
                        卸货
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <detail-row>
            <detail-col label="在途时效" :span="24">
              {{ convertMinutesToHoursAndMinutes( tableData[0].transportDurationMinutes ) }}
              <span style="color: #ccc"> | </span>
              {{ tableData[0].itemDistance }}公里
            </detail-col>
            <detail-col label="货物信息" :span="24">
              <!-- {{ tableData[0].singleTon ? tableData[0].singleTon + "吨" : "" }}
              <span style="color: #ccc" v-if="tableData[0].singleTon"> | </span> -->
              {{ tableData[0].cargoTypeClassificationValue + " / " + tableData[0].cargoType }}
            </detail-col>
            <detail-col label="货物重量" :span="24">
                <span>装 {{handleWeight(tableData[0].originalTon)}}吨</span><span style="color: #ccc"> | </span>
                <span>卸 {{handleWeight(tableData[0].currentTon)}}吨</span><span style="color: #ccc" v-if="tableData[0].freightCalcType === '0'"> | </span>
                <span v-if="tableData[0].freightCalcType === '0'">结 {{handleWeight(tableData[0].payTon)}}吨</span>
              </detail-col>
            <detail-col
              label="运输要求"
              :value="tableData[0].memo"
              :span="24"
            ></detail-col>
            <detail-col label="装货凭证" :span="24">
              <ImageUploader3 :imgList="tableData[0].originalTonImageUrl ? tableData[0].originalTonImageUrl.split(';') : []" :small="true">
              </ImageUploader3>
            </detail-col>
            <detail-col label="卸货凭证" :span="24">
              <ImageUploader3 :imgList="tableData[0].dischargeCargoImageUrl ? tableData[0].dischargeCargoImageUrl.split(';') : []" :small="true">
              </ImageUploader3>
            </detail-col>
            <detail-col
              label="回单照片"
              :span="24"
              v-if="
                tableData[0].receiptStatusName === '已上传' ||
                tableData[0].receiptStatusName === '已收回'
              "
            >
              <ImageUploader3 :imgList="tableData[0].receiptImage ? tableData[0].receiptImage.split(';') : []" :small="true">
              </ImageUploader3>
            </detail-col>
            <detail-col
              label="三方打卡图片"
              :span="24"
              v-if="tableData[0].thirdPartyCheckInImage"
            >
              <ImageUploader3
                :imgList="tableData[0].thirdPartyCheckInImage ? tableData[0].thirdPartyCheckInImage.split(';') : []"
                :small="true"
              >
              </ImageUploader3>
            </detail-col>
            <detail-col label="收货备注" :span="24" :value="tableData[0].reason || '-'"></detail-col>
          </detail-row>
        </el-col>
        <el-col :span="12">
          <div class="detail-title">
            运输进度
            <span style="margin-left: 20px" v-if="tableData[0].actualRunningTime">实际运行{{ tableData[0].actualRunningTime }}
              <span style="color: red;" v-if="tableData[0].timeoutPeriod && tableData[0].timeoutPeriod!='未超时'">{{(tableData[0].timeoutPeriod && tableData[0].timeoutPeriod!='未超时')? '，多' + tableData[0].timeoutPeriod : ''}}</span>
              <!-- 显示未超时 -->
              <span style="margin-left: 5px;" v-else>{{ tableData[0].timeoutPeriod }}</span> 
            </span>
            <!-- <span style="color: red">{{tableData[0].overTime ? "多" + convertMinutesToHoursAndMinutes(tableData[0].overTime) : ""}}</span> -->
          </div>
          <div class="transportation">
            <el-timeline>
              <el-timeline-item
                v-for="(activity, index) in tableData[0]
                  .transportationProgressVOS"
                :key="index"
                :type="activity.type"
                :color="
                  activity.name == '异常报备'
                    ? 'red'
                    : activity.showPoint
                    ? '#ed970f'
                    : '#cccccc'
                "
                :size="activity.size"
                :hide-timestamp="true"
                :timestamp="activity.timestamp"
              >
                <div class="timeline-title">
                  {{ activity.name }}
                  <!-- <el-tag v-if="activity.inFence ===false && activity.inCheck" size="mini">范围外打卡({{ activity.distance }}km)</el-tag> -->
                  <el-tag v-if="activity.inFence === false && activity.inCheck" size="mini" color="#fcd5d0" class="tag-class">
                      <span v-if="activity.name.includes('装货')">距装货地{{ activity.distance }}km处打卡</span>
                      <span v-if="activity.name.includes('经停')">距经停地{{ activity.distance }}km处打卡</span>
                      <span v-if="activity.name.includes('卸货')">距卸货地{{ activity.distance }}km处打卡</span>
                    </el-tag>
                  <el-tag v-if="activity.isLate && activity.lateDuration" size="mini" color="#fcd5d0" class="tag-class">晚点 {{ activity.lateDuration }}</el-tag>
                  <span v-if="activity.name === '异常报备' && activity.duration">(异常持续时长：{{ activity.duration }})</span>
                </div>
                <div class="timeline-content">
                  <div>
                      <span v-if="activity.name==='到达装货地' || activity.name==='到达卸货地'">
                        <span v-if="activity.showPoint">
                            操作时间：{{ formatDateTime(activity.operatingTime) || '-' }} | 要求时间：{{formatDateTime(activity.askTime)}}
                        </span>
                        <span v-else>
                            要求时间：{{ formatDateTime(activity.askTime) || '-' }}
                        </span>
                      </span>
                      <span v-else>  
                          操作时间：{{ formatDateTime(activity.operatingTime) || '-' }}
                      </span>
                    </div>
                  <div
                    style="margin-top: 10px"
                    v-if="
                      activity.name != '接单' && activity.name != '回单上传'
                    "
                  >
                    <div>
                      {{
                        activity.operatingAddress
                          ? "操作地址：" + activity.operatingAddress
                          : "要求地址：" + activity.askAddress
                      }}
                    </div>
                  </div>
                  <div
                    style="margin-top: 10px"
                    v-if="activity.name === '异常报备'"
                  >
                    <span>报备内容：</span>
                    <span>异常类型：{{ activity.exceptionType }}，</span>
                    <span>异常描述：{{ activity.reportContent }}，</span>
                    <span
                      style="color: #ed970f"
                      @click="showAbnormalImages(activity.abnormalImages)"
                      >查看</span
                    >
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-col>
      </el-row>
      <el-image-viewer
        v-if="showedPic"
        :on-close="handlePicClose"
        :url-list="showedPic"
      />
    </div>
  </div>
</template>
<script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import ReceiceDispatchInfo from "@/components/Order/ReceiceDispatchInfo.vue";
import ImageUploader3 from "../ImageUploader3/ImageUploader3.vue";
export default {
  components: { ReceiceDispatchInfo, ElImageViewer, ImageUploader3 },
  props: {
    tableData: Array,
  },
  data() {
    return {
      loadUrl: [],
      receiptImages: [],
      unloadUrl: [],
      ruleData: [],
      showedPic: "",
    };
  },
  computed: {
    overTime() {
      //overTime有两种可能，为日期字符串时原样返回，为数字时加上小时
      let overTime = this.tableData[0].overTime;
      if (overTime === null) return "-";
      if (Number.isNaN(Number(overTime))) {
        return overTime;
      } else {
        return overTime + "小时";
      }
    },
    loadDetail() {
      if (!this.tableData[0].itemLoadUnloadAddressInfoList) {
        return {};
      }
      let detail = this.tableData[0].itemLoadUnloadAddressInfoList.find(
        (item) => item.type == 1
      );
      return detail;
    },
    unloadDetail() {
      if (!this.tableData[0].itemLoadUnloadAddressInfoList) {
        return {};
      }
      let detail = this.tableData[0].itemLoadUnloadAddressInfoList.find(
        (item) => item.type == 3
      );
      return detail;
    },
    stopOverDetails() {
      if (!this.tableData[0].itemLoadUnloadAddressInfoList) {
        return [];
      }
      let detail = this.tableData[0].itemLoadUnloadAddressInfoList.filter(
        (item) => item.type == 2
      );
      detail.sort((a, b) => Number(a.stopSequence) - Number(b.stopSequence));
      return detail;
    },
  },
  methods: {
    handlePicClose() {
      this.showedPic = "";
    },
    formatDateTime(input) {
      if (!input) {
        return;
      }
      let date = new Date(input);

      let month = (date.getMonth() + 1).toString().padStart(2, "0"); // getMonth 返回 0-11，所以加 1
      let day = date.getDate().toString().padStart(2, "0");

      let hours = date.getHours().toString().padStart(2, "0");
      let minutes = date.getMinutes().toString().padStart(2, "0");

      return `${month}-${day} ${hours}:${minutes}`;
    },
    convertMinutesToHoursAndMinutes(totalMinutes) {
      const hours = Math.floor(totalMinutes / 60); // 获取小时数
      const minutes = totalMinutes % 60; // 获取剩余的分钟数

      return `${hours}小时${minutes}分钟`;
    },
    showAbnormalImages(images) {
      if (!images) {
        return;
      }
      this.showedPic = images.split(";");
    },
    formatDate(input) {
        if(!input) {return ''}
        // 确保秒只有两位数字，移除可能存在的多余数字
        let correctInput = input.replace(/(\d{2})\d+(\s|$)/, '$1$2');
        
        // 创建 Date 对象
        let date = new Date(correctInput);

        // 获取月份和日子，并确保它们是两位数格式
        let month = (date.getMonth() + 1).toString().padStart(2, '0'); // getMonth 返回 0-11，所以加 1
        let day = date.getDate().toString().padStart(2, '0');

        // 组装成期望的格式
        return `${month}月${day}日`;
      },
      formatTime(input) {
        // 确保秒只有两位数字，移除可能存在的多余数字
        if(!input) {return ''}
        let correctInput = input.replace(/(\d{2})\d+(\s|$)/, '$1$2');
        
        // 创建 Date 对象
        let date = new Date(correctInput);

        // 获取小时和分钟，并确保它们是两位数格式
        let hours = date.getHours().toString().padStart(2, '0');
        let minutes = date.getMinutes().toString().padStart(2, '0');

        // 组装成期望的格式
        return `${hours}:${minutes}`;
      },
      handleWeight(weight) {
        if (!weight) return '0.000'
        return Number(weight).toFixed(3) 
      }
  },
};
</script>
<style scoped lang="scss">
.detail-panel {
  background: #fff;
  border-radius: 7px;
}
.detail-title {
  margin-top: 30px;
  position: relative;
  padding-bottom: 9px;
  padding-left: 12px;
  color: #333333;
  font-size: 14px;
  line-height: 30px;
  background: #fff;
  &::after {
    content: "";
    position: absolute;
    top: 7px;
    left: 0;
    width: 4px;
    height: 14px;
    background-color: #f6a018;
    border-radius: 2px;
  }
}

.detail-col {
  margin-bottom: 10px;
  font-size: 14px;
  color: #333;
}

.timeline-content {
  margin-top: 10px;
  color: #999;
}
.transportation {
  height: 500px;
  overflow-y: scroll;
  padding: 10px 0;
}
.transportation::-webkit-scrollbar {
  display: none;
}

.detail-row {
  margin-bottom: 0px !important;
  .detail-col {
    margin-bottom: 10px !important;
  }
}
.dispatchInfo-all {
  margin-top: 20px;
  font-size: 14px;
  color: #333;
  .dispatchInfo-item {
    display: flex;
    margin-bottom: 20px;
    height: 40px;
  }
  .dispatchInfo-left {
    width: 70px;
    display: flex;
    justify-content: end;
  }
  .dispatchInfo-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 20px;
    .dispatchInfo-right-top {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    .dispatchInfo-right-bottom {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.label-all {
  height: 100%;
  width: 70px;
  display: inline-block;
  position: relative;
  text-align: center;
  left: 20px;
  top: 4px;
  .zhuang-div {
    width: 28px;
    height: 28px;
    background-color: #3fba91;
    border-radius: 5px;
    display: inline-block;
    text-align: center;
    line-height: 28px;
    color: #fff;
  }
  .xie-div {
    width: 28px;
    height: 28px;
    background-color: #ed970d;
    border-radius: 5px;
    display: inline-block;
    text-align: center;
    line-height: 28px;
    color: #fff;
  }
  .jing-div {
    width: 28px;
    height: 28px;
    background-color: #4A83F6;
    border-radius: 5px;
    display: inline-block;
    text-align: center;
    line-height: 28px;
    color: #fff;
  }
  .line2 {
    height: 35px;
    left: 34px;
    position: absolute;
    width: 2px;
    top: 28px;
    border-left: 1px solid transparent;
    background: linear-gradient(white, white) padding-box,
      repeating-linear-gradient(-45deg, #ccc 0, #ccc 3px, white 0, white 5px);
  }
}
.tag-class{
  color: #cf2f2f;
  margin-right: 10px;
  position: relative;
  top: -2px;
}
</style>
<style lang="scss">
.el-tooltip__popper {
  max-width: 400px !important; /* 设置最大宽度 */
  white-space: normal !important; /* 允许自动换行 */
  word-break: break-all !important; /* 在单词长度超过容器宽度时进行断词 */
}
.nomarbottom{
    margin-bottom: 0px !important;
}
</style>