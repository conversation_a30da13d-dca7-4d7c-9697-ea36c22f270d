<template>
  <div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="给司机分配油" name="first">
        <DriverAllocation/>
      </el-tab-pane>
      <el-tab-pane label="给客户分配油" name="second">
       <CustomerAllocation/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import DriverAllocation from './dirverAllocation.vue'
import CustomerAllocation from './customerAllocation.vue'
export default {
  components: {
    DriverAllocation,
    CustomerAllocation
  },
  data() {
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick() {
      
    }
  }
};
</script>

<style lang="scss" scoped>
</style>