<template>
  <el-scrollbar wrap-class="scrollbar-wrapper">
    <el-menu
      :default-active="$route.path"
      :collapse="isCollapse"
      background-color="#00152A"
      text-color="#fff"
      active-text-color="#fff"
      mode="vertical"
    >
      <sidebar-item v-for="route in permission_routers" :key="route.path" :item="route" :base-path="route.path"/>
    </el-menu>
  </el-scrollbar>
</template>

<script>
import { mapGetters } from 'vuex'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'

export default {
  components: { SidebarItem },
  computed: {
    ...mapGetters([
      'permission_routers',
      'sidebar'
    ]),
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  }
}
</script>
<style lang="scss">
.el-submenu__title,
.el-menu-item {
  background: #00152A !important;
}
.el-submenu.is-opened .el-menu-item:not(.is-active) {
  background: #000E1B !important;
}
.el-menu-item:not(.is-active):hover{
  i {
    color: inherit !important;
  }
  outline: 0 !important;
  color: #F6A018 !important;
}
.el-menu-item.is-active {
  /* border-left: 10px solid #f6a018 !important; */
  background-color: #F6A018!important;
  /*background: #f6a018 !important;*/
}
.el-menu-item:focus {
  color: #ffffff !important;
  background: #F6A018 !important;
}
.el-menu-item:hover,
.el-submenu__title:hover {
  color: #F6A018 !important;
}
.el-submenu__title i {
  color: inherit !important;
}
.el-menu-item:focus {
  color: #ffffff !important;
  /*background: #f6a018 !important;*/
}
.el-menu-item-group,
.el-menu-item-group .el-menu-item:not(.is-active) {
  background-color: #20211F !important;
}
.el-menu-item-group__title {
  padding-top: 0;
  padding-bottom: 0;
}
</style>