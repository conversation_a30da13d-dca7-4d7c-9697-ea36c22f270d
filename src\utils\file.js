import { client } from '@/utils/alioss'
import { $get } from '@/utils/http2'
import { Message } from 'element-ui'
import Cookie from 'js-cookie'
import md5 from 'js-md5'
let <PERSON><PERSON>
const ossApiUrl = "/base-center-server/sts/oss_signature" // 获取oss签名的地址
// 获取oss签名
const getOssInfo = () => {
  return $get(ossApiUrl)
    .then(data => {
      Aliyun = data
      Aliyun.region = "oss-cn-zhangjiakou"

      //oss sts过期时间为50分钟
      localStorage.setItem('ossExpireTimestamp', Date.now() + 3000000)
  })
}
const checkExpire = () => {
  if (!<PERSON>yun) return getOssInfo()
  let timestamp = localStorage.getItem('ossExpireTimestamp')
  if (!timestamp || Date.now() >= timestamp) {
    return getOssInfo()
  } else {
    return Promise.resolve()
  }
}
export function selectFile(
  config = {
    multiple: false
  }
) {
  return new Promise(resolve => {
    let input = document.createElement('input')
    input.setAttribute('type', 'file')
    let {
      multiple
    } = config
    if (multiple) {
      input.setAttribute('multiple', true)
    }
    if (config.accept) {
      input.setAttribute('accept', config.accept)
    }
    input.addEventListener('change', e => {
      let files = Array.from(e.target.files)
      if (multiple) {
        resolve(files)
      } else {
        resolve(files[0])
      }
    })
    let event = new MouseEvent('click')
    input.dispatchEvent(event)
  })
}

export function upload(file) {
  return checkExpire()
    .then(() => {
      return new Promise(resolve => {
        const tmpcnt = file.name.lastIndexOf('.')
        const exname = file.name.substring(tmpcnt + 1)
        let fileName = '/' + Aliyun.bucket + '/' + Aliyun.dir + Date.now() + '/' + md5(file.name) + '.' + exname
        client(Aliyun).put(fileName, file)
          .then(res => {
            if (res.res.status === 200) {
              resolve({
                url: res.url,
                name: res.name
              })
            }
          })
      })
    })
}

//TODO 不支持多选文件
export function selectAndUpload(config) {
  return selectFile(config)
    .then(file => {
      return upload(file)
    })
}

//TODO 不支持拖动多个文件
export class FileDragger {
  constructor(el, config = {}) {
    this.el = el
    this.config = config
    this.handleDrop = this.handleDrop.bind(this)
    this.eventList = []
  }

  handleDrop(e) {
    e.preventDefault()
    let config = this.config
    let files = Array.from(e.dataTransfer.files)
    if (config.multiple !== true && files.length > 1) {
      Message.error('最多只能拖拽一个文件')
      return
    }
    if (config.accept) {
      let acceptArr = config.accept.split(',')
      files = files.filter(v => {
        for (let i = 0; i < acceptArr.length; i++) {
          if (new RegExp(acceptArr[i] + '$').test(v.name)) {
            return true
          }
        }
        return false
      })
    }

    if (files.length === 0) return
    this.trigger('select', files)

    //暂时只支持单个文件上传
    if (config.upload && !config.multiple) {
      upload(files[0])
        .then(fileInfo => this.trigger('upload', fileInfo))
    }
  }

  handleDragOver(e) {
    e.stopPropagation()
    e.preventDefault()
  }

  startDragUpload() {
    this.el.addEventListener('drop', this.handleDrop)
    this.el.addEventListener('dragover', this.handleDragOver)
  }

  endDragUpload() {
    this.el.removeEventListener('drop', this.handleDrop)
    this.el.removeEventListener('dragover', this.handleDragOver)
  }
  
  on(eventName, fn) {
    if (!['select', 'upload'].includes(eventName)) return
    let listName = eventName + 'List'
    this[listName] = this[listName] || []
    this[listName].push(fn)
  }

  
  trigger(eventName, ...data) {
    let listName = eventName + 'List'
    if (this[listName]) {
      this[listName].forEach(fn => {
        typeof fn === 'function' && fn(...data)
      })
    }
  }
}