<template>
  <div class="main-box">
    <div class="base-info">
      <div class="title-box">
        <div>订单信息</div>
        <slot></slot>
      </div>
      <div class="list-box">
        <detail-row>
          <detail-col :span="6" label="订单单号" :value="tableData[0].sn"></detail-col>
          <detail-col :span="6" label="创建人" :value="tableData[0].orderCreateUserName"></detail-col>
          <detail-col :span="6" label="创建日期" :value="tableData[0].createdDate"></detail-col>
          <detail-col :span="6" label="订单类型" :value="tableData[0].typeParams"></detail-col>
          <detail-col :span="6" label="找车方式" :value="tableData[0].carAssignChannelName || '-'"></detail-col>
          <detail-col :span="6" label="需求车型">
            {{ tableData[0].carType && tableData[0].carType.length > 0 ? tableData[0].carType.join('/') : '-' }}
            <span style="color: #ccc;" v-if="tableData[0].carLengths"> | </span>
            {{ handleLangth(tableData[0].carLengths) }}
          </detail-col>
          <detail-col :span="6" label="订单状态" :value="tableData[0].statusEnum"></detail-col>
          <detail-col :span="6" label="订单有效期">
            <span>{{tableData[0].loadDateBegin}}</span>
            <span v-if="tableData[0].loadDateEnd" style="margin: 0 3px;"> - </span>
            <span>{{tableData[0].loadDateEnd}}</span>
          </detail-col>
          <detail-col v-if="!$store.state.user.userInfo2.hasStandardModeFlag" :span="6" label="支付方式" :value="tableData[0].paymentTypeEnum"></detail-col>
          <detail-col v-if="showCustomerName" :span="6" label="客户名称" :value="tableData[0].businessName"></detail-col>
          <!-- <detail-col :span="6" label="含税订单金额" :value="tableData[0].amount" :formatter="moneyFormatter"></detail-col> -->
          <detail-col :span="6" label="司机运费" :value="tableData[0].driverAmount + '元'" :formatter="moneyFormatter">
            <span v-if="tableData[0].driverAmount ==='' || tableData[0].driverAmount ===null">-</span>
            <span v-else>{{ tableData[0].driverAmount + '元' }}</span>
            <span v-if="tableData[0].driverFreightType">{{ tableData[0].driverFreightType=='1'?'(可电议)':'(一口价)' }}</span>
          </detail-col>
          <!-- <detail-col :span="6" label="平台服务费" :value="tableData[0].serviceFee" :formatter="moneyFormatter"></detail-col> -->
          <!-- <detail-col v-if="!$store.state.user.userInfo2.hasStandardModeFlag" :span="6" label="冻结运单金额" :value="tableData[0].frozenAmount" :formatter="moneyFormatter"></detail-col> -->
          <detail-col :span="6" label="需交押金">
            <span v-if="tableData[0].carAssignChannel && tableData[0].carAssignChannel!='2'">{{ tableData[0].depositFlag? '是' : '否' }}</span>
            <span v-else>-</span>
            
          </detail-col>
          <detail-col :span="6" v-if="tableData[0].freightCalcType == 0" label="计费规则" :value="tableData[0].ruleName">
            {{tableData[0].ruleName}}
            <el-tooltip placement="top">
              <i class="el-icon-warning-outline owner-icon"></i>
              <template #content>
                <div v-for="(item, index) in ruleData" :key="item.id" class="rule">
                  <div>{{ index + 1 }} 、{{ item.name }}</div>
                  <div>{{ item.rormulatian }}</div>
                </div>
              </template>
            </el-tooltip>
          </detail-col>
          <!-- <detail-col :span="6" label="最久待装车" :value="tableData[0].overTime !== null ? tableData[0].overTime + '小时' : ''"></detail-col> -->
          <detail-col :span="6" label="昵称" :value="tableData[0].orderNickName"></detail-col>
          <detail-col :span="6" label="运输距离" :value="kmFormatter(tableData[0].distance)"></detail-col>
          
          <detail-col :span="6" label="监管业务类型">{{tableData[0].businessTypeValue}}</detail-col>
          <detail-col :span="6" label="合作平台主体">{{tableData[0].baseName}}</detail-col>
          <detail-col :span="6" label="所属项目">{{tableData[0].projectName || '-'}}</detail-col>
          <detail-col :span="6" label="快运平台">{{tableData[0].customerName || '-'}}</detail-col>
          <detail-col :span="6" label="业务类型">{{tableData[0].businessTypeName || '-'}}</detail-col>
          <detail-col :span="6" label="线路">{{tableData[0].routeName || (tableData[0].route || '-')}}</detail-col>
          <detail-col :span="6" label="竞标ID">{{tableData[0].competitiveBiddingCode || '-'}}</detail-col>
          <detail-col :span="6" label="竞标价">{{tableData[0].competitiveBiddingPrice ? tableData[0].competitiveBiddingPrice+(tableData[0].freightCalcType === '1' ? '元/车' : '元/吨') : '-'}}</detail-col>
          <detail-col :span="6" label="无车扣款" v-if="tableData[0].noCarDeduction">{{ tableData[0].noCarDeduction + '元' }}</detail-col>
          <!-- <detail-col :span="6" label="与货主约定运费" v-if="tableData[0].operationalPeoFlag === '1'" :value="unitMoneyFormatter(tableData[0].freightCalcType, tableData[0].platformConsignorFreight)"></detail-col> -->
          <detail-col :span="6" label="运力专员">{{tableData[0].capacityPersonName || '-'}}</detail-col>
          <detail-col :span="6" label="销售人员">{{tableData[0].salePersonName || '-'}}</detail-col>
          <detail-col :span="6" label="交易凭证">
            <template>
              <el-button v-if="tableData[0].upstreamStatementUrl" type="text" @click="() => showedPic = tableData[0].upstreamStatementUrl">查看</el-button>
              <span v-else>-</span>
            </template>
          </detail-col>
          <detail-col :span="24" label="备注" :value="tableData[0].memo"></detail-col>
          <detail-col :span="24" label="驳回原因" :value="tableData[0].rejectMemo"></detail-col>
          <detail-col  :span="24" label="运输合同" value="">
            <template>
              <el-button v-if="tableData[0].transportationContractUrl" @click="openFn(tableData[0].transportationContractUrl)" type="text" size="small">{{ tableData[0].transportationContractName }}</el-button>
              <span v-else>-</span>
            </template>
          </detail-col>
        </detail-row>
      </div>
    </div>
    <div class="base-info" v-if="tableData[0].operationalPeoFlag === '1'">
      <div class="title-box">
        <div>收发货信息</div>
      </div>
      <div class="list-box">
        <ReceiceDispatchInfo :tableData="tableData"></ReceiceDispatchInfo>
      </div>
    </div>

    <div class="base-info">
      <div class="title-box">
        <div>投保信息</div>
      </div>
      <div class="list-box">
        <detail-row>
          <detail-col :span="6" label="在线投保" :value="tableData[0].onlineInsuranceValue"></detail-col>
          <!-- <detail-col :span="6" label="险种">
            <template v-if="tableData[0].insuranceType">
              {{ tableData[0].insuranceType === '0' ? '基本险' : '综合险' }}
            </template>
            <template v-else>-</template>
          </detail-col> -->
          <detail-col :span="6" label="货值">
            <template v-if="tableData[0].goodsValue">
              {{ tableData[0].goodsValue + '万元/车' }}
            </template>
            <template v-else>-</template>
          </detail-col>
          <!-- <detail-col :span="12" label="货值证明">
            <template v-if="tableData[0].proofValueImg">
              <template v-if="tableData[0].proofValueImg">
                <imageUploader2 v-for="item in tableData[0].proofValueImg.split(';')" :key="item" :defaultUrl="item" type="viewer" :size="[120, 120]"></imageUploader2>
              </template>
            </template>
            <template v-else>-</template>
          </detail-col> -->
          <detail-col :span="6" label="投保货物类型">
            <template v-if="tableData[0].onlineInsurance === '1'">
              {{ tableData[0].conveyanceIdStr }}
            </template>
            <template v-else>-</template>
          </detail-col>
          <detail-col :span="6" label="投保时机">
            <template v-if="tableData[0].onlineInsurance === '1'">
              {{ tableData[0].insuranceOccasionStr }}
            </template>
            <template v-else>-</template>
          </detail-col>
          <detail-col :span="6" label="投保方案">
            <template v-if="tableData[0].onlineInsurance === '1'">
              {{ tableData[0].insurancePlan }}
            </template>
            <template v-else>-</template>
          </detail-col>
          <!-- <detail-col :span="6" label="运输距离">
            <template v-if="tableData[0].onlineInsurance === '1'">
              {{ kmFormatter(tableData[0].distance) }}
            </template>
            <template v-else>-</template>
          </detail-col> -->
        </detail-row>
      </div>
    </div>
    <div v-if="!$store.state.user.userInfo2.hasStandardModeFlag" class="base-info">
      <div class="title-box">
        <div>油费信息</div>
      </div>
      <div class="list-box">
        <detail-row>
          <detail-col :span="6" label="油费设置" :value="(tableData[0].openOilGasFlag || tableData[0].openOilGasFlag=='1')?'是':'否'"></detail-col>
          <detail-col :span="6" label="每单油费" :value="(tableData[0].openOilGasFlag || tableData[0].openOilGasFlag=='1')?tableData[0].orderOilCost:'-'"></detail-col>
          <!-- <detail-col :span="6" label="油费支付方式" :value="tableData[0].oilPayPatternStr">{{tableData[0].oilPayPatternStr}}</detail-col> -->
        </detail-row>
      </div>
    </div>
    <div  class="base-info">
      <div class="title-box">
        <div>回单信息</div>
      </div>
      <div class="list-box">
        <detail-row>
          <detail-col :span="6" label="回单设置" :value="tableData[0].receiptFlag=='1'?'是':'否'"></detail-col>
          <detail-col :span="6" label="回单类型" :value="tableData[0].receiptTypeName||'-'"></detail-col>
          <detail-col :span="6" label="回单押款" :value="tableData[0].receiptSecurity? Number(tableData[0].receiptSecurity).toFixed(2) +'元':'-'"></detail-col>
          <detail-col :span="6" label="回单备注" :value="tableData[0].receiptRemark||'-'"></detail-col>
        </detail-row>
      </div>
    </div>
    <div class="drive-info">
      <div class="title-box">
        <div>货物信息</div>
      </div>
      <div class="list-box">
        <detail-row>
          <detail-col :span="6" label="运费单价" :value="unitMoneyFormatter(tableData[0].freightCalcType, tableData[0].freight)"></detail-col>
          <!-- <detail-col :span="6" label="含税运费单价" :value="unitMoneyFormatter(tableData[0].freightCalcType, tableData[0].taxFreight)"></detail-col> -->
          <detail-col :span="6" label="货物总量" :value="unitFormatter(tableData[0].freightCalcType, tableData[0].totalTon)"></detail-col>
          <detail-col :span="6" label="货物名称" :value="tableData[0].cargoTypeClassificationValue + ' / ' + tableData[0].cargoType"></detail-col>
          <detail-col :span="6" label="待装货" :value="unitFormatter(tableData[0].freightCalcType, tableData[0].pendingShipPingTons)"></detail-col>
          <detail-col :span="6" label="运输中" :value="unitFormatter(tableData[0].freightCalcType, tableData[0].shippingTons)"></detail-col>
          <detail-col :span="6" label="已收货" :value="unitFormatter(tableData[0].freightCalcType, tableData[0].receivedTons)"></detail-col>
          <detail-col :span="6" label="剩余货物量" :value="unitFormatter(tableData[0].freightCalcType, tableData[0].leftTon)"></detail-col>
          <!-- <detail-col :span="6" label="途耗" :value="getNormalLoss()"></detail-col> -->
          <!-- <detail-col :span="6" label="货品单价" :value="tableData[0].consumeType !== null ? unitMoneyFormatter(tableData[0].freightCalcType, tableData[0].singlePrice) : ''"></detail-col> -->
          <!-- <detail-col :span="6" label="装车费" :value="tableData[0].loadFee" :formatter="moneyFormatter"></detail-col> -->
          <!-- <detail-col :span="6" label="卸车费" :value="tableData[0].unloadFee" :formatter="moneyFormatter"></detail-col> -->
          <!-- <detail-col :span="6" label="空驶费" :value="tableData[0].emptyFreight" :formatter="moneyFormatter"></detail-col> -->
        </detail-row>
      </div>
    </div>
    <div class="other-info" v-if="tableData[0].operationalPeoFlag !== '1'">
      <div class="title-box">
        <div>收发货信息</div>
      </div>
      <div class="list-box">
        <detail-row>
          <detail-col :span="12" label="发货地址" :value="tableData[0].deliveryPlace"></detail-col>
          <detail-col :span="6" label="发货人" :value="tableData[0].consignerName"></detail-col>
          <detail-col :span="6" label="发货人电话" :value="tableData[0].consignerPhone"></detail-col>
          <detail-col :span="12" label="卸货地址" :value="tableData[0].receivePlace"></detail-col>
          <detail-col :span="6" label="收货人" :value="tableData[0].consigneeName"></detail-col>
          <detail-col :span="6" label="收货人电话" :value="tableData[0].consigneePhone"></detail-col>
        </detail-row>
      </div>
    </div>
    <el-image-viewer
      v-if="showedPic"
      :on-close="closePic"
      :url-list="[showedPic]"/>
  </div>
</template>
<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import ReceiceDispatchInfo from '@/components/Order/ReceiceDispatchInfo.vue';

export default {
  components: { ElImageViewer , ReceiceDispatchInfo},
  props: ['tableData', 'showCustomerName', 'ruleData'],
  data() {
    return {
      showedPic: null
    }
  },
  methods: {
    openFn(url){
      window.open(url)
    },
    /* 给金额添加单位 */
    formatMoney(row, column) {
      let money = row[column.property];
      // console.log(row, "money");
      if (money == null) {
        return "";
      } else {
        return money + " 元 ";
      }
    },
    moneyFormatter(value) {
      if (value === '' || value === null) return ''
      return String(value) + '元'
    },
    /* 给吨数加单位 */
    formatTon(row, column) {
      let ton = row[column.property];
      if (ton == null) {
        return "";
      } else {
        return ton + " 吨 ";
      }
    },
    tonFormatter(value) {
      if (value === '' || value === null) return ''
      return String(value) + '吨'
    },
    /* 单价 */
    formatPrice(row, column) {
      let price = row[column.property];
      return price + "元/吨";
    },
    priceFormatter(value) {
      if (value === '' || value === null) return ''
      return String(value) + '元/吨'
    },
    getNormalLoss() {
      let normalLoss = this.tableData[0].consumeRatio
      if (this.tableData[0].consumeType === '0') {
        return normalLoss + '%'
      } else if (this.tableData[0].consumeType === '1') {
        return normalLoss + '千克/车'
      } else {
        return ''
      }
    },
    viewPic(url) {
      this.showedPic = url
    },
    closePic() {
      this.showedPic = null
    },
    handleLangth(val){
      if(!val) return '-'
      console.log(val)
      let arr = val.split(',')
      arr = arr.map(item=>{
      return item+'米' 
      })
      console.log(arr)
      if(arr.length > 0){
        return arr.join('/')
      }else{
        return '-'
      }  
    }
  }
}
</script>
<style scoped lang="scss">
.main-box {
  background-color: white;
  .title-box {
    font-size: 16px;
    font-weight: 700;
    height: 40px;
    line-height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  .list-box {
    margin-top: 20px;
    border: 1px solid #cccccc;
    border-left: none;
    .item-title {
      display: flex;
      flex-direction: row;
      div {
        width: 500px;
        height: 50px;
        font-weight: bold;
        font-size: 16px;
        line-height: 50px;
        border: 1px solid #cccccc;
        border-top: none;
        border-right: none;
        background-color: rgb(249, 252, 250);
        text-align: center;
      }
    }
    .item-info {
      display: flex;
      flex-direction: row;
      div {
        font-size: 14px;
        width: 500px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        border: 1px solid #cccccc;
        border-top: none;
        border-right: none;
        border-bottom: none;
      }
    }
  }
  .base-info,
  .drive-info,
  .other-info,
  .driver-info {
    padding: 20px;
  }
}
.view-btn {
  color: blue;
}
</style>