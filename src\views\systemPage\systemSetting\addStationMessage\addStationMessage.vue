<template>
  <div class="app-container operationLog">
    <div class="select-box">
      <div class="top-title">
        <div>发布站内消息</div>
      </div>
      <div class="select-info">
        <el-form :model="ruleForm" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-form-item label="消息类型" class="selectType" required>
            <div class="wrapper">
              <el-select v-model="ruleForm.sysMessageType" placeholder="请选择消息类型">
                <el-option label="产品消息" value="0"></el-option>
                <el-option label="安全消息" value="1"></el-option>
                <el-option label="服务消息" value="2"></el-option>
                <el-option label="活动消息" value="3"></el-option>
                <el-option label="历史消息" value="4"></el-option>
                <el-option label="故障消息" value="5"></el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="接收方" prop="sysMessageRecive" required>
            <el-radio-group v-model="ruleForm.sysMessageRecive" @change="selectReceive">
              <el-radio label="0">发送给全部用户</el-radio>
              <el-radio label="1">发送给指定用户</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- <el-form-item label="选中账户" v-if="receiveType">
            <el-input
              placeholder="请输入系统用户手机号"
              v-model="ruleForm.operateAccount"
              style="width: 200px"
            ></el-input>
          </el-form-item>-->

          <!--          <el-form-item label="选中账户" v-if="receiveType">-->
          <!--            <el-autocomplete-->
          <!--              class="inline-input mediaInput"-->
          <!--              v-model="selectAccount"-->
          <!--              :fetch-suggestions="querySearch"-->
          <!--              placeholder="请输入内容"-->
          <!--              @select="handleSelect"-->
          <!--              :trigger-on-focus="false"-->
          <!--            ></el-autocomplete>-->
          <!--          </el-form-item>-->

          <el-form-item label="消息标题" required>
            <el-input v-model="ruleForm.sysTitle" style="width: 200px"></el-input>
          </el-form-item>
          <el-form-item label="消息图片:" required>
            <el-upload
              action
              list-type="picture-card"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :http-request="ossUpload"
              :file-list="fileList"
            >
              <div class="upload-box">
                <div class="icon-XZ"></div>
                <div class="icon-word">支持jpg、jpeg、png 格式不超过10M</div>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="消息概要" required>
            <el-input v-model="ruleForm.sysMessageBriefly" style="width: 600px"></el-input>
          </el-form-item>

          <el-form-item label="消息内容" required>
            <el-form-item>
              <div class="editor-wrapper">
                <div ref="editor"></div>
              </div>
            </el-form-item>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="submitInfo" v-if="updateFlag == false">提交</el-button>
            <el-button type="primary" @click="upDate" v-if='updateFlag == true'>修改</el-button>
            <el-button @click="resetForm('ruleForm')">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import { uploadFile } from '@/utils/file.js'
// 引入wangEditor富文本
import E from "wangeditor";
import "wangeditor/release/wangEditor.min.css";

export default {
  data() {
    return {
      fileList: [], //图片回显
      options: [
        {
          value: "1",
          label: "产品消息"
        },
        {
          value: "2",
          label: "安全消息"
        },
        {
          value: "3",
          label: "服务消息"
        },
        {
          value: "4",
          label: "活动消息"
        },
        {
          value: "5",
          label: "历史消息"
        },
        {
          value: "6",
          label: "故障消息"
        }
      ],
      editor: "",
      selectAccount: "",
      value: "",
      receiveType: false,
      ruleForm: {
        sysMessageType: "", //消息类型
        sysMessageRecive: "0", //接收方
        sysTitle: "", //消息标题
        sysMessage: "", //消息内容
        sysUserIds: "", //选中的id

        sysMessageUrl: "", //消息图片
        sysMessageBriefly: "" //消息概要
      },
      messageType: "",
      picUrl: "", //上传成功的图片地址
      menus: [
        // 'head',  // 标题
        "bold", // 粗体
        "fontSize", // 字号
        "fontName", // 字体
        "italic", // 斜体
        "underline", // 下划线
        "strikeThrough", // 删除线
        "foreColor", // 文字颜色
        "backColor", // 背景颜色
        "link", // 插入链接
        "list", // 列表
        "justify", // 对齐方式
        "quote", // 引用
        // 'emoticon',  // 表情
        "image", // 插入图片
        // 'table',  // 表格
        // 'video',  // 插入视频
        // 'code',  // 插入代码
        "undo", // 撤销
        "redo" // 重复
      ],
      editorContent: "",
      updateFlag:false //修改的flag
    };
  },
  activated() {
    this.initEditor(); //初始化编辑器
    if (this.$route.query.messageFlag == 1) {
      //修改
      this.updateFlag = true;
      this.stationMesDetail(); // 获取详情
    }else{
       this.updateFlag = false; //新增
    }
  },
  methods: {
    /** 上传预览 **/
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList);
    },
    /* 上传图片 */
    ossUpload(param) {
      let file = param.file; // 文件的
      uploadFile(file).then(res => {
        if (res.res.status === 200) {
            // 上传
            let imgUrl = res.res.requestUrls[0];
            this.ruleForm.sysMessageUrl = imgUrl;
            this.$message.success("上传成功");
          } else {
            this.$message.error(res.res.message);
          }
      })
    },

    /* 富文本上传图片 */

    ossUpload2(param, result) {
      // console.log(param);
      let file = param[0]; // 文件的
      uploadFile(file).then(res => {
        if (res.res.status === 200) {
            // 上传
            let imgUrl = res.res.requestUrls[0];
            this.picUrl = imgUrl;
            // console.log(this.picUrl);

            this.$message({
              type: "success",
              message: "上传成功"
            });
            this.hideUpload = true;
            // console.log(result)
            result(this.picUrl);
          } else {
            this.$message.error(res.res.message);
          }
      })
    },
    /* 提交 */
    submitInfo() {
      this.ruleForm.sysMessage = this.editorContent;
      let postData = this.ruleForm;
      // console.log(postData, "postData--------");
      if (this.ruleForm.sysMessageType == "") {
        // console.log(postData)
        this.$message({
          type: "warning",
          message: "请选择消息类型"
        });
        return;
      }
      if (this.ruleForm.sysTitle == "") {
        this.$message({
          type: "warning",
          message: "请输入消息标题"
        });
        return;
      }
      if (this.ruleForm.sysMessageUrl == "") {
        this.$message({
          type: "warning",
          message: "请上传消息图片"
        });
        return;
      }

      if (this.ruleForm.sysMessageBriefly == "") {
        this.$message({
          type: "warning",
          message: "请输入消息概要"
        });
        return;
      }

      this.$http
        .post("/admin-center-server/sys/addSysMessage", postData)
        .then(res => {
          var data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "提交成功!"
            });
            this.$router.go(-1);
          } else {
            this.$message.warning(data.message);
          }
        });
    },


    /* 修改 */
    upDate() {
      this.ruleForm.sysMessage = this.editorContent;
      let id = this.$route.query.id;
      let postData = this.ruleForm;
      postData.id = id;

      // console.log(postData, "postData--------");
      if (this.ruleForm.sysMessageType == "") {
        // console.log(postData)
        this.$message({
          type: "warning",
          message: "请选择消息类型"
        });
        return;
      }
      if (this.ruleForm.sysTitle == "") {
        this.$message({
          type: "warning",
          message: "请输入消息标题"
        });
        return;
      }
      if (this.ruleForm.sysMessageUrl == "") {
        this.$message({
          type: "warning",
          message: "请上传消息图片"
        });
        return;
      }

      if (this.ruleForm.sysMessageBriefly == "") {
        this.$message({
          type: "warning",
          message: "请输入消息概要"
        });
        return;
      }

      this.$http
        .post("/admin-center-server/sys/updateSysMessage", postData)
        .then(res => {
          var data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "提交成功!"
            });
            this.$router.go(-1);
          } else {
            this.$message.warning(data.message);
          }
        });
    },



    selectReceive() {
      // console.log(this.ruleForm.sysMessageRecive);
      let type = this.ruleForm.sysMessageRecive;
      if (type == 0) {
        this.receiveType = false;
      } else {
        this.receiveType = true;
      }
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },

    //(模糊搜索)
    querySearch(queryString, cb) {
      if (queryString != "") {
        this.getPlanTypeData(queryString, data => {
          let results = "";
          if (queryString && !data[0].noId) {
            //输入框有值且有匹配数据时
            results = data.filter(this.createFilter(queryString));
          } else {
            //没有匹配数据时显示自定义的字段
            results = data;
          }
          cb(results);
        });
      }
    },
    createFilter(queryString) {
      return restaurant => {
        // return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase())> -1);
        //后台已做筛选,不需再过滤
        return restaurant.value;
      };
    },
    //获取模糊搜索的数据
    getPlanTypeData(val, fun) {
      let dataArr = [];
      let name = val;
      this.$http
        .get(
          "/admin-center-server/commonUser/getUserInfoByRealName?name=" + name
        )
        .then(res => {
          var data = res.data;
          if (data.code === "200") {
            // if (!res) return false;
            if (data.data.length > 0) {
              data.data.forEach((item, index) => {
                dataArr.push({
                  value: item.name,
                  name: item.id
                });
              });
            } else {
              dataArr.push({
                value: "无搜索结果",
                noId: "无搜索结果"
              });
            }
            fun(dataArr);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    //搜索选中
    handleSelect(item) {
      var that = this;
      this.ruleForm.sysUserIds = item.name;
    },

    initEditor() {
      this.editor = new E(this.$refs.editor);
      this.editor.customConfig.menus = this.menus; // menu菜单
      this.editor.customConfig.uploadImgMaxSize = 2 * 1024 * 1024;
      // 限制最多上传6张图片
      this.editor.customConfig.uploadImgMaxLength = 6;
      // 设置超时
      this.editor.customConfig.uploadImgTimeout = 3 * 60 * 1000;
      // 关闭粘贴样式的过滤
      this.editor.customConfig.pasteFilterStyle = false;
      // 忽略粘贴内容中的图片
      this.editor.customConfig.pasteIgnoreImg = true;
      this.editor.customConfig.customUploadImg = async (files, insert) => {
        await this.ossUpload2(files, insert);
        // insert(this.picUrl);
      };
      this.editor.customConfig.onchange = html => {
        this.editorContent = html;
        // console.log(this.editorContent);
      };
      this.editor.create();
      this.getHtml();
    },
    // 获取html
    async getHtml() {
      if (!this.html) return;
      let html = this.html;
      if (isUrl(this.html)) {
        const res = await CommonServer.getHtml(this.html);
        html = res.data;
      }
      this.$nextTick(() => {
        this.editorContent = html;
        this.editor.txt.html(html);
      });
    },
    // 获取编辑器内容
    getContent() {
      // console.log(this.editorContent);
      // this.$emit('getContent', editor.txt.html())
    },
    // 清除内容
    clearHtml() {
      this.editor && this.editor.txt.clear();
    },

    /* 站内消息详情 */
    stationMesDetail: function() {
      let id = this.$route.query.id;
      this.$http
        .get("/admin-center-server/sys/getSysMessageInfo", {
          params: {
            id: id
          }
        })
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            // console.log(res);
            this.ruleForm.sysTitle = res.data.data.sysTitle;
            this.ruleForm.sysMessageType = res.data.data.sysMessageType; //消息类型
            this.ruleForm.sysMessageRecive = res.data.data.sysMessageRecive;
            this.ruleForm.sysMessageBriefly = res.data.data.sysMessageBriefly; //概要
            this.editorContent = this.editor.txt.html(res.data.data.sysMessage); //富文本的内容
            this.fileList.push({
              url: res.data.data.sysMessageUrl
            });
            this.ruleForm.sysMessageUrl = res.data.data.sysMessageUrl
          } else {
            this.$message.warning(res.data.message);
          }
        });
    }
  },
  created() {
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.wrapper {
  ::v-deep .el-select-dropdown {
    z-index: 9999999 !important;
    background: red !important;
  }
}
.upload-box {
  width: 100%;
  height: 100%;
  position: relative;
  .icon-XZ {
    width: 92px;
    height: 92px;
    margin: 0 auto;
    background: url("../../../../assets/xiazai.png") no-repeat;
    background-size: 100% 100%;
  }
  .icon-word {
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 10px;
    position: absolute;
    bottom: 25px;
    left: 0px;
    color: #cccccc;
  }
}
.operationLog {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
      display: flex;
      justify-content: space-between;

      .button {
        margin-right: 20px;
      }
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;
    overflow: hidden;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }

    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }

    .paging {
      margin-top: 10px;
      float: right;
    }
  }
}
</style>
