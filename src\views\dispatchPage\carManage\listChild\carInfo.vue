<template>
    <div class="app-container carInfo">
        <div class="main-box">
            <div class="title">
                车辆认证信息
            </div>
            <div class="list-info">
                <el-form ref="form" label-width="300px">
                    <el-form-item label="车牌号:">
                        <span>{{plateNumber}}</span>
                    </el-form-item>
                    <el-form-item label="车型:">
                        <span>{{carModelName}}</span>
                    </el-form-item>
                    <el-form-item label="总重量:">
                        <span>{{sumCapacityTonnage}}</span>
                    </el-form-item>
                    <el-form-item label="载重:">
                        <span>{{capacityTonnage}}吨</span>
                    </el-form-item>
                    <el-form-item label="轴数:">
                        <span>{{axleNumber}}</span>
                    </el-form-item>
                    <el-form-item label="车辆车队长:">
                        <span>{{ownerName}}</span>
                    </el-form-item>
                    <el-form-item label="车辆行驶证:">
                        <div class="item-box">
                            <div class="img-box">
                                <img :src="licenseImage" alt="">
                            </div>
                            <div class="big-img" @click="dialogImg1 = true">查看大图</div>
                        </div>
                    </el-form-item>
                    <el-form-item label="车辆道路运输证:">
                        <div class="item-box">
                            <div class="img-box">
                                <img :src="shippingCertImage" alt="">
                            </div>
                            <div class="big-img" @click="dialogImg2 = true">查看大图</div>
                        </div>
                    </el-form-item>
                    <el-form-item label="车辆道路运输证号:">
                        <span>{{shippingCert}}</span>
                    </el-form-item>
                    <el-form-item label="车辆道路运输证期限至:">
                        <span>{{shippingCertExpire}}</span>
                        <span><span v-show="shippingCertExpireSurplus>0">（提示：{{shippingCertExpireSurplus}}天后过期)</span><span v-show="shippingCertExpireSurplus<0"
                                                                                                                            style="color: red;padding-left: 20px">已过期</span></span>
                    </el-form-item>
                    <el-form-item label="车辆审核状态:">
                        <span style="color: red">{{carAuthStatus}}</span>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" style="width: 200px;" @click="adopt">通过审核</el-button>
                        <el-button type="warning" style="width: 200px;" @click="dialogVisible = true">驳回</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="bot-tab">
                <div class="tab-title">
                    车辆审核记录
                </div>
                <div class="tab-box">
                    <template>
                        <el-table
                                :data="tableData"
                                stripe
                                border
                                style="width: 100%">
                            <el-table-column
                                    prop="createddate"
                                    label="创建日期"
                            >
                            </el-table-column>
                            <el-table-column
                                    prop="authStatus"
                                    label="车辆审核状态"
                            >
                            </el-table-column>
                            <el-table-column
                                    prop="operator"
                                    label="操作员">
                            </el-table-column>
                            <el-table-column
                                    prop="memo"
                                    label="车辆审核备注">
                            </el-table-column>
                        </el-table>
                    </template>
                </div>
            </div>
        </div>
        <!-- 驳回原因弹窗 -->
        <el-dialog
                title="请选择驳回原因"
                :visible.sync="dialogVisible"
                width="30%"
                :before-close="handleClose">
            <div class="reject-box">
                <template>
                    <el-checkbox-group v-model="checkList">
                        <div class="reson-select" v-for="check in reasonList">
                            <el-radio v-model="radio" :label="check.reason" @change="onChange">{{check.reason}}</el-radio>
                        </div>
                    </el-checkbox-group>
                </template>
                <div style="color: #33aef0;cursor: pointer" @click="otherReason">其他原因</div>
                <div class="other-reson">
                    <el-input type="textarea" :disabled="isWrite" v-model="formTxt.desc"></el-input>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button @click="dialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="reject">确认驳回</el-button>
  </span>
        </el-dialog>
        <!-- 查看大图 -->
        <el-dialog :visible.sync="dialogImg1">
            <img width="100%" :src="licenseImage" alt="">
        </el-dialog>
        <el-dialog :visible.sync="dialogImg2">
            <img width="100%" :src="shippingCertImage" alt="">
        </el-dialog>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                detailType:'',// 判断是前俩 或后俩 依据
                checkList: [],
                radio: '',
                isWrite:false,
                dialogVisible: false,
                dialogImg1: false,
                dialogImg2: false,
                formTxt: {
                    desc: '',
                },
                plateNumber: '',
                carAuthStatus: '',
                axleNumber: '',
                sumCapacityTonnage: '',
                carModelName: '',
                capacityTonnage: '',
                ownerName: '',
                licenseImage: '',
                shippingCertImage: '',
                shippingCert: '',
                shippingCertExpire: '',
                shippingCertExpireSurplus: '',
                reasonList:[],
                tableData: []
            }
        },
        methods: {
            handleClose() {
                this.dialogVisible = false
            },
            onChange(value) {
                this.formTxt.desc=value
                let checkedCount = value.length;
                if(checkedCount>0){
                    this.isWrite = true
                }else if(checkedCount===0){
                    this.isWrite = false;

                }
            },
            otherReason(){
                this.isWrite = false;
                this.radio=''
            },
            /** 驳回 **/
            reject() {
                let id = sessionStorage.getItem('detailCarId');
                let dec = this.formTxt.desc;
                let type = sessionStorage.getItem('detailCarOwnerType');
                if(dec===''){
                    this.$message.warning('请选中或输入驳回原因')
                }else {
                    if(type==='2'){
                        this.$http.post('/admin-center-server/car/driving/review?status='+2+'&id='+id+'&authMemo='+dec).then(res=>{
                            let data= res.data;
                            if(data.code ==='200'){
                                this.$message.success('驳回成功');
                                this.getCarInfoType();
                                this.dialogVisible = false;
                            }else if(data.code==='422'){
                                this.$message.warning(data.message)
                            }else {
                                this.$message.error(data.message)
                            }
                        })
                    }else{
                        this.$http.post('/admin-center-server/car/review?isCarAuthPassed='+false+'&cbId='+id+'&authMemo='+dec).then(res=>{
                            let data= res.data;
                            if(data.code ==='200'){
                                this.$message.success('驳回成功');
                                this.getCarInfo()
                            }else if(data.code==='422'){
                                this.$message.warning(data.message)
                            }else {
                                this.$message.error(data.message)
                            }
                        })
                    }
                }
            },
            /** 通过审核 **/
            adopt() {
                let type = sessionStorage.getItem('detailCarOwnerType');
                let id = sessionStorage.getItem('detailCarId');
                if(type==='2'){
                    let authMemo = '';
                    this.$http.post('/admin-center-server/car/driving/review?status='+1+'&id='+id+'&authMemo='+authMemo).then(res=>{
                        let data= res.data;
                        if(data.code ==='200'){
                            this.$message.success('通过审核');
                            this.getCarInfoType();
                            setTimeout(()=>{
                                this.$router.go(-1)
                            },1500)
                        }else if(data.code==='422'){
                            this.$message.error(data.message)
                        }
                    })
                }else {
                    let dec='';
                    this.$http.post('/admin-center-server/car/review?isCarAuthPassed='+true+'&cbId='+id+'&authMemo='+dec).then(res=>{
                        let data= res.data;
                        if(data.code ==='200'){
                            this.$message.success('通过审核');
                            this.getCarInfo();
                            setTimeout(()=>{
                                this.$router.go(-1)
                            },1500)
                        }else if(data.code==='422'){
                            this.$message.error(data.message)
                        }
                    })
                }

            },
            /** 审核回显详情  车辆管理 认证管理详情 **/
            getCarInfo() {
                let id = sessionStorage.getItem('detailCarId');
                this.$http.get('/admin-center-server/car/view', {
                    params: {
                        carId: id
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.plateNumber = data.data.plateNumber
                        this.axleNumber = data.data.axleNumber
                        this.sumCapacityTonnage = data.data.sumCapacityTonnage
                        this.carModelName = data.data.carModelName
                        this.capacityTonnage = data.data.capacityTonnage
                        this.ownerName = data.data.ownerName
                        this.licenseImage = data.data.licenseImage
                        this.shippingCertImage = data.data.shippingCertImage
                        this.shippingCert = data.data.shippingCert
                        this.shippingCertExpire = data.data.shippingCertExpire
                        this.shippingCertExpireSurplus = data.data.shippingCertExpireSurplus
                        let carAuthStatus = data.data.carAuthStatus
                        if (carAuthStatus === '0') {
                            this.carAuthStatus = '未认证'
                        }else if(carAuthStatus === '1'){
                            this.carAuthStatus = '认证成功'
                        }else if(carAuthStatus === '2'){
                            this.carAuthStatus = '认证失败'
                        }else if(carAuthStatus === '3'){
                            this.carAuthStatus = '认证中'
                        }
                         this.tableData = data.data.carAuthLogs
                    }
                })
            },
            /** 驾驶授权 归属手授权 审核回显 **/
            getCarInfoType(){
                let id = sessionStorage.getItem('detailCarId');
                this.$http.get('/admin-center-server/car/driving/DrivingDesc', {
                    params: {
                        drivingId: id
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.plateNumber = data.data.plateNumber;
                        this.axleNumber = data.data.axleNumber;
                        this.sumCapacityTonnage = data.data.sumCapacityTonnage;
                        this.carModelName = data.data.carModelName;
                        this.capacityTonnage = data.data.capacityTonnage;
                        this.ownerName = data.data.ownerName;
                        this.licenseImage = data.data.licenseImage;
                        this.shippingCertImage = data.data.shippingCertImage;
                        this.shippingCert = data.data.shippingCert;
                        this.shippingCertExpire = data.data.shippingCertExpire;
                        this.shippingCertExpireSurplus = data.data.shippingCertExpireSurplus;
                        let carAuthStatus = data.data.status;
                        if (carAuthStatus === '0') {
                            this.carAuthStatus = '未认证'
                        }else if(carAuthStatus === '1'){
                            this.carAuthStatus = '认证成功'
                        }else if(carAuthStatus === '2'){
                            this.carAuthStatus = '认证失败'
                        }else if(carAuthStatus === '3'){
                            this.carAuthStatus = '认证中'
                        }
                        this.tableData = data.data.carAuthLogs
                    }
                })
            },
            /** 驳回原因列表 **/
            getReasonList(){
                this.$http.get('/admin-center-server/reject/reason/list',{
                    params:{
                        pageNumber:1,
                        pageSize:2000,
                    }
                }).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                        this.reasonList=data.data.list
                    }
                })
            },
        },
        mounted() {
            let type = sessionStorage.getItem('detailCarOwnerType');
            if(type==='2'){
                this.getCarInfoType();
            }else {
                this.getCarInfo();
            }
            this.getReasonList()
        }

    }
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .carInfo {
        .main-box {
            background-color: white;
            margin-bottom: 50px;

            .title {
                height: 40px;
                line-height: 40px;
                font-size: 16px;
                font-weight: 700;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
            }

            .list-info {
                /*height: 400px;*/
                padding-top: 20px;

                .item-box {
                    position: relative;

                    .img-box {
                        width: 300px;
                        height: 200px;
                        border: 5px solid #42b983;

                        img {
                            display: block;
                            height: 100%;
                            width: 100%;
                        }
                    }

                    .big-img {
                        position: absolute;
                        left: 310px;
                        bottom: 0;
                        cursor: pointer;
                    }
                }
            }

            .bot-tab {
                .tab-title {
                    height: 30px;
                    line-height: 30px;
                    padding-left: 10px;
                    font-weight: 700;
                    font-size: 14px;
                }

                padding: 10px;
            }
        }

        .reject-box {
            height: 350px;

            .reson-select {
                height: 40px;
                line-height: 40px;
            }

            .other-reson {
                margin-top: 10px;
            }
        }
    }

</style>
