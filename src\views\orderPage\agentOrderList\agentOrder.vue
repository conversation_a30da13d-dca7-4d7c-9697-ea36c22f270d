<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">
        <div>筛选查询</div>
        <div class="button">
          <el-button
            class="left"
            icon="el-icon-search"
            size="mini"
            type="primary"
            @click="queryData"
          >查询</el-button>
          <el-button
            class="left"
            icon="el-icon-delete"
            size="mini"
            type="danger"
            @click="resetForm"
          >清空筛选</el-button>
        </div>
      </div>

      <div class="select-info">
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="100px">
          <el-form-item label="调度员单号:">
            <el-input v-model.trim="formInline.sn" placeholder="请输入订单号" size="small"></el-input>
          </el-form-item>

          <el-form-item label="订单号:">
            <el-input v-model.trim="formInline.orderBusinessSn" placeholder="请输入订单号" size="small"></el-input>
          </el-form-item>

          <el-form-item label="调度员单状态:" label-width="150px">
            <el-select v-model="formInline.status" placeholder="请选择" clearable size="small">
              <el-option value="0" label="待接单"></el-option>
              <el-option value="1" label="已取消"></el-option>
              <el-option value="2" label="已拒绝"></el-option>
              <el-option value="3" label="派车中"></el-option>
              <el-option value="4" label="运输中"></el-option>
              <el-option value="5" label="已完成"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="是否短倒：" size="mini">
            <el-select v-model="formInline.isBrevity" placeholder="选择是否短倒" clearable>
              <el-option label="是" value="2"></el-option>
              <el-option label="否" value="1"></el-option>
            </el-select>
          </el-form-item> -->
        </el-form>

        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="100px">
          <el-form-item label="调度员名称:">
            <el-input
              v-model.trim="formInline.brokerIdName"
              placeholder="请输入调度员名称"
              size="small"
              maxlength="30"
            ></el-input>
          </el-form-item>
          <el-form-item label="客户名称:">
            <el-input
              v-model.trim="formInline.businessName"
              placeholder="请输入客户名称"
              size="small"
              maxlength="11"
              @input="checkMobile"
            ></el-input>
          </el-form-item>

          <el-form-item label="创建时间:">
            <el-date-picker
              v-model="formInline.startCreateTime"
              type="datetime"
              placeholder="选择开始时间"
              size="small"
              value-format="yyyy-MM-dd HH:mm:ss"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="formInline.endCreateTime"
              type="datetime"
              placeholder="选择结束时间"
              size="small"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="'23:59:59'"
            ></el-date-picker>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>订单列表</div>
        <div>
          <el-select
            v-model="formInline.order"
            placeholder="排序方式"
            style="width: 120px;margin-left:10px"
            @change="sort"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="list-main">
        <template>
          <el-table :data="tableData" border style="width: 100%" :height="tableHeight" ref="table"
          cell-class-name="table_cell_gray"
          header-cell-class-name="table_header_cell_gray"
          >
            <el-table-column type="index" label="序号" width="50"></el-table-column>
            <el-table-column prop="orderDispatchSn" label="调度单号" width="200"></el-table-column>
            <el-table-column prop="orderSn" label="所属订单号" width="200">
              <template slot-scope="scope">
                <el-button type="text" @click="$router.push('/order/orderManage/orderDetail?orderBusinessId=' + scope.row.orderBusinessId)">{{ scope.row.orderSn }}</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="businessName" label="货主" width="220"></el-table-column>
            <el-table-column prop="cargoType" label="货物名称"></el-table-column>
            <el-table-column prop="freight" label="运费单价" width="120" :formatter="unitMoneyTableFormatter"></el-table-column>
            <el-table-column prop="brokerMasterName" label="运力供应商"></el-table-column>
            <el-table-column prop="dispatcherName" label="调度员"></el-table-column>
            <el-table-column prop="statusString" label="状态"></el-table-column>
            <el-table-column prop="createTime" label="创建日期" width="120"></el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
              <template slot-scope="scope">
                <el-button @click="goDetail(scope.row)" type="text" size="small">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="page">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInline.pageNumber"
              :page-sizes="[20, 40, 60, 80,100]"
              :page-size="formInline.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              style="margin: 10px auto"
            ></el-pagination>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "agentOrder",
  data() {
    return {
      formInline: {
        sn: "", //调度员单号
        status: "", //调度员单状态
        orderBusinessSn: "", //订单号
        brokerIdName: "", //调度员名称
        businessName: "", //客户名称
        startCreateTime: "", //开始时间
        endCreateTime: "", //结束时间
        pageNumber: 1,
        pageSize: 20,
        order: "desc",
        isBrevity:'', //短倒
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            }
          },
          {
            text: "昨天",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            }
          },
          {
            text: "一周前",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            }
          }
        ]
      },
      value1: "",
      value2: "",
      value: "",
      tableHeight: null, //表格的高度
      date: [], //创建日期
      tableData: [],
      total: null, //总数
      tableData: [],
      options: [
        {
          value: "desc",
          label: "按时间降序"
        },
        {
          value: "asc",
          label: "按时间升序"
        }
      ]
    };
  },
  activated() {
    this.getData();
    this.tableHeight =
      window.innerHeight - this.$refs.table.$el.offsetTop - 160;
    console.log(this.tableHeight);
  },
  methods: {
    /* 选取每页多少条数据 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.formInline.pageNumber = 1;
      this.formInline.pageSize = JSON.parse(`${val}`);
      console.log(this.formInline.pageSize);
      this.getData();
    },
    /* 翻页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.formInline.pageNumber = JSON.parse(`${val}`);
      console.log(this.formInline.pageNumber);
      this.getData();
    },

    /* 查看详情页 */
    goDetail(row) {
      console.log(row);
      this.$router.push({
        path: "agentOrder/agentOrderDetail",
        query: {
          // orderDispatchId
          orderBrokerId: row.orderDispatchId //经济单主键
        }
      });
    },
    /** 获取数据 **/
    getData() {
      let postData = this.formInline;
      // 不修改现有参数名称， 直接修改请求参数名称
      postData.businessName = this.formInline.businessName
      postData.createTimeEnd = this.formInline.endCreateTime
      postData.createTimeStart = this.formInline.startCreateTime
      postData.dispatcherName = this.formInline.brokerIdName
      postData.orderDispatchSn = this.formInline.sn
      postData.orderSn = this.formInline.orderBusinessSn
      postData.pageNumber = this.formInline.pageNumber
      postData.pageSize = this.formInline.pageSize
      postData.status = this.formInline.status
      this.$http
        .post("/admin-center-server/orderDispatch/queryOrderDispatchPage", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.tableData = data.data.list;
            this.total = JSON.parse(data.data.total);
          }
        });
    },
    /* 按条件查询 */
    queryData() {
      console.log(this.formInline, "数据");
      this.formInline.pageNumber = 1;
      this.getData();
    },
    
    
    checkMobile() {
      this.formInline.consignerPhone = this.formInline.consignerPhone.replace(
        /[^\d]/g,
        ""
      );
      this.formInline.consigneePhone = this.formInline.consigneePhone.replace(
        /[^\d]/g,
        ""
      );
    },
    /* 排序 */
    sort() {
      this.getData();
    },
    /* 重置 */
    resetForm() {
      this.formInline = {
        sn: "", //调度员单号
        status: "", //调度员单状态
        orderBusinessSn: "", //订单号
        brokerIdName: "", //调度员名称
        businessName: "", //客户名称
        startCreateTime: "", //开始时间
        endCreateTime: "", //结束时间
        pageNumber: 1,
        pageSize: 20,
        order: "desc",
        isBrevity:''
      };
      this.getData();
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
      display: flex;
      justify-content: space-between;

      .button {
        margin-right: 20px;
      }
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .page {
    text-align: right;
  }
  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }

    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }
  }
}
</style>
