<template>
  <div class="app-container">
    <div class="base">当前平台主体：{{ currentBase.baseName }} <i @click="changeBase" class="el-icon-sort"></i></div>
    <div class="taxCertificate">
      <div class="upload_tax_content">
        <div class="upload_tax_content_title">
          <span>上传完税证明</span>
          <span class="upload_tax_content_des"
            >*此完税证明支持货主查看，所以敏感信息建议打码</span
          >
        </div>
        <div class="upload_tax_content_date">
          <span>月份: </span>
          <el-date-picker v-model="month" type="month" placeholder="选择月">
          </el-date-picker>
        </div>
        <div class="upload_tax_content_certificate">
          <div style="margin-right: 20px">上传个税完税证明:</div>
          <ImageUploader
            type="upload"
            :defaultUrl="personalTaxImage"
            @change="uploadPersonalTaxImage"
          ></ImageUploader>

          <div style="margin-right: 20px; margin-left: 40px">
            上传增值税附加税完税证明:
          </div>
          <ImageUploader
            type="upload"
            :defaultUrl="valueAddedTaxImage"
            @change="uploadValueAddedTaxImage"
          ></ImageUploader>
        </div>
      </div>
      <div class="upload_tax_history">
        <div class="upload_tax_content_title">
          <span>历史记录</span>
        </div>
        <div class="history_list">
          <el-table :data="list" 
          :cell-style="{'text-align': 'center', 'border': '0.5px solid #EAF0FB'}"
          :header-cell-style="{'text-align': 'center', 'border': '0.5px solid #EAF0FB'}">
            <el-table-column prop="lastModifiedDate" label="上传时间"  align="center">
            </el-table-column>
            <el-table-column prop="fileName" label="文件名"  align="center">
            </el-table-column>
            <el-table-column prop="beloneMonth" label="所属月份" align="center">
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center">
              <template slot-scope="scope">
                <el-button @click="checkTaxImage(scope.row)" type="text" size="small"
                  >查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pageNumber"
            :page-sizes="[10, 20, 40, 60, 80, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            class="pagination"
          ></el-pagination>
        </div>
      </div>
      <el-image-viewer v-if="showItem.showImage" :on-close="closeViewer" :url-list="[showItem.taxVoucherUrl]"></el-image-viewer>
    </div>
  </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer.vue'
export default {
  components: {
    ElImageViewer
  },
  data() {
    return {
      month: new Date(),
      personalTaxImage: "",
      valueAddedTaxImage: "",
      list: [],
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      showItem: {},
      currentBase: {}
    };
  },
  mounted() {
    let baseInfo = this.baseInfo = this.$store.state.user.baseInfo
    this.currentBase = baseInfo.find(v => v.defaultFlag)
    this.loadHistoryData()
  },
  methods: {
    changeBase() {
      let baseInfo = this.baseInfo
      let currentIndex = baseInfo.findIndex(v => v.id === this.currentBase.id)
      let toggleIndex = null
      if (currentIndex === baseInfo.length - 1) {
          toggleIndex = 0
      } else {
          toggleIndex = currentIndex + 1
      }
      this.currentBase = baseInfo[toggleIndex]
      
        this.personalTaxImage = ''
        this.valueAddedTaxImage = ''
        this.loadHistoryData()
        this.loadMonthTax()
      setTimeout(() => {
        this.$message.success('切换成功')
      }, 1000);
    },
    getDate(value) {
      let d = new Date(value);
      let year = d.getFullYear();
      let month = d.getMonth() + 1;
      if (month < 10) {
        month = "0" + month;
      }
      let datetime = year + "" + month;
      return datetime;
    },
    loadMonthTax() {
      if (Object.keys(this.currentBase).length == 0) {
        let baseInfo = this.baseInfo = this.$store.state.user.baseInfo
        this.currentBase = baseInfo.find(v => v.defaultFlag)
      }
      this.$post(
        "/admin-center-server/taxFrozen/getDutyPaidProofAdmin?beloneMonth=" +
          this.getDate(this.month) + '&baseId=' + this.currentBase.id
      ).then(
        (res) => {
          let pValue = res.find((item) => {
            return item.type == 1;
          });
          if (pValue != undefined) {
            this.personalTaxImage = pValue.taxVoucherUrl;
          } else {
            this.personalTaxImage = "";
          }

          let tValue = res.find((item) => {
            return item.type == 2;
          });
          if (tValue != undefined) {
            this.valueAddedTaxImage = tValue.taxVoucherUrl;
          } else {
            this.valueAddedTaxImage = "";
          }
        },
        (error) => {
          console.log(error);
        }
      );
    },
    handleSizeChange (val) {
      this.pageNumber = 1
      this.pageSize = val
      this.loadHistoryData()
    },
    handleCurrentChange (val) {
      this.pageNumber = val
      this.loadHistoryData()
    },

    loadHistoryData() {
        this.$post(
        "/admin-center-server/taxFrozen/getDutyPaidProofRecord?pageNumber=" +
          this.pageNumber + "&pageSize=" + this.pageSize + '&baseId=' + this.currentBase.id
      ).then(
        (res) => {
          this.list = res.list
          this.list.forEach((item) => {
            item.showImage = false
          })
          this.total = Number(res.total)
        }
      );
    },

    uploadPersonalTaxImage(url) {
      this.personalTaxImage = url
      this.uploadTaxImage(1, url);
    },
    uploadValueAddedTaxImage(url) {
      this.valueAddedTaxImage = url
      this.uploadTaxImage(2, url);
    },

    uploadTaxImage(type, url) {
        if (!url) {
            return
        }
      let imageData = {
        beloneMonth: this.getDate(this.month),
        taxDutyPaidProofImage: url,
        type: type,
        baseId: this.currentBase.id
      };
      this.$post(
        "/admin-center-server/taxFrozen/uploadingDutyPaidProof",
        imageData
      ).then((res) => {
        this.pageNumber = 1
        this.loadHistoryData()
        this.$message({
          type: "success",
          message: "上传成功",
        });
      });
    },
    checkTaxImage(item) {
      this.showItem = item
      this.showItem.showImage = true
    },
    closeViewer() {
      this.showItem = {}
    }
  },
  watch: {
    month: {
      immediate: true,
      handler() {
        this.loadMonthTax();
      },
    },
  },
};
</script>

<style lang="scss" scoped>
.base {
  margin-bottom: 10px;
  font-size: 14px;
  i {
      transform: rotate(90deg);
      color: #F6A018;
      cursor: pointer;
  }
}
.taxCertificate {
  background-color: white;
  padding: 20px;
  .upload_tax_content_des {
    margin-left: 8px;
    font-size: 13px;
  }
  .upload_tax_content_title:before {
    content: "";
    display: inline-block;
    width: 3px;
    height: 13px;
    background: #ed970f;
    border-radius: 1px;
    margin-right: 4px;
  }

  .upload_tax_content_date {
    margin-top: 20px;
    margin-left: 20px;
  }

  .upload_tax_content_certificate {
    display: flex;
    margin-top: 20px;
    margin-left: 20px;
  }

  .upload_tax_history {
    margin-top: 30px;
    .history_list {
        margin-top: 30px;
        margin-right: 10px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>