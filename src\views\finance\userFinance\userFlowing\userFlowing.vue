<template>
    <div class="app-container userCash">
        <div class="select-box">
            <div class="top-title">
                <div>筛选查询</div>
                <div class="button">
                    <el-button
                            class="left"
                            icon="el-icon-search"
                            size="mini"
                            type="primary"
                            @click="onSubmit"
                    >查询</el-button>
                    <el-button
                            class="left"
                            icon="el-icon-delete"
                            size="mini"
                            type="danger"
                            @click="resetSubmit"
                    >清空筛选</el-button>
                </div>
            </div>
            <div class="select-info">
                <el-form
                        size="mini"
                        :inline="true"
                        :model="formInline"
                        class="demo-form-inline"
                        label-width="150px"
                >
                    <el-form-item label="用户类型:">
                        <el-select v-model="formInline.dType" placeholder="不限" style="width:180px">
                            <el-option label="不限" value></el-option>
                            <el-option label="客户" value="1"></el-option>
                            <el-option label="调度员" value="2"></el-option>
                            <el-option label="司机(包括车队长)" value="3"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="用户名称:">
                        <el-input v-model="formInline.userName" :οnkeyup="formInline.userName=formInline.userName.replace(/\s/g, '')" placeholder="请输入用户名称或手机号"></el-input>
                    </el-form-item>
                    <el-form-item label="用户手机号:">
                        <el-input v-model="formInline.userMobile" :οnkeyup="formInline.userMobile=formInline.userMobile.replace(/\s/g, '')" placeholder="请输入用户手机号"></el-input>
                    </el-form-item>
                </el-form>
                <el-form
                        size="mini"
                        :inline="true"
                        :model="formInline"
                        class="demo-form-inline"
                        label-width="150px"
                >
                    <el-form-item label="操作类型:">
                        <el-select v-model="formInline.type" placeholder="不限" style="width:180px">
                            <el-option label="不限" value></el-option>
                            <el-option label="充值" value="1"></el-option>
                            <el-option label="提现" value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="交易金额:">
                        <el-col :span="10">
                            <el-input placeholder v-model="formInline.moneyStart" :οnkeyup="formInline.moneyStart=formInline.moneyStart.replace(/\s/g, '')">
                                <template slot="append">元</template>
                            </el-input>
                        </el-col>
                        <el-col :span="10">
                            <el-input placeholder v-model="formInline.moneyEnd" :οnkeyup="formInline.moneyEnd=formInline.moneyEnd.replace(/\s/g, '')">
                                <template slot="append" style="width: 20px">元</template>
                            </el-input>
                        </el-col>
                    </el-form-item>
                </el-form>
                <el-form
                        size="mini"
                        :inline="true"
                        :model="formInline"
                        class="demo-form-inline"
                        label-width="150px"
                >
                    <el-form-item label="银行名称:">
                        <el-input v-model="formInline.bank" :οnkeyup="formInline.bank=formInline.bank.replace(/\s/g, '')" placeholder="付款银行或收款银行名称"></el-input>
                    </el-form-item>
                    <el-form-item label="银行账号:">
                        <el-input v-model="formInline.acnumber" :οnkeyup="formInline.acnumber=formInline.acnumber.replace(/\s/g, '')" placeholder="付款银行或收款银行账号"></el-input>
                    </el-form-item>
                    <el-form-item label="操作日期:">
                        <el-date-picker
                                :clearable="false"
                                @blur="selectTime"
                                v-model="formInline.date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetimerange"
                                :default-time="['00:00:00', '00:00:00']"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                        ></el-date-picker>
                    </el-form-item>
                </el-form>
                <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="100px">
                    <el-form-item label=" ">
                        <el-radio-group v-model="formInline.timePoint" @change="radioChange">
                            <el-radio :disabled="radioInput" label="7">最近7天</el-radio>
                            <el-radio :disabled="radioInput" label="14">最近14天</el-radio>
                            <el-radio :disabled="radioInput" label="30">最近30天</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="list-box">
            <div class="list-title">
                <div>明细列表</div>
                <div>
                    <el-button :loading="loading" size="mini" type="primary" @click="exportRow">导出</el-button>
                </div>
            </div>
            <div class="list-main">
                <template>
                    <el-table
                            height="930px"
                            @selection-change="handleSelectionChange"
                            :data="tableData"
                            border
                            style="width: 100%"
                    >
                        <el-table-column type="selection" width="55"></el-table-column>
                        <el-table-column label="序号" type="index" width="50"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="userName" label="用户名称" width="200px"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="userMobile" label="用户手机号" width="180px"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="dtype" label="用户类型" width="80px"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="type" label="操作类型" width="80px"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="amount" label="充值金额/提现金额(元)" width="180px"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="bankName" label="付款银行/收款银行" width="180px"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="acctNo" label="付款账号/收款账号" width="220"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="status" label="操作状态" width="120"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="remark" label="备注" width="120"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="transactionSn" label="流水号" width="240"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="optionTime" label="操作时间" width="220"></el-table-column>
                    </el-table>
                </template>
            </div>
            <div class="paging">
                <div class="block">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 30, 40]"
                            :page-size="pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total"
                    ></el-pagination>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                loading:false,
                dialogVisible: false,
                radioInput: false,
                currentPage: 1,
                total: 1,
                pageSize: 20,
                dateInput: false,
                formInline: {
                    dType: "",
                    userName: "",
                    userMobile: "",
                    type: "",
                    moneyStart: "",
                    moneyEnd: "",
                    bank: "",
                    acnumber: "",
                    timePoint: "",
                    date: []
                },
                startTime: "",
                endTime: "",

                tableData: [],
                multipleSelection: []
            };
        },
        methods: {
            radioChange(value) {
                if (this.formInline.timePoint != "") {
                    this.dateInput = true;
                }
            },
            /** 根据时间态搜索 **/
            selectTime() {
                if (this.formInline.date!== null) {
                    this.radioInput = true;
                    let startTime = this.formInline.date[0];
                    let endTime = this.formInline.date[1];
                    this.startTime = startTime;
                    this.endTime = endTime;
                }else {
                    this.formInline.date=[]
                }
            },
            onSubmit() {
                this.currentPage = 1;
                let moneyStart = this.formInline.moneyStart;
                let moneyEnd = this.formInline.moneyEnd;
                if(moneyStart>moneyEnd){
                    this.$message.warning('交易金额查询有误')
                }else {
                    this.getDtatList();
                }
            },
            /** 清空搜索选项 **/
            resetSubmit() {
                this.formInline = {
                    dType: "",
                    userName: "",
                    userMobile: "",
                    type: "",
                    moneyStart: "",
                    moneyEnd: "",
                    bank: "",
                    acnumber: "",
                    timePoint: "",
                    date: []
                };
                this.dateInput = false;
                this.radioInput = false;
                this.startTime = ''
                this.endTime = ''
                this.getDtatList();
            },
            exportRow() {
                let ids = this.multipleSelection;
                if(ids.length<1){
                    this.$message.warning('至少选择一条明细')
                }else{
                    this.loading=true;
                    this.$http
                        .post("/admin-center-server/finance/deposit/order/export", ids)
                        .then(res => {
                            let data = res.data;
                            if (data.code === 200) {
                                let downLoad = data.data;
                                window.location.href=downLoad;
                                this.loading=false;
                            }else {
                                this.$message.warning(data.message)
                            }
                        });
                }
            },
            handleSelectionChange(val) {
                let ids = val.map((item, index) => {
                    return item.transactionSn;
                });
                this.multipleSelection = ids;
            },

            handleClose() {
                this.dialogVisible = false;
            },
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1;
                this.getDtatList();
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getDtatList();
            },
            getDtatList() {
                this.$http
                    .get("/admin-center-server/finance/deposit/order/list", {
                        params: {
                            pageSize: this.pageSize,
                            pageNumber: this.currentPage,
                            timeStart: this.startTime,
                            timeEnd: this.endTime,
                            DType: this.formInline.dType,
                            userName: this.formInline.userName,
                            userMobile: this.formInline.userMobile,
                            type: this.formInline.type,
                            moneyStart: this.formInline.moneyStart,
                            moneyEnd: this.formInline.moneyEnd,
                            bankName: this.formInline.bank,
                            acctNo: this.formInline.acnumber,
                            timePoint: this.formInline.timePoint
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.tableData = data.data.list;
                            this.total = Number(data.data.total);
                        }
                    });
            }
        },
        activated() {
            this.getDtatList();
        }
    };
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .userCash {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
                display: flex;
                justify-content: space-between;

                .button {
                    margin-right: 20px;
                }
            }

            .select-info {
                padding-top: 30px;
                padding-bottom: 30px;
            }
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;
            overflow: hidden;
            .list-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                div {
                    height: 38px;
                    line-height: 38px;
                }
            }

            .list-main {
                width: 100%;
                border: 1px solid #cccccc;
                margin-top: 10px;
            }
            .paging {
                margin-top: 10px;
                float: right;
            }
        }
    }
</style>
