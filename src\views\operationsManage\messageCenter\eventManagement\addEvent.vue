<template>
  <div class="app-container addCar">
    <div class="tip">
      <div>添加活动</div>
      <div>
        <em style="color: red">*</em>为必填项
      </div>
    </div>
    <div class="inner-box">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="200px"
        class="demo-ruleForm"
      >
        <el-form-item label="活动标题" required>
          <el-input v-model="ruleForm.title" style="width: 220px" placeholder="请输入活动标题"></el-input>
        </el-form-item>

        <el-form-item :label="date" required>
          <el-date-picker
            v-model="value1"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="活动图片:">
          <el-upload
            :v-model="ruleForm.photo"
            action
            :file-list="fileList"
            :limit="1"
            list-type="picture-card"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :http-request="ossUpload"
            :class="{hide:hideUpload}"
          >
            <div class="upload-box">
              <div class="icon-XZ"></div>
              <div class="icon-word">支持jpg、jpeg、png 格式不超过10M</div>
            </div>
          </el-upload>
          <el-dialog :visible.sync="dialogVisible" size="tiny">
            <img width="100%" :src="dialogImageUrl" alt />
          </el-dialog>
        </el-form-item>

        <el-form-item label="上架/下架">
          <el-radio v-model="radio" label="0">上线</el-radio>
          <el-radio v-model="radio" label="1">下架</el-radio>
        </el-form-item>

        <el-form-item label="活动类型" required>
          <el-radio-group v-model="eventType" @change="eventTypeFun">
            <el-radio label="0">站内活动</el-radio>
            <el-radio label="1">网页活动</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="活动内容" required :class="{'wangEditor':isDisplay == false }">
          <!-- <div id="wangeditor">
            <div id="editor">
            </div>
          </div>-->
          <el-input type="textarea" :rows="15" v-model="text" placeholder="请输入活动内容"></el-input>
        </el-form-item>

        <el-form-item label="网页链接" :class="{'wangEditor':isDisplay == true}" required>
          <el-input v-model="ruleForm.webUrl" style="width: 220px" placeholder="请输入网页链接"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button class="commitInfo" @click="commitInfo" v-if="typeFlag == 1">提交（新增）</el-button>
          <el-button class="commitInfo" @click="editorInfo" v-if="typeFlag == 2">提交（编辑）</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import { client, OSS_REGION } from "@/utils/alioss";

export default {
  data() {
    return {
      Aliyun: {},
      hideUpload: false, //控制是否显示的样式
      fileList: [], //图片回显
      isDisplay: true,
      typeFlag: "",
      editorContent: "", //编辑内容
      date: "日期",
      dialogImageUrl: "",
      radio: "0",
      eventType: "0",
      dialogVisible: false,
      ruleForm: {
        title: "", //活动标题
        photo: "", //照片
        webUrl: "" //网页链接
      },
      text: "", //活动内容

      rules: {
        name: [
          { required: true, message: "请输入活动名称", trigger: "blur" },
          { message: "长度在 3 到 5 个字符", trigger: "blur" }
        ],
        region: [{ required: true, message: "请选择车型", trigger: "change" }],
        zongzhong: [{ required: true, message: "填写总重", trigger: "blur" }],
        zaizhong: [{ required: true, message: "填写载重", trigger: "blur" }]
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      value1: "",
      value2: ""
    };
  },

  activated() {
    /* 富文本分为修改和新增 */
    let flag = this.$route.query.flag; // flag=1; 新增；2 编辑
    this.typeFlag = this.$route.query.flag;

    // 获取富文本内容
    if (flag == 1) {
      //新增
      this.typeFlag = 1;
    } else if (flag == 2) {
      this.typeFlag = 2;
      var eventType = this.$route.query.activityType; //0 站内活动  1 网页活动

      if (eventType) {
        this.isDisplay = true;
      } else {
        this.isDisrplay = false;
      }
      //修改
      var beginDate = this.$route.query.beginDate;
      var endDate = this.$route.query.endDate;
      let content = this.$route.query.content; //获取内容
      this.text = content;
      this.ruleForm.title = this.$route.query.title; //标题
      this.value1 = [beginDate, endDate];
      this.eventType = this.$route.query.activityType;
      this.ruleForm.webUrl = this.$route.query.path;

      // this.ruleForm.photo = this.$route.query.activityImageUrl //图片地址
      if (this.$route.query.activityImageUrl) {
        this.fileList = [{
          url: this.$route.query.activityImageUrl
        }];
      }
      if (this.fileList.length >= 1) {
        this.hideUpload = true;
      }
    }
  },
  methods: {
    getAliyunData() {
      const ossApiUrl = "/base-center-server/sts/oss_signature"; // 获取oss签名的地址
      // 获取oss签名
      this.$http.get(ossApiUrl).then(res => {
        if (res.data.code == "200") {
          let data = res.data;
          this.Aliyun = data.data;
          this.Aliyun.region = OSS_REGION;
          // return data
        }
      });
    },
    /* 移除图片 */

    handleRemove(file, fileList) {
      // console.log(file, fileList);
      this.hideUpload = false;
    },

    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          alert("submit!");
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    /* 选择活动类型 */
    eventTypeFun(val) {
      if (this.eventType == "0") {
        this.isDisplay = true;
        this.activityType = 0;
      } else {
        this.isDisplay = false;
        this.activityType = 1;
      }
    },
    /* 上传图片 */
    ossUpload(param) {
      let file = param.file; // 文件的
      const tmpcnt = file.name.lastIndexOf(".");
      const exname = file.name.substring(tmpcnt + 1);
      const fileName =
        "/" +
        this.Aliyun.bucket +
        "/" +
        this.Aliyun.dir +
        this.random_string(12) +
        "." +
        exname;
      client(this.Aliyun)
        .put(fileName, file)
        .then(res => {
          if (res.res.status === 200) {
            // 上传
            let imgUrl = res.res.requestUrls[0];
            this.ruleForm.photo = imgUrl;
            this.$message.success("上传成功");
             this.hideUpload = true;
          } else {
            this.$message.error(res.res.message);
          }
        });
    },
        /* 随机的名字 */
    random_string(len) {
      len = len || 32;
      var chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
      var maxPos = chars.length;
      var pwd = "";
      for (var i = 0; i < len; i++) {
        pwd += chars.charAt(Math.floor(Math.random() * maxPos));
      }
      return pwd;
    },

    /* 提交信息 */
    commitInfo() {
      // console.log(this.value1, "value1-----");
      // console.log(this.radio, "radio-----");
      // console.log(this.eventType, "eventType-----");
      this.editorContent = this.text;
      console.log(this.editorContent, "-------");
      var beginDate;
      var endDate;
      let postData = {};
      //如果选择了时间
      console.log(this.value1);
      if (this.value1 == null || this.value1 == "") {
        beginDate = "";
        endDate = "";
      } else {
        beginDate = this.value1[0];
        endDate = this.value1[1];
      }

      if (this.eventType == 0) {
        //站内活动
        postData = {
          activityName: this.ruleForm.title,
          activityType: this.eventType, // 活动 类型
          beginDate: beginDate,
          endDate: endDate,
          isOnline: this.radio, //是否上线
          content: this.editorContent, //活动内容
          activityImageUrl: this.ruleForm.photo //活动图片
        };
        /* 校验 */
        if (this.ruleForm.title == "") {
          this.$message({
            type: "warning",
            message: "请填写活动标题"
          });
          return;
        }
        console.log(beginDate);
        if (beginDate == "" || endDate == "") {
          this.$message({
            type: "warning",
            message: "请选择日期"
          });
          return;
        }
        if (this.text == "") {
          this.$message({
            type: "warning",
            message: "请填写活动内容"
          });
          return;
        }
      } else if (this.eventType == 1) {
        postData = {
          activityName: this.ruleForm.title,
          activityType: this.eventType, // 活动 类型
          beginDate: beginDate,
          endDate: endDate,
          isOnline: this.radio, //是否上线
          activityImageUrl: this.ruleForm.photo, //活动图片
          path: this.ruleForm.webUrl
        };
        if (this.ruleForm.title == "") {
          this.$message({
            type: "warning",
            message: "请填写活动标题"
          });
          return;
        }
        if (beginDate == "" || endDate == "") {
          this.$message({
            type: "warning",
            message: "请选择日期"
          });
          return;
        }
        if (this.ruleForm.webUrl.trim() == "") {
          this.$message({
            type: "warning",
            message: "请写网页地址"
          });
          return;
        }
      }
      this.$http
        .post("admin-center-server/avtivity/addActivity", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "添加成功"
            });
            this.$router.go(-1);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 编辑信息 */
    editorInfo() {
      var postData = {};

      let beginDate = this.value1[0];
      let endDate = this.value1[1];
      console.log(this.activityType, "876543");
      if (this.eventType == 0) {
        postData = {
          id: this.$route.query.id,
          activityName: this.ruleForm.title,
          activityType: this.eventType, // 活动 类型
          beginDate: beginDate,
          endDate: endDate,
          isOnline: this.radio, //是否上线
          content: this.editorContent, //活动内容
          activityImageUrl: this.ruleForm.photo //活动图片
        };
      } else if (this.eventType == 1) {
        postData = {
          id: this.$route.query.id,
          activityName: this.ruleForm.title,
          activityType: this.eventType, // 活动 类型
          beginDate: beginDate,
          endDate: endDate,
          isOnline: this.radio, //是否上线
          activityImageUrl: this.ruleForm.photo, //活动图片
          path: this.ruleForm.webUrl
        };
      }
      console.log(postData, "postData-----=");
      this.$http
        .post("/admin-center-server/avtivity/updateActivity", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "编辑成功!"
            });
            this.$router.go(-1);
          } else {
            this.$message.warning(data.message);
          }
        });
    }
  },
  created() {
    this.getAliyunData();
  }
};
</script>
<style rel="stylesheet/scss" lang="scss">
.hide .el-upload--picture-card {
  display: none;
}
.wangEditor {
  display: none;
}
.addCar {
  background-color: #ffffff;
  .tip {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 40px 40px 0;
    font-size: 12px;
    em {
      margin-right: 5px;
    }
  }
  .inner-box {
    // margin-left: 10%;
    width: 70%;
    .upload-box {
      width: 100%;
      height: 100%;
      position: relative;
      .icon-XZ {
        width: 92px;
        height: 92px;
        margin: 0 auto;
        background: url("../../../../assets/xiazai.png") no-repeat;
        background-size: 100% 100%;
      }
      .icon-word {
        width: 100%;
        height: 20px;
        line-height: 20px;
        font-size: 10px;
        position: absolute;
        bottom: 25px;
        left: 0px;
        color: #cccccc;
      }
    }
  }
}
.w-e-menu {
  z-index: 2 !important;
}
.w-e-text-container {
  z-index: 1 !important;
}
.editor {
  margin-top: 30px;
}

.myQuillEditor {
  height: 400px;
}
.editCommit {
  margin: 100px;
}
</style>
