<template>
  <div class="app-container">
    <el-form
      ref="searchForm"
      :model="search"
      :inline="true"
      class="search"
      size="mini"
    >
      <el-form-item label="司机姓名：" prop="driverName">
        <el-input
          placeholder="请输入司机姓名"
          v-model="search.driverName"
        ></el-input>
      </el-form-item>
      <el-form-item label="手机号：" prop="mobile">
        <el-input
          placeholder="请输入司机手机号"
          v-model="search.mobile"
        ></el-input>
      </el-form-item>
      <el-form-item label="到期证件类型：" prop="type">
        <el-select v-model="search.type" placeholder="请选择">
          <el-option label="身份证" value="1"></el-option>
          <el-option label="驾驶证" value="2"></el-option>
          <el-option label="从业资格证" value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" @click="doSearch" type="primary">查询</el-button>
        <el-button size="mini" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="wrapper">
      <el-table
        :data="data"
        class="table"
        style="width: 100%"
        cell-class-name="table_cell_gray"
        header-cell-class-name="table_header_cell_gray"
      >
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column label="司机姓名" prop="driverName"></el-table-column>
        <el-table-column label="手机号" prop="mobile"></el-table-column>
        <el-table-column label="身份证号" prop="idCard"></el-table-column>
        <el-table-column label="身份证到期时间" prop="idCardExpireDate">
          <template slot-scope="scope">
           <span>{{scope.row.idCardExpireDate}}</span><span class="warning">{{getWaringDays(scope.row.idCardExpireDate)}}</span>
          </template>
        </el-table-column>
        <el-table-column label="驾驶证号" prop="drivingLicencesNumber"></el-table-column>
        <el-table-column label="驾驶证到期时间" prop="drivingLicensesExpireDate">
          <template slot-scope="scope">
           <span>{{scope.row.drivingLicensesExpireDate}}</span><span class="warning">{{getWaringDays(scope.row.drivingLicensesExpireDate)}}</span>
          </template>
        </el-table-column>
        <el-table-column label="从业资格证号" prop="employmentCert"></el-table-column>
        <el-table-column label="从业资格证到期时间" prop="employmentCertExpireDate">
          <template slot-scope="scope">
           <span>{{scope.row.employmentCertExpireDate}}</span><span class="warning">{{getWaringDays(scope.row.employmentCertExpireDate)}}</span>
          </template>
        </el-table-column>
        <el-table-column label="注册时间" prop="registrationDate"></el-table-column>

      </el-table>
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :total="total"
        :current-page.sync="page.pageNumber"
        @current-change="getList"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';

export default {
  data() {
    return {
      page: {
        pageSize: 10,
        pageNumber: 1,
      },
      isSearching: false,
      search: {
        driverName: "",
        mobile: "",
        type: "",
      },
      data: [],
      total: 0,
      warningDays: 30
    };
  },
  activated() {
    this.$get("/admin-center-server/base/getSetting", {group: "Time"}).then(
      res => {
        this.warningDays = res.find((item) => {
            return item.code == 'certEarlyWarnDays'
        }).value
        this.getList();
      },
      () => {
        this.getList()
      }
    )
  },
  methods: {
    dayjs,
    getList() {
      let params = {
        ...this.page
      };
      if (this.isSearching) {
        Object.assign(params, this.search);
      }
      this.$post("/admin-center-server/certificateWarning/queryDriverCertificateWarningPage", params)
        .then((res) => {
          this.data = res.list;
          this.total = Number(res.total);
        })
        .catch(() => {
          this.data = [];
          this.total = 0;
        });
    },
    handleSizeChange(v) {
      this.page.pageSize = v;
      this.getList();
    },
    doSearch() {
      this.isSearching = true;
      this.page.pageNumber = 1;
      this.getList();
    },
    reset() {
      this.isSearching = false;
      this.$refs.searchForm.resetFields();
      this.search = {
        driverName: "",
        mobile: "",
        type: "",
      }
      this.page.pageNumber = 1;
      this.getList();
    },
    getWaringDays(date) {
      if(!date) {
        return
      }
      const currentDate = dayjs().format('YYYY-MM-DD')
      const cDate = dayjs(date)
      const days = cDate.diff(currentDate, 'day')
      if (days < 0) {
          return '(已到期)'
      } 
      if (days <= this.warningDays) {
          return `(${days}天后到期）`
      }
      return ''
    }
  },
};
</script>

<style scoped lang="scss">
.search {
  padding: 20px;
  background: #fff;
}
.wrapper {
  margin-top: 10px;
  background: #fff;
}
.el-pagination {
  margin: 10px 0;
  text-align: right;
}
.warning {
  color: red;
}
</style>