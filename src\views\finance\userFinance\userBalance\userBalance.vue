<template>
  <div class="app-container">
    <div class="search">
      <el-form
        size="mini"
        :inline="true"
        label-width="120px">
        <el-form-item label="月份">
          <el-date-picker
            v-model="searchForm.month"
            type="month"
            value-format="yyyy-MM"
            placeholder="请选择月份"
            :picker-options="pickerOptions"></el-date-picker>
        </el-form-item>
        <el-form-item label="货主名称:">
          <el-input placeholder="请输入客户名称" v-model="searchForm.name"></el-input>
        </el-form-item>
        <el-form-item label="合作平台主体:" prop="baseId" label-width="120px">
          <el-select
          v-model="searchForm.baseId"
          placeholder="请选择"
          >
            <el-option v-for="item in $store.state.user.baseInfo" :key="item.id" :value="item.id" :label="item.baseName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            icon="el-icon-search"
            size="mini"
            type="primary"
            @click="onSearch">查询</el-button>
          <el-button
            icon="el-icon-delete"
            size="mini"
            type="danger"
            @click="resetSearch">清空筛选</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="list">
      <div style="margin-bottom: 20px; text-align: right">
        <el-button @click="exportFile" type="primary" size="mini">导出Excel</el-button>
      </div>
      <el-table
        border
        :data="data"
        @selection-change="handleSelectionChange"
        >
        <!-- <el-table-column type="selection"></el-table-column> -->
        <el-table-column prop="name" label="货主名称">
          <template slot-scope="scope">
            <el-button type="text" @click="$router.push(`/consignorAccount/accountDetails?id=${scope.row.ownerId}`)">{{ scope.row.name }}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="mobile" label="手机号"></el-table-column>
        <el-table-column prop="baseName" label="合作平台主体"></el-table-column>
        <el-table-column prop="month" label="月份"></el-table-column>
        <el-table-column prop="startAmount" label="期初余额（元）"></el-table-column>
        <el-table-column prop="rechargeTotalAmount" label="本期净充值额（元）"></el-table-column>
        <el-table-column prop="billTotalAmount" label="本期开票额（元）"></el-table-column>
        <el-table-column prop="endAmount" label="期末余额（元）"></el-table-column>
      </el-table>
      <div class="pagin">
        <el-pagination
          :total="total"
          :current-page.sync="page.pageNumber"
          :page-size="page.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="getList"
          ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
let nowDate = new Date()
let month = nowDate.getMonth() + 1
if (month < 10) month = '0' + String(month)
let dateStr = `${nowDate.getFullYear()}-${month}`
export default {
  data() {
    return {
      searchForm: {
        month: dateStr
      },
      page: {
        pageSize: 20,
        pageNumber: 1
      },
      exportData:{},
      total: 0,
      data: [],
      selectedItems: [],
      pickerOptions: {
        disabledDate(date) {
          if (date.getFullYear() < 2023) {
            return true
          }
          return false
        }
      }
    }
  },
  methods: {
    onSearch() {
      this.page.pageNumber = 1
      this.getList()
    },
    resetSearch() {
      this.searchForm = {}
      this.page.pageNumber = 1
      this.getList()
    },
    handleSizeChange(val) {
      this.page.pageSize = val
      this.page.pageNumber = 1
      this.getList()
    },
    getList() {
      let params = {
        ...this.searchForm,
        ...this.page
      }
      if(!params.month){
        params.month = ''
      }
      this.$post('/admin-center-server/userAccountCheck/getUserAccountCheckPage', params)
        .then(res => {
          this.exportData = {
            ...this.searchForm
          }
          if(!this.exportData.month){
            this.exportData.month = ''
          }
          this.data = res.list
          this.total = Number(res.total)
        })
    },
    handleSelectionChange(v) {
      this.selectedItems = v
    },
    exportFile() {
      // if (this.selectedItems.length === 0) {
      //   this.$message.warning('至少选择一条')
      // }
      let params = this.exportData
      this.$post('/admin-center-server/userAccountCheck/exportUserAccountCheck', params)
        .then(res => {
          this.$message.success('正在导出，稍后您可在【导出任务】中查看导出进度及导出文件')
        })
    }
  },
  activated() {
    this.getList()
  }
}
</script>

<style scoped lang="scss">
  .search,
  .list {
    padding: 30px 0;
    background: #fff;
  }
  .list {
    margin-top: 20px;
    padding: 20px 10px;
  }
  .pagin {
    margin-top: 10px;
    text-align: right;
  }
</style>