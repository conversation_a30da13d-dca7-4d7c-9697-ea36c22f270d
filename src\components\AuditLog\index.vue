<template>
  <div class="list-box">
    <el-table
      :data="listData"
      style="width: 100%"
      ref="table"
      :cell-style="{ 'text-align': 'center', border: '0.5px solid #EAF0FB' }"
      :header-cell-style="{
        'text-align': 'center',
        border: '0.3px solid #EAF0FB',
        'background-color': '#F5F6F9',
        height: '60px',
      }"
    >
      <el-table-column label="审核日期" prop="createdDate"></el-table-column>
      <el-table-column label="审核状态" prop="authStatusStr"></el-table-column>
      <el-table-column label="操作人" prop="operator"></el-table-column>
      <el-table-column label="审核备注" prop="rejectReason"></el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
    props: ['listData']
};
</script>

<style lang="scss" scoped>
  .list-box {
    margin: 0px 10px;
    background-color: white;
      padding-top: 20px;
      min-height: 400px;
  }
</style>