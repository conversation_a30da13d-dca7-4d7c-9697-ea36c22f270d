<template>
    <div class="news-page">
        <div class="news-box">
                        <div class="news-title">
                            产品消息
                        </div>
                        <div class="news-main" v-for="news in newsList" :key="news.id">
                            <div class="new-item">
                                <div class="item-left">
                                    <img :src="news.sysMessageUrl" alt="">
                                </div>
                                <div class="item-right">
                                    <div class="item-right-title">
                                        {{news.sysTitle}}
                                        <span style="display: block;float: right;margin-right: 10px;color:red;cursor: pointer"
                                              @click="goDetail(news.id)">查看详情 》</span>
                                    </div>
                                    <div class="item-info">
                                        <div>{{news.sysMessageBriefly}}</div>
                                    </div>
                                    <div class="item-time">
                                        {{news.createTime}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
    </div>
</template>
<script>
    export default {
        data(){
            return{
                active:'',
                newsList:[],
            }
        },

        methods: {
            getNewsData(){
                this.$http.get('/admin-center-server/sys/getSysMessages',{
                    params:{
                        pageNumber:1,
                        pageSize:1000,
                        sysMessageType:0
                    }
                }).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                        this.newsList = data.data.list
                    }
                })
            },
            goDetail(id){
                sessionStorage.setItem('cur_new_detail',id);
                this.$router.push('messageDeatil')
            }
        },
        mounted() {
            this.getNewsData();
        },
    }
</script>

<style lang="scss" scoped>
    .news-page{
        width: 100%;
        background-color: #ffffff;
        .news-box{
            width: 100%;
            .news-title{
                height: 40px;
                background: #E9E9E9;
                line-height: 40px;
                font-size: 14px;
                padding-left: 20px;
            }
            .news-main{
                width: 100%;
                padding: 10px;
                .new-item{
                    width: 100%;
                    height: 200px;
                    border: 1px solid #E9E9E9;
                    margin-top: 20px;
                    overflow: hidden;
                    padding: 10px;
                    .item-left{
                        width: 180px;
                        height: 180px;
                        background: #33aef0;
                        float: left;
                        border: 1px solid #999999;
                        img{
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .item-right{
                        width: 100%;
                        height: 180px;
                        /*border: 1px solid #33aef0;*/
                        .item-right-title{
                            height: 30px;
                            line-height: 30px;
                            border-bottom: 1px solid #cccccc;
                            font-size: 12px;
                            padding-left: 190px;
                        }
                        .item-info{
                            padding: 10px;
                            height: 120px;
                            /*border: 1px solid #000000;*/
                            overflow: hidden;

                        }
                        .item-time{
                            height: 30px;
                            line-height: 30px;
                            text-align: right;
                            padding-right: 10px;
                        }
                    }
                }
            }
        }
    }
</style>
