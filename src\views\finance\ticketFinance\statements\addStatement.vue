/**
新增结算单
**/
<template>
    <div class="app-container detailedList">
        <div class="select-box" style="overflow: hidden">
            <div class="top-title">结算单新增查询</div>
            <div class="select-info">
                <el-form
                        size="mini"
                        :inline="true"
                        :model="outFormData"
                        class="demo-form-inline"
                        label-width="100px"
                >
                    <el-form-item label="订单号:">
                        <el-input v-model="outFormData.sn" :οnkeyup="outFormData.sn=outFormData.sn.replace(/\s/g, '')" placeholder="请输入订单编号"></el-input>
                    </el-form-item>
                    <el-form-item label="日期筛选:">
                        <el-date-picker
                                :clearable="false"
                                @blur="selectTime"
                                v-model="outFormData.date"
                                format="yyyy 年 MM 月 dd 日"
                                value-format="yyyy-MM-dd"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button icon="el-icon-search" size="mini" type="primary" @click="onSubmit">查询</el-button>
                        <el-button icon="el-icon-delete" size="mini" type="danger" @click="clearchSubmit">清空筛选
                        </el-button>
                    </el-form-item>
                </el-form>
                <el-form
                        size="mini"
                        :inline="true"
                        :model="outFormData"
                        class="demo-form-inline"
                        label-width="100px"
                >
                    <el-form-item label="装车点:">
                        <el-input v-model="outFormData.deliveryAddressesName" :οnkeyup="outFormData.deliveryAddressesName=outFormData.deliveryAddressesName.replace(/\s/g, '')" placeholder="请输入装车点"></el-input>
                    </el-form-item>
                    <el-form-item label="卸货点:">
                        <el-input v-model="outFormData.receiveAddressesName" :οnkeyup="outFormData.receiveAddressesName=outFormData.receiveAddressesName.replace(/\s/g, '')" placeholder="请输入卸货点"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <div class="curTotalInfo" style="float: left">结算单号：{{finalStatementSn}} 当前搜索结果总计：开票吨数 <span
                    style="color: red">{{allInvoiceTon}}</span> 吨；开票金额 <span
                    style="color: red">{{allInvoiceAmout}}</span> 元 选择计数：开票吨数 <span
                    style="color: blue">{{waitPiao}}</span> 吨；开票金额 <span style="color: blue">{{waitMoney}}</span> 元
            </div>
            <div style="float: right;margin-right: 10px;margin-bottom: 10px">
                <el-button icon="el-icon-info" :loading="loading" type="success" size="mini" @click="getTicketInfo">
                    开票信息
                </el-button>
                <el-button icon="el-icon-s-order" :loading="loading" type="primary" size="mini" @click="createListFn">
                    生成结算单
                </el-button>
            </div>
        </div>
        <div class="list-box">
            <el-row>
                <div class="list-right">
                    <div class="right-title">数据列表</div>
                    <div class="list-main">
                        <template>
                            <el-table
                                    :data="tableData"
                                    border
                                    style="width: 100%"
                                    @selection-change="handleSelectionChange"
                            >
                                <el-table-column type="selection" width="55"></el-table-column>
                                <el-table-column label="序号" type="index" width="50"></el-table-column>
                                <el-table-column show-overflow-tooltip prop="sn" label="订单号"
                                                 width="220">
                                    <template slot-scope="scope">
                                        <span @click="goDdDetail(scope.row)" style="color: blue;cursor: pointer">{{scope.row.sn}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="cargoType" label="货物类型" width="100"></el-table-column>
                                <el-table-column prop="openTickTon" label="待开票吨数(吨)"
                                                 :formatter="isNullKaipiao"
                                                 width="150"></el-table-column>
                                <el-table-column prop="openTickCount" label="待开票运单"
                                                 width="100"></el-table-column>
                                <el-table-column prop="openTickAmount"
                                                 :formatter="isNullMoney"
                                                 label="待开票金额(元)" width="150"></el-table-column>
                                <el-table-column show-overflow-tooltip prop="deliveryAddressesName" label="订单发货地址"
                                ></el-table-column>
                                <el-table-column show-overflow-tooltip prop="receiveAddressesName" label="订单收货地址"
                                ></el-table-column>
                                <el-table-column prop="createdDate" label="创建日期" width="200"></el-table-column>
                                <el-table-column fixed="right" label="操作" width="100">
                                    <template slot-scope="scope">
                                        <el-button type="text" style="color: #00cb8a" size="small"
                                                   @click="editRow(scope.row)">编辑运单
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </div>
                </div>
            </el-row>
        </div>
        <!-- 拣选订单-->
        <el-dialog title="拣选订单" :visible.sync="dialogVisible" width="85%" :before-close="handleClose">
            <div style="overflow: hidden">
                <div>
                    <el-form size=mini :inline="true" :model="innerFormData" class="demo-form-inline">
                        <el-form-item label="">
                            <el-input v-model="innerFormData.sn" :οnkeyup="innerFormData.sn=innerFormData.sn.replace(/\s/g, '')" placeholder="请输入运单号"></el-input>
                        </el-form-item>
                        <el-form-item label="">
                            <el-input v-model="innerFormData.driverName" :οnkeyup="innerFormData.driverName=innerFormData.driverName.replace(/\s/g, '')" placeholder="请输入司机"></el-input>
                        </el-form-item>
                        <el-form-item label="运单状态:">
                            <el-select v-model="innerFormData.type" placeholder="筛选运单状态">
                                <el-option label="待付款" value="0"></el-option>
                                <el-option label="已结算" value="1"></el-option>
                                <el-option label="结算中" value="2"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="onSubmitMask">查询</el-button>
                            <el-button icon="el-icon-delete" size="mini" type="danger" @click="clearchSubmitInner">清空筛选
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
                <div style="margin-bottom: 10px">
                    当前搜索结果总计：开票吨数 <span
                        style="color: red">{{allInvoiceTonInner}}</span> 吨；开票金额 <span
                        style="color: red">{{allInvoiceAmoutInner}}</span> 元 选择计数：开票吨数 <span
                        style="color: blue">{{waitPiaoInner}}</span> 吨；开票金额 <span
                        style="color: blue">{{waitMoneyInner}}</span> 元
                </div>
                <div>
                    <template>
                        <el-table
                                ref="maskTable"
                                row-key="id"
                                :row-key="getRowKey"
                                :data="maskTableData"
                                border
                                style="width: 100%"
                                @selection-change="innerSelectionChange"
                        >
                            <el-table-column
                                    type="selection"
                                    :reserve-selection="true"
                                    width="55">
                            </el-table-column>
                            <el-table-column
                                    type=index
                                    label="序号"
                                    width="50">
                            </el-table-column>
                            <el-table-column
                                    prop="sn"
                                    label="运单号"
                                    width="220">
                                <template slot-scope="scope">
                                    <span @click="goYdDetail(scope.row)" style="color: blue;cursor: pointer">{{scope.row.sn}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                    prop="freight"
                                    label="运费单价(元)"
                                    width="180">
                            </el-table-column>
                            <el-table-column
                                    prop="paymentTon"
                                    width="120"
                                    label="结算吨数(吨)">
                            </el-table-column>
                            <el-table-column
                                    prop="amount"
                                    width="120"
                                    label="结算金额(元)">
                            </el-table-column>
                            <el-table-column
                                    prop="driverName"
                                    width="200"
                                    label="司机">
                            </el-table-column>
                            <el-table-column
                                    prop="driverMobile"
                                    width="150"
                                    label="电话">
                            </el-table-column>
                            <el-table-column
                                    prop="plateNumber"
                                    width="120"
                                    label="车牌号">
                            </el-table-column>
                            <el-table-column
                                    prop="lastModifiedDate"
                                    width="180"
                                    label="结算时间">
                            </el-table-column>
                            <el-table-column
                                    fixed="right"
                                    label="操作"
                                    width="200"
                            >
                                <template slot-scope="scope">
                                    <el-button
                                            :disabled="canMark"
                                            v-if="scope.row.nonPayment==='1'" type="text" size="small"
                                            @click="changeInnerRow(scope.row)">标记为不可结算
                                    </el-button>
                                    <el-button
                                            :disabled="canMark"
                                            style="color: #00cb8a" v-if="scope.row.nonPayment==='2'" type="text"
                                            size="small" @click="changeInnerRow(scope.row)">标记为可结算
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </template>
                </div>
                <div style="float: left;margin-top: 20px">
                    <!--                    <el-button type="primary" size="mini" @click="electionReverse">反选</el-button>-->
                    <el-button type="primary" size="mini" @click="ascOrDesc(maskTableData)">全选/反选</el-button>
                    <el-button type="danger" size="mini" @click="clearSelect">清空全部选中</el-button>
                    <el-button type="success" size="mini" @click="markerOnekey">一键标记</el-button>
                </div>
                <div style="float: right;margin-top: 20px">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 30, 40]"
                            :page-size="maskPageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="maskTotal">
                    </el-pagination>
                </div>
            </div>
        </el-dialog>
        <!--开票信息-->
        <el-dialog
                title="开票信息"
                :visible.sync="listInfoMask"
                :before-close="handleClose">
            <div class="ticket-form">
                <div style="color: black;font-weight: 600">发票详情</div>
                <el-form ref="form" :model="formMask" label-width="120px" size="mini" style="margin-top: 10px">
                    <el-form-item label="抬头类型:" required>
                        {{formMask.headType}}
                    </el-form-item>
                    <el-form-item label="发票抬头:" required>
                        {{formMask.headName}}
                    </el-form-item>
                    <el-form-item label="税号:" required>
                        <el-input :disabled="tax" v-model="formMask.dutyNum" :οnkeyup="formMask.dutyNum=formMask.dutyNum.replace(/\s+/g,'')" style="width: 300px"></el-input>
                        <el-button style="margin-left: 20px;color: #00cb8a;" type="text" @click="tax=false">修改</el-button>
                    </el-form-item>
                </el-form>
                <div style="color: black;font-weight: 600;margin-top: 20px">更多内容</div>
                <el-form ref="form" :model="formMask" label-width="120px" size="mini" style="margin-top: 10px">
                    <el-form-item label="地址和电话:" required>
                        <el-input v-model="formMask.addressPhone" style="width: 300px"></el-input>
                    </el-form-item>
                    <el-form-item label="开户行和账号:" required>
                        <el-input v-model="formMask.bankAccount" style="width: 300px"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="changeTicketInfo">确 定</el-button>
  </span>
        </el-dialog>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                tax:true,
                formMask: {
                    headType:'',
                    headName:'',
                    dutyNum:'',
                    addressPhone:'',
                    bankAccount:'',
                },
                listInfoMask: false,
                loading: false,
                canMark: false,
                allInvoiceAmout: '', //合计可开票金额
                allInvoiceTon: '', //合计可开票吨数
                waitPiao: 0, //用户选择 开票吨数
                waitMoney: 0, //用户选择 开票金额


                allInvoiceAmoutInner: '', //合计可开票金额
                allInvoiceTonInner: '', //合计可开票吨数
                waitPiaoInner: 0, //用户选择 开票吨数
                waitMoneyInner: 0, //用户选择 开票金额

                editInnerId: '',
                finalStatementSn: "",
                dialogVisible: false,
                dialogChange: false,
                formInline: {
                    user: "",
                    region: "",
                    date: ""
                },
                tableData: [],
                maskTableData: [
                    {
                        date: '1',
                        name: '1',
                        address: '1'
                    }
                ],
                maskTotal: 1,
                currentPage: 1,
                maskPageSize: 10,

                multipleSelection: [],
                innerSelectionRow: [],
                outFormData: {
                    sn: '',
                    startDate: '',
                    endDate: '',
                    date: '',
                    deliveryAddressesName: '',
                    receiveAddressesName: '',
                },
                innerFormData: {
                    sn: '',
                    driverName: '',
                    type: '',
                },
                ids: [],
                idsInner: [],
                allSelect: false,
            };
        },
        methods: {
            /** 点击订单号 跳转至订单详情 **/
            goDdDetail(row) {
                this.$router.push({
                    name: "OrderDetail",
                    query: {
                        orderBusinessId: row.businessOrderId,
                    }
                });
            },
            /** 点击运单号 跳转至运单详情 **/
            goYdDetail(row) {
                let id = row.orderItemId;
                this.dialogVisible=false;
                this.$router.push({
                    name: "TransportListDetail",
                    query: {
                        orderItemId: id,
                    }
                });
            },
            /** 主表 可结算运单变成null **/
            isNullKaipiao(row) {
                if (row.openTickTon === null) {
                    return '0'
                } else {
                    return row.openTickTon
                }
            },
            isNullMoney(row) {
                if (row.openTickAmount === null) {
                    return '0'
                } else {
                    return row.openTickAmount
                }
            },
            /** 解决浮点数计算 **/
            times(num1, num2) {
                const num1String = num1.toString();
                const num2String = num2.toString();
                const num1Digits = (num1String.split('.')[1] || '').length;
                const num2Digits = (num2String.split('.')[1] || '').length;
                const baseNum = Math.pow(10, num1Digits + num2Digits);
                return Number(num1String.replace('.', '')) * Number(num2String.replace('.', '')) / baseNum

            },
            plus(num1, num2) {
                const num1Digits = (num1.toString().split('.')[1] || '').length;
                const num2Digits = (num2.toString().split('.')[1] || '').length;
                const baseNum = Math.pow(10, Math.max(num1Digits, num2Digits));
                return (this.times(num1, baseNum) + this.times(num2, baseNum)) / baseNum
            },
            /** 多选  计算开票总额和总吨数**/
            handleSelectionChange(val) {
                let ids = [];
                this.multipleSelection = val;
                this.waitPiao = 0;
                this.waitMoney = 0;
                val.map((item, index) => {
                    if (item.openTickTon === null) {
                        item.openTickTon = 0;
                        this.waitPiao = this.plus(this.waitPiao, item.openTickTon);
                        ids.push(item.id)
                    } else if (item.openTickAmount === null) {
                        item.openTickAmount = 0;
                        this.waitMoney = this.plus(this.waitMoney, item.openTickAmount);
                        ids.push(item.id)
                    } else {
                        this.waitPiao = this.plus(this.waitPiao, item.openTickTon);
                        this.waitMoney = this.plus(this.waitMoney, item.openTickAmount);
                        ids.push(item.id)
                    }
                });
                this.ids = ids;
            },
            /** 生成结算单 **/
            createListFn() {
                let list = this.multipleSelection;
                if (list.length < 1) {
                    this.$message.warning('至少选择一条运单')
                } else {
                    this.loading = true;
                    let ids = this.ids;
                    let finalStatementId = sessionStorage.getItem('finalStatementId');
                    this.$http.post('/admin-center-server/app/invoice_business_order/edit_sum', {
                        finalStatementId: finalStatementId,
                        ids: ids
                    }).then(res => {
                        let data = res.data;
                        if (data.code === '200') {
                            this.$message.success('正在生成结算单');
                            setTimeout(() => {
                                this.loading = false;
                                this.$router.push('statements')
                            }, 2000)
                        } else if (data.code === '422') {
                            this.loading = false;
                            this.$message.warning(data.message)
                        } else if (data.code === '500') {
                            this.loading = false;
                            this.$message.warning('请选择有效运单')
                        } else {
                            this.loading = false;
                            this.$message.warning(data.message)
                        }
                    });

                }
            },
            /** 弹窗 上叉号关闭**/
            handleClose(done) {
                this.dialogVisible = false;
                this.dialogChange = false;
                this.listInfoMask = false;
                this.tax = true;
                this.getDataList();
            },
            /** 编辑运单 **/
            editRow(row) {
                let id = row.id;
                this.editInnerId = id;
                this.MaskTabDataRefresh()
            },
            /** 被编辑的运单 打开弹窗后的分页 **/
            handleSizeChange(val) {
                this.maskPageSize = val;
                this.currentPage = 1;
                this.MaskTabDataRefresh()
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.MaskTabDataRefresh()
            },
            /** 内层表 标记是否结算 (单个) **/
            changeInnerRow(row) {
                this.canMark = true;
                let type = row.nonPayment; //是否结算的状态
                if (type === '1') {
                    let str = [];
                    let obj = {
                        invoiceItemId: row.id,
                        nonPayment: 2,
                    };
                    str.push(obj);
                    this.$http.post('/admin-center-server/app/invoice_order_item/operatePay', str).then(res => {
                        let data = res.data;
                        if (data.code === '200') {
                            this.MaskTabDataRefresh();
                            this.$message.success(data.message);
                            this.canMark = false;
                        } else {
                            this.$message.warning(data.message);
                            this.canMark = false;
                        }
                    })
                } else if (type === '2') {
                    this.canMark = true;
                    let str = [];
                    let obj = {
                        invoiceItemId: row.id,
                        nonPayment: 1,
                    };
                    str.push(obj);
                    this.$http.post('/admin-center-server/app/invoice_order_item/operatePay', str).then(res => {
                        let data = res.data;
                        if (data.code === '200') {
                            this.MaskTabDataRefresh();
                            this.$message.success(data.message);
                            this.canMark = false;
                        } else {
                            this.$message.warning(data.message);
                            this.canMark = false;
                        }
                    })
                }
            },
            /** 内层表格按条件查询 **/
            onSubmitMask() {
                this.currentPage = 1;
                this.MaskTabDataRefresh()
            },
            /** 内层表格全选反选操作 **/
            ascOrDesc(rows) {
                if (rows) {
                    rows.forEach(row => {
                        this.$refs.maskTable.toggleAllSelection(row, !this.allSelect)
                    });
                    this.allSelect = !this.allSelect
                }
            },
            /** 清空全部选中 **/
            clearSelect() {
                this.$refs.maskTable.clearSelection()
            },
            /** 记住选中状态 **/
            getRowKey(row) {
                return row.id
            },
            /** 内层表格多选 计算选择开票数和开票金额**/
            innerSelectionChange(val) {
                let idsInner = [];
                this.innerSelectionRow = val;
                this.waitPiaoInner = 0;
                this.waitMoneyInner = 0;
                val.map((item, index) => {
                    this.waitPiaoInner = this.plus(this.waitPiaoInner, item.paymentTon);
                    this.waitMoneyInner = this.plus(this.waitMoneyInner, item.amount);
                    idsInner.push(item.id)
                });
                this.idsInner = idsInner;
            },
            /** 一键标记 **/
            markerOnekey() {
                if (this.innerSelectionRow.length < 1) {
                    this.$message.warning('至少选择一条运单')
                } else {
                    this.canMark = true;
                    let flag = this.innerSelectionRow[0].nonPayment;
                    let isGo = true;
                    for (let i = 0; i < this.innerSelectionRow.length; i++) {
                        let curItem = this.innerSelectionRow[i];
                        if (curItem.nonPayment !== flag) {
                            isGo = false;
                            break
                        }
                    }
                    if (isGo == false) {
                        this.$message.warning('所选中有不一致的状态');
                        this.canMark = false;
                    } else {
                        let type = this.innerSelectionRow[0].nonPayment;
                        if (type === '1') {
                            let str = [];
                            this.innerSelectionRow.map((item, index) => {
                                let obj = {
                                    invoiceItemId: item.id,
                                    nonPayment: 2,
                                };
                                str.push(obj)
                            });
                            this.$http.post('/admin-center-server/app/invoice_order_item/operatePay', str).then(res => {
                                let data = res.data;
                                if (data.code === '200') {
                                    this.MaskTabDataRefresh();
                                    this.$refs.maskTable.clearSelection();
                                    this.$message.success(data.message);
                                    this.canMark = false;
                                } else {
                                    this.$message.warning(data.message);
                                    this.canMark = false;
                                }
                            })
                        } else if (type === '2') {
                            let str = [];
                            this.innerSelectionRow.map((item, index) => {
                                let obj = {
                                    invoiceItemId: item.id,
                                    nonPayment: 1,
                                };
                                str.push(obj)
                            });
                            this.$http.post('/admin-center-server/app/invoice_order_item/operatePay', str).then(res => {
                                let data = res.data;
                                if (data.code === '200') {
                                    this.MaskTabDataRefresh();
                                    this.$refs.maskTable.clearSelection();
                                    this.$message.success(data.message);
                                    this.canMark = false;
                                } else {
                                    this.$message.warning(data.message);
                                    this.canMark = false;
                                }
                            })
                        }
                    }

                }
            },

            /** 标记完后 需要刷新弹窗表格 **/
            MaskTabDataRefresh() {
                let id = this.editInnerId;
                this.$http.get('/admin-center-server/app/invoice_order_item/edit_list', {
                    params: {
                        invoiceBusinessOrderId: id,
                        pageNumber: this.currentPage,
                        pageSize: this.maskPageSize,
                        sn: this.innerFormData.sn,
                        driverName: this.innerFormData.driverName,
                        type: this.innerFormData.type,
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.dialogVisible = true;
                        this.allInvoiceTonInner = data.data.sumTon;
                        this.allInvoiceAmoutInner = data.data.sumAmount;
                        this.maskTableData = data.data.pageInfo.list;
                        this.maskTotal = Number(data.data.pageInfo.total);
                    } else {
                        this.$message.error(data.message)
                    }
                });
            },


            /** 根据时间态搜索 **/
            selectTime() {
                let startTime = this.outFormData.date[0];
                let endTime = this.outFormData.date[1];
                this.outFormData.startDate = startTime;
                this.outFormData.endDate = endTime;
            },
            /** 按条件查询 **/
            onSubmit() {
                this.getDataList()
            },
            /** 清空 外层表格搜索条件 **/
            clearchSubmit() {
                this.outFormData = {
                    sn: '',
                    startDate: '',
                    endDate: '',
                    date: '',
                    deliveryAddressesName: '',
                    receiveAddressesName: '',
                };
                this.waitPiao = 0;
                this.waitMoney = 0;
                this.getDataList()
            },
            /** 内层表格 筛选条件清空 刷新表格 **/
            clearchSubmitInner() {
                this.innerFormData = {
                    sn: '',
                    driverName: '',
                    type: '',
                };
                this.MaskTabDataRefresh();
            },
            /** 获取数据列表 **/
            getDataList() {
                let finalStatementId = sessionStorage.getItem("finalStatementId");
                let finalStatementSn = sessionStorage.getItem("finalStatementSn");
                this.finalStatementSn = finalStatementSn;
                let sn = this.outFormData.sn;
                let startDate = this.outFormData.startDate;
                let endDate = this.outFormData.endDate;
                let deliveryAddressesName = this.outFormData.deliveryAddressesName;
                let receiveAddressesName = this.outFormData.receiveAddressesName;
                this.$http
                    .get("/admin-center-server/app/invoice_business_order/edit_list", {
                        params: {
                            finalStatementId: finalStatementId,
                            deliveryAddressesName: deliveryAddressesName,
                            receiveAddressesName: receiveAddressesName,
                            sn: sn,
                            startDate: startDate,
                            endDate: endDate,
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.tableData = data.data.list;
                            this.allInvoiceAmout = data.data.allInvoiceAmout;
                            this.allInvoiceTon = data.data.allInvoiceTon;
                        }
                    });
            },
            /** 获取开票信息 **/
            getTicketInfo(){
                let userId = this.$route.query.id;
                this.$http.get('/admin-center-server/bill/getBillInfo',{
                    params:{
                        userId:userId
                    }
                }).then(res=>{
                    let data = res.data;
                    this.listInfoMask=true;
                    if(data.code==='200'){
                        this.formMask=data.data;
                    }else {
                        this.$message.warning(data.message)
                    }
                })
            },
            /** 修改开票信息 **/
            changeTicketInfo(){
                let form = this.formMask;
                if(this.formMask.dutyNum===''){
                    this.$message.warning('请填写税号')
                }else {
                    this.$http.post('/admin-center-server/bill/create',form).then(res=>{
                        let data = res.data;
                        if(data.code==='200'){
                            this.$message.success(data.message);
                            this.tax=true;
                            this.listInfoMask=false;
                        }else {
                            this.$message.warning(data.message);
                            this.tax=true;
                        }
                    })
                }

            },
        },
        activated() {
            this.getDataList();
        }
    };
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .tip {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 40px 40px 0;
        font-size: 12px;

        em {
            margin-right: 5px;
        }
    }

    .inner-box {
        margin-left: 10%;
        width: 70%;

        .upload-box {
            width: 100%;
            height: 100%;
            position: relative;

            .icon-XZ {
                width: 92px;
                height: 92px;
                margin: 0 auto;
                background-size: 100% 100%;
            }

            .icon-word {
                width: 100%;
                height: 20px;
                line-height: 20px;
                font-size: 10px;
                position: absolute;
                bottom: 25px;
                left: 0px;
                color: #cccccc;
            }
        }
    }

    .detailedList {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
            }

            .select-info {
                padding-top: 20px;
            }

            .curTotalInfo {
                padding-left: 20px;
                height: 30px;
                line-height: 30px;
                font-size: 12px;
                color: #999999;
            }
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;
            overflow: hidden;

            .list-right {
                padding-left: 10px;

                .right-title {
                    font-size: 14;
                    font-weight: 700;
                }

                .list-main {
                    width: 100%;
                    border: 1px solid #cccccc;
                    margin-top: 10px;
                }

                .paging {
                    margin-top: 10px;
                    float: right;
                }
            }
        }
    }
</style>
