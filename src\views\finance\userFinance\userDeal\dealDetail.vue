<template>
  <div class="app-container dealDetail">
    <div class="deal-top">
      <div class="deal-sn">交易流水号：{{item.transactionSn}}</div> 
      <el-button @click="$router.go(-1)">返回</el-button>
    </div>
    <div class="deal-info">
      <detail-row>
        <detail-col :span="8" label="交易金额（元）">{{item.amount}}</detail-col>
        <detail-col :span="8" label="当前余额（元）">{{accountBalance}}</detail-col>
        <detail-col :span="8" label="交易类型">{{item.type}}</detail-col>

        <detail-col :span="8" label="收支类型">{{item.flag == 1 ? '收入' : '支出'}}</detail-col>
        <detail-col :span="16" label="备注">{{item.mark}}</detail-col>

        <detail-col :span="8" label="付款人">{{item.payer}}</detail-col>
        <detail-col :span="8" label="付款账户">{{item.payerAccount}}</detail-col>
        <detail-col :span="8" label="开户行">{{item.payerBank}}</detail-col>

        <detail-col :span="8" label="收款人">{{item.payee}}</detail-col>
        <detail-col :span="8" label="收款账户">{{item.payeeAccount}}</detail-col>
        <detail-col :span="8" label="开户行">{{item.payeeBank}}</detail-col>
      </detail-row>
    </div>
    <div class="deal-list">
        <el-table
            :data="detailData"
            border
            style="width: 100%"
            class="table"
            cell-class-name="table_cell_gray"
            header-cell-class-name="table_header_cell_gray_height"
          >
            <el-table-column fixed type="index" label="序号" width="50"></el-table-column>
            <el-table-column fixed  prop="orderItemSn" label="运单号">
              <template slot-scope="scope">
                <el-button @click="openWaybillDetail(scope.row)" type="text" size="small">{{scope.row.orderItemSn}}</el-button>
              </template>
            </el-table-column>
            <el-table-column  prop="plateNumber" label="承运车辆"></el-table-column>
            <el-table-column  prop="driverName" label="承运司机"></el-table-column>
            <el-table-column  prop="amount" label="交易金额"></el-table-column>
            <el-table-column  prop="projectName" label="所属项目"></el-table-column>
            <el-table-column  prop="orderItemTime" label="接单时间"></el-table-column>
          </el-table>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      item: {},
      detailData: [],
      accountBalance: ''
    }
  },
  mounted() {
    let item = this.$route.query
    let accountType = item.accountType

    this.item = {
      transactionSn: item.transactionSn,
      amount: accountType == 2 ? item.transactionAmount : item.amount,
      type: accountType == 2 ? item.transactionTypeValue : item.typeStr,
      date: accountType == 2 ? item.transactionTime : item.time,
      flag: accountType == 2 ? item.inOrOut : item.flag,
      mark: accountType == 2 ? item.remark : item.mark,
      payer: accountType == 2 ? item.payer : item.payerName,
      payerAccount: accountType == 2 ? item.paymentAccount : item.payerAccount,
      payerBank: accountType == 2 ? item.paymentAccountBank : item.payerBankName,
      payee: accountType == 2 ? item.payee : item.payeeName,
      payeeAccount: accountType == 2 ? item.receiptAccount : item.payeeAccount,
      payeeBank: accountType == 2 ? item.receiptAccountBank : item.payeeBankName,
    }
    let params = {
      accountType: accountType,
      flag: accountType == 2 ? item.inOrOut : item.flag,
      id: item.id,
      type: accountType == 2? item.transactionType : item.type,
      transactionSn: item.transactionSn
    }
    this.$get('/admin-center-server/transaction/flow/queryAccountTransactionDetails', {}, {params: params}).then(
      res => {
        this.detailData = res.accountTransactionOrderItemVOList || []
        this.accountBalance = res.accountBalance
      }
    )

  },
  methods: {
    openWaybillDetail(item) {
      this.$router.push('/transport/transportListDetail?orderItemId=' + item.id)

    }
  }
};  
</script>

<style lang="scss" scoped>
.deal-top {
  display: flex;
  justify-content: space-between;
}
.dealDetail {
  margin: 20px;
  height: 100%;
  background-color: white;
  .deal-sn::before {
    content: "";
    display: inline-block;
    width: 3px;
    height: 13px;
    background: #ed970f;
    border-radius: 1px;
    margin-right: 4px;
    margin-top: 5px;
  }
  .deal-info {
    margin-top: 20px;
  }
  .deal-list {
    margin-top: 20px;
  }
}
</style>