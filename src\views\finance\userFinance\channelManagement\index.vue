<template>
    <div class="app-container carsList">
        <template v-if="isShowList">
            <div class="select-box">
                <div class="top-title">查询</div>
                <div class="select-info">
                    <el-form
                        :model="queryParams"
                        class="demo-form-inline"
                        label-width="100px"
                        ref="searchForm"
                    >
                        <el-row class="global-div-search">
                            <el-col :xs="24" :sm="12" :md="8" :lg="6">
                                <el-form-item label="名称:" prop="channelName">
                                    <el-input
                                        clearable
                                        v-model="queryParams.channelName"
                                        placeholder="请输入名称"
                                        size="small"
                                        @keyup.enter.native="handleQuery"
                                    >
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="12" :md="8" :lg="6">
                                <el-form-item
                                    label="绑卡状态:"
                                    prop="bindCardStatus"
                                >
                                    <el-select
                                        clearable
                                        v-model="queryParams.bindCardStatus"
                                        placeholder="请选择绑卡状态"
                                        size="small"
                                        @keyup.enter.native="handleQuery"
                                    >
                                        <el-option
                                            v-for="(item, index) in typeList"
                                            :key="index"
                                            :label="item.label"
                                            :value="item.value"
                                        >
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-form-item label-width="20px">
                                <el-button
                                    type="primary"
                                    size="small"
                                    icon="el-icon-search"
                                    @click="handleQuery"
                                    >查 询</el-button
                                >
                                <el-button
                                    type="danger"
                                    size="small"
                                    icon="el-icon-delete"
                                    @click="resetQuery"
                                    >清 空 筛 选</el-button
                                >
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
            </div>
            <div class="list-box">
                <div class="list-title">
                    <div>账户列表</div>
                    <div>
                        <el-button type="primary" @click="handleOpenCard"
                            >开通平安账户</el-button
                        >
                    </div>
                </div>
                <div class="list-main">
                    <template>
                        <el-table
                            v-loading="loading"
                            :data="tableData"
                            border
                            style="width: 100%"
                            :max-height="tableHeight"
                            ref="table"
                            :cell-style="{
                                'text-align': 'center',
                            }"
                        >
                            <template v-for="(item, index) in tableColumn">
                                <el-table-column
                                    v-if="
                                        item.prop != 'operation' && !item.hidden
                                    "
                                    :key="index"
                                    :label="item.label"
                                    :prop="item.prop"
                                    :width="item.width"
                                >
                                    <template slot-scope="scope">
                                        {{ scope.row[item.prop] || "-" }}
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    v-if="
                                        item.prop == 'operation' && !item.hidden
                                    "
                                    :key="index"
                                    :label="item.label"
                                    :prop="item.prop"
                                    :width="item.width"
                                    fixed="right"
                                >
                                    <template slot-scope="scope">
                                        <el-button
                                            v-if="scope.row.bindCardStatus == 0"
                                            type="text"
                                            @click.stop="handleBindCard(scope.row)"
                                            >绑定银行卡</el-button
                                        >
                                        <el-button
                                            v-else
                                            type="text"
                                            @click.stop="handleDetail(scope.row)"
                                            >查 看</el-button
                                        >
                                        <el-button
                                            type="text"
                                            @click.stop="handleDiscount(scope.row)"
                                            >设置折扣</el-button
                                        >
                                    </template>
                                </el-table-column>
                            </template>
                        </el-table>
                        <div
                            class="page"
                            style="margin-top: 10px; text-align: right"
                        >
                            <el-pagination
                                @size-change="getList"
                                @current-change="getList"
                                :current-page.sync="queryParams.pageNumber"
                                :page-sizes="[20, 40, 60, 80, 100]"
                                :page-size.sync="queryParams.pageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="total"
                            ></el-pagination>
                        </div>
                    </template>
                </div>
            </div>
        </template>
        <template v-else>
            <formModel ref="formModel" @cancel="formCancel"></formModel>
        </template>
        <openCard
            ref="openCard"
            v-if="isOpenCard"
            @cancel="openCardCancel"
            @success="openCardSuccess"
        ></openCard>
        <discount
            ref="discount"
            v-if="isDiscount"
            @cancel="discountCancel"
            @success="discountSuccess"
        >
        </discount>
        <bindCard
            ref="bindCard"
            v-if="isBindCard"
            @cancel="bindCardCancel"
            @success="bindCardSuccess"
        ></bindCard>
    </div>
</template>
  
<script>
import formModel from "./formModel.vue";
import openCard from "./openCardModel.vue";
import discount from "./discountModel.vue";
import bindCard from "./bindCardModel.vue";
export default {
    components: { formModel, openCard, discount, bindCard },
    name: "channelManagement",
    data() {
        return {
            isOpenCard: false,
            isDiscount: false,
            isBindCard: false,
            queryParams: {
                channelName: undefined,
                bindCardStatus: undefined,
                pageNumber: 1,
                pageSize: 20,
            },
            loading: true,
            tableHeight: null, //表格的高度
            isShowList: true,
            tableData: [],
            tableColumn: [
                {
                    label: "名称",
                    prop: "channelName",
                    width: "",
                    hidden: false,
                },
                {
                    label: "开户人姓名",
                    prop: "name",
                    width: "",
                    hidden: false,
                },
                {
                    label: "开户人手机号",
                    prop: "mobile",
                    width: "",
                    hidden: false,
                },
                {
                    label: "开户人身份证号",
                    prop: "idCard",
                    width: "",
                    hidden: false,
                },
                {
                    label: "平安子账户账号",
                    prop: "subAcctNo",
                    width: "",
                    hidden: false,
                },
                {
                    label: "绑卡状态",
                    prop: "bindCardStatusName",
                    width: "",
                    hidden: false,
                },
                {
                    label: "折扣",
                    prop: "taxThresholdStr",
                    width: "",
                    hidden: false,
                },
                {
                    label: "开户时间",
                    prop: "createTime",
                    width: "",
                    hidden: false,
                },
                {
                    label: "操作",
                    prop: "operation",
                    width: "200px",
                    hidden: false,
                },
            ],
            total: null, //总数
            typeList: [
                {
                    value: 0,
                    label: "未绑卡",
                },
                {
                    value: 1,
                    label: "已绑卡",
                },
            ],
        };
    },

    /** 事件监听 */
    watch: {},

    /** 计算属性 */
    computed: {},

    /** 生命周期 -- 实例创建后调用 */
    created() {},

    /** 生命周期 -- 实例挂载后调用 */
    mounted() {
        this.init();
        this.tableHeight =
            window.innerHeight - this.$refs.table.$el.offsetTop - 170;
    },

    /** 生命周期 -- 被 keep-alive 缓存的组件激活时调用 */
    activated() {},

    /** 生命周期 -- 被 keep-alive 缓存的组件失活时调用 */
    deactivated() {},

    /** 生命周期 -- 实例销毁后调用 */
    destroyed() {},

    methods: {
        /** 页面初始化 */
        init() {
            this.getList();
        },

        /** 获取数据列表 **/
        getList() {
            this.loading = true;
            this.$post(
                "/admin-center-server/commonUser/channelUserList",
                this.queryParams
            ).then((res) => {
                this.loading = false;
                this.tableData = res.list;
                this.total = JSON.parse(res.total);
            });
        },

        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNumber = 1;
            this.getList(); //获取查询数据
        },

        /** 重置按钮操作 */
        resetQuery() {
            this.$refs.searchForm.resetFields();
            this.handleQuery();
        },

        /** 查看详情操作 */
        handleDetail(row) {
            this.isShowList = false;
            this.$nextTick(() => {
                this.$refs.formModel.detail(row);
            });
        },

        /** 开通平安账户操作 */
        handleOpenCard() {
            this.isOpenCard = true;
            this.$nextTick(() => {
                this.$refs.openCard.edit();
            });
        },

        /** 绑定银行卡操作 */
        handleBindCard(row) {
            this.isBindCard = true;
            this.$nextTick(() => {
                this.$refs.bindCard.bind(row);
            });
        },

        /** 设置折扣比例操作 */
        handleDiscount(row) {
            this.isDiscount = true;
            this.$nextTick(() => {
                this.$refs.discount.edit(row);
            });
        },

        /** 子模块触发 */
        formCancel() {
            this.isShowList = true;
        },

        openCardCancel() {
            this.isOpenCard = false;
        },

        openCardSuccess() {
            this.isOpenCard = false;
            this.getList();
        },

        discountCancel() {
            this.isDiscount = false;
        },

        discountSuccess() {
            this.isDiscount = false;
            this.getList();
        },

        bindCardCancel() {
            this.isBindCard = false;
        },

        bindCardSuccess() {
            this.isBindCard = false;
            this.getList();
        },
    },
};
</script>
  
<style rel="stylesheet/scss" lang="scss" scoped>
.el-form-item {
    margin-bottom: 0;
}

.carsList {
    .select-box {
        /*height: 260px;*/
        background-color: #ffffff;

        .top-title {
            font-size: 16px;
            font-weight: 700;
            height: 40px;
            line-height: 40px;
            padding-left: 10px;
            border-bottom: 1px solid #cccccc;
        }

        .select-info {
            padding: 30px 10px;
        }
    }

    .list-box {
        background-color: #ffffff;
        margin-top: 20px;
        padding: 10px;

        .list-title {
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            div {
                height: 38px;
                line-height: 38px;
            }
        }
        .page {
            text-align: right;
        }
        .list-main {
            width: 100%;
            border: 1px solid #cccccc;
            margin-top: 10px;
        }
    }
}

.global-div-search {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    /* font-size: 14px; */
    /* color: #555; */
    position: relative;
}

.el-select {
    width: 100% !important;
}

.el-date-editor.el-input,
.el-date-editor.el-input__inner {
    width: 100% !important;
}
.el-input-number--medium {
    width: 100%;
}

.interval {
    width: 100%;
    padding: 5px 0px;
}
</style>
  