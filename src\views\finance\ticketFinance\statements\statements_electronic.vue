<template>
    <div class="app-container receipt">
        <div class="select-box">
            <div class="top-title">
                <div>筛选查询 <span style="margin-left: 30px;">结算单编号:  {{sn}} </span> <span style="margin-left: 30px;">客户名称：{{businessName}}</span>
                </div>
                <div class="button">
                    <el-button class="left"
                               icon="el-icon-search"
                               size="mini"
                               type="primary"
                               @click="onSubmit">查询
                    </el-button>
                    <el-button class="left"
                               icon="el-icon-delete"
                               size="mini"
                               type="danger"
                               @click="clearForm">清空筛选
                    </el-button>
                </div>
            </div>
            <div class="select-info">
                <el-form
                        :inline="true"
                        :model="formInline"
                        class="demo-form-inline"
                        label-width="120px"
                        size="mini"
                >
                    <el-form-item label="收款方名称:">
                        <el-input v-model="formInline.inName" :οnkeyup="formInline.inName=formInline.inName.replace(/\s/g, '')" placeholder="请输入收款方名称"></el-input>
                    </el-form-item>
                    <el-form-item label="收款方手机号:">
                        <el-input v-model="formInline.inMobile" :οnkeyup="formInline.inMobile=formInline.inMobile.replace(/\s/g, '')" placeholder="请输入收款方手机号"></el-input>
                    </el-form-item>
                    <el-form-item label="收款方会员代码:">
                        <el-input v-model="formInline.inUserSn" :οnkeyup="formInline.inUserSn=formInline.inUserSn.replace(/\s/g, '')" placeholder="请输入收款方会员代码"></el-input>
                    </el-form-item>
                    <el-form-item label="收款方子账户:">
                        <el-input v-model="formInline.inSubAcctNo" :οnkeyup="formInline.inSubAcctNo=formInline.inSubAcctNo.replace(/\s/g, '')" placeholder="请输入收款方子账户"></el-input>
                    </el-form-item>
                </el-form>
                <el-form
                        :inline="true"
                        :model="formInline"
                        class="demo-form-inline"
                        label-width="120px"
                        size="mini"
                >
                    <el-form-item label="关联订单编号:">
                        <el-input v-model="formInline.ddOrderNo" :οnkeyup="formInline.ddOrderNo=formInline.ddOrderNo.replace(/\s/g, '')" placeholder="请输入关联订单编号"></el-input>
                    </el-form-item>
                    <el-form-item label="关联运单编号:">
                        <el-input v-model="formInline.ydOrderNo" :οnkeyup="formInline.ydOrderNo=formInline.ydOrderNo.replace(/\s/g, '')" placeholder="请输入关联运单编号"></el-input>
                    </el-form-item>
                    <el-form-item label="流水号:">
                        <el-input v-model="formInline.frontSeqNo" :οnkeyup="formInline.frontSeqNo=formInline.frontSeqNo.replace(/\s/g, '')" placeholder="请输入流水号"></el-input>
                    </el-form-item>
                </el-form>
                <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="120px" size="mini"
                >
                    <el-form-item label="财务处理日期:">
                        <el-date-picker
                                :clearable="false"
                                @blur="selectTime"
                                v-model="date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetimerange"
                                :default-time="['00:00:00', '00:00:00']"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="list-box">
            <div class="list-title">
                <div>数据列表</div>
                <div>
                    <span style="color: #888888;font-size:12px;margin-right: 20px">共 {{total}} 条   ；金额合计：{{amtSum}} 元</span>
                    <el-button :loading="loading" type="primary" size="mini" icon="el-icon-download"
                               @click="exportExcel">导出报表
                    </el-button>
                    <el-button type="primary" size="mini" icon="el-icon-download" @click="exportEle">批量下载回执单</el-button>
                </div>
            </div>
            <div class="list-main">
                <template>
                    <el-table
                            :data="tableData"
                            border
                            height="400"
                            style="width: 100%">
                        <el-table-column
                                type="index"
                                label="序号"
                                width="50"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="frontSeqNo"
                                label="流水号"
                                show-overflow-tooltip
                        >
                        </el-table-column>
                        <el-table-column
                                prop="transactionSn"
                                label="交易网流水号"
                                show-overflow-tooltip
                                width="140"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="ddOrderNo"
                                label="关联订单编号"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="ydOrderNo"
                                label="关联运单编号"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="outType"
                                label="付款方用户类型"
                                show-overflow-tooltip
                                width="120">
                        </el-table-column>
                        <el-table-column
                                prop="outName"
                                label="付款方名称"
                                show-overflow-tooltip
                                width="100">
                        </el-table-column>
                        <el-table-column
                                prop="outUsername"
                                label="付款方户名"
                                show-overflow-tooltip
                                width="120">
                        </el-table-column>
                        <el-table-column
                                prop="outSubAcctNo"
                                label="付款方子账户"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="outMobile"
                                label="付款方手机号"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="outUserSn"
                                label="付款方会员代码"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="tranAmt"
                                label="交易金额(元)"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="inType"
                                label="收款方用户类型"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="inName"
                                label="收款方名称"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="inUsername"
                                label="收款方户名"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="inSubAcctNo"
                                label="收款方子账户"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="inMobile"
                                label="收款方手机号"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="inUserSn"
                                label="收款方会员代码"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="tranTime"
                                label="账务处理日期"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                fixed="right"
                                label="操作"
                                width="80">
                            <template slot-scope="scope">
                                <el-button @click="goDetail(scope.row)" type="text" size="small">查看</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
                <el-pagination @size-change="handleSizeChange"
                               @current-change="handleCurrentChange"
                               :current-page="pageNumber"
                               :page-sizes="[10,20,30, 40, 60, 80,100]"
                               :page-size="pageSize"
                               layout="total, sizes, prev, pager, next, jumper"
                               :total="total"
                               class="pagination"
                ></el-pagination>
            </div>
        </div>
        <el-dialog
                :visible.sync="dialogVisible"
                width='80%'
                :before-close="handleClose">
            <div id="export_content">
                <div class="downloadImg" v-for="item in tableData_ELE">
                    <div class="title_dl">
                        <div class="title_logo">
                            <div class="logo_box">

                            </div>
                        </div>
                        <div class="title_word">
                            分账账户明细
                        </div>
                        <div class="title_right">
                            回单凭证
                        </div>
                    </div>
                    <div class="main_info">
                        <div class="itemK">
                            <div class="left">财务处理日期: <span>{{item.tranTime}}</span></div>
                            <div class="right">流水号: <span>{{item.frontSeqNo}}</span></div>
                        </div>
                        <div class="itemK">
                            <div class="left">分账账户付款方编号: <span>{{item.outSubAcctNo}}</span></div>
                            <div class="right">分账账户收款方编号: <span>{{item.inSubAcctNo}}</span></div>
                        </div>
                        <div class="itemK">
                            <div class="left">付款方户名: <span>{{item.outUsername}}</span></div>
                            <div class="right">收款方户名: <span>{{item.inUsername}}</span></div>
                        </div>
                        <div class="itemK">
                            <div class="left">付款方平台端会员代码: <span>{{item.outUserSn}}</span></div>
                            <div class="right">收款方平台端会员代码: <span>{{item.inUserSn}}</span></div>
                        </div>
                        <div class="itemK">
                            <div class="left">金额 (大写) : <span>{{item.RMB}}</span></div>
                            <div class="right">小写: <span>{{item.tranAmt}}</span></div>
                        </div>
                        <div class="itemK">
                            <div class="left">项目专管汇总账户账号: <span>15000103124699</span></div>
                            <div class="right">项目专管汇总账户户名: <span>天津鸿飞达科技有限公司东疆保税港区分公司</span></div>
                        </div>
                        <div class="itemK" style="border: none">摘要:会员间交易</div>
                    </div>
                    <div class="remark">
                        <div>
                            备注：本凭证所述分账账户付款方和收款方编号，仅用于我行在接受付款方和收款方所 <br> 属交易平台委托的情况下，依据平台交易和结算指令进行分账处理，记载分账状态、明 <br>
                            细等信息。
                        </div>
                        <div class="zhang">

                        </div>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button @click="dialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="downLoad">确认下载</el-button>
  </span>
        </el-dialog>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                sn: '',
                amtSum: '',
                businessName: '',
                dialogVisible: false,
                loading: false,
                total: 0,
                pageSize: 20,
                pageNumber: 1,
                formInline: {
                    inName: '',
                    inMobile: '',
                    inUserSn: '',
                    inSubAcctNo: '',
                    ddOrderNo: '',
                    ydOrderNo: '',
                    frontSeqNo: '',

                    tranDateStart: '',
                    tranDateEnd: '',
                },
                date: '',
                tableData: [],
                tableData_ELE: [],
                nnn: ''
            }
        },
        methods: {
            handleClose(done) {
                this.dialogVisible = false;
            },
            exportExcel() {
                this.loading = true;
                let str = this.tableData;
                if (str !== 0) {
                    let ids = str.map((item, index) => {
                        return item.id
                    });
                    this.$http.post('/admin-center-server/finance/exportReconciliationTransactionListByIds', ids).then(res => {
                        let data = res.data;
                        if (data.code === 200) {
                            let url = data.data;
                            window.location.href = url;
                            this.loading = false;
                        } else {
                            this.$message.warning(data.data);
                            this.loading = false;
                        }
                    })
                }
            },
            //批量下载电子回单 金额转为大写
            exportEle() {
                let res = this.tableData;
                if(res.length>31){
                    this.$message.warning('批量下载回执单最多30条')
                }else {
                    res.map((item, index) => {
                        item.RMB = this.changeRmb(item.tranAmt)
                    });
                    this.tableData_ELE = res;
                    this.dialogVisible = true;
                }
            },
            goDetail(row) {
                let id = row.id;
                this.$router.push({
                    name: 'ReceiptDeatil',
                    query: {
                        id: id,
                    }
                })
            },
            selectTime() {
                if(this.date!==null){
                    let tranDateStart = this.date[0];
                    let tranDateEnd = this.date[1];
                    this.formInline.tranDateStart = tranDateStart;
                    this.formInline.tranDateEnd = tranDateEnd;
                }else {
                    this.date=[];
                }
            },
            onSubmit() {
                this.getDataList()
            },
            clearForm() {
                this.formInline = {
                    inName: '',
                    inMobile: '',
                    inUserSn: '',
                    inSubAcctNo: '',
                    ddOrderNo: '',
                    ydOrderNo: '',
                    frontSeqNo: '',
                    tranDateStart: '',
                    tranDateEnd: '',
                };
                    this.date = '';
                this.getDataList();
            },
            handleSizeChange(val) {
                this.pageNumber = 1;
                this.pageSize = val;
                this.getDataList()
            },
            handleCurrentChange(val) {
                this.pageNumber = val;
                this.getDataList()
            },
            getDataList() {
                this.sn = this.$route.query.sn;
                this.businessName = this.$route.query.businessName;
                let sendData = this.formInline;
                sendData.pageNumber = this.pageNumber;
                sendData.pageSize = this.pageSize;
                sendData.order = 'desc';
                sendData.idList=JSON.parse(sessionStorage.getItem('idList'));
                this.$http.post('/pay-center-server/platformFinance/getHuiDanPager', sendData).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.tableData = data.data.list;
                        this.total = Number(data.data.total);
                        this.amtSum = Number(data.data.amtSum);
                    } else {
                        this.$message.warning(data.message)
                    }
                })
            },
            changeRmb(rmb) {
                let money = rmb;
                let fraction = ['角', '分'];
                let digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
                let unit = [['元', '万', '亿'], ['', '拾', '佰', '仟']];
                let head = money < 0 ? '欠' : '';
                money = Math.abs(money);
                let s = '';
                for (let i = 0; i < fraction.length; i++) {
                    s += (digit[Math.floor(money * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
                }
                s = s || '整';
                money = Math.floor(money);
                for (let i = 0; i < unit[0].length && money > 0; i++) {
                    let p = '';
                    for (let j = 0; j < unit[1].length && money > 0; j++) {
                        p = digit[money % 10] + unit[1][j] + p;
                        money = Math.floor(money / 10);
                    }
                    s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
                }
                let resRmb = head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');

                return resRmb
            },

            downLoad() {
                this.dialogVisible = false;
                let exportBox = document.createElement('div');
                let curWidth = document.documentElement.clientWidth;
                let ddd = this.tableData_ELE;
                let curTop=246+'px';
                let str = ``;
                for (let i = 0; i < ddd.length; i++) {
                    let item = ddd[i];
                    str += `
                    <div class="downloadImgDown" style="margin-top: ${curTop}">
                    <div class="title_dl">
                        <div class="title_logo">
                            <div class="logo_box">

                            </div>
                        </div>
                        <div class="title_word">
                            分账账户明细
                        </div>
                        <div class="title_right">
                            回单凭证
                        </div>
                    </div>
                    <div class="main_info">
                        <div class="itemK">
                            <div class="left">财务处理日期: <span>${item.tranTime}</span></div>
                            <div class="right">流水号: <span>${item.frontSeqNo}</span></div>
                        </div>
                        <div class="itemK">
                            <div class="left">分账账户付款方编号: <span>${item.outSubAcctNo}</span></div>
                            <div class="right">分账账户收款方编号: <span>${item.inSubAcctNo}</span></div>
                        </div>
                        <div class="itemK">
                            <div class="left">付款方户名: <span>${item.outUsername}</span></div>
                            <div class="right">收款方户名: <span>${item.inUsername}</span></div>
                        </div>
                        <div class="itemK">
                            <div class="left">付款方平台端会员代码: <span>${item.outUserSn}</span></div>
                            <div class="right">收款方平台端会员代码: <span>${item.inUserSn}</span></div>
                        </div>
                        <div class="itemK">
                            <div class="left">金额 (大写) : <span>${item.RMB}</span></div>
                            <div class="right">小写: <span>${item.tranAmt}</span></div>
                        </div>
                        <div class="itemK">
                            <div class="left">项目专管汇总账户账号: <span>15000103124699</span></div>
                            <div class="right">项目专管汇总账户户名: <span>天津鸿飞达科技有限公司东疆保税港区分公司</span></div>
                        </div>
                        <div class="itemK" style="border: none">摘要:会员间交易</div>
                    </div>
                    <div class="remark">
                        <div>
                            备注：本凭证所述分账账户付款方和收款方编号，仅用于我行在接受付款方和收款方所 <br> 属交易平台委托的情况下，依据平台交易和结算指令进行分账处理，记载分账状态、明 <br>
                            细等信息。
                        </div>
                        <div class="zhang">

                        </div>
                    </div>
                </div>
                    `
                }
                exportBox.innerHTML = str;
                let body = document.getElementsByTagName("body")[0];
                body.appendChild(exportBox);
                html2canvas(
                    exportBox,
                    {
                        onrendered: function (canvas) {
                            this.loading1 = true;
                            document.body.appendChild(canvas);
                            let contentWidth = canvas.width;
                            let contentHeight = canvas.height;
                            //一页pdf显示html页面生成的canvas高度;
                            let pageHeight = contentWidth / 592.28 * 841.89;
                            //未生成pdf的html页面高度
                            let leftHeight = contentHeight;
                            //pdf页面偏移
                            let position = 0;
                            //html页面生成的canvas在pdf中图片的宽高（a4纸的尺寸[595.28,841.89]）
                            let imgWidth = 595.28;
                            let imgHeight = 592.28 / contentWidth * contentHeight;
                            let pageData = canvas.toDataURL('image/jpeg', 1.0);
                            let pdf = new jsPDF('', 'pt', 'a4');
                            if (leftHeight < pageHeight) {
                                pdf.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight);
                            } else {
                                while (leftHeight > 0) {
                                    pdf.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight);
                                    leftHeight -= pageHeight;
                                    position -= 841.89;
                                    if (leftHeight > 0) {
                                        pdf.addPage();
                                    }
                                }
                            }
                            pdf.save('回单.pdf');
                            body.removeChild(exportBox);
                            body.removeChild(canvas);
                            this.loading1=false;
                        },
                        background: "#fff",
                    }
                )
            },
        },
        activated() {
            this.getDataList()
        }
    }
</script>
<style>
    .downloadImg {
        margin-left: 20px;
        margin-right: 20px;
        margin-top: 20px;
        font-family: Monospace;
        height: 487px;
        background-color: #ffffff;
        padding-left: 20px;
        padding-right: 20px;
    }

    .downloadImg .title_dl {
        height: 90px;
        background-color: #ffffff;
        border-bottom: 3px solid #FF0000;
        padding-top: 10px;
        display: flex;
    }

    .downloadImg .title_dl .title_logo {
        width: 1000px;
    }

    .downloadImg .title_dl .title_logo .logo_box {
        width: 260px;
        height: 70px;
        background: url("img/pingan.png") no-repeat;
        background-size: 100% 100%;
    }

    .downloadImg .title_word {
        /*font-family: "Noto Sans SC";*/
        width: 1000px;;
        text-align: center;
        font-size: 21px;
        height: 30px;
        line-height: 30px;
        margin: 0 auto;
        margin-top: 40px;
        /*float: left;*/
    }

    .downloadImg .title_right {
        /*font-family: "Noto Sans SC";*/
        width: 1000px;
        margin-right: 20px;
        margin-top: 40px;
        text-align: right;
        font-weight: 500;
        font-size: 26px;
    }

    .downloadImg .itemK {
        font-size: 12px;
        height: 30px;
        line-height: 30px;
        border-bottom: 1px dashed #DEDEDE;
        /*font-family: "Noto Sans SC";*/
        color: #000000;
        display: flex;
    }

    .downloadImg .left {
        width: 1000px;
    }

    .downloadImg .right {
        width: 1000px;
    }

    .downloadImg .zhang {
        position: absolute;
        width: 147px;
        height: 67px;
        background: url("img/qianzhang.png") no-repeat;
        background-size: 100% 100%;
        left: 525px;
        top: 20px
    }

    .downloadImg .remark {
        /*font-family: "Noto Sans SC";*/
        font-size: 12px;
        color: #000000;
        line-height: 30px;
        border-bottom: 2px solid #141414;
        padding-top: 10px;
        padding-bottom: 10px;
        position: relative;
    }

    .downloadImgDown {
        margin-left: 20px;
        margin-right: 20px;
        margin-top: 10px;
        font-family: Monospace;
        background-color: #ffffff;
        padding-left: 20px;
        padding-right: 20px;
    }
    .downloadImgDown .title_dl {
        height: 130px;
        background-color: #ffffff;
        border-bottom: 3px solid #FF0000;
        padding-top: 10px;
        display: flex;
    }
    .downloadImgDown .title_dl .title_logo {
        width: 1000px;
    }
    .downloadImgDown .title_dl .title_logo .logo_box {
        width: 310px;
        height: 110px;
        background: url("img/pingan.png") no-repeat;
        background-size: 100% 100%;
    }
    .downloadImgDown .title_word {
        width: 1000px;;
        text-align: center;
        font-size: 26px;
        height: 70px;
        line-height: 70px;
        margin-top: 60px;
    }
    .downloadImgDown .title_right {
        /*font-family: "Noto Sans SC";*/
        width: 1000px;
        margin-right: 20px;
        margin-top: 70px;
        text-align: right;
        font-weight: 500;
        font-size: 29px;
    }
    .downloadImgDown .itemK {
        font-size: 22px;
        height: 60px;
        line-height: 60px;
        border-bottom: 1px dashed #DEDEDE;
        /*font-family: "Noto Sans SC";*/
        color: #000000;
        display: flex;
    }
    .downloadImgDown .left {
        width: 1000px;
    }
    .downloadImgDown .right {
        width: 1000px;
    }
    .downloadImgDown .zhang {
        position: absolute;
        width: 200px;
        height: 91px;
        background: url("img/qianzhang.png") no-repeat;
        background-size: 100% 100%;
        left: 800px;
        top: 0px
    }
    .downloadImgDown .remark {
        /*font-family: "Noto Sans SC";*/
        font-size: 20px;
        color: #000000;
        line-height: 30px;
        border-bottom: 2px solid #141414;
        padding-top: 10px;
        padding-bottom: 10px;
        position: relative;
    }

</style>
<style rel="stylesheet/scss" lang="scss" scoped>
    .receipt {
        /*position: relative;*/
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
                display: flex;
                justify-content: space-between;

                .button {
                    margin-right: 20px;
                }
            }

            .select-info {
                padding-top: 30px;
                padding-bottom: 30px;
            }
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;

            .list-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                div {
                    font-size: 14px;
                    height: 30px;
                    line-height: 30px;
                }
            }

            .list-main {
                width: 100%;
                margin-top: 10px;

                .pagination {
                    text-align: right;
                    margin-top: 10px;
                }
            }

            .releaseMessage {
                margin-right: 20px;
            }

            .pagination {
                text-align: right;
                margin-top: 10px;
            }
        }

        .downloadImg {
            /*font-family: "Noto Sans SC";*/
            /*  position: absolute;
              z-index: -100;
              visibility:hidden;*/
            margin-left: 20px;
            margin-right: 20px;
            margin-top: 20px;
            font-family: Monospace;
            height: 487px;
            background-color: #ffffff;
            padding-left: 20px;
            padding-right: 20px;

            .title_dl {
                height: 90px;
                background-color: #ffffff;
                border-bottom: 3px solid #FF0000;
                padding-top: 10px;
                display: flex;

                .title_logo {
                    width: 1000px;

                    .logo_box {
                        width: 260px;
                        height: 70px;
                        background: url("img/pingan.png") no-repeat;
                        background-size: 100% 100%;
                    }
                }

                .title_word {
                    /*font-family: "Noto Sans SC";*/
                    width: 1000px;;
                    text-align: center;
                    font-size: 21px;
                    height: 30px;
                    line-height: 30px;
                    margin: 0 auto;
                    margin-top: 40px;
                    /*float: left;*/
                }

                .title_right {
                    /*font-family: "Noto Sans SC";*/
                    width: 1000px;
                    margin-right: 20px;
                    margin-top: 40px;
                    text-align: right;
                    font-weight: 500;
                    font-size: 26px;
                }
            }

            .main_info {
                border-bottom: 2px solid #141414;

                .itemK {
                    font-size: 12px;
                    height: 30px;
                    line-height: 30px;
                    border-bottom: 1px dashed #DEDEDE;
                    /*font-family: "Noto Sans SC";*/
                    color: #000000;
                    display: flex;

                    .left {
                        width: 1000px;
                    }

                    .right {
                        width: 1000px;
                    }
                }
            }

            .remark {
                /*font-family: "Noto Sans SC";*/
                font-size: 12px;
                color: #000000;
                line-height: 30px;
                border-bottom: 2px solid #141414;
                padding-top: 10px;
                padding-bottom: 10px;
                position: relative;

                .zhang {
                    position: absolute;
                    width: 147px;
                    height: 67px;
                    background: url("img/qianzhang.png") no-repeat;
                    background-size: 100% 100%;
                    left: 525px;
                    top: 20px
                }
            }
        }
    }
</style>
