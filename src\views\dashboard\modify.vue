<template>
  <div class="wrap">
    <h1>全部导航</h1>
    <el-tree :data="data"
             show-checkbox
             node-key="id"
             ref='tree'
             default-expand-all
             :default-checked-keys="checkedID"
             @check="handleNodeClick"
             :props="defaultProps">
    </el-tree>
    <h1>已选择导航<span>最多选择 8 个常用导航</span></h1>
    <el-row :gutter="20">
      <el-col v-for='(item,lan) in data2'
              v-if='!item.children'
              :span="6">
        <div class="grid-content bg-purple">·{{item.name}}</div>
      </el-col>
    </el-row>
    <el-button class='btn-navigation'
               @click="getCheckedKeys">保存</el-button>
  </div>
</template>

<script>
//const list = '/admin-center-server/home/<USER>'//菜单树结构
const list = '/admin-center-server/sys/resource/queryInfoAndMenu'//菜单树结构
const navigation = '/admin-center-server/home/<USER>'//保存导航
const updateNav = '/admin-center-server/home/<USER>'//修改导航
const listNav = '/admin-center-server/home/<USER>'//保存导航
export default {
  data () {
    return {
      data2: [],
      checkedID: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      data: []
    }
  },
  computed: {

  },
  created () {

  },
  methods: {
    handleNodeClick (node, value) {
      this.data2 = value.checkedNodes
    },
    getCheckedKeys () {
      var ids = this.$refs.tree.getCheckedKeys();
      var arr = [];
      for (var i = 0; i < this.data2.length; i++) {
        if (!this.data2[i].children) {
          arr.push(this.data2[i].id)
        }
      }
      if (arr.length > 8) {
        this.$message({
          type: 'info',
          message: '最多选择 8 个常用导航'
        })
      } else {
        arr = arr.join(',');
        this.$http.post(navigation + '?ids=' + arr).then(res => {
          var that = this
          if (res.data.code == '200') {
            this.$message({
              message: res.data.message,
              type: 'success'
            });
            setTimeout(function () {
              that.$router.go(-1);
            }, 3000);
          } else {
            this.$message.error(res.data.message);
          }
        })
      }
    }
  },
  mounted () {
    this.$http.get(listNav).then(res => {
      var listNavData = res.data.data
      //console.log(listNavData)
      for (var i = 0; i < listNavData.length; i++) {
        this.checkedID.push(listNavData[i].sysResourceId)
      }
      this.$http.post(list).then(res => {
        var data = res.data.data.menuList
        //var data = res.data.data
        //去掉近期数据
        for (var i = 0; i < data.length; i++) {
          if (data[i].permission == 'FORBID') {
            data.splice(i, 1)
          }
        }
        this.data = data
      })
    })
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
// .wrap {
//   margin-left: 100px;
// }
.btn-navigation {
  //margin: 0 0 20px 100px;
  width: 25% !important;

  margin-bottom: 20px;
}
h1 {
  font-weight: normal;
  font-size: 16px;
  margin: 20px 0 0 20px;
  color: #1898ff;
  span {
    font-size: 14px;
    color: #999;
    margin-left: 10px;
  }
}
.el-tree {
  margin: 20px;
  background: none;
  width: 500px;
}
.el-button {
  margin-left: 20px;
}
.el-row {
  margin-bottom: 20px;
  border-radius: 5px;
  padding: 20px 30px 10px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 4px;
}
.bg-purple-dark {
  background: #fff;
}
.bg-purple {
  background: #fff;
  text-align: center;
  line-height: 30px;
  font-size: 14px;
  color: #000;
  margin-bottom: 10px;
}
.bg-purple-light {
  background: #e5e9f2;
}
.grid-content {
  border-radius: 4px;
  min-height: 30px;
  border: 1px solid #ccc;
}
.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}
</style>

