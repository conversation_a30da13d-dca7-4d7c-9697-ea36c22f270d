<template>
  <div class="agreement-setup">
    <el-form :model="searchForm" label-width="100px" size="mini" :inline="true" ref="searchForm">
      <el-form-item label="签署用户" prop="signatory">
        <el-input v-model="searchForm.signatory" placeholder="请输入签署用户"></el-input>
      </el-form-item>
      <el-form-item label="用户联系方式" prop="mobile">
        <el-input v-model="searchForm.mobile" placeholder="请输入用户联系方式"></el-input>
      </el-form-item>
      <el-form-item label="用户类型" prop="userType">
        <el-select v-model="searchForm.userType" placeholder="请选择用户类型" clearable @change="changeFn">
          <el-option label="不限" value=""></el-option>
          <el-option label="货主" value="1"></el-option>
          <el-option label="司机" value="3"></el-option>
          <el-option label="运力供应商" value="6"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="运单号" prop="signatory">
        <el-input v-model="searchForm.orderItemSn" placeholder="请输入运单号"></el-input>
      </el-form-item>
      <el-form-item label="是否作废" prop="voidFlag" >
        <el-select v-model="searchForm.voidFlag"  placeholder="请选择是否作废" clearable @change="changeFn">
          <el-option label="不限" value=""></el-option>
          <el-option label="否" :value="0"></el-option>
          <el-option label="是" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="归档时间">
        <el-date-picker v-model="searchDate" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange" :default-time="['00:00:00', '23:59:59']" start-placeholder="选择开始时间"
        end-placeholder="选择结束时间"></el-date-picker>
      </el-form-item>
      <el-form-item label="合同编号" prop="contractId">
        <el-input v-model="searchForm.contractId" placeholder="请输入合同编号"></el-input>
      </el-form-item>
      <el-form-item label="合同主体" prop="baseId">
        <el-select v-model="searchForm.baseId" placeholder="请选择合同主体" clearable @change="changeFn">
          <el-option v-for="item in $store.state.user.baseInfo" :key="item.id" :value="item.id" :label="item.baseName" clearable></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="合同类型" prop="type">
        <el-select v-model="searchForm.type" placeholder="请选择合同类型" clearable @change="changeFn">
          <el-option label="不限" value=""></el-option>
          <el-option label="运输合同" :value="1"></el-option>
          <el-option label="车主声明" :value="2"></el-option>
          <el-option label="授权自动签" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="doSearch" type="primary" icon="el-icon-search">查询</el-button>
        <el-button @click="resetSeach" icon="el-icon-refresh-right">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="btns">
      <el-button @click="showAddDialog" size="medium"><i class="el-icon-plus"></i> 新增</el-button>
    </div>
    <el-table :data="data" border>
      <el-table-column label="序号" type="index"></el-table-column>
      <el-table-column label="合同">
        <template slot-scope="scope">
          <a :href="scope.row.viewpdfUrl" target="_blank" class="link">{{ scope.row.name }}</a>
        </template>
      </el-table-column>
      <el-table-column label="合同编号" prop="contractId"></el-table-column>
      <el-table-column label="合同期限">
        <template slot-scope="scope">
          <template v-if="scope.row.expirationDate && scope.row.expirationDate.length === 2">
            {{ scope.row.expirationDate }}
          </template>
          <template v-else>{{ `${scope.row.startExpirationDate.slice(0, 10) } 至 ${scope.row.expirationDate}` }}</template>
        </template>
      </el-table-column>
      <el-table-column label="签署用户">
        <template slot-scope="scope">
          <router-link v-if="scope.row.userType === '1'" :to="`/consignorAccount/accountDetails?id=${scope.row.userId}`" class="link">{{ scope.row.signatory }}</router-link>
          <router-link v-else-if="scope.row.userType === '3'" :to="`/driverAccount/accountDetails?id=${scope.row.userId}&type=1`" class="link">{{ scope.row.signatory }}</router-link>
          <router-link v-else-if="scope.row.userType === '6'" :to="`/driverAccount/accountDetails?id=${scope.row.userId}&type=5`" class="link">{{ scope.row.signatory }}</router-link>
        </template>
      </el-table-column>
      <el-table-column label="用户联系方式" prop="mobile"></el-table-column>
      <el-table-column label="用户类型" prop="userTypeName"></el-table-column>
      <el-table-column label="运单号" prop="dataId"></el-table-column>
      <el-table-column label="合同主体" prop="baseName"></el-table-column>
      <el-table-column label="合同类型" prop="type">
        <template slot-scope="scope">
          <template v-if="scope.row.type === '1'">运输合同</template>
          <template v-if="scope.row.type === '2'">车主声明</template>
          <template v-if="scope.row.type === '3'">授权自动签</template>
        </template>
      </el-table-column>
      <el-table-column label="归档人" prop="fileUser"></el-table-column>
      <el-table-column label="归档时间" prop="fileDate"></el-table-column>
      <el-table-column label="是否作废">
        <template slot-scope="scope">
          <template v-if="scope.row.voidFlag === '0'">未作废</template>
          <template v-if="scope.row.voidFlag === '1'">已作废</template>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-popconfirm title="确认作废？" @onConfirm="cancel(scope.row.id)">
            <el-button slot="reference" :disabled="scope.row.voidFlag === '1' || scope.row.userType === '3'" type="text">作废</el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page.sync="page.pageNumber"
      @current-change="getList"
      @size-change="handleSizeChange"
      :total="total"
      layout="sizes, total, prev, pager, next"
      style="text-align: right;">
    </el-pagination>
    <el-dialog
      :visible.sync="isAddShow"
      width="600px"
      title="新增合同"
      :destroy-on-close="true">
      <el-form :rules="addRules" :model="addForm" ref="addForm" label-width="100px" class="add-form">
        <el-form-item label="签署用户" prop="userId">
          <el-select
            v-model="addForm.userId"
            :remote-method="getUsers"
            @change="handleUserSelect"
            filterable
            remote
            placeholder="按照公司名称、姓名、手机号搜索">
            <el-option v-for="item in users" :label="item.name" :value="item.userId" :key="item.userId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="合同编号" prop="contractId">
          <el-input v-model="addForm.contractId" placeholder="请输入合同编号"></el-input>
        </el-form-item>
        <el-form-item prop="downloadUrl" label="合同文件" class="form-item-upload">
          <el-upload
            :show-file-list="true"
            action=""
            :http-request="handleFileUpload"
            :limit="1"
            :on-exceed="handleExceed"
            :on-remove="handleFileRemove"
            accept=".doc,.docx,.pdf"
            drag>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传文件</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="合同期限" prop="date">
          <el-date-picker :disabled="isLong" type="daterange" value-format="yyyy-MM-dd" v-model="date"  start-placeholder="选择开始时间"
          end-placeholder="选择结束时间"></el-date-picker>
          <el-checkbox v-model="isLong" label="长期"></el-checkbox>
        </el-form-item>
        <el-form-item label="合同主体" prop="baseId">
          <el-radio-group v-model="addForm.baseId">
            <el-radio v-for="item in $store.state.user.baseInfo" :key="item.id" :label="item.id">{{ item.baseName }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="isAddShow = false">取消</el-button>
        <el-button @click="addSubmit" type="primary">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { upload } from '@/utils/file'
export default {
  data() {
    return {
      searchForm: {
        signatory: '',
        mobile: '',
        userType: '',
        voidFlag: ''
      },
      searchDate: null,
      data: [],
      page: {
        pageSize: 10,
        pageNumber: 1
      },
      total: 0,
      isAddShow: false,
      addForm: {
        mobile: '',
        signatory: '',
        userId: '',
        userType: '',
        downloadUrl: '',
        viewpdfUrl: '',
        name: ''
      },
      addRules: {
        userId: [
          { required: true, message: '请输入签署用户', trigger: 'change' }
        ],
        contractId: [
          { required: true, message: '请输入合同编号', trigger: 'blur' }
        ],
        baseId: [
          { required: true, message: '请选择合同主体', trigger: 'change' }
        ],
        downloadUrl: [
          { required: true, message: '请上传合同文件' },
          {
            validator: (rule, value, cb) => {
              console.log('this.addForm.downloadUrl', this.addForm.downloadUrl)
              if (!this.addForm.downloadUrl) {
                cb('请上传合同文件')
              }
              cb()
            }
          }
        ],
        date: [
          {
            validator: (rule, value, cb) => {
              if (!this.date && !this.isLong) {
                cb('请输入合同期限')
              }
              cb()
            }
          }
        ]
      },
      users: [],
      date: null,
      isLong: false
    }
  },
  methods: {
    changeFn(){
      this.$forceUpdate()
    },
    init() {
      this.getList()
    },
    doSearch() {
      this.page.pageNumber = 1
      this.getList()
    },
    resetSeach() {
      this.$refs.searchForm.resetFields()
      this.searchDate = null
      this.getList()
    },
    getList() {
      const searchForm = {
        ...this.searchForm
      }
      if (this.searchDate) {
        searchForm.startExpirationDate = this.searchDate[0]
        searchForm.expirationDate = this.searchDate[1]
      }
      this.$post('/admin-center-server/admin/querySignContractList', {
        ...searchForm,
        ...this.page
      })
        .then(res => {
          this.data = res.list
          this.total = Number(res.total)
        })
    },
    handleSizeChange(v) {
      this.page.pageSize = v
      this.page.pageNumber = 1
      this.getList()
    },
    cancel(id) {
      this.$get('/admin-center-server/admin/modifySignContractVoidFlag', {
        id
      })
        .then(() => {
          this.getList()
        })
    },
    showAddDialog() {
      this.addForm = {}
      this.date = null
      this.isLong = false
      this.isAddShow = true
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate()
      })
    },
    getUsers(keyword) {
      if (!keyword) return
      this.$get('/user-center-server/user/queryUserListByNameOrMobile', {
        searchCondition: keyword
      })
        .then(res => {
          this.users = res
        })
    },
    handleUserSelect(userId) {
      const item = this.users.find(item => item.userId === userId)
      this.addForm.mobile = item.mobile
      this.addForm.signatory = item.name
      this.addForm.userId = item.userId
      this.addForm.userType = item.dtype
      if(item.capacityFlag){
        this.addForm.userType = '6'
      }
    },
    handleFileUpload(e) {
      return upload(e.file)
        .then(res => {
          this.$set(this.addForm,'downloadUrl',res.url)
          this.$set(this.addForm,'viewpdfUrl',res.url)
          this.$set(this.addForm,'name',e.file.name)
          this.$refs.addForm.validateField('downloadUrl')
        })
    },
    handleExceed() {
      this.$message.error('文件已上传')
    },
    handleFileRemove() {
      this.addForm.downloadUrl = ''
      this.addForm.viewpdfUrl = ''
      this.addForm.name = ''
      this.$refs.addForm.validateField('downloadUrl')
    },
    addSubmit() {
      this.$refs.addForm.validate()
        .then(res => {
          if (!res) return
          let params = {
            ...this.addForm,
            type:1
          }
          if (this.isLong) {
            params.expirationDate = '长期'
          } else {
            params.startExpirationDate = this.date[0]
            params.expirationDate = this.date[1]
          }
          this.$post('/admin-center-server/admin/addSignContract', params)
            .then(() => {
              this.isAddShow = false
              this.getList()
            })
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.agreement-setup {
  padding: 10px;
}
.btns {
  margin: 10px 0;
  text-align: right;
}
.el-pagination {
  margin-top: 10px;
}
.add-form {
  width: 550px;
  .el-select,.el-input {
    width: 370px;
  }
  .el-checkbox {
    margin-left: 30px;
  }
  .form-item-upload ::v-deep .el-form-item__content {
    line-height: normal;
  }
}
.link {
  color: #f6a018;
}
</style>