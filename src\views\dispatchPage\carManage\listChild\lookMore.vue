<template>
    <div class="app-container">
        <div class="list-main">
            <div class="title">可驾驶司机</div>
            <div class="list-table">
                <template>
                    <el-table
                            :data="tableData"
                            border
                            style="width: 100%">
                        <el-table-column
                                prop="driverName"
                                label="司机姓名"
                                width="180">
                        </el-table-column>
                        <el-table-column
                                prop="driverMobile"
                                label="手机号"
                                width="180">
                        </el-table-column>
                        <el-table-column
                                prop="idCard"
                                label="身份证号">
                        </el-table-column>
                    </el-table>
                </template>
            </div>
            <div class="pagination" style="float: right;margin-top: 20px">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 40]"
                        :page-size="10"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total=total>
                </el-pagination>
            </div>
        </div>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                tableData: [],
                currentPage: 1,
                pageSize: 10,
                total: 1,
            }
        },
        methods: {
            /** 分页方法 **/
            handleSizeChange(val) {
                this.pageSize = val
                this.getListData()
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getListData()
            },
            getListData() {
                let plateNumber = this.$route.query.plateNumber
                let total = this.$route.query.total;
                this.$http.get('/admin-center-server/car/plateNumber/list', {
                    params: {
                        pageNumber: this.currentPage,
                        pageSize: this.pageSize,
                        plateNumber: plateNumber
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.tableData = data.data.list
                        this.total = Number(total)
                    }
                })
            },
        },
        activated() {
            this.getListData()
        }
    }
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .list-main {
        background-color: white;
        overflow: hidden;

        .title {
            font-size: 16px;
            font-weight: 700;
            height: 40px;
            line-height: 40px;
            padding-left: 10px;
        }

        .list-table {
            padding: 10px;
        }
    }
    .el-table ::v-deep .cell {
        text-align: center;
    }
</style>
