<template>
  <div>
    <div class="list-box">
      <el-row class="global-div-search" :gutter="10">
        <el-col :span="8">
          <div class="my_card">
            渠道油卡总额： {{ topData.sumRechargeMoney || '-' }} 元
          </div>
        </el-col>
        <el-col :span="8">
          <div class="my_card">
            渠道油卡已消耗总额： {{ topData.sumConsumeMoney || '-' }} 元
            <div class="bottom">
              <span>
                已被购买总额： {{ topData.sumBuyMoney || '-' }} 元
              </span>
              <span>
                未被购买总额： {{ topData.sumSurplusMoney || '-' }} 元
              </span>
            </div>
          </div>
        </el-col>
        <el-col :span="8">  
          <div class="my_card">
            渠道油卡未消耗总额： {{ topData.sumNoConsumeMoney || '-'}} 元
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="list-box">
      <div class="main-box">
        <div class="list-info">
          <el-form ref="form" label-width="140px" :inline="true" size="mini">
            <el-form-item label="客户名称：">
              <el-input v-model="search.name"></el-input>
            </el-form-item>
            <el-form-item label="客户手机号：">
              <el-input v-model="search.mobile"></el-input>
            </el-form-item>
            <el-form-item label="油卡卡号：">
              <el-input v-model="search.oilAccount"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="doSearch">查询</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="btns">
        <div class="btn">
          <el-button size="mini" @click="isBalanceShow = true">导入余额数据</el-button>
          <div v-if="operateTime1">操作时间：{{ operateTime1 }}</div>
        </div>
        <div class="btn">
          <el-button size="mini" @click="isConsumeShow = true">导入消耗数据</el-button>
          <div v-if="operateTime2">操作时间：{{ operateTime2 }}</div>
        </div>
      </div>
      <el-table :data="data" style="margin-top: 20px;" :cell-style="{'text-align': 'center',}">
        <el-table-column label="油卡卡号" prop="oilAccount"></el-table-column>
        <el-table-column label="客户名称" prop="name"></el-table-column>
        <el-table-column label="客户手机号" prop="mobile"></el-table-column>
        <el-table-column label="油卡余额" prop="oilAmout"></el-table-column>
        <el-table-column label="可使用油耗" prop="billOilMoney">
          <template slot-scope="scope">
            {{scope.row.billOilMoney || '-'}}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="toList(1, scope.row.userId)">充值记录</el-button>
            <el-button type="text" @click="toList(2, scope.row.userId)">消耗记录</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 10px; text-align: right">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="getList"
          :current-page.sync="page.pageNumber"
          :page-sizes="[10,20, 40, 60, 80, 100]"
          :page-size="page.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
        ></el-pagination>
      </div>
    </div>
    <el-dialog
      :visible.sync="isBalanceShow"
      title="导入余额数据"
      width="500px">
      <div>请到<a href="https://www.sinopecsales.com/" target="_blank" class="link">中国石化网上营业厅</a>“业务查询-卡余额”导出当前余额数据，并将导出文件上传至中台</div>
      <div style="margin-top: 20px; text-align: center">
        <el-button type="primary" style="margin-right: 10px;" @click="exportFile(1)">上传文件</el-button>
        <el-button type="text" @click="viewRecord(1)">导入记录</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="isConsumeShow"
      title="导入消耗数据"
      width="500px">
      <!-- <div>请到<a href="https://www.sinopecsales.com/" target="_blank" class="link">中国石化网上营业厅</a>“业务查询-交易明细”导出昨日油卡使用数据，并将导出文件上传至中台。</div>
      <div>注意：</div>
      <div>1. 请每天上传前一天数据。开始时间、结束时间均选择 “当前日期-1”</div>
      <div>2. 请不要修改文件名</div> -->
      <div style="margin-top: 20px; text-align: center">
        <el-button type="primary" style="margin-right: 10px;" @click="exportFile(2)">上传文件</el-button>
        <el-button type="text" @click="viewRecord(2)">导入记录</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="导入记录"
      :visible.sync="isRecordShow">
      <el-table :data="recordData">
        <el-table-column label="导入时间" prop="createTime"></el-table-column>
        <!-- 只有消耗数据显示文件名 -->
        <el-table-column v-if="recordType === 2" label="文件名" prop="cmFileName"></el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="downloadFile(scope.row.filePath)">查看</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作人" prop="createUserName"></el-table-column>
      </el-table>
      <div style="margin-top: 10px; text-align: right">
        <el-pagination
          @size-change="handleRecordSizeChange"
          @current-change="getRecordList"
          :current-page.sync="recordPage.pageNumber"
          :page-sizes="[10,20, 40, 60, 80, 100]"
          :page-size="recordPage.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="recordTotal"
          class="pagination"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { selectFile } from '@/utils/file'
const exportRecordApi = '/admin-center-server/oilRecharge/oilExportMoneyList'
export default {
  data() {
    return {
      search: {},
      isSearch: false,
      page: {
        pageNumber: 1,
        pageSize: 10
      },
      data: [],
      total: 0,
      isBalanceShow: false,
      isConsumeShow: false,
      isRecordShow: false,
      recordPage: {
        pageNumber: 1,
        pageSize: 10
      },
      recordTotal: 0,
      recordData: [],
      recordType: null,
      operateTime1: '',
      operateTime2: '',
      topData: '',
    }
  },
  activated() {
    this.getList()
    this.getTime()
    this.getChannelOilCardMoney()
  },
  methods: {
    getList() {
      let params = { ...this.page }
      if (this.isSearch) {
        Object.assign(params, this.search)
      }
      this.$post('/admin-center-server/oilRecharge/oilMoneyList', params)
        .then(res => {
          this.data = res.list
          this.total = Number(res.total)
        })
    },

    getChannelOilCardMoney() {
      this.$get('/admin-center-server/oilRecharge/getChannelOilCardMoney').then((res) => {
        this.topData = res
      })
    },

    doSearch() {
      this.page.pageNumber = 1
      this.isSearch = true
      this.getList()
    },
    reset() {
      this.search = {}
      this.page.pageNumber = 1
      this.isSearch = false
      this.getList()    
    },
    handleSizeChange(v) {
      this.page.pageSize = v
      this.getList()
    },
    handleRecordSizeChange(v) {
      this.recordPage.pageSize = v
      this.getList()
    },
    exportFile(type) {
      let api = type === 1 ? '/admin-center-server/oilRecharge/exportOilExportMoney' : '/admin-center-server/oilRecharge/exportOilConsumeMoney'
      selectFile({
        accept: '.xls,.xlsx'
      })
        .then(file => {
          let formData = new FormData()
          formData.append('file', file)
          this.$post(api, formData)
            .then(res => {
              this.isBalanceShow = false
              this.isConsumeShow = false
              this.getList()
              this.getTime()
              this.getChannelOilCardMoney()
              this.$message.success('文件上传成功')
            })
        })
    },
    viewRecord(type) {
      this.recordType = type
      this.recordPage.pageNumber = 1
      this.getRecordList()
      this.isRecordShow = true
    },
    getRecordList() {
      let type = this.recordType === 1 ? 0 : 1
      let params = {
        ...this.recordPage,
        exportType: type
      }
      this.$post(exportRecordApi, params)
        .then(res => {
          this.recordData = res.list
          this.recordTotal = Number(res.total)
        })
    },
    downloadFile(url) {
      window.open(url)
    },
    toList(type, userId) {
      let path = type === 1 ? '/finance/associated/recharge' : '/finance/associated/expend'
      this.$router.push(path + '?userId=' + userId)
    },
    getTime() {
      this.$post(exportRecordApi, {
        pageNumber: 1,
        pageSize: 1,
        exportType: 0
      })
        .then(res => {
          if (res.list.length > 0) {
            this.operateTime1 = res.list[0].createTime.slice(0, 10)
          } else {
            this.operateTime1 = ''
          }
        })
      
      this.$post(exportRecordApi, {
        pageNumber: 1,
        pageSize: 1,
        exportType: 1
      })
        .then(res => {
          if (res.list.length > 0) {
            this.operateTime2 = res.list[0].createTime.slice(0, 10)
          } else {
            this.operateTime2 = ''
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.list-box {
  background-color: #ffffff;
  margin: 20px;
  padding: 10px;

  .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
          font-size: 14px;
          height: 30px;
          line-height: 30px;
      }
  }

  .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
  }

  .releaseMessage {
      margin-right: 20px;
  }

  .pagination {
      text-align: right;
      margin-top: 10px;
  }
}
.btns {
  text-align: right;
}
.btn {
  display: inline-block;
  margin-left: 30px;
  text-align: center;
  > div {
    margin-top: 10px;
    font-size: 14px;
    color: #ED3E3C;
  }
}
.link {
  color: #169BD5;
}
.global-div-search {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    /* font-size: 14px; */
    /* color: #555; */
    position: relative;
}

.my_card {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  font-size: 20px;
  font-weight: bold;
  color: #606266;
  position: relative; 
  border: 1px solid #ebeef5;
  border-radius: 4px;
  transition: box-shadow 0.25s;

  .bottom {
    position: absolute;
    bottom: 10px;
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-size: 14px;
    font-weight: 300;
  }
}
.my_card:hover, .mhy_card:active {
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}
</style>