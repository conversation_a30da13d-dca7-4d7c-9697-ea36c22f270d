<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">筛选查询</div>
      <div class="select-info">
        <el-form :inline="true"
                 :model="formInline"
                 ref="formInline"
                 class="demo-form-inline"
                 size="mini"
                 label-width="90px">
          <el-form-item label="合同名称:"
                        prop='name'>
            <el-input v-model="formInline.name"
                      placeholder="请输入合同名称"></el-input>
          </el-form-item>
          <el-form-item label="合同编号:"
                        prop='tel'>
            <el-input v-model="formInline.tel"
                      placeholder="请输入合同编号"></el-input>
          </el-form-item>
          <el-form-item label="甲方名称:"
                        prop='firstParty'>
            <el-input v-model="formInline.firstParty"
                      placeholder="请输入甲方名称"></el-input>
          </el-form-item>
          <el-form-item label="乙方名称:"
                        prop='partyB'>
            <el-input v-model="formInline.partyB"
                      placeholder="请输入乙方名称"></el-input>
          </el-form-item>
          <!-- <el-form-item label="合同状态："
                        label-width="100px">
            <el-select v-model="formInline.contractStatus"
                       placeholder="请选择"
                       clearable
                       label-width="10px">
              <el-option v-for="(item, lan) in statusWrap"
                         :label="item.name"
                         :value="item.id"></el-option>
            </el-select>
          </el-form-item> -->

          <el-form-item>
            <el-button class="left"
                       icon="el-icon-search"
                       @click="onSubmit">查询</el-button>
            <el-button class="left"
                       @click="resetForm('formInline')"
                       icon="el-icon-refresh-right">重置</el-button>
            <el-button class="left"
                       @click="refreshfn"
                       icon="el-icon-refresh-right">刷新</el-button>
            <el-button class="left"
                       icon="el-icon-plus"
                       @click="addnewfn">添加</el-button>

          </el-form-item>
        </el-form>

      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>客户列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table ref="multipleTable"
                    :data="tableData"
                    
                    tooltip-effect="dark"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                    border>
            <el-table-column type="selection"
                             width="55"></el-table-column>
            <el-table-column type="index"
                             label="序号"
                             width="55">
            </el-table-column>
            <el-table-column show-overflow-tooltip
                             v-for="item in tableLabel"
                             :label="item.label">
              <template slot-scope="scope">
                <span style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
              </template>
            </el-table-column>

            <el-table-column fixed="right"
                             label="操作">
              <template slot-scope="scope">
                <el-button @click="goDetail(scope.row)"
                           type="text"
                           size="small">查看</el-button>
                <el-button @click="goEdit(scope.row)" type="text" size="small">编辑</el-button>
                <el-button @click="del(scope.row)" type="text" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page="pageNumber"
                         :page-sizes="[10,20, 40, 60, 80,100]"
                         :page-size="pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total"
                         class="pagination"></el-pagination>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
const queryContractList = '/admin-center-server/contract/queryContractList'//列表
export default {
  name: "CarsList",
  data () {
    return {
      tableHeight: null, //表格的高度
      tableLabel: [
        {
          prop: 'no',
          label: '合同编号',
          width: 200
        },
        {
          prop: 'name',
          label: '合同名称'
        },
        {
          prop: 'partyA',
          label: '甲方名称'
        },
        {
          prop: 'partyB',
          label: '乙方名称'
        },
        // {
        //   prop: 'status',
        //   label: '合同状态'
        // },
        // {
        //   prop: 'signingDate',    // 2020年3月8号产品说这个去掉
        //   label: '签约日期'
        // },
        {
          prop: 'ljExpirationDate',
          label: '合同有效期'
        },
        {
          prop: 'entryPerson',
          label: '合同录入人'
        }
      ],
      statusWrap: [
        {
          name: '未审核',
          id: '1'
        },
        {
          name: '审核通过',
          id: '2'
        },
        {
          name: '拒绝通过',
          id: '3'
        }
      ],
      pageSize: 10,
      total: 0,
      pageNumber: 1,
      formInline: {
        name: "",
        tel: "",
        contractStatus: '',
        partyB: '',
        firstParty: ''
      },
      tableData: []
    };
  },
  methods: {
    // 刷新当前页
    refreshfn () {
      this.$router.go(0)
    },
    //重置
    resetForm (formName) {
      this.$refs[formName].resetFields();
      this.pageNumber = 1
      this.getData()
    },
    onSubmit () {
      this.pageNumber = 1
      this.getData()
    },
    goDetail (row) {
      this.$router.push("/contract/contractDetiles?id=" + row.id);
    },
    goEdit(row) {
      this.$router.push("/contract/addContract?id=" + row.id)
    },
    del(row) {
      this.$confirm('确定要删除吗')
        .then(res => {
          this.$http.post('/admin-center-server/contract/deleteByContractIds?ids=' + row.id)
            .then(res => {
              if (res.data.code === '200') {
                this.$message.success('删除成功')
                this.getData()
              }
            })
        })
    },
    addnewfn () { this.$router.push("/contract/addContract"); },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },

    handleSizeChange (val) {
      this.pageNumber = 1
      this.pageSize = val
      this.getData()
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange (val) {
      this.pageNumber = val
      this.getData()
      console.log(`当前页: ${val}`);
    },
    getData () {
      var v = this.formInline
      var data = {
        name: v.name,
        no: v.tel,
        status: v.contractStatus,
        partyB: v.partyB,
        partyA: v.firstParty,
        pageNumber: this.pageNumber,
        pageSize: this.pageSize
      }
      this.$http.post(queryContractList, data).then(res => {
        this.tableData = res.data.data.list
        this.total = Number(res.data.data.total)
      })
      console.log(data)
    }
  },
  activated () {
    this.getData()
    this.tableHeight =
      window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 160;
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
