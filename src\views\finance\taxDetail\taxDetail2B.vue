<template>
  <div class="app-container">
    <div class="main-box">
      <div class="base-info">
        <div class="title-box">
          <div>基本信息</div>
          <slot></slot>
        </div>
        <div class="list-box">
          <detail-row>
            <detail-col :span="6" label="订单号" :value="data.ddbh || '-'">
              <template>
                <el-button type="text" @click="$router.push(`/order/orderManage/orderDetail?orderBusinessId=${data.orderBusinessId}&status=${data.orderBusinessStatus}&statusEnum=${data.statusEnum}&type=${data.orderBusinessType}`)">{{ data.ddbh }}</el-button>
              </template>
            </detail-col>
            <detail-col :span="6" label="运单号" :value="data.sn || '-'">
              <template>
                <el-button type="text" @click="$router.push(`/transport/transportListDetail?orderItemId=${data.orderItemId}&status=${data.orderBusinessType}`)">{{ data.dduuid }}</el-button>
              </template>
            </detail-col>
          </detail-row>
          <detail-row>
            <detail-col :span="6" label="发布方唯一标识" :value="data.fbfuuid || '-'"></detail-col>
            <detail-col :span="6" label="发布方企业名称" :value="data.fbfnsrmc || '-'">
              <template>
                <!-- <el-button type="text" @click="$router.push('/consignorAccount/accountDetails?id='+data.)">{{ data.fbfnsrmc }}</el-button> -->
              </template>
            </detail-col>
            <detail-col :span="12" label="发布方企业纳税人识别号" :value="data.fbfnsrsbh || '-'"></detail-col>
          </detail-row>
          <detail-row>
            <detail-col :span="6" label="服务方唯一标识" :value="data.fwfuuid || '-'"></detail-col>
            <detail-col :span="6" label="服务方企业名称" :value="data.fwfnsrmc || '-'">
              <!-- <template>
                <el-button type="text" @click="$router.push(`/driverAccount/accountDetails?id=${data.fwfuuid}&type=5`)">{{ data.fwfnsrmc }}</el-button>
              </template> -->
            </detail-col>
            <detail-col :span="12" label="服务方企业纳税人识别号" :value="data.fwfnsrsbh || '-'"></detail-col>
          </detail-row>
        </div>
      </div>
      <div class="base-info">
        <div class="title-box">
          <div>运输信息</div>
          <slot></slot>
        </div>
        <div class="list-box">
          <detail-row>
            <detail-col :span="6" label="发单时间" :value="data.fdsj || '-'"></detail-col>
            <detail-col :span="6" label="接单时间" :value="data.jdsj || '-'"></detail-col>
            <detail-col :span="6" label="发车时间" :value="data.qsfwsj || '-'"></detail-col>
            <detail-col :span="6" label="到达时间" :value="data.jsfwsj || '-'"></detail-col>
            <detail-col :span="6" label="结束时间" :value="data.jssj || '-'"></detail-col>
          </detail-row>
          <detail-row>
            <detail-col :span="12" label="装车点" :value="data.sfdxxdz || '-'"></detail-col>
            <detail-col :span="12" label="卸车点" :value="data.mddxxdz || '-'"></detail-col>
          </detail-row>
          <detail-row>
            <detail-col :span="12" label="装车凭证" class="img-view">
              <img v-for="(item,index) in data.thdfj" :src="item" @click="() => showedPic = item" class="thumbnail" :key="index">
            </detail-col>
            <detail-col :span="12" label="卸车凭证" class="img-view">
              <img v-for="(item,index) in data.rkdfj" :src="item" @click="() => showedPic = item" class="thumbnail" :key="index">
            </detail-col>
          </detail-row>
          <detail-row>
            <detail-col :span="6" label="运输距离" :value="data.lc?data.lc+'km':''"></detail-col>
            <detail-col :span="6" label="承运车辆" :value="data.cycph">
              <template>
                <el-button v-if="data.carId" type="text" @click="$router.push('/carsList/carDetail?carId='+data.carId)">{{ data.cycph }}</el-button>
                <span v-else>{{ data.cycph }}</span>
              </template>
            </detail-col>
            <detail-col :span="6" label="货物名称" :value="data.hwmc || '-'"></detail-col>
            <detail-col :span="6" label="货物重量" :value="data.hwzl + data.hwdw"></detail-col>
          </detail-row>
        </div>
      </div>
      <div class="base-info">
        <div class="title-box">
          <div>费用信息</div>
          <slot></slot>
        </div>
        <div class="list-box">
          <detail-row>
            <detail-col :span="6" label="订单金额" :value="data.fbfddje?data.fbfddje+'元':''"></detail-col>
            <detail-col :span="6" label="发布方付款凭证">
              <template>
                <el-button v-if="data.fkpzfj" type="text" @click="() => showedPic = data.fkpzfj">查看</el-button>
                <span v-else>-</span>              
              </template>
            </detail-col>
            <detail-col :span="6" label="运费金额" :value="data.fwfddje?data.fwfddje+'元':''"></detail-col>
            <detail-col :span="6" label="服务方付款凭证">
              <template>
                <el-button v-if="data.skpzfj" type="text" @click="() => showedPic=data.skpzfj">查看</el-button>
                <span v-else>-</span>
              </template>
            </detail-col>
            <detail-col :span="6" label="单价" :value="data.jydj?data.jydj+'元':''"></detail-col>
          </detail-row>
        </div>
      </div>
      
      <div class="base-info">
        <div class="title-box">
          <div>合同信息</div>
          <slot></slot>
        </div>
        <div class="list-box">
          <detail-row>
            <detail-col :span="6" label="发布方合同编号" :value="data.wtfhtbh || '-'"></detail-col>
            <detail-col :span="6" label="发布方合同" :value="data.qsfwsj">
              <template>
                <el-button type="text" @click="openFn(data.wtdhtfj)" :disabled="!data.wtdhtfj">查看</el-button>
              </template>
            </detail-col>
          </detail-row>
          <detail-row>
            <detail-col :span="6" label="服务方合同编号" :value="data.tyfhtbh || '-'"></detail-col>
            <detail-col :span="6" label="服务方合同" :value="data.jsfwsj">
              <template>
                <el-button type="text" @click="openFn(data.tydhtfj)" :disabled="!data.tydhtfj">查看</el-button>
              </template>
            </detail-col>
          </detail-row>
        </div>
      </div>
    </div>
    <el-image-viewer v-if="showedPic" :url-list="[showedPic]" :on-close="() => showedPic = null"/>
  </div>
</template>

<script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer"
import { data } from "jquery";
  export default {
    components: { ElImageViewer },
    created() {
      this.$get('/admin-center-server/tax/getDaxxToBDetail?id=' + this.$route.query.taxId)
        .then(res => {
          this.data = res
        })
    },
    data() {
      return {
        data: {},
        showedPic: null,
      }
    },
    methods: {
      showFn (url) {
        this.showedPic = url
      },
      openFn(url){
        window.open(url)
      }
    },
  }
</script>
<style scoped lang="scss">
.main-box {
  background-color: white;
  .title-box {
    font-size: 16px;
    font-weight: 700;
    height: 40px;
    line-height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid #cccccc;
  }
  .list-box {
    margin-top: 20px;
    //border: 1px solid #cccccc;
    border-left: none;
    .item-title {
      display: flex;
      flex-direction: row;
      div {
        width: 500px;
        height: 50px;
        font-weight: bold;
        font-size: 16px;
        line-height: 50px;
        border: 1px solid #cccccc;
        border-top: none;
        border-right: none;
        background-color: rgb(249, 252, 250);
        text-align: center;
      }
    }
    .item-info {
      display: flex;
      flex-direction: row;
      div {
        font-size: 14px;
        width: 500px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        border: 1px solid #cccccc;
        border-top: none;
        border-right: none;
        border-bottom: none;
      }
    }
  }
  .base-info {
    padding: 20px;
  }
}
.upload-wrapper {
  width: 100% !important;
}
</style>