<template>
  <div class="cargoOwnerMenu">
    <div class="wrapper">
      <el-button @click="add" class="add-btn">新增</el-button>
      <el-tree
        :data="data"
        default-expand-all
        node-key="id"
        ref="tree"
        highlight-current
        :props="defaultProps"
        :expand-on-click-node="false">
        <div class="custom-tree-node" slot-scope="{ node, data}">
          <div class="custom-label">
            <i v-if="data.type === '0'" class="el-icon-folder"></i>
            <i v-else-if="data.type === '1'" class="el-icon-document"></i>
            <!-- <i v-else-if="data.type === '2'" class="el-icon-setting"></i> -->
            <i v-else-if="data.type === '2'" class="el-icon-thumb"></i>
            {{ node.label }}
          </div>
          <div class="custom-btns">
            <el-button @click="add(data)" type="text" size='mini'>新增</el-button>
            <el-button @click="edit(data)" type="text" size='mini'>编辑</el-button>
            <el-button @click="del(data)" type="text" size='mini'>删除</el-button>
          </div>
        </div>
      </el-tree>
    </div>
    <el-dialog
      :visible.sync="isEditShow"
      :title="editType === 'add' ? '新增' : '编辑'"
      width="600px">
      <el-form
        label-width="100px"
        class="edit-form">
        <el-form-item label="上级目录">
          <!-- {{ parent.name }} -->
          <el-select :value="treeSelectorValue" ref="treeSelector">
            <el-option value="">
              <el-tree
                :data="data"
                default-expand-all
                node-key="id"
                ref="tree"
                highlight-current
                :props="defaultProps"
                :expand-on-click-node="false"
                @node-click="handleSelectorTreeClick">
                <div class="custom-tree-node" slot-scope="{ node, data}">
                  <div class="custom-label">
                    <i v-if="data.type === '0'" class="el-icon-folder"></i>
                    <i v-else-if="data.type === '1'" class="el-icon-document"></i>
                    <i v-else-if="data.type === '2'" class="el-icon-setting"></i>
                    {{ node.label }}
                  </div>
                </div>
              </el-tree>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-radio-group v-model="form.type">
            <el-radio label="1">菜单</el-radio>
            <el-radio label="0">目录</el-radio>
            <el-radio label="2">按钮</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="标题">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <template v-if="isMenu">
          <el-form-item label="地址">
            <el-input v-model="form.path"></el-input>
          </el-form-item>
          <el-form-item label="组件路径">
            <el-input v-model="form.component"></el-input>
          </el-form-item>
          <el-form-item label="名称">
            <el-input v-model="form.pageName"></el-input>
          </el-form-item>
          <el-form-item label="跳转地址">
            <el-input v-model="form.redirect"></el-input>
          </el-form-item>
          <el-form-item label="激活的菜单">
            <el-input v-model="form.activeMenu"></el-input>
          </el-form-item>
          <el-form-item label="不开启缓存">
            <el-switch v-model="form.noCache"></el-switch>
          </el-form-item>
          <el-form-item label="在菜单隐藏">
            <el-switch v-model="form.hidden"></el-switch>
          </el-form-item>
        </template>
        <template v-if="isCatalog || isMenu">
          <el-form-item label="排序">
            <el-input v-model="form.sort"></el-input>
          </el-form-item>
          <el-form-item label="图标名">
            <el-input v-model="form.icon"></el-input>
          </el-form-item>
        </template>
        <el-form-item>
          <el-button @click="submit" type="primary">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
const formDefault = {
  type: '1'
}
export default {
  data() {
    return {
      data: [],
      defaultProps: {
        label: 'name'
      },
      isEditShow: false,
      editType: '', //add新增 edit编辑
      parent: {},
      form: {},
      treeSelectorValue: ''
    }
  },
  created() {
    this.getList()
  },
  computed: {
    isButton() {
      return this.form.type === '2'
    },
    isMenu() {
      return this.form.type === '1'
    },
    isCatalog() {
      return this.form.type === '0'
    }
  },
  methods: {
    getList() {
      this.$get('/admin-center-server/user/resources/findAll')
      .then(res => {
        this.data = res
      })
    },
    findItemFromList(arr, id) {
      for (let i of arr) {
        if (i.id === id) return i
        if (i.children) {
          let item = this.findItemFromList(i.children, id)
          if (item) return item
        }
      }
    },
    add(parent) {
      this.editType = 'add'
      //新增一级
      if (!parent) {
        this.form = { ...formDefault }
      } else {
        this.form = {
          ...formDefault,
          parentId: parent.id
        }
      }
      this.parent = parent
      this.treeSelectorValue = parent.name
      this.isEditShow = true
    },
    edit(data) {
      this.editType = 'edit'
      this.form = data
      let parent = this.findItemFromList(this.data, data.parentId)
      if (parent) this.treeSelectorValue = parent.name //一级没有父级
      this.isEditShow = true
    },
    submit() {
      let params = { ...this.form }
      params.userType = '1' //后端要求，必填写死
      //是否隐藏不能传空，没选传false
      if (params.hidden === undefined) {
        params.hidden = false
      }

      //按钮不填排序
      if (this.isButton && !params.sort) {
        params.sort = 0
      }

      this.$post('/admin-center-server/user/resources/save', params)
        .then(() => {
          this.$message.success('保存成功')
          this.isEditShow = false
          this.getList()
        })
    },
    del(data) {
      this.$confirm('确定要删除吗')
        .then(() => {
          this.$post('/admin-center-server/user/resources/logicalDelete', {
            id: data.id
          })
            .then(res => {
              this.$message.success('删除成功')
              this.getList()
            })
        })
      
    },
    handleSelectorTreeClick(data) {
      this.$refs.treeSelector.blur()
      this.treeSelectorValue = data.name
      this.form.parentId = data.id
    }
  }
}
</script>

<style scoped>
.cargoOwnerMenu {
  padding: 20px;
  margin: 10px;
  background-color: #fff;
}
.wrapper {
  max-width: 700px;
}
.add-btn {
  margin-bottom: 20px;
}
.custom-tree-node {
  display: flex;
  justify-content: space-between;
  width: 100%;
  font-size: 14px;
}
.custom-label {
  line-height: 28px;
}
.btn {
  margin-top: 30px;
}
.edit-form {
  width: 500px;
}
.el-select-dropdown__item {
  overflow-y: auto;
  height: 250px;
}
</style>