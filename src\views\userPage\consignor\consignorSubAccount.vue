<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">子账户查询</div>
      <div class="select-info">
        <el-form :inline="true"
                 :model="formInline"
                 ref='formInline'
                 class="demo-form-inline"
                 size="mini"
                 label-width="90px">
          <el-form-item label="所属公司:"
                        prop='name'>
            <el-input v-model="formInline.name"
                      placeholder="请输入所属公司名称"></el-input>
          </el-form-item>
          <el-form-item label="手机号:"
                        prop='tel'>
            <el-input v-model="formInline.tel"
                      oninput="value=value.replace(/[^\d]/g,'')"
                      maxlength="11"
                      placeholder="请输入子账户手机号"></el-input>
          </el-form-item>
          <el-form-item label="注册时间："
                        label-width="100px"
                        prop='date'>
            <el-col>
              <el-date-picker v-model="formInline.date"
                              value-format=""
                              type="datetimerange"
                              range-separator="至"
                              start-placeholder="选择开始时间"
                              end-placeholder="选择结束时间"
                              :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button class="left"
                       icon="el-icon-search"
                       @click="onSubmit">查询</el-button>
            <el-button class="left"
                       @click="resetForm('formInline')"
                       icon="el-icon-refresh-right">重置</el-button>

            <el-button class="left"
                       @click="refreshfn"
                       icon="el-icon-refresh-right">刷新</el-button>
            <el-button class="left"
                       icon="el-icon-plus"
                       @click="rolefn">角色管理</el-button>

          </el-form-item>
        </el-form>

      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>账户列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table ref="multipleTable"
                    :data="tableData"
                    :height="tableHeight"
                    tooltip-effect="dark"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                     cell-class-name="table_cell_gray"
                    header-cell-class-name="table_header_cell_gray"
                    border>
            <el-table-column type="selection"
                             width="55"></el-table-column>
            <el-table-column type="index"
                             label="序号"
                             width="55">
            </el-table-column>
            <el-table-column show-overflow-tooltip
                             v-for="item in tableLabel"
                             :label="item.label"
                             :width='item.width?item.width:""'>
              <template slot-scope="scope">
                <span style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
              </template>
            </el-table-column>

            <el-table-column fixed="right"
                             min-width="90"
                             label="操作">
              <template slot-scope="scope">
                <el-button @click="goDetail(scope.row)"
                           type="text"
                           size="small">查看</el-button>
                <el-button v-if='scope.row.deleteFlag=="禁用"'
                           type="text"
                           @click='enablefn(scope.row)'
                           size="small">启用</el-button>
                <el-button v-if='scope.row.deleteFlag=="启用"'
                           type="text"
                           @click='prohibitfn(scope.row)'
                           size="small">禁用</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page="pageNumber"
                         :page-sizes="[10,20, 40, 60, 80,100]"
                         :page-size="pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total"
                         class="pagination"></el-pagination>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
const list = '/admin-center-server/commonUser/sub/list'//列表
const updateUserStatus = '/admin-center-server/commonUser/updateUserStatus'//启用、禁用
import { mixins } from "@/mixin/table.js";
export default {
  name: "consignorSubAccount",
  mixins: [mixins],
  data () {
    return {
      tableHeight: null, //表格的高度
      tableLabel: [
        {
          prop: 'companyName',
          label: '所属公司',
          width: 200
        },
        {
          prop: 'mobile',
          label: '手机号（子账户）',
          width: 140
        },
        {
          prop: 'name',
          label: '名称'
        },
        {
          prop: 'roleName',
          label: '角色名称'
        },
        {
          prop: 'deleteFlag',
          label: '账户状态'
        },
        {
          prop: 'createTime',
          label: '注册时间',
          width: 180
        }
      ],
      pageSize: 10,
      total: 0,
      pageNumber: 1,
      formInline: {
        name: "",
        tel: "",
        date: ""
      },
      tableData: []
    };
  },
  methods: {
    //重置
    resetForm (formName) {
      this.$refs[formName].resetFields();
      this.formInline.date = ''
      this.pageNumber = 1
      this.getData()
    },
    // 刷新当前页
    refreshfn () {
      this.$router.go(0)
    },
    rolefn () {
      this.$router.push("/consignorSubAccount/roleManagement?type=1");
    },
    onSubmit () {
      this.pageNumber = 1
      this.getData()
    },
    goDetail (row) {
      this.$router.push("/consignorSubAccount/brokerDetails?id=" + row.id + "&type=1");
    },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },

    handleSizeChange (val) {
      this.pageNumber = 1
      this.pageSize = val
      this.getData()
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange (val) {
      this.pageNumber = val
      this.getData()
      console.log(`当前页: ${val}`);
    },
    //启用
    enablefn (row) {
      var that = this
      console.log(row)
      this.$confirm('是否启用子账户，恢复全部功能，启用子账户后，全部功能将正常使用', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          that.$http.post(updateUserStatus + `/${row.id}?deleteFlag=0`).then(res => {
            console.log(res)
            that.getData()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    //禁用
    prohibitfn (row) {
      var that = this
      console.log(row)
      this.$confirm('是否确认禁用该子账户，禁用子账户操作后，该子账户无法再登录并使用平台相关产品及服务，主账户不受影响', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          that.$http.post(updateUserStatus + `/${row.id}?deleteFlag=-1`).then(res => {
            console.log(res)
            that.getData()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    getData () {
      var v = this.formInline,
        data1 = '',
        data2 = '';
      if (v.date) {
        data1 = this.getDate(v.date[0])
        data2 = this.getDate(v.date[1])
      } else {
        data1 = ''
        data2 = ''
      }
      v.name = this.Trim(v.name, 'g')
      var url = list + `?userType=1&companyName=${v.name}&mobile=${v.tel}&page=${this.pageNumber}&pageSize=${this.pageSize}&minCreateTime=${data1}&maxCreateTime=${data2}`
      this.$http.get(url).then(res => {
        console.log(res.data.data.total)
        this.tableData = res.data.data.list
        this.total = Number(res.data.data.total)
      })

    }
  },
  activated () {
    this.getData()
    this.tableHeight =
      window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 160;
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
