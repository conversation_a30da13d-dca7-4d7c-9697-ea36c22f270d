import { getDecimalsLenth } from '@/utils/tools'
export default {
  data(){
    return {
      formRules: {
        plateNumber: [
          { required: true, message: '车牌号不能为空', trigger: 'blur' }
        ],
        affiliatedFlag: [
          {
            validator: (rule, value, cb) => {
              if (!this.isTrailer && !this.form.affiliatedFlag) {
                cb('是否挂靠不能为空')
              } else {
                cb()
              }
            },
            trigger: 'blur'
          }
        ],
        typeAndLength: [
          {
            required: true,
            validator: (rule, value, cb) => {
              if (!this.form.carLength || !this.form.carTypeId) {
                cb('车长车型不能为空')
              } else {
                if(isNaN(this.form.carLength) || isNaN(parseFloat(this.form.carLength))){
                  cb('车长请输入数字')
                }else{
                  if(this.form.carLength < 1 || this.form.carLength > 21){
                    cb('车长不能大于21米或小于1米')
                  }
                }
                cb()
              }
            },
            trigger: 'blur'
          }
        ],
        licenseOwner: [
          { required: true, message: '行驶证所有人不能为空', trigger: 'blur' }
        ],
        carModelId: [
          { required: true, message: '车辆类型不能为空', trigger: 'blur' }
        ],
        licensesUseCharacter: [
          {
            validator: (rule, value, cb) => {
              if (this.checkLighterThan(this.form.sumCapacityTonnage) && !this.form.licensesUseCharacter) {
                cb('总质量4.5吨及以下不能为空')
              } else {
                cb()
              }
            },
            trigger: 'blur'
          }
        ],
        licensesVin: [
          {
            validator: (rule, value, cb) => {
              if (this.checkLighterThan(this.form.sumCapacityTonnage) && !this.form.licensesVin) {
                cb('总质量4.5吨及以下不能为空')
              } else {
                cb()
              }
            },
            trigger: 'blur'
          }
        ],
        licensesIssueOrg: [
          {
            validator: (rule, value, cb) => {
              if (this.checkLighterThan(this.form.sumCapacityTonnage) && !this.form.licensesIssueOrg) {
                cb('总质量4.5吨及以下不能为空')
              } else {
                cb()
              }
            },
            trigger: 'blur'
          }
        ],
        licensesRegisterDate: [
          {
            validator: (rule, value, cb) => {
              if (this.checkLighterThan(this.form.sumCapacityTonnage) && !this.form.licensesRegisterDate) {
                cb('总质量4.5吨及以下不能为空')
              } else {
                cb()
              }
            },
            trigger: 'blur'
          }
        ],
        licensesIssueDate: [
          {
            validator: (rule, value, cb) => {
              if (this.checkLighterThan(this.form.sumCapacityTonnage) && !this.form.licensesIssueDate) {
                cb('总质量4.5吨及以下不能为空')
              } else {
                cb()
              }
            },
            trigger: 'blur'
          }
        ],
        licenseNumber: [
          { required: true, message: '行驶证档案编号不能为空', trigger: 'blur' }
        ],
        sumCapacityTonnage: [
          {
            validator: (rule, value, cb) => {
              if (!this.isMainTractor && !this.form.sumCapacityTonnage) {
                return cb('总质量不能为空')
              }
              let num = Number(this.form.sumCapacityTonnage)
              if (Number.isNaN(num)) {
                return cb('必须为数字')
              }
              if (getDecimalsLenth(num) > 0) {
                return cb('必须为整数')
              }
              cb()
            }
          }
        ],
        capacityTonnage: [
          {
            validator: (rule, value, cb) => {
              if (!this.isMainTractor && !this.form.capacityTonnage) {
                cb('核定载质量不能为空')
              }
              let num = Number(this.form.capacityTonnage)
              if (Number.isNaN(num)) {
                return cb('必须为数字')
              }
              if (getDecimalsLenth(num) > 0) {
                return cb('必须为整数')
              }
              cb()
            }
          }
        ],
        curbWeight: [
          { required: true, message: '整备质量不能为空', trigger: 'blur' },
          {
            validator: (rule, value, cb) => {
              let num = Number(this.form.curbWeight)
              if (Number.isNaN(num)) {
                return cb('必须为数字')
              }
              if (getDecimalsLenth(num) > 0) {
                return cb('必须为整数')
              }
              cb()
            }
          }
        ],
        tractionMass: [
          {
            validator: (rule, value, cb) => {
              if (this.isMainTractor && !this.form.tractionMass) {
                cb('准牵引总质量不能为空')
              }
              let num = Number(this.form.tractionMass)
              if (Number.isNaN(num)) {
                return cb('必须为数字')
              }
              if (getDecimalsLenth(num) > 0) {
                return cb('必须为整数')
              }
              cb()
            }
          }
        ],
        size: [
          {
            validator: (rule, value, cb) => {
              if (!this.form.carLong) {
                cb('请填写长度')
              } else if (!this.form.carWeight) {
                cb('请填写宽度')
              } else if (!this.form.carHigh) {
                cb('请填写高度')
              } else {
                cb()
              }
            },
            trigger: 'blur'
          }
        ],
        licenseExpire: [
          { required: true, message: '行驶证有效期不能为空', trigger: 'blur' }
        ],
        energyType: [
          { required: true, message: '能源类型不能为空', trigger: 'blur' }
        ],
        plateColor: [
          {
            validator: (rule, value, cb) => {
              //挂车不做校验
              if (!this.isTrailer && !this.form.plateColor) {
                cb('车牌颜色不能为空')
              } else {
                cb()
              }
            },
            trigger: 'blur'
          }
        ],
        shippingCert: [
          {
            validator: (rule, value, cb) => {
              //挂车不做校验
              if (!this.isTrailer && this.checkHeavierThan(this.form.sumCapacityTonnage) && !this.form.shippingCert) {
                cb('总质量4.5吨以上不能为空')
              } else {
                cb()
              }
            },
            trigger: 'blur'
          }
        ],
      }
    }
  },
  methods: {
    itemLicensesUseCharacterRules(item) {
      return [
        {
          validator: (rule, value, cb) => {
            if (this.checkLighterThan(item.sumCapacityTonnage) && !item.licensesUseCharacter) {
              cb('总质量4.5吨及以下不能为空')
            } else {
              cb()
            }
          },
          trigger: 'blur'
        }
      ]
    },
    itemLicensesVinRules(item) {
      return [
        {
          validator: (rule, value, cb) => {
            if (this.checkLighterThan(item.sumCapacityTonnage) && !item.licensesVin) {
              cb('总质量4.5吨及以下不能为空')
            } else {
              cb()
            }
          },
          trigger: 'blur'
        }
      ]
    },
    itemLicensesIssueOrgRules(item) {
      return [
        {
          validator: (rule, value, cb) => {
            if (this.checkLighterThan(item.sumCapacityTonnage) && !item.licensesIssueOrg) {
              cb('总质量4.5吨及以下不能为空')
            } else {
              cb()
            }
          },
          trigger: 'blur'
        }
      ]
    },
    itemLicensesRegisterDateRules(item) {
      return [
        {
          validator: (rule, value, cb) => {
            if (this.checkLighterThan(item.sumCapacityTonnage) && !item.licensesRegisterDate) {
              cb('总质量4.5吨及以下不能为空')
            } else {
              cb()
            }
          },
          trigger: 'blur'
        }
      ]
    },
    itemLicensesIssueDateRules(item) {
      return [
        {
          validator: (rule, value, cb) => {
            if (this.checkLighterThan(item.sumCapacityTonnage) && !item.licensesIssueDate) {
              cb('总质量4.5吨及以下不能为空')
            } else {
              cb()
            }
          },
          trigger: 'blur'
        }
      ]
    },
    itemSizeRules(item) {
      return [
        {
          validator: (rule, value, cb) => {
            if (!item.carLong) {
              cb('请填写长度')
            } else if (!item.carWeight) {
              cb('请填写宽度')
            } else if (!item.carHigh) {
              cb('请填写高度')
            } else {
              cb()
            }
          },
          trigger: 'blur'
        }
      ]
    },
  }
}