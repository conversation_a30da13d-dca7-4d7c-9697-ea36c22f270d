<template>
  <div>
    <div class="error-display">
      <el-collapse v-model="activeNames">
        <el-collapse-item name="1">
          <template slot="title">
            <div class="error-collapse">运单异常信息</div>
          </template>
          <div v-if="errorData.length === 0">未检查出异常</div>
          <div v-else>
              <div v-for="info of errorData" :key="info.warnItemTitle">
                <div class="error-title"><i class="el-icon-warning warn-icon"></i>{{info.warnItemTitle}}</div>
                <div class="error-detail">
                  <span v-for="errorDetail of info.warnItemDetails" :key="errorDetail" class="error-item">
                    &nbsp;<span v-for="(errorText, index) in (errorDetail || '').split('@')" :key="index" :style="{color: index%2 == 0 ? '#333' : 'red'}">
                      {{errorText}}
                    </span>
                  </span>
                </div>
              </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div class="audit">
      <div class="audit-left">运单状态：{{tableData[0].ydStatusEnum}}</div>
      <div class="audit-right">
        <div class="audit-current">当前轨迹：{{ source }}轨迹</div>
        <el-button :loading="loading" type="primary" @click="audit">通过审核</el-button>
        <el-button :loading="loading" @click="showReject">驳回</el-button>
      </div>
    </div>
    <div class="audit-top">
      <div class="top-content">
        <div class="top-title">{{tableData[0].dataFrom == 1 ? '导入运单审核' : '实时延迟付单审核'}} <span class="top-sn">{{tableData[0].sn}}</span></div>
        <div class="top-button">
          <div class="top-button-info" :class="{'top-button-select': selectIndex == 0}" @click="selectIndex = 0">基本信息</div>
          <div class="top-button-log" :class="{'top-button-select': selectIndex == 1}" @click="selectIndex = 1">运单日志</div>
        </div>
      </div>
      
    </div>
    <div class="main-box" v-show="selectIndex == 0">
      <div class="base-info">
        <div class="title-box">
          <div>
            <p>基本信息</p>
          </div>
        </div>
        <div class="list-box">
          <detail-row>
            <detail-col v-if="!$store.state.user.userInfo2.hasStandardModeFlag" :span="6" label="调度单号">
              <span class="list-order-dispatch" v-if="tableData[0].orderDispatchSn" @click="openDispatchDetail">{{tableData[0].orderDispatchSn}}</span>
              <span v-else>-</span>
            </detail-col>
            <detail-col :span="6" label="货主名称" :value="tableData[0].businessName"></detail-col>
            <detail-col :span="6" label="运单号" :value="tableData[0].sn"></detail-col>
            <detail-col :span="6" label="创建人" :value="tableData[0].orderCreateUserName"></detail-col>
            <detail-col :span="6" label="创建日期" :value="tableData[0].ydCreatedDate"></detail-col>
            <detail-col v-if="!$store.state.user.userInfo2.hasStandardModeFlag" :span="6" label="订单类型" :value="tableData[0].typeParams"></detail-col>
            <detail-col :span="6" label="所属项目" :value="orderDetail[0].projectName || '-'"></detail-col>
            <detail-col :span="6" label="运力专员" :value="tableData[0].capacityPersonName || '-'"></detail-col>
            <detail-col :span="6" label="接单司机">
              {{ tableData[0].driverName }}
              <!-- 除认证成功以外显示红色 -->
              <span v-if="tableData[0].authStatusDTO.driverAuthStatus !== '2'" class="reject-text">（{{ tableData[0].authStatusDTO.driverAuthStatusStr }}）</span>
            </detail-col>
            <detail-col :span="6" label="接单司机电话">
              {{ tableData[0].driverPhone }}
              <span v-if="tableData[0].authStatusDTO.mobileAuthStatus !== '1'" class="mobile-warn">(<span>未验证</span>)</span>
            </detail-col>
            <detail-col :span="6" label="接单车辆">
              {{ tableData[0].carNumber }}
              <span v-if="tableData[0].authStatusDTO.carAuthStatus !== '1'" class="reject-text">（{{ tableData[0].authStatusDTO.carAuthStatusStr }}）</span>
              <template v-if="tableData[0].authStatusDTO.secondCarNumber">
                -
                {{ tableData[0].authStatusDTO.secondCarNumber }}
                <span v-if="tableData[0].authStatusDTO.secondCarAuthStatus !== '1'" class="reject-text">（{{ tableData[0].authStatusDTO.secondCarAuthStatusStr }}）</span>
              </template>
            </detail-col>
            <detail-col :span="6" label="调度员" :value="tableData[0].dispatcherName || '-'"></detail-col>
            <detail-col :span="6" label="调度员手机" :value="tableData[0].dispatcherPhone || '-'"></detail-col>
            <detail-col :span="6" label="运力供应商" :value="tableData[0].brokerMasterName || '-'"></detail-col>

            <detail-col :span="6" label="车辆所有人">
              {{ tableData[0].licenseOwner }} 
              <el-tooltip v-if="tableData[0].licenseOwner === tableData[0].businessName" placement="bottom">
                <i class="el-icon-warning-outline owner-icon" style="color: #ED970F"></i>
                <template slot="content">
                  系统检测到“车辆所有人”与“货主名称”相同，请注意审核
                </template>
              </el-tooltip>
            </detail-col>
            <detail-col :span="6" label="车辆类型" :value="tableData[0].carTypeStr"></detail-col>
            <detail-col :span="6" label="车辆载重">
              <template v-if="tableData[0].authStatusDTO.secondCapacityTonnage">
                {{ tonFormatter(tableData[0].authStatusDTO.secondCapacityTonnage) }}
              </template>
              <template v-else>
                {{ tonFormatter(tableData[0].capacityTonnage) }}
              </template>
            </detail-col>
            <!-- <detail-col :span="6" label="含税运费单价" :value="unitMoneyFormatter(tableData[0].freightCalcType, tableData[0].taxFreight)"></detail-col> -->
            <detail-col :span="6" label="运费单价" :value="unitMoneyFormatter(tableData[0].freightCalcType, tableData[0].freight)"></detail-col>
            <detail-col :span="6" label="运单状态" :value="tableData[0].ydStatusEnum"></detail-col>
            <detail-col :span="6" label="运输距离" :value="kmFormatter(tableData[0].actualDistance)"></detail-col>
            <!-- <detail-col :span="6" label="最久待装车" :value="tableData[0].overTimeStr"></detail-col> -->
            <detail-col :span="6" label="货物名称">
              {{ tableData[0].cargoTypeClassificationValue + ' / ' + tableData[0].cargoType }}
            </detail-col>
            <!-- <detail-col :span="6" label="与货主约定运费" v-if="tableData[0].operationalPeoFlag === '1'" :value="unitMoneyFormatter(tableData[0].freightCalcType, tableData[0].platformConsignorFreight)"></detail-col> -->
            <detail-col :span="6" label="在线签约状态">
              {{ tableData[0].onLineSigningStatus }} <el-button type="text" v-if="tableData[0].signingUrl" @change="openUrl">（电子合同）</el-button>
            </detail-col>
            <detail-col :span="6" label="合作平台主体" :value="(tableData[0].baseName)"></detail-col>
          </detail-row>
        </div>
      </div>
      <div class="drive-info">
        <div class="title-box">
          <div>收发信息</div>
        </div>
        <div v-if="tableData[0].operationalPeoFlag != 1" class="list-box">
          <detail-row>
            <detail-col :span="6" label="装车点" :value="tableData[0].deliveryPlace"></detail-col>
            <detail-col :span="6" label="发货联系人" :value="tableData[0].consignerName"></detail-col>
            <detail-col :span="6" label="发货人电话" :value="tableData[0].consignerPhone"></detail-col>
            <detail-col :span="6" label="装货时间" :value="tableData[0].gpsStartTime">
              {{tableData[0].gpsStartTime}}{{tableData[0].loadingCheckInType ? '(' + tableData[0].loadingCheckInType + ')' : ''}}
            </detail-col>
            <detail-col :span="6" label="卸货点" :value="tableData[0].receivePlace"></detail-col>
            <detail-col :span="6" label="收货联系人" :value="tableData[0].consigneeName"></detail-col>
            <detail-col :span="6" label="收货人电话" :value="tableData[0].consigneePhone"></detail-col>
            <detail-col :span="6" label="卸货时间" :value="tableData[0].gpsEndTime">
              {{tableData[0].gpsEndTime}}{{tableData[0].unloadCheckInType ? '(' + tableData[0].unloadCheckInType + ')' : ''}}
            </detail-col>
          </detail-row>
        </div>
        <OperaterItemDetail v-else :tableData="tableData"></OperaterItemDetail>
      </div>
      <div class="drive-info">
        <div class="title-box">
          <div>行驶轨迹
            <el-tooltip content="地图默认展示轨迹逻辑为：依次判断不同类型的轨迹（三方、鸿飞达设备、北斗、APP轨迹）是否存在轨迹点，存在即展示当前轨迹，与轨迹点的多少无关">
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </div>
        </div>
        <div class="track-tool">
          <div class="track-selector">
            选择地图展示轨迹：
            <el-radio-group v-model="trackType" @change="trackTypeChange">
              <el-radio label="0">北斗设备轨迹</el-radio>
              <el-radio label="2">鸿飞达设备轨迹</el-radio>
              <el-radio label="4">APP轨迹</el-radio>
              <el-radio label="5">三方轨迹</el-radio>
            </el-radio-group>
          </div>
          <div class="track_operate">
            <span class="load_track_button" @click="loadAmapTrack"></span>
            <span class="source">轨迹来源：{{ source }}</span>
          </div>
        </div>
        <div v-if="isMapFail" class="no-map-warn">暂无轨迹数据</div>
        <Track v-else ref="track" @error="isMapFail = true" @source="getSource" :isAuditDetail="true"></Track>
      </div>
      <div class="driver-info">
        <div class="title-box">
          <div>费用信息</div>
        </div>
        <costInfo v-if="tableData[0].operationalPeoFlag !== '1'" :tableData="tableData" :ruleData="ruleData"></costInfo>
        <cost-operation-info v-else :tableData="tableData" :ruleData="ruleData" :orderDetail='orderDetail'></cost-operation-info>
        <!-- <div class="list-box">
          <detail-row>
            <detail-col :span="6" v-if="tableData[0].freightCalcType == 0" label="计费规则" :value="tableData[0].ruleName">
              {{tableData[0].ruleName}}
              <el-tooltip placement="top">
                <i class="el-icon-warning-outline owner-icon"></i>
                <template #content>
                  <div v-for="(item, index) in ruleData" :key="item.id" class="rule">
                    <div>{{ index + 1 }} 、{{ item.name }}</div>
                    <div>{{ item.rormulatian }}</div>
                  </div>
                </template>
              </el-tooltip>
            </detail-col>
            <detail-col :span="6" label="抢单吨数（吨）" :value="tableData[0].ton"></detail-col>
            <detail-col :span="6" label="装车吨数（吨）">
              {{ tableData[0].originalTon }}
              <span v-if="tableData[0].vehicleWeightLimitValue" class="warn">（限重规则：上浮{{ tableData[0].vehicleWeightLimitValue }}{{ tableData[0].vehicleWeightLimitWay === '0' ? '吨' : '%'}}）</span>
            </detail-col>
            <detail-col :span="6" label="卸货吨数（吨）">
              {{ tableData[0].currentTon }}
              <span v-if="tableData[0].vehicleWeightLimitValue" class="warn">（限重规则：上浮{{ tableData[0].vehicleWeightLimitValue }}{{ tableData[0].vehicleWeightLimitWay === '0' ? '吨' : '%'}}）</span>
            </detail-col>
            <detail-col :span="6" label="结算吨数（吨）" :value="tableData[0].paymentTon"></detail-col>
            <detail-col :span="6" label="收款方姓名" :value="tableData[0].agentName"></detail-col>
            <detail-col :span="6" label="收款方手机号" :value="tableData[0].agentMobile"></detail-col>
            <detail-col :span="6" label="收款方平安子账户号" :value="tableData[0].agentSubAcctNo"></detail-col>
            <detail-col :span="6" label="付款方式" :value="tableData[0].paymentTypeParams"></detail-col>
            <detail-col :span="6" label="司机运费（元）" :value="tableData[0].actualDriverAmount">
              {{tableData[0].actualDriverAmount}}
              <el-tooltip placement="top" v-if="tableData[0].freightCalcType == 0">
                      <i class="el-icon-warning-outline owner-icon"></i>
                      <template #content>计算公式：
                        {{ tableData[0].freight }} * {{ tableData[0].payTon }}
                  
                        <template v-if="tableData[0].shortTonDeductPrice"> - {{ tableData[0].shortTonDeductPrice }}</template>
                        <template v-if="tableData[0].cutPayment"> - {{ tableData[0].cutPayment }}</template>
                        <template v-if="tableData[0].compensationPrice"> + {{ tableData[0].compensationPrice }}</template>
                        <template v-if="tableData[0].roundOffNumber"> - {{ tableData[0].roundOffNumber }}</template>
                     </template>
                    </el-tooltip>
            </detail-col>
          </detail-row>
          <el-table border :data="payTableData" :cell-style="{ 'text-align': 'center' }">
            <el-table-column label="支付类型">
              <template slot-scope="scope">
                <template v-if="scope.$index === 0">装货付<span v-if="!$store.state.user.userInfo2.hasStandardModeFlag">（油费）</span></template>
                <template v-else-if="scope.$index === 1">卸货付</template>
                <template v-else-if="scope.$index === 2">结算付</template>
              </template>
            </el-table-column>
            <el-table-column label="支付时间" prop="paymentTime" :formatter="valueFormatter"></el-table-column>
            <el-table-column label="支付司机运费（元）" prop="amountToDriver" :formatter="valueFormatter"></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="其中油费（元）" prop="oilCost" :formatter="valueFormatter"></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="油费比例" prop="oilRatioStr" :formatter="valueFormatter"></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="支付服务费（元）" prop="actualPlatformServiceAmount" :formatter="valueFormatter"></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="服务费比例" prop="actualPlatformServiceRatio" :formatter="percentFormatter"></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="支付总额" prop="paySum" :formatter="valueFormatter"></el-table-column>
          </el-table>
        </div> -->
      </div>
      <div class="other-info" v-if="tableData[0].operationalPeoFlag != 1">
              <div class="title-box">
                <div>收货信息</div>
              </div>
              <div class="list-box">
                <detail-row>
                  <detail-col :span="12" :label="tableData[0].freightCalcType === '0' ? '装货磅单' : '装货凭证'" class="img-view">
                    <img v-for="item in loadUrlList" :src="item" :key="item" @click="() => showedPic = item" class="thumbnail">
                  </detail-col>
                  <detail-col :span="12" :label="tableData[0].freightCalcType === '0' ? '卸货磅单' : '卸货凭证'" class="img-view">
                    <img v-for="item in unloadUrlList" :src="item" :key="item" @click="() => showedPic = item" class="thumbnail">
                  </detail-col>
                </detail-row>
                <detail-row>
                  <detail-col label="装货吨数" :value="tableData[0].originalTon?tableData[0].originalTon + '吨':'-'"></detail-col>
                  <detail-col label="卸货吨数" :value="tableData[0].currentTon ? tableData[0].currentTon +'吨':''"></detail-col>
                  <detail-col v-if="tableData[0].freightCalcType == 0" label="结算吨数" :value="tableData[0].payTon?tableData[0].payTon + '吨':'-'"></detail-col>
                </detail-row>
              </div>
            </div>

      <div class="driver-info" v-if="tableData[0].operationalPeoFlag != 1">
        <div class="title-box">
          <div>运单备注</div>
        </div>
        <div class="list-box">
          <detail-row>
            <detail-col :span="24" label="运单备注" :value="tableData[0].memo"></detail-col>
          </detail-row>
        </div>
      </div>
    </div>
    <div class="main-box" v-show="selectIndex == 1">
      <LogList :listData="orderItemLogList"></LogList>
    </div>
    <!--  :visible.sync="isRejectDialogShow" -->
    <el-dialog
      :visible.sync="isRejectDialogShow"
      title="驳回原因"
      width="700px">
      <el-form :model="form" label-width="120px">
        <el-form-item label="请选择驳回原因">
          <el-checkbox-group v-model="rejectSelectedReason">
            <div v-for='item in rejectReasonList' :key="item.id">
              <el-checkbox :label="item" >{{ item.content }}</el-checkbox>
            </div>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="其他原因">
          <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入其他驳回原因"
            v-model="rejectOtherReason"
            resize="none">
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="isRejectDialogShow = false">取消</el-button>
          <el-button type="primary" @click="reject">确认驳回</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-image-viewer v-if="showedPic"
      :on-close="handlePicClose"
      :url-list="[showedPic]"/>
  </div>
</template>
<script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer"
import { Big } from 'big.js'
import Track from '@/components/Track/Track.vue'
import LogList from './transportAuditLog.vue'
import costInfo from '../components/costInfo.vue'
import costOperationInfo from '../components/costOperationInfo.vue';
import OperaterItemDetail from '@/components/OrderDetail/OperaterItemDetail.vue'
export default {
  components: { ElImageViewer, Track, LogList,costInfo,costOperationInfo, OperaterItemDetail },
  data() {
    return {
      selectIndex: 0,
      isRejectDialogShow: false,
      rejectReasonList: [],
      orderItemLogList: [],
      form: {},
      loading: false,
      tableData: [{}],
      orderDetail: [{}],
      showedPic: null,
      rejectSelectedReason: [],
      rejectOtherReason: '',
      isMapFail: false,
      source: '',
      payTableData: [],
      isNavigation: false, // 轨迹来源
      ruleData: [],
      errorData: [],
      activeNames: ['1'],
      trackType: ''
    }
  },
  activated() {
    let orderItemId = this.$route.query.orderItemId;
    this.$get("/admin-center-server/orderItem/getOrderItemDetail", {
      OrderItemId: orderItemId //后台给的key 首字母是大写
    })
    .then(res => {
      this.tableData = res.orderItemByOrderBrokerDetail
      this.orderDetail = res.orderItemByOrderBusinessDetail
      this.orderItemLogList = res.orderItemLogList
      let from = res.orderItemByOrderBrokerDetail[0].dataFrom
      let rejectCode = from === '0' ? 8 : 9
      this.$post('/order-center-server/app/reason/queryReasonByType?type=' + rejectCode)
        .then(res => {
          this.rejectReasonList = res
        })
      this.$nextTick(() => {
        this.showTrack()
      })
      let title
      if (from !== '0') {
        title = '导入运单审核'
      } else {
        if (this.tableData[0].paymentTypeParams === '预付款') {
          title = '实时预付单审核'
        } else {
          title = '实时延迟付单审核'
        }
      }
      document.title = title + '-' + this.tableData[0].sn

      if (!this.tableData[0].invoiceOrderItemDetails) return
      let payTableData = [...this.tableData[0].invoiceOrderItemDetails]

      //按照装货、卸货、结算排序
      let _payTableData = []
      ;['20', '3', '6'].forEach(v => {
        let item = payTableData.find(item => item.orderItemStatus === v)
        if (!item) return
        _payTableData.push(item)
      })
      payTableData = _payTableData

      let countSum = key => {
        let sum = payTableData.reduce((prev, current) => {
          return Big(prev).plus(current[key] || 0).toNumber()
        }, 0)
        return '合计：' + String(sum)
      }
      payTableData.push({
        amountToDriver: countSum('amountToDriver'),
        oilCost: countSum('oilCost'),
        actualPlatformServiceAmount: countSum('actualPlatformServiceAmount'),
        paySum: countSum('paySum')
      })
      this.payTableData = payTableData

      this.$get(`/admin-center-server/rule/getRuleRormulatianById?id=${this.tableData[0].ruleId}&isOperated=${this.tableData[0].operationalPeoFlag}`).then(
          res => {
            this.ruleData = res
          }
        )
    })
    this.$get("/admin-center-server//orderItem/getOrderItemWarn", {orderItemId: orderItemId}).then(
      res => {
        this.errorData = res
      }
    )
  },
  methods: {
    audit() {
      if(this.tableData[0].licensesUseCharacter){
        if(this.tableData[0].licensesUseCharacter.includes('客运')){
            this.$message.error('操作失败，原因：车辆使用性质不符合平台要求')
            return
        }
      }
      this.$confirm('确认通过审核', '提示', {
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          this.$post("/admin-center-server/orderItem/audit", {
            auditResult: 1,
            orderItemSn: this.tableData[0].sn
          })
            .then(res => {
              this.$message.success('操作成功')
              setTimeout(() => {
                window.close()
              }, 1000)
            })
            .catch(() => {
              // this.$router.push('/transport/transportAudit')
            })
            .finally(() => {
              this.loading = false
            })
        })
    },
    reject() {
      this.loading = true
      let params = {
        orderItemSn: this.tableData[0].sn,
        auditResult: 0,
        otherReason: this.rejectOtherReason,
        reasonList: this.rejectSelectedReason.map(v => {
          return {
            id: v.id,
            content: v.content
          }
        })
      }
      this.$post("/admin-center-server/orderItem/audit", params)
        .then(res => {
          this.$message.success('操作成功')
          setTimeout(() => {
            window.close()
          }, 1000)
        })
        .finally(() => {
          this.isRejectDialogShow = false
          this.loading = false
        })
    },
    handlePicClose() {
      this.showedPic = null
    },
    getSource(source) {
      this.source = source.str
      if (source.code === '2' || source.code === '3') {
        this.trackType = '2'
      } else {
        this.trackType = source.code
      }
    },
    valueFormatter(row, column) {
      let value = row[column.property]
      return value === null ? '-' : value
    },
    percentFormatter(row, column) {
      let value = row[column.property]
      if (value === undefined) return ''
      if (value === null) return '-'
      value = Number(value)
      return value.toFixed(2) + '%'
    },
    showTrack() {
      this.$refs.track.init({
        waybillData: this.tableData[0],
        orderData: this.orderDetail[0],
        isWaybillDetail: false,
        // isWaybillDetail: true,
        isNavigation: this.isNavigation
      })
    },
    loadAmapTrack() {
      this.$confirm('一旦执行此操作，原运单轨迹将被覆盖，请谨慎操作。确定继续吗？', '提示', {
        type: 'warning'
      }).then(() => {
          this.isNavigation = true
          this.isMapFail = false
          this.trackType = ''
          this.$nextTick(() => {
            this.showTrack()
          })
        })
    },
    trackTypeChange(v) {
      this.$refs.track.auditSelect(v)
    },
    openDispatchDetail() {
      this.$router.push({
        path: "/order/agentOrder/agentOrderDetail",
        query: {
          orderBrokerId: this.tableData[0].orderDispatchId //经济单主键
        }
      })
    },
    showReject(){
      console.log('showReject',this.isRejectDialogShow)
      this.isRejectDialogShow = true
      console.log('showReject',this.isRejectDialogShow)
    },
    openUrl() {
      window.open(this.tableData[0].signingUrl) 
    }
  },
  computed: {
    received() {
      return this.tableData[0].statusBd === '6' || this.tableData[0].statusBd === '21' || this.tableData[0].statusBd === '22'
    },
    loadUrlList() {
      return typeof this.tableData[0].originalTonImageUrl === 'string' ? this.tableData[0].originalTonImageUrl.split(';') : []
    },
    unloadUrlList() {
      return typeof this.tableData[0].dischargeCargoImageUrl === 'string' ? this.tableData[0].dischargeCargoImageUrl.split(';') : []
    }
  }
}
</script>
<style lang="scss" scoped>
  .error-display {
    position: fixed;
    top: 93px;
    left: 210px;
    right: 20px;
    z-index: 100;
    // margin: 5px;
    padding: 0 20px;
    .error-title {
      font-weight: 600;
    }
    .error-collapse {
      font-size: 16px;
      font-weight: 700;
    }
    .error-item > span ::v-deep > span  {
      color: red;
    }
    background-color: white;
  }
  .audit-top {
    position: fixed;
    top: 142px;
    left: 220px;
    right: 35px;
    z-index: 99;
    // background-color: rgb(249, 249, 249);
    background-color: white;
    .top-content {
      background-color: rgb(249, 249, 249);
      margin:0px 1px;
    }
    .top-title {
      padding: 20px;
      font-size: 18px;
      font-weight: 800;
    }
    .top-sn {
      font-size: 16px;
      font-weight: 400;
    }

    .top-button {
      display: flex;
      .top-button-info {
        width: 108px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        cursor: pointer;
      }
      .top-button-log {
        width: 108px;
        text-align: center;
        height: 30px;
        line-height: 30px;
        cursor: pointer;
      }

      .top-button-select {
        background-color: white;
      }
    }
  }
  .audit {
    position: fixed;
    bottom: 0;
    right: 0;
    z-index: 100;
    box-sizing: border-box;
    width: calc(100% - 180px);
    height: 100px;
    padding-top: 30px;
    background: #fff;
    text-align: center;
  }
  .audit-left {
    float: left;
    margin-left: 60px;
    font-size: 14px;
    line-height: 36px;
    span {
      margin-right: 20px;
      color: red;
    }
  }
  .audit-right {
    float: right;
    margin-right: 20px;
  }
  .audit-current {
    display: inline-block;
    margin-right: 20px;
    font-size: 14px;
    color: #D9001B;
  }
  .main-box {
    padding: 20px;
    padding-bottom: 100px;
    margin: 140px 20px 20px;
    background-color: white;
    .title-box {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }

    .list-box {
      margin-top: 20px;
      border-left: none;

      .item-title {
        display: flex;
        flex-direction: row;

        div {
          width: 500px;
          height: 50px;
          font-weight: bold;
          font-size: 16px;
          line-height: 50px;
          border: 1px solid #cccccc;
          border-top: none;
          border-right: none;
          background-color: rgb(249, 252, 250);
          text-align: center;
        }
      }

      .item-info {
        display: flex;
        flex-direction: row;

        div {
          font-size: 14px;
          width: 500px;
          height: 50px;
          line-height: 50px;
          text-align: center;
          border: 1px solid #cccccc;
          border-top: none;
          border-right: none;
          border-bottom: none;
        }
      }
    }
  }
  .open-big {
    vertical-align: middle;
  }
  .no-map-warn {
    padding: 20px 0;
    font-size: 14px;
  }
  .source {
    margin-right: 20px;
    font-size: 14px;
    font-weight: normal;
    vertical-align: middle;
  }
  .amap-page-container {
    // width: 73%;
  }
  .mobile-warn span {
    color: #D9001B;
  }
.track-tool {
  display: flex;
  justify-content: space-between;
}
.track-selector {
  font-size: 14px;
}
.track_operate {
  display: flex;
  justify-items: center;
  align-items: center;
}
.load_track_button {
  display: inline-block;
  background-image: url(~@/assets/images/track/load_track_icon.png);
  height: 30px;
  width: 30px;
  margin-right: 10px;
}
.map-wrapper {
  position: relative;
}
.map-info {
  z-index: 10;
  position: absolute;
  top: 10px;
  right: 20px;
  width: 240px;
}
.address {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 10px;
}
.address-item {
  box-sizing: border-box;
  padding-left: 35px;
  padding-top: 15px;
  padding-bottom: 15px;
  width: 95%;
  margin: 0 auto;
  background: 10px 15px no-repeat;
}
.address-item-unload {
  background-image: url(~@/assets/images/track/address1.png);
}
.address-item-load {
  border-top: 1px solid #f2f2f2;
  background-image: url(~@/assets/images/track/address2.png);
}
.address-title {
  font-size: 14px;
  color: rgb(51, 51, 51);
  span {
    margin-left: 10px;
    font-size: 12px;
  }
}
.address-text {
  margin-top: 10px;
  font-size: 14px;
  line-height: 24px;
}
.reject-text {
  color: #D45353;
}
.warn {
  color: rgb(221, 32, 66);
}
.list-order-dispatch {
  color: #f6a018;
  cursor: pointer;
}
.warn-icon{
    color: #f56c6c;
    margin-right: 8px;
  }
</style>