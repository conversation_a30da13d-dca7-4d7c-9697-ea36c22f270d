<template>
    <div class="app-container carsList">
      <div class="select-box">
        <div class="top-title">筛选查询</div>
        <div class="select-info">
          <el-form :inline="true" :model="form" size="mini" class="demo-form-inline" label-width="150px">
            <el-form-item label="司机名称:">
              <el-input v-model="form.userName" placeholder="请输入司机名称"></el-input>
            </el-form-item>
            <el-form-item label="司机手机号:">
              <el-input v-model="form.phone" placeholder="请输入司机手机号" maxlength="11"></el-input>
            </el-form-item>
            <el-form-item label="审核状态:">
                <el-select v-model="form.status" placeholder="请选择处理状态" clearable>
                    <el-option value="" label="全部"></el-option>
                    <el-option value="0" label="待审核"></el-option>
                    <el-option value="1" label="已通过"></el-option>
                    <el-option value="2" label="已驳回"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="提交时间:">
                <el-date-picker
                    v-model="submitTime"
                    type="datetimerange"
                    start-placeholder="开始日期"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                    ></el-date-picker>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="queryFun">查询</el-button>
            </el-form-item>
          </el-form>
          <el-form
            :inline="true"
            :model="ruleForm"
            class="demo-form-inline"
            label-width="150px"
            style="margin-top: 30px"
          ></el-form>
        </div>
      </div>
      <div class="list-box">
        <div class="list-title">
          <div>数据列表</div>
        </div>
        <div class="list-main">
          <template>
            <el-table
              ref="multipleTable"
              :data="tableData"
              tooltip-effect="dark"
              style="width: 100%"
              @selection-change="handleSelectionChange"
              border
              :max-height="tableHeight"
              :cell-style="{ 'text-align': 'center', border: '0.5px solid #EAF0FB' }"
              :header-cell-style="{
                'text-align': 'center',
                border: '0.3px solid #EAF0FB',
                'background-color': '#F5F6F9',
                height: '60px',
              }"
            >
              <el-table-column type="index" width="50" label="序号"></el-table-column>
              <el-table-column prop="applicationNo" width="180" label="申请单号" :formatter="nullValueTableFormatter"></el-table-column>
              <el-table-column prop="userName" label="司机姓名" :formatter="nullValueTableFormatter"></el-table-column>
              <el-table-column prop="phone" width="180" label="司机手机号" :formatter="nullValueTableFormatter"></el-table-column>
              <el-table-column prop="reason" width="200" label="押金未解冻原因" :formatter="nullValueTableFormatter"></el-table-column>
              <el-table-column prop="statusStr" label="审核状态">
                <template slot-scope="scope">
                  <span v-if="scope.row.status=='0'" class="normal-color">{{ scope.row.stateStr }}</span>
                  <span v-if="scope.row.status=='1'" class="success-color">{{ scope.row.stateStr }}</span>
                  <span v-if="scope.row.status=='2'" class="error-color">{{ scope.row.stateStr }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="处理备注">
                <template slot-scope="scope">
                  <span v-if="scope.row.remark" :class="scope.row.status=='3'?'error-color':''">{{scope.row.remark}}</span>
                  <span v-else>-</span>
                </template>
                 
              </el-table-column>
              <el-table-column prop="auditTime" label="审核时间" width="180" :formatter="nullValueTableFormatter"></el-table-column>
              <el-table-column prop="createTime" label="提交时间" width="180" :formatter="nullValueTableFormatter"></el-table-column>
              <el-table-column fixed="right" label="操作" width="160">
                <template slot-scope="scope">
                  <el-button @click="handleFn(scope.row,'pass')" type="text" size="small" v-if="scope.row.status=='0'">通过</el-button>
                  <el-button @click="handleFn(scope.row,'reject')" type="text" size="small" v-if="scope.row.status=='0'">驳回</el-button>
                  <span v-else>-</span>
                </template>
              </el-table-column>
            </el-table>
            <div class="page">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="form.pageNumber"
                :page-sizes="[20, 40, 60, 80,100]"
                :page-size="form.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                style="margin: 10px auto"
              ></el-pagination>
            </div>
  
            <!-- 通过/驳回弹窗 -->
            <el-dialog title="" :visible.sync="showDialog" class="showDialog" width="400px">
              <div class="dialog-title" v-if="isPass">通过后，系统将自动退还司机押金，确认继续？</div>
              <div class="dialog-title" v-else>驳回后，系统将自动扣除司机押金，请确保无误后操作</div> 
              <el-form :model="ruleForm" ref="ruleForm" :rules="rules" status-icon label-width="0px" class="demo-ruleForm">
                <el-form-item prop="remarks">
                  <el-input
                    type="textarea"
                    v-model="ruleForm.remarks"
                     :placeholder="isPass?'不必填，在此输入审核备注':'必填，请输入驳回原因'"
                  ></el-input>
                </el-form-item>
              </el-form>
              <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="showDialog = false">取 消</el-button>
                <el-button type="primary" size="small" @click="submitFn">确 定</el-button>
              </span>
            </el-dialog>
          </template>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    data() {
      return {
        form: {
          userName: "",
          phone: "",
          pageNumber: 1,
          pageSize: 20,
          sort: "desc"
        },
        ruleForm: {
          id: "",
          remarks: "",
          status: "",
        },
        total: null,
        showDialog: false, //弹窗
        tableData: [],
        submitTime: [],  
        isPass: false, //是否通过
        rules: {
          remarks: [
            {
              validator: (rule, value, cb) => {
                if (!this.isPass && !value) {
                  return cb('请输入驳回原因')
                }
                cb()
              }
            }
          ] 
        },
        tableHeight:null,
      };
    },
    activated() {
      this.getDataList();
      this.tableHeight =
      window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 170;
    },
    methods: {
      goDetail(row) {
        console.log(row);
        // window.location.href=''
        this.$router.push("/carsList/carDetail");
        // this.$router.push('/dispatch/page/carsAttestation')
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
  
      /* 选取每页多少条数据 */
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`);
        this.form.pageNumber =1;
        this.form.pageSize = JSON.parse(`${val}`);
        console.log(this.form.pageSize);
        this.getDataList();
      },
      /* 翻页 */
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`);
        this.form.pageNumber = JSON.parse(`${val}`);
        console.log(this.form.pageNumber);
        this.getDataList();
      },
      //搜索选中
      handleSelect(item) {
        this.ruleForm.id = Number(item.name);
        this.mobile = item.mobile;
      },
      /* 查询 */
      queryFun() {
        this.form.pageNumber =1
        this.getDataList();
      },
      /* 重置 */
      resetForm() {
        this.form={}
        this.submitTime = []
        this.getDataList();
      },
      /* 数据列表 */
      getDataList() {
        let postData ={
          ...this.form
        };
        if(this.submitTime && this.submitTime.length > 0){
          postData.startTime = this.submitTime[0]
          postData.endTime = this.submitTime[1] 
        }
        this.$http
          .post("/admin-center-server/depositRefundApplication/pageList", postData)
          .then(res => {
            let data = res.data;
            if (data.code === "200") {
              this.tableData = data.data.list;
              this.total = Number(data.data.total) || 0;
            } else {
              this.$message.warning(data.message);
            }
          });
      },
      //  通过/驳回操作
      handleFn(row,str) {
        if(str == 'pass'){
          this.isPass = true 
        }else{
          this.isPass = false
        }
        this.ruleForm.id = row.id;
        this.ruleForm.remarks = ''
        this.showDialog = true; 
      },
      submitFn() {
        this.$refs.ruleForm.validate(valid => {
          if (!valid) return false;
            let data = {
            id: this.ruleForm.id,
            remark: this.ruleForm.remarks,
            status: this.isPass ? 1 : 2
          }
          this.$http
          .post("/admin-center-server/depositRefundApplication/applicationProcessing", data).then(res => {
            this.showDialog = false;
            let data = res.data;
            if (data.code !== "200") {
              this.$message.warning(data.message);
              return false;
            }
            if(this.isPass){
              this.$message.success('正在向微信申请退还司机押金，稍后可到司机账户详情中查看押金退还结果')
            }else{
              this.$message.success('正在向微信申请扣除司机押金，稍后可到司机账户详情中查看押金扣除结果')
            }
            this.getDataList();
          });
        });
        
      }
    }
  };
  </script>
  
  <style rel="stylesheet/scss" lang="scss" scoped>
  ::v-deep input::-webkit-outer-spin-button,
  ::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }
  ::v-deep input[type="number"] {
    -moz-appearance: textfield;
  }
  .carsList {
    .page {
      text-align: right;
    }
    .select-box {
      /*height: 260px;*/
      background-color: #ffffff;
  
      .top-title {
        font-size: 16px;
        font-weight: 700;
        height: 40px;
        line-height: 40px;
        padding-left: 10px;
        border-bottom: 1px solid #cccccc;
      }
  
      .select-info {
        padding-top: 30px;
        padding-bottom: 10px;
      }
    }
  
    .list-box {
      background-color: #ffffff;
      margin-top: 20px;
      padding: 10px;
  
      .list-title {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
  
        div {
          height: 38px;
          line-height: 38px;
        }
      }
  
      .list-main {
        width: 100%;
        //   border: 1px solid #cccccc;
        margin-top: 10px;
      }
      .releaseMessage {
        margin-right: 20px;
      }
      .pagination {
        text-align: right;
        margin-top: 10px;
      }
      .el-form-item {
        margin: 30px auto;
      }
    }
  }
  .dialog-title {
    color: red; 
  }
  </style>
  