<template>
  <div>
    <el-row v-if="tableData[0].operationalPeoFlag=='0'" :gutter="10" class="detail-col">
            <el-col :span="18">
              司机运费： {{ moneyFormatter(tableData[0].actualDriverAmount) }}
            <el-tooltip v-if="tableData[0].freightCalcType === '0'">
              <i class="el-icon-warning-outline"></i>
              <template #content>
                1、计费规则：
                <div v-for="(item, index) in ruleData" :key="item.id" class="rule">
                  <div> {{ handleNum(index + 1) }} 、{{ item.name }}</div>
                  <div>{{ item.rormulatian }}</div>
                </div>
                2、计算公式：
                {{ handleMoney(tableData[0].freight) }} * {{ tableData[0].payTon }}
                <template v-if="tableData[0].shortTonDeductPrice && tableData[0].currentTon"> - {{ handleMoney(tableData[0].shortTonDeductPrice) }}</template>
                <template v-if="tableData[0].cutPayment"> - {{ handleMoney(tableData[0].cutPayment) }}</template>
                <template v-if="tableData[0].compensationPrice"> + {{ handleMoney(tableData[0].compensationPrice) }}</template>
                <template v-if="tableData[0].roundOffNumber"> - {{handleMoney(tableData[0].roundOffNumber) }}</template>
              </template>
            </el-tooltip>
          </el-col>
          <el-col :span="6" v-if="tableData[0].operationalPeoFlag=='1'">
            在线投保：<el-switch v-model="isonlineInsurance" class="cost-info-switch" disabled></el-switch>
          </el-col>
    </el-row>
    <el-row v-if="tableData[0].operationalPeoFlag=='0'" :gutter="10">
          <el-col :span="6">
            <div class="cost-info">
              <div class="cost-info-left">
                其中油费
              </div>
              <div class="cost-info-right">
                <el-switch v-model="tableData[0].orderItemOpenOilGasFlag" class="cost-info-switch" disabled></el-switch>
                <div class="cost-info-right-body">
                  <div>油费金额：{{ tableData[0].orderItemOpenOilGasFlag?moneyFn(tableData[0].allocationOilPayMoneyDisplay,"+"):'-' }}</div>
                  <div>实际油费比例：{{tableData[0].orderItemOpenOilGasFlag?tableData[0].actualOilRatio?tableData[0].actualOilRatio+'%':'-':'-' }}</div>
                  <div>支付状态：
                    <span :class="[tableData[0].allocationOilPayStatus=='已支付'?'success-color':'',(tableData[0].allocationOilPayStatus=='支付失败'||tableData[0].allocationOilPayStatus=='已退款')?'error-color':'']">
                      {{ tableData[0].allocationOilPayStatus }} 
                    </span>
                    <span v-if="tableData[0].allocationOilPayStatus=='已支付'">(实付：{{moneyFn(tableData[0].actualOil,'+')}})</span>
                  </div>
                  <div>平台分油时间：{{ tableData[0].orderItemOpenOilGasFlag?tableData[0].allocationTime:'-' }}</div>
                  <div>平台分油金额：{{ tableData[0].orderItemOpenOilGasFlag?tableData[0].allocationOilMoney:'-' }} <span v-if="tableData[0].oilRechargeStatus == 1" style="color:#ED970F;cursor:pointer" @click="checkOilStatus">查看分油凭证</span></div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="cost-info">
              <div class="cost-info-left">
                其中回单
              </div>
              <div class="cost-info-right">
                <el-switch v-model="receiptFlag" class="cost-info-switch" disabled></el-switch>
                <div class="cost-info-right-body">
                  <div>回单类型：{{receiptFlag?tableData[0].receiptTypeName:'-' }}</div>
                  <div>回单押款：{{receiptFlag?moneyFn(tableData[0].receiptSecurity,'+'):'-'}}</div>
                  <div>扣款金额：{{receiptFlag?moneyFn(tableData[0].receiptDeduction,'-'):'-'}} 
                    <el-tooltip v-if="showTip(tableData[0].receiptDeduction)">
                      <i class="el-icon-warning-outline"></i>
                      <template #content>
                        {{tableData[0].receiptReason || '暂无扣款备注'}}   
                      </template>
                    </el-tooltip>
                  </div>
                  <div>回单状态：
                    <span :class="[tableData[0].receiptStatusName=='已收回' ||tableData[0].receiptStatusName=='已上传'?'success-color':'']">{{ tableData[0].receiptStatusName||'-' }}</span>
                  </div>
                  <div>支付状态：
                    <span :class="[tableData[0].receiptPayStatus=='已支付'?'success-color':'',tableData[0].receiptPayStatus=='支付失败'?'error-color':'']">{{ tableData[0].receiptPayStatus||'-' }}</span>
                    <span v-if="tableData[0].receiptPayStatus=='已支付'">(实付：{{moneyFn(tableData[0].actualReceipt,'+')}})</span>
                  </div>
                  <div>快递单号：{{ tableData[0].receiptTrackingNumber||'-' }}</div>
                  <div class="remark-div">回单备注：
                      <el-tooltip effect="dark" class="auto-wrap" :content="tableData[0].receiptRemark" placement="top-start" >
                       <span>{{ tableData[0].receiptRemark || '-'}}</span> 
                      </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="cost-info">
              <div class="cost-info-left">
                其中结算付运费
              </div>
              <div class="cost-info-right">
                <el-switch v-model="showTrue" class="cost-info-switch" disabled></el-switch>
                <div class="cost-info-right-body">
                  <div>计划现金运费：{{ moneyFn(tableData[0].planCashFreight,"+") }}</div>
                  <div v-if="tableData[0].freightCalcType=='0'">亏吨扣款：{{ moneyFn(tableData[0].shortTonDeductPrice,'-') }}</div>
                  <div v-if="tableData[0].freightCalcType=='0'">自动抹零：{{ moneyFn(tableData[0].roundOffNumber,'-')}}</div>
                  <div>收货补贴：{{ moneyFn(tableData[0].compensationPrice,'+')}}
                    <el-tooltip v-if="showTip(tableData[0].compensationPrice)">
                      <i class="el-icon-warning-outline"></i>
                      <template #content>
                       {{ tableData[0].compensationPriceRemark|| '暂无补贴备注' }}
                      </template>
                    </el-tooltip>
                  </div>
                  <div>收货扣款：{{ moneyFn(tableData[0].cutPayment,'-')}}
                    <el-tooltip v-if="showTip(tableData[0].cutPayment)">
                      <i class="el-icon-warning-outline"></i>
                      <template #content>
                        {{ tableData[0].cutPaymentRemark || '暂无扣款备注' }}
                      </template>
                    </el-tooltip>
                  </div>
                  <div>支付状态：
                    <span :class="[tableData[0].settlementPaymentPayStatus=='已支付'?'success-color':'',tableData[0].settlementPaymentPayStatus=='支付失败'?'error-color':'']">{{ tableData[0].settlementPaymentPayStatus }}</span>
                    <span v-if="tableData[0].settlementPaymentPayStatus=='已支付'">(实付：{{moneyFn(tableData[0].actualSettlement,'+')}})</span>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="6" v-if="tableData[0].operationalPeoFlag=='1'">
            <div class="cost-info">
              <div class="cost-info-left">
                投保信息
              </div>
              <div class="cost-info-right">
                <div class="cost-info-right-body">
                  <div>投保货物类型：{{isonlineInsurance?isInsurance(tableData[0].conveyanceIdEnum):'-' }}</div>
                  <div>险种：{{isonlineInsurance?isInsurance(tableData[0].typeStr):'-' }}</div>
                  <div>投保货值：{{isonlineInsurance?isInsurance(tableData[0].goodsValue?tableData[0].goodsValue+'万元/车':"-"):'-'}}</div>
                  <div>投保状态：
                    <span :class="[tableData[0].insureStatusStr=='已投保'?'success-color':'',tableData[0].insureStatusStr=='投保失败'?'error-color':'']">{{ isInsurance(tableData[0].insureStatusStr) }}</span>
                  </div>
                  <div>电子保单：
                    <el-button  v-if="tableData[0].onlineInsurance === '1' && tableData[0].insureStatus === '2'" @click="openInsure(tableData[0].policyOfInsuranceUrl)" size="mini" type="text">点此查看</el-button>
                    <span v-else>-</span>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
    </el-row>
     <!-- 新ui  开始 -->
    <el-row v-if="tableData[0].operationalPeoFlag=='1'" :gutter="10" class="detail-col">
            <el-col :span="6">
              <!-- 应付司机运费： {{ moneyFormatter(tableData[0].actualDriverAmount) }} -->
              应付司机运费： {{planTotal}}元
            </el-col>
            <el-col :span="14">
              计划司机运费： {{ planFreight }}元 | 运费单价{{handleMoney(tableData[0].freight) }}{{tableData[0].freightCalcType=='0'?'元/吨':'元/车'}} <span v-if="tableData[0].freightCalcType=='0'" style="margin-right:5px">| {{tableData[0].payTon}}吨</span><el-tooltip v-if="tableData[0].freightCalcType === '0'">
              <i class="el-icon-warning-outline"></i>
              <template #content>
                抢单吨数：{{tableData[0].payTon}}吨，装货前按此吨数计算 <br>
                装货吨数：{{tableData[0].originalTon}}吨，卸货前按此吨数计算  <br>
                卸货吨数：{{tableData[0].paymentTon}}吨，卸货后按“结算吨数类型”计算 <br>
                结算吨数类型：{{freightTonTypeText}}
              </template>
            </el-tooltip>
            </el-col>
    </el-row>
    <el-table v-if="tableData[0].operationalPeoFlag=='1'" :key="tableData[0].orderCommonId" border tooltip-effect="dark" :data="tableData[0].downAmountInfos" style="width: 100%"  cell-class-name="table_cell_gray" header-cell-class-name="table_header_cell_gray_height">
      <el-table-column prop="payTypeStr" label="支付类型" >
      </el-table-column>
      <el-table-column prop="planPayAmount" label="计划支付金额" width="180">
        <template slot-scope="scope">
          {{ scope.row.planPayAmount||'-' }} <span v-if="scope.row.payType==1&&!tableData[0].oilQuantify&&tableData[0].orderItemOpenOilGasFlag">({{tableData[0].actualOilRatio||'-'}}%)</span>
        </template>
      </el-table-column>
      <el-table-column v-if="tableData[0].freightCalcType=='0'" prop="shortTonDeductPrice" label="亏吨扣款" width="180">
        <template slot-scope="scope">
          <div v-if="scope.row.payType=='2'">{{ scope.row.shortTonDeductPrice?'-'+scope.row.shortTonDeductPrice:'-' }} <el-tooltip v-if="Number(scope.row.shortTonDeductPrice)>=0">
              <i class="el-icon-warning-outline"></i>
              <template #content>
                亏吨扣款单价:{{tableData[0].lossChargeUnitPrice||'-'}} <br>
                认亏值:{{tableData[0].lossValue||'-'}}<span v-if="tableData[0].lossValue">{{tableData[0].lossType=='1'?'吨':'‰'}}</span> 
              </template>
            </el-tooltip></div>
          <div v-else>/</div>
        </template>
      </el-table-column>
      <el-table-column prop="compensationPrice" label="补贴合计">
        <template slot-scope="scope">
          <div v-if="scope.row.payType=='2'">{{ scope.row.compensationPrice? '+'+scope.row.compensationPrice:'-' }} 
            <el-tooltip v-if="tableData[0].orderItemFeeSubsidyDownPt.length>0||totalMoney(scope.row.compensationPrice,tableData[0].orderItemFeeSubsidyDownPt)">
                      <i class="el-icon-warning-outline"></i>
                      <template #content>
                        <div v-for="(item,index) in tableData[0].orderItemFeeSubsidyDownPt" :key="index">{{ item.dictLabel||'补贴' }}：{{'+'+item.feeAmount+'元' }}</div>
                        <div v-if="totalMoney(scope.row.compensationPrice,tableData[0].orderItemFeeSubsidyDownPt)">补贴:+{{totalMoney(scope.row.compensationPrice,tableData[0].orderItemFeeSubsidyDownPt)}}元 ({{tableData[0].compensationPriceRemark}})</div>
                      </template>
              </el-tooltip>
            </div>
          <div v-else>/</div>
        </template>
      </el-table-column>
      <el-table-column prop="cutPayment" label="扣款合计">
        <template slot-scope="scope">
          <div v-if="scope.row.payType=='1'">/</div>
          <div v-if="scope.row.payType=='3'">{{scope.row.cutPayment?scope.row.cutPayment:'-' }}</div>
          <div v-if="scope.row.payType=='2'">{{ scope.row.cutPayment?'-'+scope.row.cutPayment:'-' }} 
            <el-tooltip v-if="tableData[0].orderItemFeeDeductionDown.length>0||tableData[0].receiptCutPayment">
                      <i class="el-icon-warning-outline"></i>
                      <template #content>
                        <div v-for="(item,index) in tableData[0].orderItemFeeDeductionDown" :key="index">{{ item.dictLabel}}：{{'-'+item.feeAmount+'元' }} ({{item.remark}})</div>
                        <div v-if="tableData[0].receiptCutPayment">收货扣款：-{{tableData[0].receiptCutPayment}}元 ({{tableData[0].cutPaymentRemark}})</div>
                      </template>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="tableData[0].freightCalcType=='0'" prop="roundOffNumber" label="抹零">
        <template slot-scope="scope">
          <div v-if="scope.row.payType=='2'">{{ scope.row.roundOffNumber?'-'+scope.row.roundOffNumber:'-' }} <el-tooltip v-if="scope.row.payType=='2'">
              <i class="el-icon-warning-outline"></i>
              <template #content>
               自动抹零规则：{{tableData[0].autoRoundOffEnum}}
              </template>
            </el-tooltip></div>
          <div v-else>/</div>
        </template>
      </el-table-column>
      <el-table-column prop="copeAmount" label="应付金额">
        <template slot-scope="scope">
          {{ scope.row.copeAmount||'-' }} <el-tooltip v-if="scope.row.payType=='2'&&tableData[0].freightCalcType=='0'">
              <i class="el-icon-warning-outline"></i>
              <template #content>
                计划支付金额 - 亏吨扣款 + 补贴合计 - 扣款合计 - 抹零
              </template>
            </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="actualAmount" label="实付金额">
        <template slot-scope="scope">
          {{ scope.row.status=='已支付'?scope.row.actualAmount||'-' :'-'}}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态">
      </el-table-column>
      <el-table-column prop="payTime" label="支付时间">
      </el-table-column>
      <el-table-column prop="remark" label="备注">
      </el-table-column>
    </el-table>
    <!-- 新ui 结束 -->
     <el-row :gutter="10" style="margin-top: 20px" class="detail-col">
         <!-- <el-col :span="6" v-if="tableData[0].operationalPeoFlag === '1'">
          与货主约定运费：{{ unitMoneyFormatter(tableData[0].freightCalcType, tableData[0].platformConsignorFreight) }}
         </el-col> -->
         <el-col :span="6">
          服务费：{{tableData[0].sfServiceFee?tableData[0].sfServiceFee+'元':'-'  }}{{ tableData[0].taxThreshold?`（服务费比例：${tableData[0].taxThreshold}%）`:'' }}
         </el-col>
         <el-col :span="6" v-if="tableData[0].operationalPeoFlag !== '1'">
          费用合计：{{ tableData[0].totalCostAmount ? handleMoney(tableData[0].totalCostAmount) +'元' : '-' }}
         </el-col> 
         <el-col :span="12">
          收款方：{{ tableData[0].agentName+' / '+tableData[0].agentMobile+' / '+ tableData[0].agentSubAcctNo  }}
         </el-col>
        <el-col :span="8" v-if="tableData[0].operationalPeoFlag=='1'">
          在线投保：<span :class="[tableData[0].insureStatusStr=='已投保'?'success-color':'',tableData[0].insureStatusStr=='投保失败'?'error-color':'']" >{{ isInsurance(tableData[0].insureStatusStr) }}</span> 
          <span v-if="tableData[0].insureStatusStr=='已投保'">(保费: <span class="orange">{{tableData[0].sumpremium}}</span>元)</span>
          <span class="tagText" v-if="tableData[0].insureStatusStr=='投保失败'">重新投保</span> | 
          <span>电子保单: <span class="tagText" v-if="(tableData[0].onlineInsurance === '1' && tableData[0].insureStatus === '2' && tableData[0].policyOfInsuranceUrl) ||(tableData[0].policyOfInsuranceUrl && tableData[0].insuranceChannel=='1') " @click="openInsure(tableData[0].policyOfInsuranceUrl)">查看</span> <span v-else>-</span> </span> | 
          <span style="margin-right:5px">投保详情</span>
            <el-tooltip >
              <i class="el-icon-warning-outline"></i>
              <template #content>
                <div>投保货物类型：{{isonlineInsurance?isInsurance(tableData[0].conveyanceIdStr):'-' }}</div>
                <div>投保货值：{{isonlineInsurance?isInsurance(tableData[0].goodsValue?tableData[0].goodsValue+'万元/车':"-"):'-'}}</div>
                <div>投保时机：{{isonlineInsurance?isInsurance(tableData[0].insuranceOccasion=='1'?'接单后投保':'装货后投保' ):'-' }}</div>
                <div>投保方案：{{isonlineInsurance?isInsurance(tableData[0].insurancePlan):'-' }}</div>
              </template>
            </el-tooltip>
         </el-col>
     </el-row>
     <!-- 查看分油凭证的弹框 -->
     <el-dialog title="分油凭证" :visible.sync="dialogOilStatus">
        <div class="oil_images" v-for="(image, index) of oilImageUrls" :key="index">
          <div>分油凭证{{index + 1}}.jpg</div>
          <div @click="checkOilImage(image)" style="color:#ED970F; margin-left: 10px;cursor: pointer;">查看</div>
        </div>
      </el-dialog>
      <el-image-viewer style="z-index: 10000" v-if="showImageUrl" :on-close="closeOilViewer" :url-list="[showImageUrl]"></el-image-viewer>
  </div>
</template>

<script>
import { Big } from 'big.js'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  name: 'costInfo',
  props: ['tableData','ruleData'],
  components: {
    ElImageViewer
  },
  data() {
    return {
        showTrue:true,
        dialogOilStatus:false,
        oilImageUrls: [],
        showImageUrl: '',
    };
  },
  mounted() {
    
  },
  watch: {
    tableData:{
      handler(newVal, oldVal){
        console.log('this.tableData[0].allocationOilProofUrl',this.tableData[0].allocationOilProofUrl)
         if (this.tableData[0] && this.tableData[0].allocationOilProofUrl != null) {
            this.oilImageUrls = this.tableData[0].allocationOilProofUrl.split(',')
          }
      },
      deep:true,
      immediate: true,
    }
  },
  // if (this.tableData[0].allocationOilProofUrl != null) {
  //             this.oilImageUrls = this.tableData[0].allocationOilProofUrl.split(',')
  //           }
  computed: {
    //计算老扣款
    totalMoney(){
      return (total,list)=>{
       if(!Number(total))return false
       if(!list.length)return Number(total).toFixed(2)
       let sum = 0
       list.forEach(item => {
         sum = Big(sum).plus(Number(item.feeAmount))
       });
      if(!(Number(total)-sum)) return false
      
       return (Number(total)-sum).toFixed(2)

      }
    },
    freightTonTypeText(){
      switch (this.tableData[0].freightTonType) {
        case '0':
          return '按照原发吨数'
        case '1':
          return '按照实收吨数'
        case '2':
          return '按装卸吨数较小值结算'
        default:
          return '按照原发吨数'
      }
    },
    // 应付司机费用合计
    planTotal(){
      //this.tableData[0].downAmountInfos copeAmount合计
      let total = 0
      if(!this.tableData[0].downAmountInfos) return 0
      this.tableData[0].downAmountInfos.forEach(item => {
        total = Big(total).plus(Number(item.copeAmount)).toFixed(2)
      });
      return total
    },
        //计划运费
    planFreight(){
      let total = 0
      if(!this.tableData[0].downAmountInfos) return 0
      this.tableData[0].downAmountInfos.forEach(item => {
          total = Big(total).plus(Number(item.planPayAmount)).toFixed(2)
      });
      return total
    },
    isonlineInsurance(){
      return this.tableData[0].onlineInsurance ==='1'
    },
    receiptFlag(){
      return this.tableData[0].receiptFlag ==='1'
    },
    countMoney(){
      let actualDriverAmount = this.tableData[0].actualDriverAmount|| 0
      let sfServiceFee = this.tableData[0].sfServiceFee|| 0
      return Big(actualDriverAmount).plus(Big(sfServiceFee)).toFixed(2)
    },
  },
  methods: {
    handleMoney(num){
      if(num && !Number.isNaN(num)){
        return Number(num).toFixed(2) 
      }else{
        return num
      }
    },
    checkOilImage(url) {
      this.showImageUrl = url
    },
    closeOilViewer() {
      this.showImageUrl = ""
    },
    checkOilStatus() {
      this.dialogOilStatus = true
    },
    openInsure(url) {
      window.open(url)
    },
    handleNum(num){
      if(num === 1){
        return '①'
      }else if(num === 2){
        return '②'
      }else if(num === 3){
        return '③'
      }else if(num === 4){
        return '④'
      }else if(num === 5){
        return '⑤'
      }else if(num === 6){
        return '⑥'
      }else if(num === 7){
        return '⑦'
      }else if(num === 8){
        return '⑧'
      }else if(num === 9){
        return '⑨'
      }else if(num === 10){
        return '⑩'
      }else if(num === 11){
        return '⑪'
      }
    },
    moneyFn(num,str='+'){
      console.log('num',num)  
      if(!num||num==='-'){
        return '-'
      }
      if(num){
        num = num.replace(/元/g, '')
        if(num.indexOf('-')!==-1){
           num = Number(num).toFixed(2)
           return num +'元'
        }else{
          num = Number(num).toFixed(2)
          return str + num +'元'
        }
        
      }
      return num
    },
    showTip(str){
      if(Number(str)!=0 && Number(str)){
        return true
      }
      return false
    },
    isInsurance(str){
      if(!this.tableData[0].onlineInsurance || str == ''){
        return '-'
      }
      return str
    },
  },
};
</script>
<style scoped src="@/assets/scss/detail.scss" lang="scss"></style>
<style scoped lang="scss">
.cost-info{
  width: 100%;
  height: 250px;
  background-color: #f5f6f9;
  display: flex;
  color: #333;
  .cost-info-left{
    background-color: #dcdfe6;
    width: 40px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    writing-mode: vertical-rl;
    color: #808185;
    font-size: 14px;
  }
  .cost-info-right{
    position: relative;
    padding: 20px;
    height: 100%;
    width: calc(100% - 40px);
    font-size: 14px;
    overflow-y: auto;
    &-body{
      line-height: 25px;
      margin-top: 30px;
    }
    .cost-info-switch{
      position: absolute;
      right: 10px;
      top: 10px;
    }
  }
}
.success-color{
  color: #1ACA3B;
}
.error-color{
  color: #D92929;
}
.detail-col{
  margin-bottom: 10px;
  font-size: 14px;
  color: #333;
}
.remark-div{
  white-space: nowrap;      /* 确保文本在一行内显示 */
  overflow: hidden;         /* 超出容器部分隐藏 */
  text-overflow: ellipsis;
  // width: 180px;
}
.auto-wrap .el-tooltip__popper{
  max-width: 200px; /* 限制提示框的最大宽度 */
  word-break: break-all;
}
.el-icon-warning-outline{
  color: #ed970f;
}
</style>