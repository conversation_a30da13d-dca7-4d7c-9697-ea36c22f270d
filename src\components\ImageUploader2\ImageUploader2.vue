<template>
  <div class="upload-wrapper" :style="wrapperWidthStyle">
    <div class="upload-box" ref="upload" :style="wrapperHeightStyle">
      <div class="uploaded">
        <!-- 有默认图片 -->
        <template v-if="url">
          <img :src="url" class="upload-pic" key="pic1">
        </template>
        <!-- 没有默认图片时，如果有示例图则显示 -->
        <template v-else-if="examplePic">
          <img :src="examplePic" class="upload-pic" key="pic1">
        </template>
        <!-- 没有默认图片也没有示例图，显示空 -->
        <template v-else>
          <div class="empty">
            <img v-if="type === 'viewer'" src="@/assets/images/empty.png">
            <img v-else class="upload-empty-img" src="@/assets/images/upload/cloud.png">
          </div>
        </template>
      </div>

      <!-- 浮在图片上方的遮罩和按钮 -->
      <!-- 如果只读模式，又没有可显示的图片，也就没有任何按钮，不展示遮罩。用v-if语法也就是不为viewer，或是有url，就显示遮罩 -->
      <div v-if="isViewerAndEmpty" class="mask">
        <!-- 已上传图片或设置了初始图片，显示放大镜按钮 -->
        <div v-if="url || examplePic" class="btn btn-open">
          <i @click="viewPic" class="el-icon-zoom-in"></i>
        </div>
        <!-- 如果模式不是只读，而是带有上传功能，继续判断 -->
        <template v-if="type !== 'viewer'">
          <div v-if="url" class="btn btn-del">
            <i @click="del" class="el-icon-delete"></i>
          </div>
          <div v-else class="btn btn-upload">
            <i @click="upload" class="el-icon-upload2"></i>
          </div>
        </template>
      </div>
    </div>
    <div v-if="description" class="description">
        {{ description }}
      </div>
    <el-image-viewer v-if="isViewerShow"
      :on-close="handlePicClose"
      :url-list="[url || examplePic]"/>
  </div>
</template>

<script>
import { selectAndUpload, FileDragger } from '@/utils/file'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  components: {
    ElImageViewer
  },
  props: {
    type: {
      default: 'viewer' // viewer upload
    },
    defaultUrl: String,
    description: String,
    examplePic: String,
    size: Array //[width, height]
  },
  data() {
    return {
      url: '',
      isViewerShow: false
    }
  },
  mounted() {
    let dragger = new FileDragger(this.$refs.upload, {
      upload: true,
      accept: '.png,.jpg'
    })
    dragger.startDragUpload()
    dragger.on('upload', data => {
      this.handleUpload(data.url)
    })
  },
  computed: {
    isViewerAndEmpty() {
      return this.type !== 'viewer' || this.url
    },
    wrapperWidthStyle() {
      if (!Array.isArray(this.size) || this.size.length === 0) {
        return {}
      }
      return {
        width: this.size[0] + 'px'
      }
    },
    wrapperHeightStyle() {
      if (!Array.isArray(this.size) || this.size.length === 0) {
        return {}
      }
      return {
        height: this.size[1] + 'px'
      }
    }
  },
  methods: {
    upload() {
      selectAndUpload({
        accept: '.png,.jpg'
      })
        .then(data => {
          this.handleUpload(data.url)
        })
    },
    handleUpload(url) {
      this.url = url
      this.$emit('change', url)
    },
    viewPic() {
      this.isViewerShow = true
      
      //为了避免图片被挡在对话框后面
      this.$nextTick(() => {
        let image = document.querySelector('.el-image-viewer__wrapper')
        let imageZindex = Number(getComputedStyle(image).zIndex)
        let dialogs = document.querySelectorAll('.el-dialog__wrapper')
        if (!dialogs) return
        let maxZIndex = 0
        Array.from(dialogs).forEach(v => {
          let zIndex = Number(getComputedStyle(v).zIndex)
          if (maxZIndex < zIndex) maxZIndex = zIndex
        })
        if (imageZindex < maxZIndex) image.style.zIndex = maxZIndex + 1
      })
    },
    handlePicClose() {
      this.isViewerShow = false
    },
    del() {
      this.url = ''
      this.$emit('change', this.url)
    },
    reset() {
      this.url = ''
    }
  },
  watch: {
    defaultUrl: {
      immediate: true,
      handler(v) {
        if (!v) {
          this.url = ''
          return
        }
        this.url = this.defaultUrl
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .upload-wrapper {
    display: inline-block;
    vertical-align: top;
    width: 270px;
    margin-right: 10px;
  }
  .upload-box {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 174px;
    border: 2px dashed #DCDFE6;
    background: #fff center center no-repeat;
  }
  .mask {
    position: absolute;
    width: 100%;
    height: 100%;
    display: none;
    background-color: rgba(0, 0, 0, 0.5);
  }
  .upload-box:hover .mask {
    display: flex;
  }
  .btn {
    flex-grow: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 35px;
    color: #fff;
    cursor: pointer;
  }
  .description {
    margin-top: 10px;
    text-align: center;
    color: #333;
    font-size: 14px;
    line-height: 14px;
  }
  .empty {
    display: flex;
    text-align: center;
    img {
      width: 100px;
    }
    > div {
      color: #d2cfcf;
      font-size: 13px;
    }
  }
  .uploaded {
    position: relative;
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .upload-pic {
    width: 90%;
    height: 90%;
  }
</style>