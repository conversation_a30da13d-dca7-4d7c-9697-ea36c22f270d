/**
结算单下 回单
**/
<template>
    <div class="app-container owner_agent">
        <el-tabs type="border-card">
            <el-tab-pane label="货主结算">
                <section class="ownerBox">
                    <div class="ownerBox_title">
                        明细列表
                    </div>
                    <div class="title_info">
                        <div>结算状态：{{business}}</div>
                        <div>结算单编号: <span style="color: red">{{sn}}</span>  客户名称：<span style="color: red">{{businessName}}</span> </div>
                    </div>
                    <div class="owner_list">
                        <el-table
                                :data="tableData"
                                border
                                show-summary
                                :summary-method="getSummaries"
                                style="width: 100%">
                            <el-table-column
                                    prop="inType"
                                    :formatter="moneyType"
                                    label="收款方"
                            >
                            </el-table-column>
                            <el-table-column
                                    prop="count"
                                    label="回执笔数"
                            >
                            </el-table-column>
                            <el-table-column
                                    prop="tranAmtSum"
                                    label="回执合计金额(元)">
                            </el-table-column>
                            <el-table-column
                                    fixed="right"
                                    label="操作"
                                    width="100">
                                <template slot-scope="scope">
                                    <el-button style="color: #00cb8a" @click="handleClick(scope.row,type='1')" type="text"
                                               size="small">查看详情
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </section>
            </el-tab-pane>
            <el-tab-pane label="调度员结算">
                <section class="ownerBox">
                    <div class="ownerBox_title">
                        明细列表
                    </div>
                    <div class="title_info">
                        <div>结算状态：{{broker}}</div>
                        <div>结算单编号: <span style="color: red">{{sn}}</span>  客户名称：<span style="color: red">{{businessName}}</span> </div>
                    </div>
                    <div class="owner_list">
                        <el-table
                                :data="tableData1"
                                border
                                show-summary
                                :summary-method="getSummaries"
                                style="width: 100%">
                            <el-table-column
                                    prop="inType"
                                    :formatter="moneyType"
                                    label="收款方"
                            >
                            </el-table-column>
                            <el-table-column
                                    prop="count"
                                    label="回执笔数"
                            >
                            </el-table-column>
                            <el-table-column
                                    prop="tranAmtSum"
                                    label="回执合计金额(元)">
                            </el-table-column>
                            <el-table-column
                                    fixed="right"
                                    label="操作"
                                    width="100">
                                <template slot-scope="scope">
                                    <el-button style="color: #00cb8a" @click="handleClick(scope.row,type='2')" type="text"
                                               size="small">查看详情
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </section>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                business:'暂无',
                broker:'暂无',
                businessName:'',
                sn:'',
                tableData: [],
                tableData1: [],
            }
        },
        methods: {
            /** 货主 收款类型 **/
            moneyType(row){
                let type=row.inType;
                if(type==='0'){
                    return '平台'
                }else if(type==='1'){
                    return '货主'
                }else if(type==='2'){
                    return '调度员'
                }else if(type==='3'){
                    return '司机'
                }else if(type==='5'){
                    return '油气'
                }
            },
            handleClick(row,type) {
                let form={
                    outType:type,
                    inType:row.inType,
                    ydOrderNoList: JSON.parse(sessionStorage.getItem('snList')),
                };
                this.$http.post('/pay-center-server/platformFinance/getHuiDanSummaryIdList',form).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                        let idList = data.data;
                        sessionStorage.setItem('idList',JSON.stringify(idList))
                        this.$router.push({
                            path: 'statements_electronic',
                            query:{
                                outType:type,
                                inType:row.inType,
                                sn:this.$route.query.sn,
                                businessName:this.$route.query.businessName,
                            }
                        })
                    }else {
                        this.$message.warning(data.message)
                    }
                });
            },
            getOwnerData() {
                this.businessName = this.$route.query.businessName;
                this.sn = this.$route.query.sn;
                let id = this.$route.query.id;
                let ydOrderNoList = sessionStorage.getItem('snList');
                let form={
                    outType: 1,
                    ydOrderNoList: JSON.parse(ydOrderNoList),
                };
                let form1={
                    outType: 2,
                    ydOrderNoList: JSON.parse(ydOrderNoList),
                };
                this.$http.post('/pay-center-server/platformFinance/getHuiDanSummary',form).then(res => {
                    let data = res.data;
                    if(data.code==='200'){
                        this.tableData=data.data;
                    }
                });
                this.$http.post('/pay-center-server/platformFinance/getHuiDanSummary',form1).then(res => {
                    let data = res.data;
                    if(data.code==='200'){
                        this.tableData1=data.data;
                    }else {
                        this.$message.warning(data.message)
                    }
                });

                this.$http.get('/admin-center-server/app/final_statement/checkIsVoice',{
                    params:{
                        id:id
                    }
                }).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                        if(data.data.broker===false){
                            this.broker='调度员未结算完成'
                        }else {
                            this.broker='调度员结算完成'
                        }
                        if(data.data.business===false){
                            this.business='客户未结算完成'
                        }else {
                            this.business='客户结算完成'
                        }
                    }
                })
            },
            /** 计算结果为整数 小数点后留两位 **/
            getSummaries(param){
                const { columns, data } = param;
                const sums = [];
                columns.forEach((column, index) => {
                    if (index === 0) {
                        sums[index] = '合计';
                        return;
                    }
                    const values = data.map(item => Number(item[column.property]));
                    if (!values.every(value => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return prev + curr;
                            } else {
                                return prev;
                            }
                        }, 0);
                        if(index===2){
                            sums[index] = sums[index].toFixed(2)
                        }else {
                            sums[index] += '';
                        }
                    } else {
                        // sums[index] = 'N/A';
                    }
                });

                return sums;
            },
        },
        activated() {
            this.getOwnerData()
        }
    }
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .owner_agent {
        .ownerBox {
            border: 1px solid #cccccc;
            padding: 10px;
            font-size: 14px;

            .ownerBox_title {
                height: 30px;
                line-height: 30px;
            }

            .title_info {
                height: 30px;
                line-height: 30px;
                display: flex;
                justify-content: space-between;
            }

            .owner_list {
                margin-top: 10px;
            }
        }
    }
</style>
