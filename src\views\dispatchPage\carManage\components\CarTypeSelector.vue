<template>
  <div>
    <el-autocomplete
      class="form-input"
      :fetch-suggestions="handleModelInput"
      @select="handleModelSelect"
      @change="handleModelChange"
      v-model="carModelText"
      :placeholder="placeholder"></el-autocomplete>
  </div>
</template>
<script>
  export default {
    props: ['value', 'placeholder'],
    data() {
      return {
        carModelText: '',
        allCarModel: []
      }
    },
    created() {
      this.getModelPromise = this.getModel()
        .then(res => {
          this.allCarModel = res
        })
    },
    methods: {
      getModel(name = '') {
        return this.$post('/admin-center-server/carmodel/listNoPage', {
          name
        })
      },
      handleModelInput(value, cb) {
        this.getModel(value)
          .then(data => {
            let list = data.map(v => {
              return {
                value: v.name,
                id: v.id
              }
            })
            cb(list)
          })
      },
      getModelText() {
        if (this.carModelText) return
        this.getModelPromise
          .then(() => {
            let item = this.allCarModel.find(item => item.id === this.value)
            if (!item) return
            this.carModelText = item.name
          })
      },
      handleModelChange(v) {
        // this.$emit('input', '')
        // this.$emit('changeName', '')
      },
      handleModelSelect(v) {
        this.$emit('input', v.id)
        this.carModelText = v.value
        this.$emit('changeName', v.value)
      },
      //在父组件中执行，传入品牌名，如果有此品牌则选中
      selectByName(name) {
        this.getModelPromise
          .then(() => {
            let item = this.allCarModel.find(item => item.name === name)
            if (item) {
              this.handleModelSelect({
                value: item.name,
                id: item.id
              })
            }
          })
      }
    },
    watch: {
      value() {
        this.getModelText()
      }
    }
  }
</script>

<style scoped>
.el-autocomplete {
  width: 100%;
}
</style>