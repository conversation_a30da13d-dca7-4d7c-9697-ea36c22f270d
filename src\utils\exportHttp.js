import axios from 'axios'
import {Message} from 'element-ui'
import store from '@/store'
import {getToken} from '@/utils/auth'

let base_url = process.env.VUE_APP_BASE_API
// if (process.env.NODE_ENV === 'production') {
//     base_url = 'https://dev.hongfeida.cn'
//     // base_url = 'http://127.0.0.1:7015'
// } else if (process.env.NODE_ENV === 'development') {
//     base_url = '/api'
// }
// create an axios instance
const serviceHeaderExport = axios.create({
    baseURL: base_url, // api 的 base_url
    timeout: 1000 * 60 * 30 // request timeout
});

// request interceptor
serviceHeaderExport.interceptors.request.use(
    config => {
        config.headers['AuthC'] = getToken();
        return config
    },
    error => {
        // Do something with request error
        console.log(error) // for debug
        Promise.reject(error)
    }
)

// response interceptor
serviceHeaderExport.interceptors.response.use(
    response => response,
    error => {
        console.log('err' + error); // for debug
        Message({
            message: '服务器错误',
            type: 'error',
            duration: 1000 * 60 * 30
        })
        return Promise.reject(error)
    }
)

export default serviceHeaderExport
