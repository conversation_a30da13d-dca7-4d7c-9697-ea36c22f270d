export const trackTypes = {
  app: '1',
  hfd: '2',
  beidou: '3',
  third: '4'
}
export const trackApi = {
  [trackTypes.app]: '/admin-center-server/app/orderItemGps/getTrackFromApp',
  [trackTypes.hfd]: '/admin-center-server/app/orderItemGps/getTrackFromGpsDevice',
  [trackTypes.beidou]: '/admin-center-server/app/orderItemGps/getGpsFromZJ',
  [trackTypes.third]: '/admin-center-server/app/orderItemGps/getTrackFromSf'
}
export const trackBeidouNotUnloadedApi = '/admin-center-server/app/orderItemGps/getGpsFromZJRealTime' //北斗 未卸货状态调用

export const currentApi = '/admin-center-server/app/orderItemGps/getGpsDeviceInfoByPlateNumber'
export const currentApiBeidou = '/order-center-server/app/orderItemGps/getLastPositionByCar'
export const auditTrackApi = '/admin-center-server/app/orderItemGps/getGpsV2'
export const auditSelectApi = '/admin-center-server/app/orderItemGps/createGpsFile'