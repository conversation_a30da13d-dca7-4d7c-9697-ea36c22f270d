import { instance as http, $get, $post } from '@/utils/http2'
import loadIcon from './images/start.png'
import endIcon from './images/end.png'
import auditStartIcon from './images/audit2.png'
import auditEndIcon from './images/audit1.png'
import { paintShape } from '@/components/FenceConfigurator/fence.js'
import { trackTypes, trackApi, currentApi, auditTrackApi, auditSelectApi, trackBeidouNotUnloadedApi, currentApiBeidou } from './trackConfig'
import { $httpMap } from '@/utils/http2'
import { webKey } from '@/utils/config'
import { Message } from 'element-ui'
export class Track {
  type
  requestId = 1 //避免多次请求时，偶尔出现标签页和显示的数据不符
  constructor({
    mapId,
    waybillData,
    orderData,
    isWaybillDetail,
    isNavigation,
    clockInList,// 打卡列表
    updateLoad,//修改后的装货点
    updateUnload, // 修改后的卸货点
    isAuditDetail, // 是否是审核详情页
    loadingData, // 装货地发车  审核详情页展示
    unloadingData,//卸货完成   审核详情页展示
  }) {
    this.mapId = mapId
    this.waybillData = waybillData
    this.orderData = orderData
    this.isWaybillDetail = isWaybillDetail
    this.isNavigation = isNavigation
    this.auditSelectType = null
    this.listeners = {}
    this.clockInList = clockInList
    this.updateLoad = updateLoad
    this.updateUnload = updateUnload
    this.isAuditDetail = isAuditDetail
    this.loadingData = loadingData
    this.unloadingData = unloadingData
    if (this.isWaybillDetail) {
      this.type = trackTypes.app
    } else {
      this.type = trackTypes.beidou
    }
  }

  on(eventName, fn) {
    this.listeners[eventName] = this.listeners[eventName] || []
    this.listeners[eventName].push(fn)
  }

  off(eventName, fn) {
    let handlers = this.listeners[eventName]
    if (handlers) {
      let index = handlers.indexOf(fn)
      if (index !== -1) {
        handlers.splice(index, 1)
      }
    }
  }

  trigger(eventName, payload) {
    let handlers = this.listeners[eventName]
    if (handlers) {
      handlers.forEach(v => v(payload))
    }
  }
  
  _filterTrack(positionObj) {
    positionObj = positionObj || []
    let positionArr = []
    let passFlag = false
    for (var i = 0; i < positionObj.length; i++) {
      if (passFlag) {
        passFlag = false
        continue
      }
      if (
        i >= 2
        &&
        i <= positionObj.length - 2
        &&
        positionObj[i].longitudeDegree === positionObj[i - 2].longitudeDegree
        &&
        positionObj[i].latitudeDegree === positionObj[i - 2].latitudeDegree
        &&
        positionObj[i + 1].longitudeDegree === positionObj[i - 1].longitudeDegree
        &&
        positionObj[i + 1].latitudeDegree === positionObj[i - 1].latitudeDegree
      ) {
        passFlag = true
        continue
      }
      var a = [];
      a.push(
        positionObj[i].longitudeDegree,
        positionObj[i].latitudeDegree,
        positionObj[i].positionTime,
        positionObj[i].speed,
        positionObj[i].estimatedArrival,
        positionObj[i].estimatedTimeliness,
        positionObj[i].mileage,
        positionObj[i].milesRemaining
      );
      positionArr.push(a);
    }
    return positionArr
  }

  _addMarkers(map) {
    let waybillData = this.waybillData,
      orderData = this.orderData
    // if (this.isWaybillDetail) {
      let  orderDeliveryLongitude =  orderData.deliveryLongitude
      let  orderDeliveryLatitude = orderData.deliveryLatitude
      let orderCommonDeliveryPlace = orderData.orderCommonDeliveryPlace
      let  orderRreceiveLongitude = orderData.receiveLongitude
      let  orderReceiveLatitude = orderData.receiveLatitude
      let orderCommonReceivePlace = orderData.orderCommonReceivePlace
      waybillData.itemLoadUnloadAddressInfoList.forEach(item => {
        if(item.type=='1'){
          orderDeliveryLongitude =  item.longitude
          orderDeliveryLatitude = item.latitude
          orderCommonDeliveryPlace = item.addressName
        }
        if(item.type=='3'){
          orderRreceiveLongitude = item.longitude
          orderReceiveLatitude = item.latitude
          orderCommonReceivePlace = item.addressName
        }
      })
      let loadMarker = new AMap.Marker({
        position: new AMap.LngLat(orderDeliveryLongitude, orderDeliveryLatitude),
        icon: loadIcon,
        offset: [-16, -16]
      })
      loadMarker.on('mouseover', event => {
        this.trigger('positionHover', {
          event,
          addressInfo: {
            address: orderCommonDeliveryPlace
          }
        })
      })
      let loadTextMarker = new AMap.Marker({
        position: new AMap.LngLat(orderDeliveryLongitude, orderDeliveryLatitude),
        content: '<div style="width:100px; margin-top:5px; margin-left:5px; color:rgb(20, 119, 18); font-size:14px; font-weight:bold">装货地</div>'
      })
      loadTextMarker.on('mouseover', event => {
        this.trigger('positionHover', {
          event,
          addressInfo: {
            address: orderCommonDeliveryPlace
          }
        })
      })
      let unloadMarker = new AMap.Marker({
        position: new AMap.LngLat(orderRreceiveLongitude, orderReceiveLatitude),
        icon: endIcon,
        offset: [-16, -16]
      })
      unloadMarker.on('mouseover', event => {
        this.trigger('positionHover', {
          event,
          addressInfo: {
            address: orderCommonReceivePlace
          }
        })
      })
      let unloadTextMarker = new AMap.Marker({
        position: new AMap.LngLat(orderRreceiveLongitude, orderReceiveLatitude),
        content: '<div style="width:100px; margin-top:5px; margin-left:5px; color:rgb(221, 32, 66); font-size:14px; font-weight:bold">卸货地</div>'
      })
      unloadTextMarker.on('mouseover', event => {
        this.trigger('positionHover', {
          event,
          addressInfo: {
            address: orderCommonReceivePlace
          }
        })
      })
      map.add(loadMarker)
      map.add(loadTextMarker)
      map.add(unloadMarker)
      map.add(unloadTextMarker)
    // } else {
      // let start = waybillData.gpsStartLonlat ? waybillData.gpsStartLonlat.split(',') : []
      // let end = waybillData.gpsEndLonlat ? waybillData.gpsEndLonlat.split(',') : []
      // let auditStartMarker = new AMap.Marker({
      //   position: new AMap.LngLat(start[0], start[1]),
      //   icon: auditStartIcon
      // })
      // let auditEndMarker = new AMap.Marker({
      //   position: new AMap.LngLat(end[0], end[1]),
      //   icon: auditEndIcon
      // })
      // map.add(auditStartMarker)
      // map.add(auditEndMarker)
    // }
  }
  // 取消点位信息框
  closeInfoWindow(id) {
    let infoWindowAll = document.getElementById(`info-window-all${id}`);
    if (!infoWindowAll) return;
    infoWindowAll.style.display = 'none';
  }
  _createMap(positionArr) {
    let mapId = this.mapId,
      waybillData = this.waybillData
    let map = new AMap.Map(
      mapId,
      {
        center: [112.405289, 40.904987],
        zoom: 6
      },
      AMapUI.loadUI(["misc/PathSimplifier"], PathSimplifier => {
        if (!positionArr || positionArr.length === 0) {
          return
        }
        //创建组件实例
        var pathSimplifierIns = new PathSimplifier({
          zIndex: 100,
          map: map, //所属的地图实例
          getPath: pathData => {
            return pathData.path
          },
          getHoverTitle: () => {
            return false
          },
          renderOptions: {
            keyPointTolerance: 1,
            //轨迹线的样式
            pathLineStyle: {
              lineWidth: 6,
              strokeStyle: "blue",
              dirArrowStyle: false
            },
            //轨迹节点样式
            // keyPointStyle: {
            //   radius: 5,
            //   fillStyle: '#fff',
            //   strokeStyle: 'black',
            //   lineWidth: 1
            // },
            keyPointStyle: {
              radius: 3,
              fillStyle: 'blue',
              // strokeStyle: 'black',
              strokeStyle: "blue",
              lineWidth: 0
            },
            // 禁用轨迹线悬停效果
            pathLineHoverStyle: false
          }
        });
        pathSimplifierIns.on('pointMouseover', (event, pointInfo) => {
          let data = pointInfo.pathData.path[pointInfo.pointIndex]
          let coordinate = data.slice(0, 2)
          let address
          $httpMap.get('https://restapi.amap.com/v3/geocode/regeo?parameters', {
            params: {
              location: `${coordinate[0]},${coordinate[1]}`,
              key: webKey
            }
          })
            .then(res => {
              address = res.data.regeocode.formatted_address
              this.trigger('pointHover', {
                event, pointInfo, address
              })
            })
        })
        
        pathSimplifierIns.setData([
          {
            name: "行驶轨迹",
            path: positionArr
          }
        ])

        var nav = pathSimplifierIns.createPathNavigator(0, {
          loop: true,
          speed: 10000, //distance / 1000 / 5 * 3600, //千米/小时，意为5秒走完
          pathNavigatorStyle: {
            strokeStyle: null,
            fillStyle: null
          }
        });
        nav.start();
      }),
      AMapUI.loadUI(['overlay/SimpleMarker'], SimpleMarker => {
        // 运行审核展示
        if (this.isAuditDetail) {
          if (this.loadingData && this.loadingData.checkInLon && this.loadingData.checkInLat) {
            let loadMarker = new SimpleMarker({
              iconLabel: `<div style="width: 100px;margin-top: 3px;margin-left: -32px;font-size: 16px;text-align: center; color: #fff">装</div>`,
              iconTheme: 'default',
              iconStyle: 'green',
              map: map,
              position: [this.loadingData.checkInLon, this.loadingData.checkInLat]
            })
            loadMarker.on('mouseover', event => {
              this.trigger('positionHover', {
                event,
                addressInfo: {
                  address: this.loadingData.checkInAddress || '--'
                }
              })
            })
          }
          if (this.unloadingData && this.unloadingData.checkInLon && this.unloadingData.checkInLat) {
            let unloadMarker = new SimpleMarker({
              iconLabel: `<div style="width: 100px;margin-top: 3px;margin-left: -32px;font-size: 16px;text-align: center; color: #fff">卸</div>`,
              iconTheme: 'default',
              iconStyle: 'red',
              map: map,
              position: [this.unloadingData.checkInLon, this.unloadingData.checkInLat]
            })
            unloadMarker.on('mouseover', event => {
              this.trigger('positionHover', {
                event,
                addressInfo: {
                  address: this.unloadingData.checkInAddress || '--'
                }
              })
            })
          }
        }else{
          // 运行详情展示
          if (this.updateLoad && this.updateLoad.checkInLon && this.updateLoad.checkInLat) {
            // 装货点图标
            let loadMarker = new SimpleMarker({
              iconLabel: `<div style="width: 32px;margin-top: 3px;margin-left: 2px;font-size: 16px;text-align: center; color: #fff">装</div>`,
              iconTheme: 'default',
              iconStyle: 'green',
              map: map,
              position: [this.updateLoad.checkInLon,this.updateLoad.checkInLat]
            })
            // 装货点信息
            let loadMarkerInfo = new SimpleMarker({
                iconLabel:`<div class="info-window-all position2" id='info-window-all-loadInfo'>
                          <div onclick="closeInfoWindow('-loadInfo')" class="info-window-icon" >
                            <i class="el-icon-close"></i>
                          </div>
                          <p class="info-item">
                            <span class='info-window-title'>节点</span>
                            <span class='info-content'>${this.updateLoad.checkInTypeStr || '--'}</span>
                          </p>
                          <p class="info-item">
                            <span class='info-window-title'>时间</span>
                            <span class='info-content'>${this.updateLoad.checkInTime || '--'}</span>
                          </p>
                           <p class="info-item">
                            <span class='info-window-title'>位置</span>
                            <span class='info-content'>${this.updateLoad.checkInAddress || '--'}</span>
                          </p>
                        </div>`,
              iconTheme: 'default',
              map: map,
              position: [this.updateLoad.checkInLon,this.updateLoad.checkInLat]
            })
          }
          if (this.updateUnload && this.updateUnload.checkInLon && this.updateUnload.checkInLat) {
            // 卸货点图标
            let unloadMarker = new SimpleMarker({
              iconLabel: `<div style="width: 32px;margin-top: 3px;margin-left: 2px;font-size: 16px;text-align: center; color: #fff">卸</div>`,
              iconTheme: 'default',
              iconStyle: 'red',
              map: map,
              position: [this.updateUnload.checkInLon,this.updateUnload.checkInLat]
            })
            // 卸货点信息
            let unloadMarkerInfo = new SimpleMarker({
                iconLabel:`<div class="info-window-all position2" id='info-window-all-unloadInfo'>
                          <div onclick="closeInfoWindow('-unloadInfo')" class="info-window-icon" >
                            <i class="el-icon-close"></i>
                          </div>
                          <p class="info-item">
                            <span class='info-window-title'>节点</span>
                            <span class='info-content'>${this.updateUnload.checkInTypeStr || '--'}</span>
                          </p>
                          <p class="info-item">
                            <span class='info-window-title'>时间</span>
                            <span class='info-content'>${this.updateUnload.checkInTime || '--'}</span>
                          </p>
                           <p class="info-item">
                            <span class='info-window-title'>位置</span>
                            <span class='info-content'>${this.updateUnload.checkInAddress || '--'}</span>
                          </p>
                        </div>`,
              iconStyle:{
                // src: require('./images/positionImg.png')
                width: '0px',
                height: '0px'
              },
              iconTheme: 'default',
              map: map,
              position: [this.updateUnload.checkInLon,this.updateUnload.checkInLat]
            })
          }
          if(this.type === trackTypes.beidou ||  this.type === trackTypes.app){
            this._addOfflineMarkers(map)
          }
            // 渲染经停点
            // 获取运单各个打卡点信息
              let trackInstance = this;
              // 定义全局函数来调用实例方法
              window.closeInfoWindow = function(id) {
                if (trackInstance) {
                  trackInstance.closeInfoWindow(id);
                }
              };
              this.clockInList.forEach((ele,index) => {
                if(ele.checkInLon&&ele.checkInLat){
                    let str2 = ''
                  if(ele.inFence === false && ele.inCheck){
                    if(ele.checkInTypeStr.includes('装货')){
                      str2 = `<div class='tag-window-tag-contain'><span size="mini" color="#fcd5d0" class="tag-class tag-window-tag">
                              <span>距装货地${ele.distance}km处打卡</span>
                            </span></div>` 
                    }else if(ele.checkInTypeStr.includes('经停')){
                      str2 = `<div class='tag-window-tag-contain'><span size="mini" color="#fcd5d0" class="tag-class tag-window-tag">
                              <span>距经停地${ele.distance}km处打卡</span>
                            </span></div>`
                    }else if(ele.checkInTypeStr.includes('卸货')){
                      str2 = `<div class='tag-window-tag-contain'><span size="mini" color="#fcd5d0" class="tag-class tag-window-tag">
                              <span>距卸货地${ele.distance}km处打卡</span>
                            </span></div>`  
                    }
                  }
                  let str3 = ''
                  if(ele.isLate && ele.lateDuration){
                    str3 = `<div class='tag-window-tag-contain'><span size="mini" color="#fcd5d0" class="tag-class tag-window-tag">
                              <span>晚点 ${ele.lateDuration}</span>
                            </span></div>` 
                  }
                  let className=''
                  if(str2){
                    className = 'classTop'
                    if(str3){
                      className = 'classTop2'
                    }
                  }   
                  let str = `<div class="info-window-all ${className}" id='info-window-all${ele.id?ele.id:index}'>
                              <div onclick="closeInfoWindow(${ele.id?ele.id:index})" class="info-window-icon">
                                <i class="el-icon-close"></i>
                              </div>
                              <p class="info-item">
                                <span class='info-window-title'>节点</span>
                                <span class='info-content'>${ele.checkInTypeStr || '--'}</span>
                              </p>
                              <p class="info-item">
                                <span class='info-window-title'>时间</span>
                                <span class='info-content'>${ele.checkInTime || '--'}</span>
                              </p>
                              <p class="info-item">
                                <span class='info-window-title'>位置</span>
                                <span class='info-content'>${ele.checkInAddress || '--'}</span>
                              </p>
                              ${str2}
                              ${str3}
                            </div>`
                   // 经停点图标
                  let clockMarker = new SimpleMarker({
                    iconLabel:str,
                    iconStyle:{
                      src: require('./images/positionImg.png'),
                      style: {
                          // width: '40px',
                          // height: '50px'
                      }
                    },
                    iconTheme: 'default',
                    map: map,
                    position: [ele.checkInLon,ele.checkInLat]
                  })
                  clockMarker.on('click', (event) => {
                    let infoWindowAll = document.getElementById(`info-window-all${ele.id}`);
                    if(infoWindowAll.style.display === 'none'){
                      infoWindowAll.style.display = 'block';
                    }
                  });
                }   
            })
        }
       
      })
    )
    return map
  }

  async _addCurrentPosition(map, lastLocation) {
    let waybillData = this.waybillData,
      orderData = this.orderData,
      currentLocation,
      distance,
      time,
      address
    //待卸货状态显示当前位置
    // if (waybillData.statusBd !== '1') return
    if (!(waybillData.statusBd == '1' || waybillData.statusBd == '10' || waybillData.statusBd == '11' ||waybillData.statusBd == '12' ||waybillData.statusBd == '13' ||waybillData.statusBd == '14' || waybillData.statusBd == '15')) return
    
    //只有app和鸿飞达设备，北斗显示轨迹
    if (![trackTypes.app, trackTypes.hfd, trackTypes.beidou].includes(this.type)) {
      return
    }
    let currentInfo = null
    if (this.type === trackTypes.app) {
      //轨迹最后一个点
      currentLocation = `${lastLocation[0]},${lastLocation[1]}`
      time = lastLocation[2]
      console.log('*****************',lastLocation)
      currentInfo = {
        longitude: lastLocation[0],
        latitude: lastLocation[1],
        positionTime: lastLocation[2],
        speed: lastLocation[3],
        estimatedArrival: lastLocation[4], // 预计到达时间
        estimatedTimeliness: lastLocation[5], // 预计时效
        mileage: lastLocation[6], // 总里程
        milesRemaining: lastLocation[7] // 剩余里程
      }
    } else if (this.type === trackTypes.hfd) {
      //车辆位置
      currentInfo = await $get(currentApi, {
        plateNumber: waybillData.carNumber
      })
      currentLocation = `${currentInfo.longitude},${currentInfo.latitude}`
      time = currentInfo.posTime
    }  else if (this.type === trackTypes.beidou) {
      //车辆位置
      currentInfo = await $get(currentApiBeidou, {
        carId: waybillData.carId,
        orderItemId: waybillData.orderItemId
      })
      currentLocation = `${currentInfo.longitude},${currentInfo.latitude}`
      time = currentInfo.positionTime
    }

    //逆地理编码
    let addressTextRes = await $httpMap.get('https://restapi.amap.com/v3/geocode/regeo?parameters', {
      params: {
        location: currentLocation,
        key: webKey
      }
    })
    address = addressTextRes.data.regeocode.formatted_address
    
    let receiveLongitude = orderData.receiveLongitude
    let receiveLatitude = orderData.receiveLatitude
    waybillData.itemLoadUnloadAddressInfoList.forEach(item => {
      if(item.type=='3'){
          receiveLongitude = item.longitude
          receiveLatitude = item.latitude
        }
    })
    //计算距终点距离
    let distanceRes = await $httpMap.get('https://restapi.amap.com/v3/direction/driving?parameters', {
      params: {
        origin: currentLocation,
        destination: `${receiveLongitude},${receiveLatitude}`,
        key: webKey
      }
    })
    let distanceData = distanceRes.data
    let paths = distanceData.route.paths
    if (paths.length > 0) {
      distance = paths[0].distance
      distance /= 1000
      distance = distance.toFixed(2)
    }
    const triggerCurrentShow = (event) => {
      if(currentInfo){
        this.trigger('currentHover', {
          event,
          currentInfo: {
              address,
              time,
              speed: currentInfo.speed, // 速度
              distance:currentInfo.milesRemaining? currentInfo.milesRemaining : distance,
              estimatedArrival: currentInfo.estimatedArrival, // 预计到达时间
              estimatedTimeliness: currentInfo.estimatedTimeliness, // 预计时效
              mileage: currentInfo.mileage // 总里程
            }
        })
      }
    }
    AMapUI.loadUI(['overlay/SimpleMarker'], SimpleMarker => {
      let current = new SimpleMarker({
        iconLabel: `<div style="width: 100px;margin-top: 3px;margin-left: -32px;font-size: 16px;text-align: center; color: #fff">车</div>`,
        iconTheme: 'default',
        iconStyle: 'blue',
        map: map,
        position: currentLocation.split(',')
      })
      current.on('mouseover', event => {
        triggerCurrentShow(event)
      })
    })
    // triggerCurrentShow()

    //车辆位置与终点连线
    if ((this.type === trackTypes.app || this.type === trackTypes.hfd || this.type === trackTypes.beidou) && paths.length > 0) {
      let positionArr = []
      paths[0].steps.forEach(v => { 
        let coordinates = v.polyline.split(';')
        positionArr = positionArr.concat(coordinates.map(v => v.split(',')))
      })
      AMapUI.loadUI(["misc/PathSimplifier"], PathSimplifier => {
        if (!positionArr || positionArr.length === 0) {
          return
        }

        //创建组件实例
        var pathSimplifierIns = new PathSimplifier({
          zIndex: 100,
          map: map, //所属的地图实例
          getPath: pathData => {
            return pathData.path
          },
          getHoverTitle: () => {
            return false
          },
          renderOptions: {
            keyPointTolerance: 1,
            //轨迹线的样式
            pathLineStyle: {
              lineWidth: 6,
              strokeStyle: "grey",
              dirArrowStyle: false
            },
            //轨迹节点样式
            // keyPointStyle: {
            //   radius: 5,
            //   fillStyle: '#fff',
            //   strokeStyle: 'black',
            //   lineWidth: 1
            // },
            keyPointStyle: {
              radius: 3,
              fillStyle: 'grey',
              // strokeStyle: 'black',
              strokeStyle: "grey",
              lineWidth: 0
            },
            // 禁用轨迹线悬停效果
            pathLineHoverStyle: false
          }
        });

        pathSimplifierIns.on('pointMouseover', (event, pointInfo) => {
          let data = pointInfo.pathData.path[pointInfo.pointIndex]
          let coordinate = data.slice(0, 2)
          let address
          $httpMap.get('https://restapi.amap.com/v3/geocode/regeo?parameters', {
            params: {
              location: `${coordinate[0]},${coordinate[1]}`,
              key: webKey
            }
          })
            .then(res => {
              address = res.data.regeocode.formatted_address
              // this.trigger('pointHover', {
              //   event, pointInfo, address
              // })
            })
        })
        
        pathSimplifierIns.setData([
          {
            name: "行驶轨迹",
            path: positionArr
          }
        ])
      })
    }
  }

  showTrack() {
    let waybillData = this.waybillData
    let url
    let requestId = ++this.requestId
    if (this.isNavigation) {
      url = "/admin-center-server/app/orderItemGps/getGpsFromGD"
    } else if (this.auditSelectType !== null) {
      url = auditSelectApi
    } else if (!this.isWaybillDetail) {
      url = auditTrackApi
    } else if (this.type === trackTypes.beidou && (waybillData.statusBd === '1' || waybillData.statusBd === '10' || waybillData.statusBd === '11' || waybillData.statusBd === '12' || waybillData.statusBd === '13' || waybillData.statusBd === '14' || waybillData.statusBd === '15')) {
      url = trackBeidouNotUnloadedApi
    } else {
      url = trackApi[this.type]
    }

    let data
    let params = {
      id: waybillData.orderItemId
    }
    if (this.auditSelectType !== null) {
      params.gpsFileFromEnum = this.auditSelectType
    }
    $post(url, {}, {
      params
    })
    .then(res => {
      if (requestId !== this.requestId) return
      if (res && res.routePathFrom) {
        if(!res.routePathFromStr){
          if(res.routePathFrom == '0'){
             res.routePathFromStr='北斗设备'
          }else if(res.routePathFrom == '2'){
             res.routePathFromStr='鸿飞达设备'
          }else if(res.routePathFrom == '4'){
            res.routePathFromStr='APP'
          }else if(res.routePathFrom == '5'){
            res.routePathFromStr='三方'
          }
        }
        this.trigger('source', {
          str: res.routePathFromStr,
          code: res.routePathFrom
        })
      }
      if (res.errorMsg) {
        Message.error(res.errorMsg)
        return
      } else {
        data = res
      }
    })
    .finally(() => {
      if (requestId !== this.requestId) return
      data = data || {}
      let pathPormise
      //这两种情况返回文件，需要再次请求文件：北斗并且不是待卸货状态、运单审核。其它情况直接返回轨迹信息
      // if ((this.type === trackTypes.beidou && waybillData.statusBd !== '1') || !this.isWaybillDetail) {
      if ((this.type === trackTypes.beidou && (!(waybillData.statusBd == '1' || waybillData.statusBd == '10' || waybillData.statusBd == '11' ||waybillData.statusBd == '12' ||waybillData.statusBd == '13' ||waybillData.statusBd == '14' || waybillData.statusBd == '15'))) || !this.isWaybillDetail) {
        if (!data.path) {
          pathPormise = Promise.resolve([])
        } else {
          pathPormise = http.get(data.path + '?' + Date.now())
            .then(res => {
              let positionArr
              if (res && res.data) {
                let positionStr = res.data
                if (typeof positionStr === 'object') {
                  positionStr = JSON.stringify(positionStr)
                }
                positionStr = "[" + positionStr + "]";
                positionArr = JSON.parse(positionStr);
                positionArr = this._filterTrack(positionArr)
                return positionArr
              }
            })
          }
      } else {
        let positionArr = data.gpsOssROList
        positionArr = this._filterTrack(positionArr)
        pathPormise = Promise.resolve(positionArr)
      }
      pathPormise.then(positionArr => {
        let map = this._createMap(positionArr)
        this._addMarkers(map)
        //待卸货状态显示当前位置
        console.log('1111',positionArr ,positionArr[positionArr.length - 1])
        this._addCurrentPosition(map, positionArr[positionArr.length - 1])
        if(this.type === trackTypes.beidou){
          if(data.parkArray && data.parkArray.length > 0){
            data.parkArray.forEach((ele,index) => {
              ele.parkBteAllStr = this.dateFormat(ele.parkBte, "yyyy-MM-dd HH:mm:ss")
              ele.parkEteAllStr = this.dateFormat(ele.parkEte, "yyyy-MM-dd HH:mm:ss")
              ele.parkBteStr = this.dateFormat(ele.parkBte, "MM-dd HH:mm")
              ele.parkEteStr = this.dateFormat(ele.parkEte, "MM-dd HH:mm")  
              ele.parkMinsStr = this.formatMinutes(ele.parkMins)
            })
            this._addStopMarkers(map, data.parkArray)
          }
          this.trigger('stopList', data.parkArray)
        }
        //围栏
        paintShape(map, waybillData)
        
        this.map = map
      })
    });
  }
//日期格式化
  dateFormat(date, format = "yyyy-MM-dd HH:mm:ss") {
    if (typeof date != "number" && !(date instanceof Date)) {
      date = date.replace(/-/g, "/");
    }
    let t = new Date(date);
    let tf = function(i) {
      return (i < 10 ? "0" : "") + i;
    };
    return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function(a) {
      switch (a) {
        case "yyyy":
          return tf(t.getFullYear());
          break;
        case "MM":
          return tf(t.getMonth() + 1);
          break;
        case "mm":
          return tf(t.getMinutes());
          break;
        case "dd":
          return tf(t.getDate());
          break;
        case "HH":
          return tf(t.getHours());
          break;
        case "ss":
          return tf(t.getSeconds());
          break;
      }
    });
  }
  // 格式化分钟
  formatMinutes(minutes) {
    if (isNaN(minutes) || minutes < 0) return '--';
    
    const days = Math.floor(minutes / 1440); // 1440分钟 = 1天
    const hours = Math.floor((minutes % 1440) / 60);
    const mins = minutes % 60;
    
    let result = '';
    if (days > 0) result += `${days}天`;
    if (hours > 0) result += `${hours}小时`;
    if (mins > 0 || result === '') result += `${mins}分钟`;
    
    return result;
  }
  // 添加停车点
  _addStopMarkers(map, stopList) {
     AMapUI.loadUI(['overlay/SimpleMarker'], SimpleMarker => {

      stopList.forEach((ele,index) => {
            if(ele.parkLon&&ele.parkLat){
              // 停车点图标
              let stopMarker = new SimpleMarker({
                iconTheme: 'default',
                iconStyle: {
                  src: require('./images/stop.png'),
                },
                map: map,
                position: [ele.parkLon,ele.parkLat]
              })
              const stopMarkerShow = (event) => {
                console.log('stopMarkerShow', event)
                this.trigger('stopHover', {
                  event,
                  dataInfo: ele
                })
              }
              stopMarker.on('mouseover', event => {
                stopMarkerShow(event)
              })
              // stopMarker.on('mouseover', event => {
              //   this.trigger('stopHover', {
              //     event,
              //     dataInfo: ele
              //   })
              // })
              // stopMarker.on('mouseout', () => this.trigger('stopOut'))
              // 离线点信息
              // let stopMarkerInfo = new SimpleMarker({
              //     iconLabel:`<div class="info-window-all position2" id='info-window-all-unloadInfo'>
              //               <div onclick="closeInfoWindow('-unloadInfo')" class="info-window-icon" >
              //                 <i class="el-icon-close"></i>
              //               </div>
              //               <p class="info-item">
              //                 <span class='info-window-title'>节点</span>
              //                 <span class='info-content'>停车</span>
              //               </p>
              //               <p class="info-item">
              //                 <span class='info-window-title'>位置</span>
              //                 <span class='info-content'>${ ele.parkAdr || '--'}</span>
              //               </p>
              //               <p class="info-item">
              //                 <span class='info-window-title'>开始</span>
              //                 <span class='info-content'>${ ele.parkBte || '--'}</span>
              //               </p>
                            
              //               <p class="info-item">
              //                 <span class='info-window-title'>结束</span>
              //                 <span class='info-content'>${ ele.parkEte || '--'}</span>
              //               </p>
              //               <p class="info-item">
              //                 <span class='info-window-title'>时长</span>
              //                 <span class='info-content'>${ele.parkMins || '--'}</span>
              //               </p>
              //             </div>`,
              //   iconStyle:{
              //     // src: require('./images/positionImg.png')
              //     width: '0px',
              //     height: '0px'
              //   },
              //   iconTheme: 'default',
              //   map: map,
              //   position: [ele.parkLon,ele.parkLat]
              // })
              }
            })
     })
     
  }
  // 添加离线点
  _addOfflineMarkers(map) {
    $get('/admin-center-server/app/orderItemGps/getBdOfflineRecord',{
      id: this.waybillData.orderItemId,
      type: this.type === trackTypes.beidou ? 1 : 2
    }).then(res => {
      if(res && res.length > 0){
        // 渲染离线点
        AMapUI.loadUI(['overlay/SimpleMarker'], SimpleMarker => {
          res.forEach((ele,index) => {
          if(ele.lastLongitude&&ele.lastLatitude){
            // 离线点图标
            let offlineMarker = new SimpleMarker({
              iconTheme: 'default',
              iconStyle: {
                src: require('./images/offlineImg.png'),
              },
              map: map,
              position: [ele.lastLongitude,ele.lastLatitude]
            })
            const offlineMarkerShow = (event) => {
              this.trigger('offlineHover', {
                event,
                dataInfo: ele
              })
            }
            offlineMarker.on('mouseover', event => {
              offlineMarkerShow(event)
            })
            // offlineMarker.on('mouseover', event => {
            //  this.trigger('offlineHover', {
            //       event,
            //       dataInfo: ele
            //     })
            // })
            // offlineMarker.on('mouseout', () => this.trigger('offlineOut'))
            // 离线点信息
            // let offlineMarkerInfo = new SimpleMarker({
            //     iconLabel:`<div class="info-window-all position2" id='info-window-all-unloadInfo'>
            //               <div onclick="closeInfoWindow('-unloadInfo')" class="info-window-icon" >
            //                 <i class="el-icon-close"></i>
            //               </div>
            //               <p class="info-item">
            //                 <span class='info-window-title'>节点</span>
            //                 <span class='info-content'>离线</span>
            //               </p>
            //               <p class="info-item">
            //                 <span class='info-window-title'>位置</span>
            //                 <span class='info-content'>${ele.lastLocation || '--'}</span>
            //               </p>
            //               <p class="info-item">
            //                 <span class='info-window-title'>开始</span>
            //                 <span class='info-content'>${ele.offlineStartTime || '--'}</span>
            //               </p>
                          
            //               <p class="info-item">
            //                 <span class='info-window-title'>结束</span>
            //                 <span class='info-content'>${ele.offlineEndTime || '--'}</span>
            //               </p>
            //               <p class="info-item">
            //                 <span class='info-window-title'>时长</span>
            //                 <span class='info-content'>${ele.offlineDurationStr || '--'}</span>
            //               </p>
            //             </div>`,
            //   iconStyle:{
            //     // src: require('./images/positionImg.png')
            //     width: '0px',
            //     height: '0px'
            //   },
            //   iconTheme: 'default',
            //   map: map,
            //   position: [ele.lastLongitude,ele.lastLatitude]
            // })
            }
          })
        })
      }
      this.trigger('offline', res)
    })
  }
  switchType(type) {
    this.type = type
    this.showTrack()
  }

  auditSelect(type) {
    this.auditSelectType = type
    this.isNavigation = false
    this.showTrack()
  }

  zoomIn() {
    this.map.zoomIn()
  }

  zoomOut() {
    this.map.zoomOut()
  }
}