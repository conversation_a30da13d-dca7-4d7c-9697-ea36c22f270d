//计算经度或纬度 加上或减去米 返回计算后的经度或纬度
export const GPSCoordinateComputeWithMeter = ({
  type, //lat lng
  coordinate,
  meter // can be negative
}) => {
  let perMeter = {
    lng: 0.00001141,
    lat: 0.00000899
  }
  return coordinate + meter * perMeter[type]
}

//已知中心坐标和半径的圆形区域，返回圆周的正上下左右点的坐标
export const getCoordinatesOfFourDirectionsOfCircle = ({
  centerCoordinate, //array
  radius
}) => {
  let top = [
    centerCoordinate[0],
    GPSCoordinateComputeWithMeter({
      coordinate: centerCoordinate[1],
      meter: -radius,
      type: 'lat'
    })
  ]
  let right = [
    GPSCoordinateComputeWithMeter({
      coordinate: centerCoordinate[0],
      meter: radius,
      type: 'lng'
    }),
    centerCoordinate[1]
  ]
  let bottom = [
    centerCoordinate[0],
    GPSCoordinateComputeWithMeter({
      coordinate: centerCoordinate[1],
      meter: radius,
      type: 'lat'
    })
  ]
  let left = [
    GPSCoordinateComputeWithMeter({
      coordinate: centerCoordinate[0],
      meter: -radius,
      type: 'lng'
    }),
    centerCoordinate[1]
  ]
  return [
    top,
    right,
    bottom,
    left
  ]
}