<template>
  <div class="app-container carDetail">
    <template>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="订单详情" name="first">
          <OrderDetail :tableData="tableData" :ruleData="ruleData"></OrderDetail>
        </el-tab-pane>

        <el-tab-pane label="所属运单" name="third">
          <div class="main-box">
            <div class="base-info">
              <div class="title-box flex">
                <div>当前订单状态：{{statusEnum}}</div>
              </div>
              <div class="list-main">
                <template>
                  <el-table :data="waybillTableData" border style="width: 100%">
                    <el-table-column prop="sn" label="运单单号" width="200">
                      <template slot-scope="scope">
                        <el-button type="text" @click="$router.push('/transport/transportListDetail?orderItemId=' + scope.row.orderItemId)">{{ scope.row.sn }}</el-button>
                      </template>
                    </el-table-column>
                    <el-table-column prop="amount" label="司机运费" :formatter="formatMoney"></el-table-column>
                    <el-table-column prop="driverName" label="接单司机"></el-table-column>
                    <el-table-column prop="carNumber" label="接单车辆"></el-table-column>
                    <el-table-column prop="ton" label="抢单吨数" :formatter="formatTon"></el-table-column>
                    <el-table-column prop="originalTon" label="装货吨数" :formatter="formatTon"></el-table-column>
                    <el-table-column prop="currentTon" label="卸货吨数" :formatter="formatTon"></el-table-column>
                  </el-table>
                </template>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="所属调度单" name="second">
          <div class="main-box">
            <div class="base-info">
              <div class="title-box">
                <div>当前订单状态：{{statusEnum}}</div>
              </div>
              <div class="list-main">
                <template>
                  <el-table :data="agentTableData" border style="width: 100%"
                  cell-class-name="table_cell_gray"
                  header-cell-class-name="table_header_cell_gray"
                  >
                    <el-table-column prop="orderDispatchSn" label="调度单号" width="100">
                      <template slot-scope="scope">
                        <el-button type="text" @click="$router.push('/order/agentOrder/agentOrderDetail?orderBrokerId=' + scope.row.orderDispatchId)">{{ scope.row.orderDispatchSn }}</el-button>
                      </template>
                    </el-table-column>
                    <el-table-column prop="capacitySupplierName" label="运力供应商"></el-table-column>
                    <el-table-column prop="capacitySupplierMobile" label="手机号"></el-table-column>
                    <el-table-column prop="sumTon" :label="'总货量' + '(' + labelUnit() + ')'" :formatter="unitTableFormatter"></el-table-column>
                    <el-table-column prop="pendingShippingTons" :label="'待装车' + '(' + labelUnit() + ')'" :formatter="unitTableFormatter"></el-table-column>
                    <el-table-column prop="shippingTons" :label="'运输中' + '(' + labelUnit() + ')'" :formatter="unitTableFormatter"></el-table-column>
                    <el-table-column prop="pendingReceivedTons" :label="'待收货' + '(' + labelUnit() + ')'" :formatter="unitTableFormatter"></el-table-column>
                    <el-table-column prop="receivedTons" :label="'已收货' + '(' + labelUnit() + ')'" :formatter="unitTableFormatter"></el-table-column>
                    <el-table-column prop="remainTon" :label="'剩余货量' + '(' + labelUnit() + ')'" :formatter="unitTableFormatter"></el-table-column>
                    <el-table-column prop="statusString" label="调度单状态"></el-table-column>
                  </el-table>
                </template>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="订单日志" name="fourth">
          <div class="main-box">
            <div class="base-info">
              <div class="title-box">
                <div>当前订单状态：{{statusEnum}}</div>
              </div>
              <div class="list-main">
                <template>
                  <el-table :data="logTableData" border style="width: 100%">
                    <el-table-column type="index" label="编号" width="50"></el-table-column>
                    <el-table-column prop="operator" label="操作者"></el-table-column>
                    <el-table-column prop="createdDate" label="操作时间"></el-table-column>
                    <el-table-column prop="statusEnum" label="订单状态"></el-table-column>
                    <el-table-column prop="content" label="备注"></el-table-column>
                  </el-table>
                </template>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div class="buttons flex">
        <!-- <el-button size="small" type="primary" @click="assignSupplier">指派运力供应商</el-button> -->
        <el-button size="small" type="primary" @click="queryPlatformDesignationRecord">承运司机</el-button>
        <el-button size="small" type="primary" @click="openEleFence">查看电子围栏</el-button>
        <el-button size="small" type="primary" @click="changeTime">修改时间</el-button>
        <el-button size="small" type="primary" v-if="tableData[0].operationalPeoFlag == 1" @click="dialogChangePlatformConsignorFreight2 = true">修改竞标价</el-button>

        <el-button
          type="primary"
          size="small"
          @click="changeFee"
          v-if="paymentType==1 && type!=2"
        >修改运费</el-button>
        <el-button type="primary" size="small" @click="changeCargoType">修改货物名称</el-button>
        <el-button
          type="primary"
          @click="frozenOrder"
          size="small"
          v-if="freezeStatus==0"
        >冻结订单</el-button>
        <el-button
          type="primary"
          @click="unFrozen"
          size="small"
          v-if="freezeStatus==1"
        >解冻订单</el-button>
        <el-button type="primary" @click="cancelOrder" size="small">取消订单</el-button>
      </div>
      <!-- 修改货物类型的弹窗 -->
      <el-dialog title="修改货物名称" :visible.sync="updateCargoTypeDialog" width="600px">
        <el-select v-model="cargoType" placeholder="请选择货物类型" @change="handleCargoTypeChange">
          <el-option v-for="item in cargoTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select v-model="cargoName" placeholder="请选择货物名称" style="margin-left: 10px">
          <el-option v-for="item in subCargoTypes" :key="item.id" :label="item.name" :value="item.name"></el-option>
        </el-select>

        <span slot="footer" class="dialog-footer">
          <el-button @click="updateCargoTypeDialog = false">取 消</el-button>
          <el-button type="primary" @click="updateCargoType">确 定</el-button>
        </span>
      </el-dialog>
      <!--  取消的弹窗 -->
      <el-dialog title=" 取消订单" :visible.sync="dialogForCancel">
        <el-form :model="form">
          <el-form-item label="取消原因" :label-width="formLabelWidth" required>
            <el-select v-model="form.cancelReason" placeholder="请选择取消原因">
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.content"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="其他原因" :label-width="formLabelWidth" style="margin-top:20px">
            <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="form.otherReason"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogForCancel = false">取 消</el-button>
          <el-button type="primary" @click="cancelSure">确定</el-button>
        </div>
      </el-dialog>

      <!-- 冻结的弹窗 -->
      <el-dialog title=" 冻结订单" :visible.sync="dialogForFrozen">
        <el-form :model="form">
          <el-form-item label="冻结原因" :label-width="formLabelWidth" required>
            <el-select v-model="form.fronzeReason" placeholder="请选择冻结原因">
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.content"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            label="其他原因"
            :label-width="formLabelWidth"
            style="
          margin-top:20px"
          >
            <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="form.fronzeOterReason"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogForFrozen = false">取 消</el-button>
          <el-button type="primary" @click="frozenSure" v-if="freezeStatus == 0">确 定</el-button>
          <el-button type="primary" @click="unFrozen" v-if="freezeStatus == 1">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 修改时间的弹窗 -->
      <el-dialog title="修改时间" :visible.sync="dialogForTime" :before-close="handleClose">
        <div>
          <el-form>
            <el-form-item label="当前订单创建时间：">
              <span>{{creatTime}}</span>
            </el-form-item>
            <el-form-item label="修改后订单创建时间：">
              <el-date-picker
                v-model="changeDate"
                type="datetime"
                placeholder="选择日期时间"
                value-format="yyyy-MM-dd HH:mm:ss"
              ></el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogForTime = false">取 消</el-button>
          <el-button type="primary" @click="changeTimeSure">确 定</el-button>
        </span>
      </el-dialog>

      <!-- 修改与货主约定运费 -->
      <el-dialog
        title="修改与货主约定运费单价"
        :visible.sync="dialogChangePlatformConsignorFreight">
        <div>当前运费单价：{{unitMoneyFormatter(tableData[0].freightCalcType, tableData[0].platformConsignorFreight)}}</div>
        <div style="margin-top: 15px">
          修改后运费单价：<el-input v-model="inputPlatformConsignorFreight" style="width: 200px" placeholder="请输入与货主约定运费单价" oninput="value=value.match(/\d+\.?\d{0,2}/,'')"></el-input>
          {{tableData[0].freightCalcType === '0' ? '元/吨' : '元/车'}}
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogChangePlatformConsignorFreight = false">取 消</el-button>
          <el-button type="primary" @click="changePlatformConsignorFreight">确 定</el-button>
        </span>
      </el-dialog>
      <el-dialog
        title="修改竞标价"
        :visible.sync="dialogChangePlatformConsignorFreight2">
        <el-form
        :model="competitiveBiddingform"
        ref="competitiveBiddingform"
        :rules="rules"
        label-width="120px"
        size="mini"
        class="edit-form">
        <el-form-item label="竞标ID" prop="competitiveBiddingCode">
          <el-input  v-model="competitiveBiddingform.competitiveBiddingCode" placeholder="请输入竞标ID" class="self-adaption-form-item"></el-input>
        </el-form-item>
        <el-form-item label="竞标价" prop="competitiveBiddingPrice" class="self-adaption-form-item">
          <!-- @blur="getCalculateFn" -->
          <el-input v-model="competitiveBiddingform.competitiveBiddingPrice"  placeholder="请输入竞标价">
              <template slot="append" v-if="tableData[0].freightCalcType === '0'">元/吨</template>
              <template slot="append" v-else-if="tableData[0].freightCalcType === '1'">元/车</template>
          </el-input>
        </el-form-item>
        <!-- <el-form-item label="与货主约定运费" v-if="false" prop="platformConsignorFreight" class="self-adaption-form-item">
            <el-input v-model="competitiveBiddingform.platformConsignorFreight" placeholder="修改竞标价后，自动计算" disabled>
                <template slot="append" v-if="tableData[0].freightCalcType === '0'">元/吨</template>
                <template slot="append" v-else-if="tableData[0].freightCalcType === '1'">元/车</template>
              </el-input>
          </el-form-item> -->
      </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogChangePlatformConsignorFreight2 = false">取 消</el-button>
          <el-button type="primary" @click="submitCompetitiveBiddingFn" :loading="competitiveBiddingLoading">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 承运车辆 -->
      <el-dialog
        title="承运司机"
        :visible.sync="addDriverDialog">
        <div>
          <div class="add-driver-item" v-for="(item, index) in addDrivers" :key="index">
            <el-autocomplete
              class="add-driver-input"
              v-model="item.driver.name"
              :fetch-suggestions="querySearchDriver"
              placeholder="请选择承运司机，可按姓名或手机号搜索"
              @select="handleSelectDriver($event, index)"
            ></el-autocomplete>
            <el-select
              class="add-driver-input"
              v-model="item.selectedCars"
              multiple
              placeholder="请选择承运车辆"
              >
              <el-option
                v-for="i in item.cars"
                :key="i.id"
                :label="i.label"
                :value="i.id">
              </el-option>
            </el-select>
            <div class="add-driver-delete" @click="addOneDriver(2, index)">—</div>
          </div>
          <el-button size="small" style="margin-left:20px" @click="addOneDriver(1)">+添加承运司机</el-button>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="addDriverDialog = false">取 消</el-button>
          <el-button type="primary" @click="confrimAddDriver">确 定</el-button>
        </span>
      </el-dialog>
    </template>
    <AssignSupplier ref="assignSupplier" @assignSupplierSuccess="handleAssignSupplierSuccess"></AssignSupplier>
  </div>
</template>
<script>
import DetailRow from '../../../components/OrderDetail/DetailRow.vue';
import { showFenceConfigurator } from '@/components/FenceConfigurator/FenceConfigurator.js'
import AssignSupplier from '@/components/AssignSupplier/AssignSupplier'
export default {
  components: { DetailRow,AssignSupplier },
  name: "CarDetail",
  data() {
    return {
      isBrevityName: "", //是否短倒
      cargoType: "", //获取类型
      cargoName: '',
      businessId: "",
      activeName: "first",
      changeDate: "", //修改后的时间
      dialogChangePlatformConsignorFreight: false,
      dialogChangePlatformConsignorFreight2:false,
      competitiveBiddingform:{}, //修改与货主约定运费单价对象
      inputPlatformConsignorFreight: '',
      dialogForCancel: false, // 取消订单
      dialogForFrozen: false, //冻结订单
      dialogForTime: false, //修改时间的弹窗
      updateCargoTypeDialog: false, //修改货物类型
      freezeStatus: null, //判断冻结 活着解冻的状态
      statusEnum: "", //当前订单状态
      creatTime: "", //当前订单创建时间
      baseInfo: {},
      statusBd: "", // 订单详情下标识时货主还是调度员1是货主 2是调度员
      formLabelWidth: "120px",
      tableData: [{}],
      agentTableData: [], // 调度员单
      waybillTableData: [], //所属运单
      logTableData: [], //日志
      options: [], //  取消/冻结 原因
      cargoTypes: [],
      subCargoTypes: [],
      form: {
        cancelReason: "", //取消原因的ID
        otherReason: "", //其他原因
        fronzeReason: "", //冻结原因 ID
        fronzeOterReason: "" //冻结的其他原因
      },
      type: "", //订单类型
      paymentType: "", //付款方式
      freight: "", //运费单价
      ruleData: [],
      addDriverDialog: false,
      addDrivers: [],
      orderAuditDialog: false,
      orderAuditMemo: '',
      competitiveBiddingLoading:false,
      rules:{
        competitiveBiddingCode:[
          { required: true, message: '请输入竞标ID', trigger: 'blur' }
        ],
        competitiveBiddingPrice:[
          { required: true, message: '请输入竞标价', trigger: 'blur' },
          {
            validator(rule, value, cb) {
              let num = Number(value)
              if (Number.isNaN(num) || num <= 0) {
                cb('必须为正数')
              }
              cb()
            },
            trigger: 'blur'
          }
        ],
        platformConsignorFreight: [
          { required: true, message: '请修改竞标价后，自动计算', trigger: 'blur' },
          {
            validator(rule, value, cb) {
              let num = Number(value)
              if (Number.isNaN(num) || num <= 0) {
                cb('必须为正数，最多两位小数')
              }
              if (!Number.isInteger(num)) {
                let decimalDigits = value.split('.')[1]
                if (decimalDigits.length > 2) {
                  cb('必须为正数，最多两位小数')
                }
              }
              cb()
            },
            trigger: 'blur'
          }
        ],
      }
    };
  },
  activated() {
    this.getOrderDetail(); //获取订单详情
    this.type = this.$route.query.type;
  },
  methods: {
    assignSupplier() {
      this.$refs.assignSupplier.show({
        id: this.tableData[0].orderCommonId,
        left: this.tableData[0].leftTon,
        unit: this.tableData[0].freightCalcType,
        sum: this.tableData[0].totalTon,
        defaultIdList: this.tableData[0].capacitySupplierIds
      })
    },
    handleAssignSupplierSuccess() {
      // this.$refs.supplierDetail.getData()
      this.getOrderDetail(); //获取订单详情
      this.type = this.$route.query.type;
    },
    goRevise() {
      this.$router.push("/carsList/carRevise");
    },
    /* 取消订单 */
    cancelOrder() {
      this.dialogForCancel = true;
      this.$http
        .get("/admin-center-server/order/reasonlist", {
          params: {
            reasonType: 2
          }
        })
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.options = data.data;
            // console.log(this.options);
          }
        });
    },
    /* 冻结订单 */
    frozenOrder() {
      this.dialogForFrozen = true;
      this.$http
        .get("/admin-center-server/order/reasonlist", {
          params: {
            reasonType: 1
          }
        })
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.options = data.data;
            // console.log(this.options);
          }
        });
    },
    changePlatformConsignorFreight() {
      this.$post('admin-center-server/order/updatePlatformConsignorFreightByOrderSn',
       {orderSn: this.tableData[0].sn, platformConsignorFreight: this.inputPlatformConsignorFreight}).then(
         res => {
           this.$message.success('操作成功')
           this.dialogChangePlatformConsignorFreight = false
           this.getOrderDetail()
         }
       )
    },
    // 通过竞标价和项目公式计算出与货主约定运费
    getCalculateFn(){
      if(!this.competitiveBiddingform.competitiveBiddingPrice){
        this.competitiveBiddingform.platformConsignorFreight = ''
        return
      }
      this.$refs.competitiveBiddingform.validate(valid => {
        if (!valid) return
        this.competitiveBiddingLoading = true
        this.$post('/order-center-server/project/calculate?biddingPrice='+this.competitiveBiddingform.competitiveBiddingPrice+'&id='+this.tableData[0].projectId, {})
          .then(res => {
            this.competitiveBiddingform.platformConsignorFreight = res
            this.competitiveBiddingLoading = false
          })
      })
    },
    // 提交修改竞标价
    submitCompetitiveBiddingFn(){
      this.$refs.competitiveBiddingform.validate(valid => {
        if (!valid) return
        let params = {
          ...this.competitiveBiddingform,
          id:this.tableData[0].orderCommonId
        }
        this.$post(
          "/admin-center-server/order/updateCompetitiveById",
          params
        )
        .then(() => {
          this.dialogChangePlatformConsignorFreight2 = false; //关闭弹窗
          this.$message.success("修改成功");
          //修改竞标价之后刷新下 订单详情
          this.getOrderDetail();
        })
      })
    },
    async queryPlatformDesignationRecord() {
      this.addDrivers = []
      this.addDriverDialog = true
    },
    addOneDriver(isAdd, index) {
      if (isAdd == 1) {
        let item = {
          driver: {
            name: '',
            value: '',
            driverId: ''
          },
          id: '',
          cars: [],
          selectedCars: []
        }
        this.addDrivers.push(item)
      } else {
        this.addDrivers.splice(index, 1)
      }
      
    },
    querySearchDriver(search, cb) {
      this.$post('/admin-center-server/order/orderBusinessDriver', {driverNameOrMobile: search, orderBusinessId: this.$route.query.orderBusinessId}).then(
        res => {
          let drivers = res.map(item => {
            return {
              name: item.name + ' ' + item.mobile,
              value: item.name + ' ' + item.mobile,
              driverId: item.userId
            }
          })
          cb(drivers)
        }
      )
    },
    handleSelectDriver(item, index) {
      this.addDrivers[index].driver = item
      this.addDrivers[index].selectedCars = []

      this.$post('/admin-center-server/order/orderBusinessCar',{driverId: item.driverId, orderBusinessId: this.$route.query.orderBusinessId}).then(
        res => {
          this.addDrivers[index].cars = res.map(v => {
            return {
              id: v.carId,
              value: v.carNumber + ' ' + v.name + '' + v.capacityTonnage + 't',
              label: v.carNumber + ' ' + v.name + '' + v.capacityTonnage + 't'
            }
          })
        }
      )

    },
    confrimAddDriver() {
      if (this.addDrivers.length == 0) {
        this.$message.warning("请添加司机与车辆");
        return
      }
      
      let params = this.addDrivers.map(v => {
        return {
          driverId: v.driver.driverId,
          id: v.id,
          orderBusinessId: this.$route.query.orderBusinessId,
          carIdList: v.selectedCars
        }
      })

      this.$post('/admin-center-server/order/platformDesignation',params).then(
        res => {
          this.$message.success("操作成功");
          this.addDriverDialog = false
        }
      )
    },
    /* 获取订单详情 */
    getOrderDetail() {
      let orderBusinessId = Number(this.$route.query.orderBusinessId);
      var postData = {
        orderBusinessId: orderBusinessId
      };
      this.$http
        .get(
          "/admin-center-server/order/getOrderBusinessDetailOrder?orderBusinessId=" +
            orderBusinessId
        )
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            //订单详情
            this.tableData = data.data.orderBusinessDetail;
            //this.isBrevityName = data.data.orderBusinessDetail[0].isBrevityName; //是否短倒
            this.statusBd = data.data.orderBusinessDetail[0].userType;
            this.type = data.data.orderBusinessDetail[0].type;
            this.statusEnum = data.data.orderBusinessDetail[0].statusEnum;
            this.paymentType = data.data.orderBusinessDetail[0].paymentType; //付款方式
            this.freight = data.data.orderBusinessDetail[0].freight; //运费单价
            this.businessId = data.data.orderBusinessDetail[0].businessId;
            this.creatTime = data.data.orderBusinessDetail[0].createdDate; //订单创建日期
            //冻结解冻的状态
            this.freezeStatus = data.data.orderBusinessDetail[0].freezeStatus;
            this.competitiveBiddingform = {
              competitiveBiddingCode:data.data.orderBusinessDetail[0].competitiveBiddingCode,
              competitiveBiddingPrice:data.data.orderBusinessDetail[0].competitiveBiddingPrice,
              platformConsignorFreight:data.data.orderBusinessDetail[0].platformConsignorFreight
            }
            this.$get(`/admin-center-server/rule/getRuleRormulatianById?id=${this.tableData[0].ruleId}&isOperated=${this.tableData[0].operationalPeoFlag}`).then(
              res => {
                this.ruleData = res
              }
            )
            }
        });
    
    },
    handleClose() {
      this.dialogForTime = false;
    },
    /* 取消确定 */
    cancelSure() {
      if (this.form.cancelReason == "") {
        this.$message.warning("请选择原因");
        return;
      }
      let userType = this.statusBd;
      // console.log(userType);
      let type = this.$route.query.type; //订单类型
      let postData = {
        userType: 1,
        type: parseInt(type),
        reasonId: parseInt(this.form.cancelReason),
        reasonMemo: this.form.otherReason,
        orderId: parseInt(this.$route.query.orderBusinessId)
      };

      // console.log(postData);
      this.$http
        .post("/admin-center-server/order/cancelOrder", postData)
        .then(res => {
          if (res.data.code == "200") {
            this.dialogForCancel = false
            this.$router.go(-1); //成功之后返回
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },
    /* 冻结确认 */
    frozenSure() {
      // console.log(this.form.fronzeReason);
      let userType = this.statusBd;
      if (this.form.fronzeReason == "") {
        this.$message.warning("请选择原因");
        return;
      }
      // console.log(this.statusBd);
      let type = this.$route.query.type; //订单类型  0 -广播单 ；1定向单；2 货主单
      let status = this.$route.query.status; //订单状态
      let postData = {
        orderId: parseInt(this.$route.query.orderBusinessId),
        reasonId: parseInt(this.form.fronzeReason),
        reasonMemo: this.form.fronzeOterReason,
        userType: 1,
        type: parseInt(type),
        freezeStatus: 1,
        status: parseInt(status)
      };

      // console.log(postData);
      this.$http
        .post("/admin-center-server/order/freezeOrder", postData)
        .then(res => {
          if (res.data.code == "200") {
            this.$message({
              type: "success",
              message: "冻结成功!"
            });
             this.dialogForFrozen = false
            this.$router.go(-1);
           
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },
    handleClick(tab, event) {
      if (tab.name == "first") {
        //订单详情
        this.getOrderDetail();
      }
      if (tab.name == "second") {
        //所属调度员单
        //所属调度员单
        this.belongAgentOrder();
      }
      if (tab.name == "third") {
        //所属运单
        let orderBusinessId = Number(this.$route.query.orderBusinessId);
        //运单详情
        this.$http
          .get(
            "/admin-center-server/order/getOrderBusinessItemDetailYd?orderBusinessId=" +
              orderBusinessId
          )
          .then(res => {
            let data = res.data;
            // console.log(res.data.data);
            if (data.code === "200") {
              this.waybillTableData = res.data.data;
            } else {
              this.$message.warning(res.data.message);
            }
          });
      }
      if (tab.name == "fourth") {
        //订单日志
        this.orderLog();
      }
    },
    /* 解冻 */
    unFrozen() {
      // console.log(this.form.fronzeReason);
      let userType = this.statusBd;
      let type = this.$route.query.type; //订单类型
      let status = this.$route.query.status; //订单状态
      let postData = {
        orderId: parseInt(this.$route.query.orderBusinessId),
        reasonId: parseInt(this.form.fronzeReason),
        reasonMemo: this.form.fronzeOterReason,
        userType: 1, // 1代表客户 2代表调度员
        type: parseInt(type),
        freezeStatus: 0,
        status: parseInt(status)
      };

      // console.log(postData);
      this.$http
        .post("/admin-center-server/order/freezeOrder", postData)
        .then(res => {
          if (res.data.code == "200") {
            this.$message({
              type: "success",
              message: "解冻成功!"
            });
            this.dialogForFrozen = false
            this.$router.go(-1);
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },
    /* 给金额添加单位 */
    formatMoney(row, column) {
      let money = row[column.property];
      // console.log(row, "money");
      if (money == null) {
        return "";
      } else {
        return money + " 元 ";
      }
    },
    moneyFormatter(value) {
      if (value === '' || value === null) return ''
      return String(value) + '元'
    },
    /* 给吨数加单位 */
    formatTon(row, column) {
      let ton = row[column.property];
      if (ton == null) {
        return "";
      } else {
        return ton + " 吨 ";
      }
    },
    tonFormatter(value) {
      if (value === '' || value === null) return ''
      return String(value) + '吨'
    },
    /* 单价 */
    formatPrice(row, column) {
      let price = row[column.property];
      return price + "元/吨";
    },
    priceFormatter(value) {
      if (value === '' || value === null) return ''
      return String(value) + '元/吨'
    },
    labelUnit() {
      return this.tableData[0].freightCalcType === '0' ? '吨' : '车'
    },
    /* 所属调度员单 */
    belongAgentOrder() {
      let orderBusinessId = Number(this.$route.query.orderBusinessId);
      // this.$http
      //   .get(
      //     "/admin-center-server/order/getOrderBusinessItemDetailBroker?orderBusinessId=" +
      //       orderBusinessId
      //   )
      //   .then(res => {
      //     let data = res.data;
      //     // console.log(res.data.data);
      //     if (data.code === "200") {
      //       this.agentTableData = res.data.data;
      //     } else {
      //       this.$message.warning(res.data.message);
      //     }
      //   });
            // 获取调度单
      this.$get('admin-center-server/orderDispatch/getorderDispatchByOrderBusinessId?orderBusinessId=' + orderBusinessId).then(
        res => {
          this.agentTableData = res;

          // 测试
          // this.agentTableData = [{
          //   "capacitySupplierMobile": "***********",
          //   "capacitySupplierName": "运力供应商姓名",
          //   "dispatcher": 0,
          //   "dispatcherName": "",
          //   "dispatcherPhone": "",
          //   "freightCalcType": 0,
          //   "orderDispatchId": 0,
          //   "orderDispatchSn": "T00000001",
          //   "pendingReceivedTons": 0,
          //   "pendingShippingTons": 0,
          //   "receivedTons": 0,
          //   "shippingTons": 0,
          //   "status": 0,
          //   "sumTon": 0
          // }]
        }
      )
    },
    /* 订单日志 */
    orderLog() {
      let orderBusinessId = Number(this.$route.query.orderBusinessId);
      this.$http
        .get(
          "/admin-center-server/order/getOrderBusinessItemDetailLog?orderBusinessId=" +
            orderBusinessId
        )
        .then(res => {
          let data = res.data;
          // console.log(res.data.data);
          if (data.code === "200") {
            this.logTableData = res.data.data;
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },
    /* 修改运费 */
    changeFee() {
      this.$router.push({
        path: "/orderManage/changeFee",
        query: {
          orderBusinessId: this.$route.query.orderBusinessId, //订单的id
          orderType: this.$route.query.type, //订单类型
          freight: this.freight, //运费单价
          statusEnum: this.statusEnum //订单状态（汉字）
        }
      });
    },
    /* 修改货物类型的弹窗 */
    changeCargoType() {
      this.cargoType = this.tableData[0].cargoTypeClassificationCode
      this.cargoName = this.tableData[0].cargoType
      this.getSubCargoType(this.cargoType)
      this.getCargotype();
      this.updateCargoTypeDialog = true;
    },
    /* 货物类型列表 */
    getCargotype() {
      this.$get('/admin-center-server/order/dict/findDictByType', {
        dictType: 'cargoType'
      })
        .then(res => {
          this.cargoTypes = res
        })
    },
    handleCargoTypeChange(v){
      this.cargoName = ''
      this.getSubCargoType(v)
    },
    getSubCargoType(v) {
      this.$get('/admin-center-server/cargotype/cargoTypelistByUserId', {
        userId: Number(this.businessId),
        code: v
      })
        .then(res => {
          this.subCargoTypes = res
        })
    },
    /* 修改货物类型 */
    updateCargoType() {
      if (!this.cargoType || !this.cargoName) {
        this.$message.error('请选择货物类型和货物名称')
        return
      }
      let orderId = Number(this.$route.query.orderBusinessId);
      this.$post(
          "/admin-center-server/order/updateCargoName?cargoTypeClassificationCode=" +
            this.cargoType +
            "&orderId=" +
            orderId + 
            '&cargoName=' +
            this.cargoName
        )
        .then(res => {
          this.$message({
            type: "success",
            message: "修改成功"
          });
          this.getOrderDetail()
          this.updateCargoTypeDialog = false;
        });
    },
    openEleFence() {
      showFenceConfigurator({
        deliveryPoint: [this.tableData[0].deliveryLongitude, this.tableData[0].deliveryLatitude],
        receivePoint: [this.tableData[0].receiveLongitude, this.tableData[0].receiveLatitude],
        deliveryFenceId: this.tableData[0].deliveryFenceId,
        receiveFenceId: this.tableData[0].receiveFenceId
      })
    },
    /* 修改时间的弹窗 */
    changeTime() {
      this.dialogForTime = true;
    },
    /*提交修改时间 */
    changeTimeSure() {
      let orderId = parseInt(this.$route.query.orderBusinessId);
      let createTime = this.changeDate;
      this.$http
        .get(
          "/admin-center-server/order/updateOrderTime?orderId=" +
            orderId +
            "&createTime=" +
            createTime
        )
        .then(res => {
          console.log(res);
          if (res.data.code == "200") {
            this.$message.success("修改成功");
            this.dialogForTime = false;
            this.$router.go(-1); //成功之后返回
          } else {
            this.$message.warning(res.data.message);
          }
        });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.buttons {
  position: absolute;
  right: 20px;
  top: 20px;
}
ul li {
  list-style: none;
}
.flex{
  display: flex;
}
.audit-detail {
  display: flex;
  flex-direction: row-reverse;
  background-color: white;
  .audit-item {
    margin-right: 10px;
  }
}

.add-driver-item {
  display: flex;
  align-items: center;
  margin: 10px;
  .add-driver-input {
    margin: 10px;
    width: 35%;
  }
  .add-driver-delete {
    border: solid 1px #cccccc;
    border-radius: 5px;
    height: 35px;
    width: 35px;
    margin-left: 10px;
    text-align: center;
    line-height: 35px;
  }
}
.carDetail {
  .main-box {
    background-color: white;
    .title-box {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .list-box {
      margin-top: 20px;
      border: 1px solid #cccccc;
      border-left: none;
      .item-title {
        display: flex;
        flex-direction: row;
        div {
          width: 500px;
          height: 50px;
          font-weight: bold;
          font-size: 16px;
          line-height: 50px;
          border: 1px solid #cccccc;
          border-top: none;
          border-right: none;
          background-color: rgb(249, 252, 250);
          text-align: center;
        }
      }
      .item-info {
        display: flex;
        flex-direction: row;
        div {
          font-size: 14px;
          width: 500px;
          height: 50px;
          line-height: 50px;
          text-align: center;
          border: 1px solid #cccccc;
          border-top: none;
          border-right: none;
          border-bottom: none;
        }
      }
    }
    .base-info,
    .drive-info,
    .other-info,
    .driver-info {
      padding: 20px;
    }
  }
}
.edit-form{
  ::v-deep .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before{
    content: "" !important;
  }
}
</style>
