<template>
    <div class="app-container detailedList">
        <div class="select-box">
            <div class="top-title">
                <div>清单管理</div>
               <div style="float: right">
                   <el-button
                           class="left"
                           icon="el-icon-refresh-right"
                           size="mini"
                           type="success"
                           @click="()=>{this.$router.go(0)}"
                   >刷新
                   </el-button>
                  <el-button icon="el-icon-search" size="mini" type="primary" @click="onSubmit">查询</el-button>
                  <el-button icon="el-icon-delete" size="mini" type="danger" @click="clearchSubmit">清空筛选
                  </el-button>
              </div>
            </div>
            <div class="select-info">
                <el-form
                        size="mini"
                        :inline="true"
                        :model="formInline"
                        class="demo-form-inline"
                        label-width="100px"
                >
                    <el-form-item label="清单编号:">
                        <el-input v-model="formInline.sn" :οnkeyup="formInline.sn=formInline.sn.replace(/\s/g, '')" placeholder="请输入清单编号"></el-input>
                    </el-form-item>
                    <el-form-item label="结算单号:">
                        <el-input v-model="formInline.finalStatementSn" :οnkeyup="formInline.finalStatementSn=formInline.finalStatementSn.replace(/\s/g, '')" placeholder="请输入结算单号"></el-input>
                    </el-form-item>
                    <el-form-item label="日期筛选:">
                        <el-date-picker
                                :clearable="false"
                                @blur="selectTime"
                                v-model="formInline.date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetimerange"
                                :default-time="['00:00:00', '00:00:00']"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="清单状态:">
                        <el-select v-model="formInline.invoice" placeholder="全部" style="width: 178px">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="待确认" value="0"></el-option>
                            <el-option label="待结算" value="1"></el-option>
                            <el-option label="已结算" value="2"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>
<!--            开票金额 <span style="color: red">还没有呢</span> 元-->
            <div class="curTotalInfo">当前搜索结果总计：开票吨数 <span style="color: red">{{allTon}}</span> 吨；结算金额 <span
                    style="color: red">{{allMoney}}</span> 元；
            </div>
        </div>
        <div class="list-box">
            <el-row>
                <div class="list-right">
                    <div class="right-title">数据列表</div>
                    <div class="list-main">
                        <template>
                            <el-table :data="tableData" border style="width: 100%">
                                <el-table-column prop="num" label="序号" type="index" width="50"></el-table-column>
                                <el-table-column show-overflow-tooltip prop="sn" label="清单号"></el-table-column>
                                <el-table-column show-overflow-tooltip prop="finalStatementSn" label="结算单号"></el-table-column>
                                <el-table-column prop="amount" label="结算金额(元)"></el-table-column>
                                <el-table-column prop="taxAmount" label="补充运费(元)"></el-table-column>
                                <el-table-column show-overflow-tooltip prop="waitPayMoney" label="开票金额(元)"></el-table-column>

                                <el-table-column show-overflow-tooltip prop="transportAmount" label="结算运费(元)"></el-table-column>
                                <el-table-column show-overflow-tooltip prop="gasAmount" label="结算油气(元)"></el-table-column>

                                <el-table-column prop="ton" label="开票吨数(吨)"></el-table-column>
                                <el-table-column prop="invoice" label="清单状态"
                                                 :formatter="invoiceStatus"></el-table-column>
                                <el-table-column show-overflow-tooltip prop="createdDate" label="创建时间"></el-table-column>

                                <el-table-column fixed="right" label="操作" width="120">
                                    <template slot-scope="scope">
                                      <div v-if="scope.row.invoice==='0'" style="color: #00cb8a">
                                        <el-button style="color: #00cb8a"  size="small" type="text" @click="goDetail(scope.row)">查看
                                        </el-button>
                                        <el-button style="color: #00cb8a" type="text" size="small" @click="deleteRow(scope.row)">删除</el-button>
                                        <el-button :disabled="canExport"  type="text" size="small" @click="exportRow(scope.row)">导出</el-button>
                                      </div>
                                      <div v-if="scope.row.invoice!=='0'">
                                        <el-button style="color: #00cb8a" type="text" size="small"  @click="goDetail(scope.row)">查看
                                        </el-button>
                                        <el-button :disabled="canExport"  type="text" size="small" @click="exportRow(scope.row)">导出</el-button>
                                      </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </template>
                    </div>
                    <div class="paging">
                        <div class="block">
                            <el-pagination
                                    @size-change="handleSizeChange"
                                    @current-change="handleCurrentChange"
                                    :current-page="currentPage"
                                    :page-sizes="[10, 20, 30, 40,50,100]"
                                    :page-size="pageSize"
                                    layout="total, sizes, prev, pager, next, jumper"
                                    :total="tableTotal"
                            ></el-pagination>
                        </div>
                    </div>
                </div>
            </el-row>
        </div>
        <!-- 删除列表弹窗-->
        <el-dialog
                title="删除确认"
                :visible.sync="dialogVisible"
                width="30%"
                :before-close="handleClose">
            <div style="height: 40px;">
                 <i class="el-icon-info" style="color: red;"></i>  <span style=" margin-left: 10px;
">是否确认删除清单？</span>
            </div>
            <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="suerDeleteRow">确 定</el-button>
      </span>
        </el-dialog>

    </div>
</template>
<script>
    import faultNews from "../../../messagePage/messageType/faultNews/faultNews";

    export default {
        data() {
            return {
                canDelete:false,
                canExport:false,
                loading:false,
                allMoney:'',
                allTon:'',
                dialogVisible: false,
                dialogChange: false,
                currentPage: 1,
                tableTotal: 1,
                pageSize: 20,
                formInline: {
                    sn: "",
                    finalStatementSn: "",
                    date: '',
                    startDate: '',
                    endDate: '',
                    invoice:'',
                },
                tableData: [],
            };
        },
        methods: {
            goDetail(row){
                let id = row.id;
                let finalStatementSn = row.finalStatementSn;
                let sn = row.sn;
                let businessName = row.businessName;
                let amount = row.amount;
                let ton = row.ton;
                this.$router.push({
                    path:'detailedListLook',
                    query:{
                        id:id,
                        businessName:businessName,
                        finalStatementSn:finalStatementSn,
                        sn:sn,
                        amount:amount,
                        ton:ton,
                    }
                })
            },
            invoiceStatus(row) {
                if (row.invoice === '0') {
                    return '待确认'
                } else if (row.invoice === '1') {
                    return '待结算'
                } else if (row.invoice === '2') {
                    return '已结算'
                }
            },
            /** 删除行 **/
            deleteRow(row) {
                this.dialogVisible = true;
                let id = row.id;
                this.deleteId=id;
            },
            /** 确认删除 **/
            suerDeleteRow(){
                let id = this.deleteId;
                this.$http.post('admin-center-server/app/invoice_schedule/delete?invoiceScheduleId='+id).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                        this.$message.success(data.message);
                        this.dialogVisible = false;
                        this.getDataList();
                    }else {
                        this.$message.warning(data.message)
                    }
                })
            },
            /** 导出清单行 **/
            exportRow(row){
                let id = row.id;
                this.canExport=true;
                this.$http.get('/admin-center-server/app/invoice_schedule/exportSchedules',{
                    params:{
                        invoiceScheduleId:id,
                    }
                }).then(res=>{
                    let data = res.data;
                    if(data.code===200){
                        let url = data.data;
                        window.location.href=url;
                        this.canExport=false;
                    }else {
                        this.$message.warning(data.message);
                        this.canExport=false;
                    }
                })
            },
            /** 弹窗 上叉号关闭 支持异常回调**/
            handleClose(done) {
                this.dialogVisible = false;
                this.dialogChange = false;
            },
            /** 编辑行 同新增参数 **/
            handleClick(row) {

                /****/
            },
            /** 根据时间态搜索 **/
            selectTime() {
                if(this.formInline.date!==null){
                    let startTime = this.formInline.date[0];
                    let endTime = this.formInline.date[1];
                    this.formInline.startDate = startTime;
                    this.formInline.endDate = endTime;
                }else {
                    this.formInline.date=[];
                }
            },
            onSubmit() {
                this.currentPage = 1;
                this.getDataList()
            },
            /** 清除筛选查询 **/
            clearchSubmit() {
                this.formInline = {
                    sn: "",
                    finalStatementSn: "",
                    date: '',
                    startDate: '',
                    endDate: '',
                    invoice: '',
                };
                this.getDataList()
            },
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1;
                this.getDataList();
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getDataList();
            },
            getDataList() {
                this.$http
                    .get("/admin-center-server/app/invoice_schedule/list", {
                        params: {
                            pageNumber: this.currentPage,
                            pageSize: this.pageSize,
                            sn: this.formInline.sn, //清单号
                            finalStatementSn: this.formInline.finalStatementSn, //结算单号
                            invoice: this.formInline.invoice, //清单状态
                            startDate: this.formInline.startDate,
                            endDate: this.formInline.endDate,
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.tableTotal = Number(data.data.total);
                            let useList = data.data.list;
                            useList.map((item, index) => {
                                if (item.taxAmount === null) {
                                    item.taxAmount = 0;
                                }
                                if (item.amount === null) {
                                    item.amount = 0;
                                }
                            });
                            useList.map((item, index) => {
                                item.waitPayMoney = this.plus(item.amount, item.taxAmount)
                            });
                            this.tableData = useList;
                        }
                    });
            },
            getTitleInfo(){
                this.$http.get('/admin-center-server/app/invoice_schedule/getCounts').then(res=>{
                    let data = res.data;
                    console.log(data);
                    if(data.code==='200'){
                        this.allMoney= data.data.allMoney;
                        this.allTon= data.data.allTon;
                    }
                })
            },
            /** 解决浮点数计算 **/
            times(num1, num2) {
                const num1String = num1.toString();
                const num2String = num2.toString();
                const num1Digits = (num1String.split('.')[1] || '').length;
                const num2Digits = (num2String.split('.')[1] || '').length;
                const baseNum = Math.pow(10, num1Digits + num2Digits);
                return Number(num1String.replace('.', '')) * Number(num2String.replace('.', '')) / baseNum
            },
            plus(num1, num2) {
                const num1Digits = (num1.toString().split('.')[1] || '').length;
                const num2Digits = (num2.toString().split('.')[1] || '').length;
                const baseNum = Math.pow(10, Math.max(num1Digits, num2Digits));
                return (this.times(num1, baseNum) + this.times(num2, baseNum)) / baseNum
            },
        },
        activated() {
            this.getDataList();
            this.getTitleInfo();
        }
    };
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .tip {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 40px 40px 0;
        font-size: 12px;

        em {
            margin-right: 5px;
        }
    }

    .inner-box {
        margin-left: 10%;
        width: 70%;

        .upload-box {
            width: 100%;
            height: 100%;
            position: relative;

            .icon-XZ {
                width: 92px;
                height: 92px;
                margin: 0 auto;
                background-size: 100% 100%;
            }

            .icon-word {
                width: 100%;
                height: 20px;
                line-height: 20px;
                font-size: 10px;
                position: absolute;
                bottom: 25px;
                left: 0px;
                color: #cccccc;
            }
        }
    }

    .detailedList {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                padding-right: 10px;
            }

            .select-info {
                padding-top: 20px;
            }

            .curTotalInfo {
                padding-left: 20px;
                height: 30px;
                line-height: 30px;
                font-size: 12px;
                color: #999999;
            }
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;
            overflow: hidden;

            .list-right {
                padding-left: 10px;

                .right-title {
                    font-size: 14;
                    font-weight: 700;
                }

                .list-main {
                    width: 100%;
                    border: 1px solid #cccccc;
                    margin-top: 10px;
                }

                .paging {
                    margin-top: 10px;
                    float: right;
                }
            }
        }
    }
</style>
