/**
对账单管理 小火车 2020/6/15
**/
<template>
    <div class="app-container carsList">
        <div class="select-box">
            <div class="top-title">
                <div>筛选查询</div>
                <div class="button">
                    <el-button
                            class="left"
                            icon="el-icon-search"
                            size="mini"
                            type="primary"
                            @click="()=>{this.currentPage = 1; this.getDataList()}"
                    >查询
                    </el-button>
                    <el-button
                            class="left"
                            icon="el-icon-refresh-right"
                            size="mini"
                            type="success"
                            @click="clearSearch"
                    >清空筛选
                    </el-button>
                </div>
            </div>
            <div class="select-info">
                <el-form
                        class="demo-form-inline"
                        :inline="true"
                        :model="formInline"
                        label-width="90px"
                        size="mini"
                >
                    <el-form-item label="平台名称:">
                        <el-input v-model="formInline.platformName" :οnkeyup="formInline.platformName=formInline.platformName.replace(/\s/g, '')" placeholder="请输入平台名称"></el-input>
                    </el-form-item>

                    <el-form-item label="对账单号:">
                        <el-input v-model="formInline.oilStatementSn" :οnkeyup="formInline.oilStatementSn=formInline.oilStatementSn.replace(/\s/g, '')" placeholder="请输入对账单号"></el-input>
                    </el-form-item>
                    <el-form-item label="对账单状态:">
                        <el-select v-model="formInline.status" placeholder="全部" style="width: 178px">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="未开票" value="0"></el-option>
                            <el-option label="已开票" value="1"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <el-form
                        :inline="true"
                        :model="formInline"
                        class="demo-form-inline"
                        label-width="90px"
                        size="mini"
                >
                    <el-form-item label="创建日期:">
                        <el-date-picker
                                :clearable="false"
                                @blur="selectTime"
                                v-model="date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetimerange"
                                :default-time="['00:00:00', '00:00:00']"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                        ></el-date-picker>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="list-box">
            <div class="list-title">
                <div>数据列表</div>
                <div style="display: flex;font-size: 14px;">
                    <div>
                        <div>结果总计：已开油气票： <span style="color: red">{{totalAmont}}</span> 元；</div>
                    </div>
                    <div style="margin-left: 20px">
                        <el-button size="mini" type="primary" @click="dialogAdd=true">
                            <i class="el-icon-plus"></i>
                            添加对账单
                        </el-button>
                    </div>
                </div>
            </div>
            <div class="list-main">
                <el-table
                        :data="tableData"
                        border
                        style="width: 100%">
                    <el-table-column
                            label="序号"
                            width="55"
                            type="index"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="oilStatementSn"
                            show-overflow-tooltip
                            label="对账单号"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="platformName"
                            show-overflow-tooltip
                            label="平台名称"
                            width="120">
                    </el-table-column>
                    <el-table-column
                            prop="totalAmount"
                            show-overflow-tooltip
                            width="120"
                            label="对账总金额(元)">
                    </el-table-column>
                    <el-table-column
                            prop="consumerAmount"
                            show-overflow-tooltip
                            width="120"
                            label="消费合计(元)">
                    </el-table-column>
                    <el-table-column
                            prop="refundAmount"
                            show-overflow-tooltip
                            width="120"
                            label="退款合计(元)">
                    </el-table-column>
                    <el-table-column
                            prop="dealNum"
                            show-overflow-tooltip
                            width="120"
                            label="交易笔数">
                    </el-table-column>
                    <el-table-column
                            prop="startDate"
                            show-overflow-tooltip
                            width="120"
                            label="开始时间">
                    </el-table-column>
                    <el-table-column
                            prop="endDate"
                            show-overflow-tooltip
                            width="120"
                            label="结束时间">
                    </el-table-column>
                    <el-table-column
                            prop="statusText"
                            show-overflow-tooltip
                            width="120"
                            label="开票状态">
                    </el-table-column>
                    <el-table-column
                            prop="updateDatetime"
                            show-overflow-tooltip
                            width="120"
                            label="开票日期">
                    </el-table-column>
                    <el-table-column
                            prop="createdDatetime"
                            show-overflow-tooltip
                            width="120"
                            label="创建日期">
                    </el-table-column>
                    <el-table-column
                            prop="createdUsername"
                            show-overflow-tooltip
                            width="120"
                            label="创建人">
                    </el-table-column>
                    <el-table-column
                            fixed="right"
                            label="操作"
                            width="200">
                        <template slot-scope="scope">
                            <el-button v-show="scope.row.status==='0'" type="text" size="small" style="color: #00cb8a" @click="deleteRow(scope.row)">
                                删除
                            </el-button>
                            <el-button @click="handleClick(scope.row)" type="text" size="small" style="color: #00cb8a">
                                查看
                            </el-button>
                            <el-button :disabled="loading" type="text" size="small" style="color: #00cb8a"
                                       @click="exportsRow(scope.row)">
                                导出
                            </el-button>
                            <el-button v-show="scope.row.status==='0'" type="text" size="small" style="color: #00cb8a"
                                       @click="signRow(scope.row)">
                                标记开票
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="pagination" style="float: right;margin-top: 20px">
                <el-pagination
                        background
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 40,50,100]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"
                ></el-pagination>
            </div>
        </div>
        <!--        添加对账单-->
        <el-dialog
                title="添加对账单"
                :visible.sync="dialogAdd"
                width="30%"
                :before-close="handleClose">
            <el-form ref="form" :model="formAdd" label-width="80px">
                <el-form-item label="平台:">
                    <el-autocomplete
                            style="width: 220px"
                            class="inline-input"
                            v-model="formAdd.platformName"
                            :fetch-suggestions="querySearch"
                            placeholder="请输入平台名称"
                            :trigger-on-focus="false"
                            @select="handleSelect"
                    ></el-autocomplete>
                </el-form-item>
                <el-form-item label="开始日期:">
                    <el-date-picker
                            :clearable="false"
                            v-model="formAdd.startTime"
                            type="date"
                            format="yyyy 年 MM 月 dd 日"
                            value-format="yyyy-MM-dd"
                            placeholder="开始日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="结束日期:">
                    <el-date-picker
                            :clearable="false"
                            v-model="formAdd.endTime"
                            type="date"
                            format="yyyy 年 MM 月 dd 日"
                            value-format="yyyy-MM-dd"
                            placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
    <el-button @click="handleClose">取 消</el-button>
    <el-button type="primary" @click="sureAdd">确 定</el-button>
  </span>
        </el-dialog>
        <!--删除对账单-->
        <el-dialog
                title="删除确认"
                :visible.sync="deleteMask"
                width="30%"
                :before-close="handleClose">
            <span>是否删除该对账单？</span>
            <span slot="footer" class="dialog-footer">
    <el-button @click="deleteMask = false">取 消</el-button>
    <el-button type="primary" @click="sureDeleta">确 定</el-button>
  </span>
        </el-dialog>
        <!--标记对账单-->
        <el-dialog
                title="标记确认"
                :visible.sync="signMask"
                width="30%"
                :before-close="handleClose">
            <span>确定要标记该对账单吗？</span>
            <span slot="footer" class="dialog-footer">
    <el-button @click="signMask = false">取 消</el-button>
    <el-button type="primary" @click="sureSign">确 定</el-button>
  </span>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: "CarsList",
        data() {
            return {
                loading: false,
                deleteOilStatementId: '',
                signId: '',
                totalAmont: '0',
                restaurants: [],
                dialogAdd: false,
                deleteMask: false,
                signMask: false,
                total: 0,
                currentPage: 1,
                pageSize: 20,
                formAdd: {
                    platformName: '',
                    platformId: '',
                    startTime: '',
                    endTime: '',
                },
                date: [],
                formInline: {
                    platformName: '',
                    oilStatementSn: '',
                    status: '',
                    startTime: '',
                    endTime: '',
                },
                tableData: [],
            };
        },
        methods: {
            /** 清空搜索结果 **/
            clearSearch() {
                this.date = [];
                this.formInline = {
                    platformName: '',
                    oilStatementSn: '',
                    status: '',
                    startTime: '',
                    endTime: '',
                };
                this.currentPage = 1;
                this.pageSize = 20;
                this.getDataList();
            },
            /** 根据时间态搜索 **/
            selectTime() {
                if(this.date!==null){
                    let startTime = this.date[0];
                    let endTime = this.date[1];
                    this.formInline.startTime = startTime;
                    this.formInline.endTime = endTime;
                }else {
                    this.date=[];
                }
            },
            /** 添加对账单模糊查询 **/
            querySearch(queryString, cb) {
                var restaurants = this.restaurants;
                var results = queryString
                    ? restaurants.filter(this.createFilter(queryString))
                    : restaurants;
                // 调用 callback 返回建议列表的数据
                cb(results);
            },
            createFilter(queryString) {
                return restaurant => {
                    return (
                        restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
                        0
                    );
                };
            },
            /** 模糊搜索 获取ID **/
            handleSelect(item) {
                this.formAdd.platformId = item.id
            },
            handleClose() {
                this.dialogAdd = false;
                this.deleteMask = false;
                this.signMask = false;
                this.formAdd = {
                    platformName: '',
                    platformId: '',
                    startTime: '',
                    endTime: '',
                }
            },
            /** 分页 **/
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1;
                this.getDataList();
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getDataList();
            },
            /** 详情 **/
            handleClick(row) {
                this.$router.push({
                    path: 'billDetail',
                    query: {
                        id: row.id,
                    }
                })
            },
            /** 确定添加 **/
            sureAdd() {
                let addForm = {
                    endTime: this.formAdd.endTime,
                    oilPlatformId: this.formAdd.platformId,
                    startTime: this.formAdd.startTime,
                };
                this.$http.post('/admin-center-server/oilStatement/addOilStatement', addForm).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.getDataList();
                        this.dialogAdd = false;
                        this.formAdd = {
                            platformName: '',
                            platformId: '',
                            startTime: '',
                            endTime: '',
                        }
                    } else {
                        this.$message.warning(data.message)
                    }
                })
            },
            /** 获取数据列表 **/
            getDataList() {
                let sendForm = {
                    pageNumber: this.currentPage,
                    pageSize: this.pageSize,

                    platformName: this.formInline.platformName,
                    oilStatementSn: this.formInline.oilStatementSn,
                    status: this.formInline.status,
                    startTime: this.formInline.startTime,
                    endTime: this.formInline.endTime,
                    order: 'desc'
                };
                this.$http.post('/admin-center-server/oilStatement/listOilStatement', sendForm).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.tableData = data.data.listOilStatementVo.list;
                        this.total = Number(data.data.listOilStatementVo.total);
                        if (data.data.totalAmont === null) {
                            this.totalAmont = 0
                        } else {
                            this.totalAmont = data.data.totalAmont;
                        }
                    } else {
                        this.$message.warning(data.message)
                    }
                })
            },
            /** 删除行 **/
            deleteRow(row) {
                this.deleteMask = true;
                this.deleteOilStatementId = row.id;
            },
            /** 确认删除 **/
            sureDeleta() {
                let oilStatementId = this.deleteOilStatementId;
                this.$http.post('/admin-center-server/oilStatement/deleteOilStatement', {
                    oilStatementId: oilStatementId
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.getDataList();
                        this.$message.success('删除成功');
                        this.deleteMask = false;
                    } else {
                        this.$message.warning(data.message)
                    }
                })
            },
            /** 标记行 **/
            signRow(row) {
                this.signMask = true;
                this.signId = row.id;
            },
            /** 确认标记 **/
            sureSign() {
                let oilStatementId = this.signId;
                this.$http.post('/admin-center-server/oilStatement/invoiceOilStatement', {
                    oilStatementId: oilStatementId
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.getDataList();
                        this.$message.success('标记成功');
                        this.signMask = false;
                    } else {
                        this.$message.warning(data.message)
                    }
                })
            },
            /** 获取平台list **/
            getPTlist() {
                this.$http.post('/admin-center-server/oilPlatform/listAllOilPlatform').then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        let resData = data.data;
                        let str = [];
                        resData.map((item, index) => {
                            if (item.platformName != null) {
                                let obj = {
                                    value: item.platformName,
                                    id: item.id
                                };
                                str.push(obj);
                            }
                        });
                        this.restaurants = str;
                    } else {
                        this.$message.warning(data.message)
                    }
                })
            },
            /** 导出 **/
            exportsRow(row) {
                this.loading = true;
                this.$http.post('/admin-center-server/oilStatement/exportOilStatementDetail', {
                    oilStatementId: row.id
                }).then(res => {
                    let data = res.data;
                    if (data.code === 200) {
                        this.loading = false;
                        window.location.href = data.data;
                    } else {
                        this.loading = false;
                        this.$message.warning(data.message)
                    }
                })
            },
        },
        activated() {
            this.getDataList();
            this.getPTlist();
        }
    };
</script>

<style>
    .el-range-editor.el-input__inner {
        width: 400px;
    }
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
    /*.el-form-item {*/
    /*    margin-bottom: 0;*/
    /*}*/

    .carsList {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
                display: flex;
                justify-content: space-between;

                .button {
                    margin-right: 20px;
                }
            }

            .select-info {
                padding-top: 30px;
                padding-bottom: 30px;
            }
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;
            overflow: hidden;

            .list-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                div {
                    height: 38px;
                    line-height: 38px;
                }
            }

            .list-main {
                width: 100%;
                border: 1px solid #cccccc;
                margin-top: 10px;
            }
        }
    }
</style>
