import { Message } from 'element-ui'
import { getCoordinatesOfFourDirectionsOfCircle } from '@/utils/gpsGeometry'
import { $httpMap as serviceMap } from '@/utils/http2'
import { webKey } from '@/utils/config'

const checkInFence = ({
  shape,
  currentPoint
}) => {
  let lnglat = new AMap.LngLat(currentPoint[0], currentPoint[1])
  if (shape.contains(lnglat)) return true
  return false
}

const checkRadius = ({
  shapeRadius,
  maxRaduis
}) => {
  if (shapeRadius > maxRaduis) {
    return false
  }
  return true
}

const checkDistrict = async ({
  coordinates,
  currentPoint
}) => {
  coordinates.unshift(currentPoint)
  const regeoPromises = coordinates.map(v => {
    return serviceMap.get('https://restapi.amap.com/v3/geocode/regeo?parameters', {
      params: {
        location: v.join(','),
        key: webKey
      }
    })
      .then(res => res.data.regeocode.addressComponent.district)
  })
  let regeoResults = []
  for (let regeoPromise of regeoPromises) {
    regeoResults.push(await regeoPromise)
  }
  let currentDistrict = regeoResults.shift(0)
  let isCrossDistrict = regeoResults.some(v => v !== currentDistrict)
  return !isCrossDistrict
}

const checkDistance = ({
  currentPoint,
  coordinates,
  maxDistance
}) => {
  let currentLngLat = new AMap.LngLat(currentPoint[0], currentPoint[1])
  let isFail = coordinates.some(v => {
    let lngLat = new AMap.LngLat(v[0], v[1])
    if (AMap.GeometryUtil.distance(lngLat, currentLngLat) > maxDistance) {
      return true
    }
  })
  return !isFail
}

export const checkCircle = ({
  shape,
  shapeRadius,
  shapeCenter,
  currentPoint,
  maxRaduis
}) => {
  if (!checkInFence({
    shape,
    currentPoint
  })) {
    Message.error('围栏覆盖范围需包含当前地址，请修改后再结束编辑围栏')
    return
  }
  if (!checkRadius({
    shapeRadius,
    maxRaduis
  })) {
    Message.error('围栏半径已超最大值，请修改后再结束编辑围栏')
    return
  }
  let coordinates = getCoordinatesOfFourDirectionsOfCircle({
    centerCoordinate: [shapeCenter.lng, shapeCenter.lat],
    radius: shapeRadius
  })
  if (!checkDistrict({
    coordinates,
    currentPoint
  })) {
    Message.error('围栏覆盖范围需与当前地址在同一区，请修改后再结束编辑围栏')
    return
  }
  return true
}

export const checkRectangle = ({
  shape,
  currentPoint,
  coordinates,
  maxDistance
}) => {
  if (!checkInFence({
    shape,
    currentPoint
  })) {
    Message.error('围栏覆盖范围需包含当前地址，请修改后再结束编辑围栏')
    return
  }
  if (!checkDistance({
    currentPoint,
    coordinates,
    maxDistance
  })) {
    Message.error(`围栏最远坐标点与当前地址坐标点之间的距离不能超过${maxDistance}米`)
    return
  }
  if (!checkDistrict({
    coordinates,
    currentPoint
  })) {
    Message.error('围栏覆盖范围需与当前地址在同一区，请修改后再结束编辑围栏')
    return
  }
  return true
}

export const checkPolygon = ({
  currentPoint,
  coordinates,
  maxDistance
}) => {
  if (!checkDistance({
    currentPoint,
    coordinates,
    maxDistance
  })) {
    Message.error(`围栏最远坐标点与当前地址坐标点之间的距离不能超过${maxDistance}米`)
    return
  }
  return true
}

export const shapeTypes = {
  circle: '0',
  rectangle: '1',
  polygon: '2'
}