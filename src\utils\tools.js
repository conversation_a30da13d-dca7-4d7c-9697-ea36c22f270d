export function cloneDeep (origin) {
  if (typeof origin !== 'object') return origin
  if (Array.isArray(origin)) {
    return origin.map(v => cloneDeep(v))
  } else {
    let result = {}
    for (let i in origin) {
      result[i] = cloneDeep(origin[i])
    }
    return result
  }
}

export function objToFormData(obj) {
  let formData = new FormData()
  for (let i in obj) {
    formData.append(i, obj[i])
  }
  return formData
}

//获取数字的小数位数
export function getDecimalsLenth(number) {
  let splitNumberArr = String(number).split('.')
  if (splitNumberArr.length < 2) return 0
  let decimal = splitNumberArr[1]
  return decimal.length
}