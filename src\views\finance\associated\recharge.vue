<template>
  <div>
    <div class="list-box">
      <div v-if="data.length > 0">客户：{{ data[0].name }} {{ data[0].mobile }}</div>
      <el-table
        :data="data" :cell-style="{'text-align': 'center',}">
        <el-table-column label="充值时间" prop="createTime"></el-table-column>
        <el-table-column label="到账时间" prop="allocationTime"></el-table-column>
        <el-table-column label="充值金额" prop="money"></el-table-column>
        <el-table-column label="油分配金额" prop="allocationOilMoney"></el-table-column>
        <el-table-column label="积分分配金额" prop="integralMoney"></el-table-column>
        <el-table-column label="操作人" prop="allocationUserName"></el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="getList"
        :current-page.sync="page.pageNumber"
        :page-sizes="[10,20, 40, 60, 80, 100]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      page: {
        pageNumber: 1,
        pageSize: 10
      },
      total: 0,
      data: [],
    }
  },
  activated() {
    this.getList()
  },
  methods: {
    getList() {
      let params = { ...this.page, userId: this.$route.query.userId }
      this.$post('/admin-center-server/oilRecharge/oilRechargeRecordList', params)
        .then(res => {
          this.data = res.list
          this.total = Number(res.total)
        })
    },
    handleSizeChange(v) {
      this.page.pageSize = v
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.list-box {
  background-color: #ffffff;
  margin: 20px;
  padding: 10px;
}
.el-table {
  margin-top: 20px;
}
.pagination {
  text-align: right;
  margin-top: 20px;
}
</style>