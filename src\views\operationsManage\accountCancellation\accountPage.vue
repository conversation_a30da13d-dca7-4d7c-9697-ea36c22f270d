<template>
    <div class="app-container accountPage">
        <div class="main-box">
            <el-form ref="form" :model="form" label-width="120px">
                <el-form-item label="司机名称:" required>
                    <el-input v-model="form.userName" placeholder="请输入司机名称" style="width: 300px"></el-input>
                </el-form-item>
                <el-form-item label="司机手机号:" required>
                    <el-input v-model="form.userMobile" placeholder="请输入司机手机号" style="width: 300px"></el-input>
                </el-form-item>
                <el-form-item label="司机身份证号:" required>
                    <el-input v-model="form.idCard" placeholder="请输入司机身份证号" style="width: 300px"></el-input>
                </el-form-item>
                <el-form-item label="销户凭证:" required>
                    <el-upload
                            action
                            :limit="6"
                            multiple
                            :on-exceed="handleExceed"
                            list-type="picture-card"
                            :http-request="ossUpload"
                            :on-remove='handleRemove'
                    >
                        <div class="upload-box">
                            <div class="icon-XZ"></div>
                            <div class="icon-word">支持jpg、jpeg、png 格式不超过10M</div>
                        </div>
                    </el-upload>
                </el-form-item>
                <el-form-item label="销户备注:">
                    <el-input type="textarea" v-model="form.closeRemark" placeholder="请输入销户备注"
                              style="width: 300px"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="isNullFn">确 认</el-button>
                    <el-button @click="goBack">返 回</el-button>
                </el-form-item>
            </el-form>
        </div>
        <!-- 确认销户弹窗-->
        <el-dialog
                title="提示"
                :visible.sync="dialogVisible"
                width="30%"
                :before-close="handleClose">
            <div>
                请确认以下销户信息，此操作为 <span style="color: red">不可逆</span>
                操作，请谨慎操作。司机姓名：{{form.userName}}，手机号{{form.userMobile}}.司机身份证号{{form.idCard}}，请问您还要继续销户吗？
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button @click="dialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="submitFn" :loading="loading">确 定</el-button>
  </span>
        </el-dialog>
    </div>
</template>
<script>
    import {client, OSS_REGION} from '@/utils/alioss'

    export default {
        data() {
            return {
                loading:false,
                dialogVisible: false,
                Aliyun: {},
                form: {
                    userName: '',
                    userMobile: '',
                    idCard: '',
                    closeRemark: '',
                    images: [],
                },
                images: [],
            };
        },
        methods: {
            getAliyunData() {
                const ossApiUrl = "/base-center-server/sts/oss_signature"; // 获取oss签名的地址
                // 获取oss签名
                this.$http.get(ossApiUrl).then(res => {
                    if (res.data.code == "200") {
                        let data = res.data;
                        this.Aliyun = data.data;
                        this.Aliyun.region = OSS_REGION;
                    }
                });
            },
            /** 上传成功后 删除 重新上传集合 **/
            handleRemove(file, fileList) {
                let uid = file.uid;
                let str = [];
                this.images.map((item, index) => {
                    if (item.uid !== uid) {
                        str.push(item)
                    }
                });
                this.images = str;
            },
            /** 单个上传 重组上传集合 **/
            ossUpload(param) {
                let file = param.file; // 文件的
                let uid = file.uid;
                const tmpcnt = file.name.lastIndexOf('.');
                const exname = file.name.substring(tmpcnt + 1);
                const fileName = '/' + this.Aliyun.bucket + '/' + this.Aliyun.dir + this.$md5(file.name) + '.' + exname;
                client(this.Aliyun).put(fileName, file).then(res => {
                    if (res.res.status === 200) {
                        let imgUrl = res.res.requestUrls[0];
                        let obj = {imgUrl: imgUrl, uid: uid};
                        this.images.push(obj);
                        this.$message.success('上传成功')
                    } else {
                        this.$message.error(res.res.message)
                    }
                })
            },
            /** 上传数量限制 **/
            handleExceed() {
                this.$message.warning('最多上传6张图片')
            },
            /** 处理完后的 图片集合 **/
            isNullFn() {
                let images = this.images.map((item, index) => {
                    return item.imgUrl
                });
                this.form.images = images.toString();
                if (this.form.userName === '' || this.form.idCard === '' || this.form.userMobile === '' || this.form.images.length < 1) {
                    this.$message.warning('请填写完整')
                } else {
                    this.dialogVisible = true;
                }
            },
            /** 确认销户 **/
            submitFn() {
                this.loading = true;
                let postData = this.form;
                this.$http.post('/admin-center-server/acctChange/addAcctChangeReques', postData).then(res => {
                    let data = res.data;
                    if(data.code==='200'){
                        this.$message.success(data.message);
                        this.dialogVisible=false;
                        this.loading = false;
                        setTimeout(()=>{
                            this.$router.push('accountCancellation')
                        },1500);
                    }else {
                        this.$message.warning(data.message);
                        this.loading = false;
                    }
                })
            },
            /** 返回 **/
            goBack() {
                this.$router.push('accountCancellation')
            },
            handleClose() {
                this.dialogVisible = false;
            },
        },
        created() {
            this.getAliyunData();
        }
    };
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .accountPage {
        background-color: white;
    }

    .main-box {
        height: 900px;

        .upload-box {
            width: 100%;
            height: 100%;
            position: relative;

            .icon-XZ {
                width: 92px;
                height: 92px;
                margin: 0 auto;
                background: url("./images/xiazai.png") no-repeat;
                background-size: 100% 100%;
            }

            .icon-word {
                width: 100%;
                height: 20px;
                line-height: 20px;
                font-size: 10px;
                position: absolute;
                bottom: 25px;
                left: 0px;
                color: #cccccc;
            }
        }
    }
</style>
