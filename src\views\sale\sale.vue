<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">筛选查询</div>
      <div class="select-info">
        <el-form :inline="true"
                 :model="formInline"
                 ref="formInline"
                 class="demo-form-inline"
                 size="mini"
                 label-width="90px">
          <el-form-item label="客户名称:"
                        prop='name'>
            <el-input v-model="formInline.name"
                      placeholder="请输入客户名称"></el-input>
          </el-form-item>
          <el-form-item label="客户电话:"
                        prop='tel'>
            <el-input v-model="formInline.tel"
                      oninput="value=value.replace(/[^\d]/g,'')"
                      maxlength="11"
                      placeholder="请输入客户电话"></el-input>
          </el-form-item>
          <el-form-item label="销售人员:"
                        prop='salesPerson'>
            <el-input v-model="formInline.salesPerson"
                      placeholder="请输入销售人员姓名"></el-input>
          </el-form-item>
          <el-form-item label="客户星级："
                        prop='starClass'
                        label-width="100px">
            <el-select v-model="formInline.starClass"
                       placeholder="请选择"
                       clearable
                       label-width="10px">
              <el-option v-for="(item, lan) in statusWrap1"
                         :label="item.name"
                         :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="电话状态："
                        prop='telStatus'
                        label-width="100px">
            <el-select v-model="formInline.telStatus"
                       placeholder="请选择"
                       clearable
                       label-width="10px">
              <el-option v-for="(item, lan) in statusWrap"
                         :label="item.name"
                         :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="需求状态："
                        prop='demandStatus'
                        label-width="100px">
            <el-select v-model="formInline.demandStatus"
                       placeholder="请选择"
                       clearable
                       label-width="10px">
              <el-option v-for="(item, lan) in statusWrap2"
                         :label="item.name"
                         :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="意向度："
                        prop='Intentionality'
                        label-width="100px">
            <el-select v-model="formInline.Intentionality"
                       placeholder="请选择"
                       clearable
                       label-width="10px">
              <el-option v-for="(item, lan) in statusWrap3"
                         :label="item.name"
                         :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="跟进方式："
                        prop='follow'
                        label-width="100px">
            <el-select v-model="formInline.follow"
                       placeholder="请选择"
                       clearable
                       label-width="10px">
              <el-option v-for="(item, lan) in statusWrap4"
                         :label="item.name"
                         :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button class="left"
                       icon="el-icon-search"
                       @click="onSubmit">查询</el-button>
            <el-button class="left"
                       @click="resetForm('formInline')"
                       icon="el-icon-refresh-right">重置</el-button>
            <el-button class="left"
                       @click="refreshfn"
                       icon="el-icon-refresh-right">刷新</el-button>
            <el-button class="left"
                       icon="el-icon-plus"
                       @click="addnewfn">新增</el-button>

          </el-form-item>
        </el-form>

      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>客户列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table ref="multipleTable"
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%"
                    :height="tableHeight"
                    @selection-change="handleSelectionChange"
                    border>
            <el-table-column type="selection"
                             width="55"></el-table-column>
            <el-table-column type="index"
                             label="序号"
                             width="55">
            </el-table-column>
            <el-table-column show-overflow-tooltip
                             v-for="item in tableLabel"
                             :label="item.label">
              <template slot-scope="scope">
                <span style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
              </template>
            </el-table-column>

            <el-table-column fixed="right"
                             label="操作">
              <template slot-scope="scope">
                <el-button @click="goDetail(scope.row)"
                           type="text"
                           size="small">查看</el-button>
                <el-button type="text"
                           @click="gomodify(scope.row)"
                           size="small">修改</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page="pageNumber"
                         :page-sizes="[10,20, 40, 60, 80,100]"
                         :page-size="pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total"
                         class="pagination"></el-pagination>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
const queryCustomerList = '/admin-center-server/customer/queryCustomerList'//列表
export default {
  name: "CarsList",
  data () {
    return {
      tableHeight: null, //表格的高度
      tableLabel: [
        {
          prop: 'cusName',
          label: '客户名称',
          width: 200
        },
        {
          prop: 'cusTel',
          label: '客户电话'
        },
        {
          prop: 'cusAddress',
          label: '客户地址'
        },
        {
          prop: 'requireStatusName',
          label: '需求状态'
        },
        {
          prop: 'saleName',
          label: '业务员'
        }
      ],
      statusWrap1: [
        {
          name: '一星',
          id: '0'
        },
        {
          name: '二星',
          id: '1'
        },
        {
          name: '三星',
          id: '3'
        },
        {
          name: '四星',
          id: '4'
        },
        {
          name: '五星',
          id: '5'
        }
      ],
      statusWrap: [
        {
          name: '无效电话',
          id: '0'
        },
        {
          name: '直接拒绝',
          id: '1'
        },
        {
          name: '无人接听',
          id: '2'
        },
        {
          name: '持续跟进',
          id: '3'
        },
        {
          name: '同行',
          id: '5'
        }
      ],
      statusWrap2: [
        {
          name: '有合作方',
          id: '0'
        }, {
          name: '无合作方',
          id: '1'
        }, {
          name: '考虑中',
          id: '2'
        }, {
          name: '不考虑',
          id: '3'
        }],
      statusWrap3: [
        {
          name: '0%',
          id: '0'
        }, {
          name: '30%',
          id: '1'
        }, {
          name: '60%',
          id: '2'
        }, {
          name: '80%',
          id: '3'
        }, {
          name: '已签约',
          id: '4'
        }],
      statusWrap4: [
        {
          name: '上门',
          id: '0'
        }, {
          name: '电话',
          id: '1'
        }, {
          name: '微信',
          id: '2'
        }, {
          name: '短信',
          id: '3'
        }, {
          name: '其他',
          id: '4'
        }],
      pageSize: 10,
      total: 0,
      pageNumber: 1,
      formInline: {
        name: "",
        tel: "",
        salesPerson: '',
        starClass: '',
        demandStatus: '',
        telStatus: "",
        Intentionality: '',
        follow: ''
      },
      tableData: []
    };
  },
  methods: {
    // 刷新当前页
    refreshfn () {
      this.$router.go(0)
    },
    //重置
    resetForm (formName) {
      this.$refs[formName].resetFields();
      this.pageNumber = 1
      this.getData()
    },
    onSubmit () {
      this.pageNumber = 1
      this.getData()
    },
    goDetail (row) {
      this.$router.push("add?type=3&id=" + row.id);
    },
    addnewfn (row) { this.$router.push("add?type=1"); },
    gomodify (row) { this.$router.push('add?type=2&id=' + row.id) },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },
    handleSizeChange (val) {
      this.pageNumber = 1
      this.pageSize = val
      this.getData()
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange (val) {
      this.pageNumber = val
      this.getData()
      console.log(`当前页: ${val}`);
    },
    getData () {
      var v = this.formInline
      var data = {
        cusName: v.name,//客户名称
        cusTel: v.tel,//客户电话
        saleName: v.salesPerson,//销售人员
        cusStar: v.starClass,//客户星级
        requireStatus: v.demandStatus,//需求状态
        phoneStatus: v.telStatus,//电话状态
        intent: v.Intentionality,//意向
        followStatus: v.follow,//跟进
        pageNumber: this.pageNumber,
        pageSize: this.pageSize
      }
      this.$http.post(queryCustomerList, data).then(res => {
        this.tableData = res.data.data.list
        this.total = Number(res.data.data.total)
      })
      console.log(data)
    }
  },
  activated () {
    this.getData()
    this.tableHeight =
      window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 160;
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
