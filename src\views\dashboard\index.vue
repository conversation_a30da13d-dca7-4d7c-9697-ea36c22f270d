<template>
  <div class="dashboard-container">
    <div class="nav">
      <div class="nav-head">
        <div class="nav-title">常用功能</div>
        <el-button icon="el-icon-edit"
          size='mini'
          @click="goModify"
          type="primary">修改</el-button>
      </div>
      <div class="nav-wrapper">
        <!-- <div
          class="nav-item"
          v-for="item,index in listNavData"
          :key="index"
           @click="go(item.url)">
          <div class="nav-img">
            <i class="icon-nav-shouye"></i>
          </div>
          <div class="nav-text">{{ item.name }}</div>
        </div> -->
        <el-row :gutter="20" style="width: 100%">
          <el-col :span="6"
                  v-for='(item,lan) in listNavData'>
            <div @click="go(item.url)"
                class="grid-content bg-purple">{{item.name}}</div>
          </el-col>
        </el-row>
      </div>
    </div>
    <h1 v-if='dataFlag'>
      <span>近期数据</span>
      <el-button size='mini'
                 class='day30'
                 @click="latelyfn(30)"
                 :type="type==30?'primary':''">30日</el-button>
      <el-button size='mini'
                 @click="latelyfn(7)"
                 :type="type==7?'primary':''">7日</el-button>
      <el-button size='mini'
                 @click="latelyfn(1)"
                 :type="type==1?'primary':''">昨日</el-button>
    </h1>
    <div class="data-wrap"
         v-if='dataFlag'>
      <ul>
        <li class="bg7">
          <p>{{todayNum}}日新注册用户</p>
          <p>{{latelyData.newUserSum}}<span>人</span></p>
        </li>
        <li class="bg3">
          <p>{{todayNum}}日新增客户</p>
          <p>{{latelyData.newCustomerSize}}<span>人</span></p>
        </li>
        <li class="bg1">
          <p>{{todayNum}}日新增调度员</p>
          <p>{{latelyData.newAgentSize}}<span>人</span></p>
        </li>
        <li class="bg2">
          <p>{{todayNum}}日新增司机</p>
          <p>{{latelyData.newDriverSize}}<span>人</span></p>
        </li>
      </ul>
      <ul>
        <li class="bg8">
          <p>{{todayNum}}日新增车辆</p>
          <p>{{latelyData.newCarSize}}<span>辆</span></p>
        </li>
        <li class="bg6">
          <p>{{todayNum}}日新增总订单</p>
          <p>{{latelyData.newOrderSize}}<span>单</span></p>
        </li>
        <li class="bg5">
          <p>{{todayNum}}日新增调度员单</p>
          <p>{{latelyData.newAgentOrderSize}}<span>单</span></p>
        </li>
        <li class="bg4">
          <p>{{todayNum}}日新增运单</p>
          <p>{{latelyData.newAgentWaybillSize}}<span>单</span></p>
        </li>
      </ul>
      <div class="data-loading" v-if="isDataLoading">
        <i class="el-icon-loading"></i>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
const latelyData = '/admin-center-server/home/<USER>'//最近数据
const listNav = '/admin-center-server/home/<USER>'//常用导航
export default {
  name: 'Dashboard',
  data () {
    return {
      currentRole: 'adminDashboard',
      latelyData: {},
      listNavData: [],
      todayNum: '昨',
      type: '1',
      dataFlag: false,
      isDataLoading: true,
      ajaxId: 0
    }
  },
  computed: {
    ...mapGetters([
      'roles'
    ])
  },
  created () {
    if (!this.roles.includes('admin')) {
      this.currentRole = 'editorDashboard'
    }
  },
  methods: {
    goModify () {
      this.$router.push("/dashboard/modify");
    },
    //切换时间
    latelyfn (type) {
      this.type = type
      if (type == '1') {
        this.todayNum = '昨'
      } else {
        this.todayNum = type
      }

      let ajaxId = ++this.ajaxId
      this.isDataLoading = true
      this.$http.get(latelyData + '?number=' + type).then(res => {
        if (ajaxId !== this.ajaxId) return
        if (res.data.data) {
          this.latelyData = res.data.data
        } else {
          this.latelyData = []
        }

      })
        .finally(() => {
          if (ajaxId !== this.ajaxId) return
          this.isDataLoading = false
        })
    },
    go (url) {
      if (url) {
        this.$router.push(url);
      } else {
        this.$message({
          message: '没有配置访问地址',
          type: 'warning'
        });
      }

    },
    listNavfn () {
      this.$http.get(listNav).then(res => {
        this.listNavData = res.data.data
      })
    },
    rolesfn () {
      var that = this
      var roles = this.$store.getters.roles
      for (var i = 0; i < roles.length; i++) {
        if (roles[i].permission == 'FORBID') {
          that.latelyfn(1)
          that.dataFlag = true
        }
      }
    }

  },
  watch: {
    $route () {
      //跳转到该页面后需要进行的操作
      this.rolesfn()
      this.listNavfn()
    }
  },

  mounted () {
    this.rolesfn()
    this.listNavfn()
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
* {
  margin: 0;
  list-style-type: none;
}
.nav {
  background: #fff;
  border-radius: 3px;
}
.nav-head {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  .el-button {
    margin: 15px 15px 0 0;
  }
}
.nav-title {
  height: 46px;
  line-height: 46px;
  padding-left: 20px;
  border-bottom: 1px solid #fafafa;
  color: #292929;
}
.nav-wrapper {
  display: flex;
  flex-wrap: wrap;
}
.nav-item {
  margin: 0 30px;
  width: 100px;
  cursor: pointer;
}
.nav-img {
  margin: 0 auto;
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  background: green;
  border-radius: 20px;
  color: #fff;
  i {
    vertical-align: middle;
  }
}
.nav-text {
  margin-top: 10px;
  text-align: center;
}
.dashboard-container {
  width: 100%;
  height: 100%;
  padding: 20px 32px;
  background-color: #f2f2f2;
  overflow: hidden;
}
h1 {
  font-size: 1.5rem;
  margin: 10px 0;
  span {
    font-size: 14px;
    // border-bottom: 3.5px solid #2ca1ff;
  }
  button {
    float: right;
  }
}
.el-row {
  margin-left: 0 !important;
  margin-right: 0 !important;
  margin-bottom: 20px;
  background: #fff;
  border-radius: 5px;
  padding: 20px 30px 10px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 4px;
  cursor: pointer;
}
.bg-purple-dark {
  background: #fff;
}
.bg-purple {
  background: #fff;
  text-align: center;
  line-height: 30px;
  font-size: 14px;
  color: #000;
  margin-bottom: 10px;
}
.bg-purple-light {
  background: #e5e9f2;
}
.grid-content {
  border-radius: 4px;
  min-height: 30px;
  border: 1px solid #ccc;
}
.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}
.data-wrap {
  position: relative;
  overflow: hidden;
  border-radius: 5px;
  padding-top: 10px;
  .data-loading {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(255, 255, 255, 0.7);
    i {
      font-size: 30px;
      color: #666;
    }
  }
  ul {
    width: 100%;
    overflow: hidden;
    padding-left: 0;
    li {
      font-size: 14px;
      color: #000;
      margin-bottom: 20px;
      height: 120px;
      border-radius: 4px;
      width: 23.8%;
      //width: 266px;
      float: left;
      margin-left: 1.5%;
      background: #fff !important;
      &:first-child {
        margin-left: 0;
      }
      p {
        color: #333;
        margin-top: 20px;
        margin-left: 20px;
      }
      p:last-child {
        margin-top: 20px;
        font-size: 26px;
        > span {
          font-size: 14px;
        }
      }
      p:first-child {
        
      }
    }
    .bg1 {
      background: url(img/bg1.png) no-repeat;
      background-size: 100% 100%;
    }
    .bg2 {
      background: url(img/bg2.png) no-repeat;
      background-size: 100% 100%;
    }
    .bg3 {
      background: url(img/bg3.png) no-repeat;
      background-size: 100% 100%;
    }
    .bg4 {
      background: url(img/bg4.png) no-repeat;
      background-size: 100% 100%;
    }
    .bg5 {
      background: url(img/bg5.png) no-repeat;
      background-size: 100% 100%;
    }
    .bg6 {
      background: url(img/bg6.png) no-repeat;
      background-size: 100% 100%;
    }
    .bg7 {
      background: url(img/bg7.png) no-repeat;
      background-size: 100% 100%;
    }
    .bg8 {
      background: url(img/bg8.png) no-repeat;
      background-size: 100% 100%;
    }
  }
}
.day30 {
  margin-left: 10px;
}
// @media screen and (min-width: 1300px) {
//   .data-wrap {
//     ul {
//       li {
//         width: 23.2%;
//       }
//     }
//   }
// }
</style>

