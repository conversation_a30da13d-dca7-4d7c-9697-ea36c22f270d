<template>
  <div>
    <el-tabs type="border-card"
             v-model="activeName"
             @tab-click="handleClick">
      <div class="flex">
        <el-row style="float:right">
          <el-button size="mini"
                     type="primary"
                     class="btn"
                     @click="enablefn">{{deleteFlagText}}</el-button>
        </el-row>
      </div>
      <el-tab-pane name='first'
                   label="子账户详情">
        <h2>基本信息</h2>
        <div class="ul">
          <p>
            <span>姓名：</span>
            <span class="span-1">{{datainfo.name}}</span>
          </p>
          <p>
            <span>手机号：</span>
            <span class="span-1">{{datainfo.mobile}}</span>
          </p>
          <p v-if='type==1'>
            <span>所属公司：</span>
            <span class="span-1">{{datainfo.parantName}}</span>
          </p>
          <p v-if='type==2'>
            <span>所属调度员：</span>
            <span class="span-1">{{datainfo.parantName}}</span>
          </p>
          <p>
            <span>创建日期：</span>
            <span class="span-1">{{datainfo.createdTime}}</span>
          </p>
          <p>
            <span>账户状态：</span>
            <span class="span-1">{{datainfo.deleteFlagTxt}}</span>
          </p>
        </div>
      </el-tab-pane>
      <el-tab-pane name='second'
                   label="登录日志">
        <el-table :data="tableData"
                  border
                  style="width: 100%;"
                  highlight-current-row>

          <el-table-column show-overflow-tooltip
                           v-for="item in tableLabel"
                           :label="item.label"
                           :width="item.width">
            <template slot-scope="scope">
              <span style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageNumber"
          :page-sizes="[10, 20, 40, 60, 80, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"></el-pagination>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
const info = '/admin-center-server/commonUser/info'//详情
const logList = '/admin-center-server/commonUser/logList'//日志
const updateUserStatus = '/admin-center-server/commonUser/updateUserStatus'//启用、禁用
export default {
  data () {
    return {
      datainfo: {},
      activeName: 'first',
      id: '',
      type: '',
      deleteFlagText: '',
      deleteFlags: '',
      tableData: [],
      tableLabel: [
      {
          prop: 'loginTime',
          label: '时间'
        },
        {
          prop: 'loginIp',
          label: 'IP'
        },
        {
          prop: 'loginTerminal',
          label: '登陆设备'
        },
        {
          prop: 'systemVersion',
          label: '系统版本'
        },
        {
          prop: 'appVersion',
          label: 'APP版本'
        },
        {
          prop: 'area',
          label: '地区'
        },
        {
          prop: 'loginType',
          label: '登陆方式'
        }
      ],
      pageSize: 10,
      pageNumber: 1,
      total: 0
    };
  },
  methods: {
    handleClick (tab, event) {
      console.log(tab, event);
      if (tab.name == 'second') {
        // 触发‘配置管理’事件
        this.second();
      } else {
        // 触发‘用户管理’事件
        this.getData();
      }
    },
    getData () {
      this.$http.get(info + `?id=${this.id}&userType=${this.type}&driverType=`).then(res => {
        if (res.data.data.userBasicinformation.deleteFlag == -1) {
          this.deleteFlagText = '启动'
          this.deleteFlags = 0
        } else {
          this.deleteFlagText = '禁用'
          this.deleteFlags = -1
        }

        this.datainfo = res.data.data.userBasicinformation
        console.log(this.datainfo)

      })

    },
    /**
     * 触发‘配置管理’事件
     */
    second () {
      this.$http.get(logList + `?order=desc&pageNumber=${this.pageNumber}&pageSize=${this.pageSize}&userId=${this.id}`).then(res => {
        this.tableData = res.data.data.list
        this.total = Number(res.data.data.total)
      })
    },
    enablefn () {
      var that = this
      this.$http.post(updateUserStatus + `/${this.id}?deleteFlag=${this.deleteFlags}`).then(res => {
        if (res.data.code == 200) {
          this.$message({
            message: res.data.message,
            type: 'success'
          });
          setTimeout(function () {
            that.getData()
          }, 3000);
        } else {
          this.$message.error(res.data.message);
        }
        console.log(res)
      })
    },
    handleSizeChange(v) {
      this.pageNumber = 1
      this.pageSize = v
      this.second()
    },
    handleCurrentChange(v) {
      this.pageNumber = v
      this.second()
    }
  },
  activated () {
    this.id = this.$route.query.id;
    this.type = this.$route.query.type;
    this.getData()
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
h2 {
  font-size: 16px;
  font-weight: normal;
  color: #409eff;
}
.ul {
  font-size: 14px;
  .span-1 {
    color: #333;
  }
}
.btn {
  margin-bottom: 10px;
}
.pagination {
  text-align: right;
  margin-top: 10px;
}
</style>
