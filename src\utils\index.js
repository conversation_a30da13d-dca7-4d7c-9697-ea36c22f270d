export function param2Obj(url) {
    const search = url.split('?')[1]
    if (!search) {
        return {}
    }
    return JSON.parse(
        '{"' +
        decodeURIComponent(search)
            .replace(/"/g, '\\"')
            .replace(/&/g, '","')
            .replace(/=/g, '":"') +
        '"}'
    )
}

   // 自动定位到表单报错项
export function  moveToErr() {
    this.$nextTick(() => {
      let isError = document.getElementsByClassName('is-error')
      if (isError.length) {
        isError[0].scrollIntoView({
          block: 'center',
          behavior: 'smooth'
        })
      }
    })
}

// 灰度环境判断
export function grayFn() {
  return window.location.host.indexOf('gray') > -1
  // return window.location.host.indexOf('dev') > -1
  // return true
}