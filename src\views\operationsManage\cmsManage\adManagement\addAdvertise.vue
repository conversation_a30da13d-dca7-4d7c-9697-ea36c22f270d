<template>
  <div class="app-container addCar">
    <div class="tip">
      <div>添加广告</div>
      <div>
        <em style="color: red">*</em>为必填项
      </div>
    </div>
    <div class="inner-box">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="200px"
        class="demo-ruleForm"
      >
        <el-form-item label="广告名称" required>
          <el-input v-model="ruleForm.title" style="width: 220px" placeholder="请输入广告名称"></el-input>
        </el-form-item>

        <el-form-item label="广告位置" required>
          <el-select v-model="ruleForm.adPosition" placeholder="请选择">
            <el-option v-for="item in positons" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="开始时间" required>
          <el-date-picker
            v-model="ruleForm.beginDate"
            type="datetime"
            placeholder="选择开始时间"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="到期时间" required>
          <el-date-picker
            v-model="ruleForm.endDate"
            type="datetime"
            placeholder="选择到期时间"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="展示图片:" required>
          <el-upload
            :v-model="ruleForm.photo"
            action
            :file-list="fileList"
            :limit="1"
            list-type="picture-card"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :http-request="ossUpload"
            :class="{hide:hideUpload}"
          >
            <div class="upload-box">
              <div class="icon-XZ"></div>
              <div class="icon-word">支持jpg、jpeg、png 格式不超过10M</div>
            </div>
          </el-upload>
          <el-dialog :visible.sync="dialogVisible" size="tiny">
            <img width="100%" :src="dialogImageUrl" alt />
          </el-dialog>
        </el-form-item>

        <el-form-item label="广告链接" required>
          <el-input v-model="ruleForm.url" style="width: 220px" placeholder="请输入广告链接"></el-input>
        </el-form-item>

        <el-form-item label="广告显示">
          <el-switch v-model="ruleForm.adValue" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            type="textarea"
            placeholder="请输入内容"
            v-model="ruleForm.content"
            maxlength="30"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button @click="commitInfo" v-if="typeFlag ==1">提交</el-button>
          <el-button @click="editorInfo" v-if="typeFlag ==2">修改</el-button>
          <el-button @click="recet">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import { client, OSS_REGION } from "@/utils/alioss";
export default {
  data() {
    return {
      Aliyun: {},
      date: "日期",
      dialogImageUrl: "",
      radio: "1",
      eventType: "1",
      dialogVisible: false,
      hideUpload: false, //控制是否显示的样式
      fileList: [], //图片回显
      ruleForm: {
        title: "", //广告名称
        beginDate: "", //开始时间
        endDate: "", //结束时间
        url: "", //广告链接
        photo: "", //图片地址
        adValue: false, //控制广告是否显示的开关
        textarea: "", //备注
        adPosition: "", //广告位下拉选的值
        content: "" //备注
      },
      rules: {
        name: [
          { required: true, message: "请输入活动名称", trigger: "blur" },
          { message: "长度在 3 到 5 个字符", trigger: "blur" }
        ]
      },

      positons: [], //广告位下拉选
      value: "",

      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      value1: "",
      value2: "",
      typeFlag: ""
    };
  },
  activated() {
    
  },
  methods: {
    /* 获取阿里云的签名地址 */
    getAliyunData() {
      const ossApiUrl = "/base-center-server/sts/oss_signature"; // 获取oss签名的地址
      // 获取oss签名
      this.$http.get(ossApiUrl).then(res => {
        if (res.data.code == "200") {
          let data = res.data;
          this.Aliyun = data.data;
          this.Aliyun.region = OSS_REGION;
          // return data
        }
      });
    },

    ossUpload(param) {
      let file = param.file; // 文件的
      const tmpcnt = file.name.lastIndexOf(".");
      const exname = file.name.substring(tmpcnt + 1);
      const fileName =
        "/" +
        this.Aliyun.bucket +
        "/" +
        this.Aliyun.dir +
        this.random_string(10) +
        "." +
        exname;
      client(this.Aliyun)
        .put(fileName, file)
        .then(res => {
          if (res.res.status === 200) {
            // 上传
            let imgUrl = res.res.requestUrls[0];
            // console.log(imgUrl)
            this.ruleForm.photo = imgUrl;
            this.$message.success("上传成功");
            this.hideUpload = true;
          } else {
            this.$message.error(res.res.message);
          }
        });
    },
    /* 随机的名字 */
    random_string(len) {
      len = len || 32;
      var chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
      var maxPos = chars.length;
      var pwd = "";
      for (var i = 0; i < len; i++) {
        pwd += chars.charAt(Math.floor(Math.random() * maxPos));
      }
      return pwd;
    },
    /* 移除图片 */
    handleRemove(file, fileList) {
      // console.log(file, fileList);
      this.hideUpload = false;
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          alert("submit!");
        } else {
          // console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },

    /* 广告位下拉列表接口 */
    getAdSelectData() {
      /* 后端定的请求时传这些字段 */
      let postData = {
        description: "",
        height: 0,
        id: "",
        name: "",
        order: "asc",
        pageNumber: 0,
        pageSize: 0,
        template: "",
        width: 0
      };

      this.$http
        .post("/admin-center-server/ad/queryAdpositionList", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.positons = data.data.list;
          } else {
            this.$message.warning(data.message);
          }
        });
    },

    /*添加的提交*/
    commitInfo() {
      let postData = {
        adpositionId: Number(this.ruleForm.adPosition),
        beginDate: this.ruleForm.beginDate,
        endDate: this.ruleForm.endDate,
        isShow: this.ruleForm.adValue,
        path: this.ruleForm.photo, //图片地址
        title: this.ruleForm.title,
        content: this.ruleForm.content, //备注
        url: this.ruleForm.url //广告的地址
      };
      /* 校验 */
      if (this.ruleForm.title.trim() == "") {
        this.$message({
          type: "warning",
          message: "请输入广告位"
        });
        return;
      }
      if (this.ruleForm.adPosition == "") {
        this.$message({
          type: "warning",
          message: "请选择广告位置"
        });
        return;
      }

      if (this.ruleForm.beginDate == "") {
        this.$message({
          type: "warning",
          message: "请选择开始时间"
        });
        return;
      }
      if ( new Date(this.ruleForm.beginDate).getTime()
        > new Date(this.ruleForm.endDate).getTime() ) {
        this.$message({
          type: "warning",
          message: "开始时间不能早于到期时间"
        })
        return
      }
      if (this.ruleForm.photo == "") {
        this.$message({
          type: "warning",
          message: "请上传图片"
        });
        return;
      }
      if (this.ruleForm.url == "") {
        this.$message({
          type: "warning",
          message: "请输入广告位链接"
        });
        return;
      }

      this.$http.post("/admin-center-server/ad/addAd", postData).then(res => {
        let data = res.data;
        if (data.code === "200") {
          this.$message({
            type: "success",
            message: "添加成功!"
          });
          this.$router.go(-1);
        } else {
          this.$message.warning(data.message);
        }
      });
    },
    /* 编辑 */
    editorInfo() {
      /* 校验 */
      if (this.ruleForm.title.trim() == "") {
        this.$message({
          type: "warning",
          message: "请输入广告位"
        });
        return;
      }
      if (this.ruleForm.adPosition == "") {
        this.$message({
          type: "warning",
          message: "请选择广告位置"
        });
        return;
      }

      if (this.ruleForm.beginDate == "") {
        this.$message({
          type: "warning",
          message: "请选择开始时间"
        });
        return;
      }
      if (this.ruleForm.photo == "") {
        this.$message({
          type: "warning",
          message: "请上传图片"
        });
        return;
      }
      if (this.ruleForm.url == "") {
        this.$message({
          type: "warning",
          message: "请输入广告位链接"
        });
        return;
      }

      let postData = {
        adpositionId: Number(this.ruleForm.adPosition),
        beginDate: this.ruleForm.beginDate,
        endDate: this.ruleForm.endDate,
        isShow: this.ruleForm.adValue,
        path: this.ruleForm.photo, //图片地址
        title: this.ruleForm.title,
        content: this.ruleForm.content, //备注
        id: this.$route.query.id
      };

      this.$http
        .post("/admin-center-server/ad/updateAd", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "修改成功!"
            });
            this.$router.go(-1);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 重置 */
    recet() {
      this.ruleForm = {};
    },
    /* 获取编辑的详情 */
    getDeitorDetail() {
      let id = this.$route.query.id;
      this.$http.get("/admin-center-server/ad/getByAdId?id=" + id).then(res => {
        let data = res.data;
        if (data.code === "200") {
          console.log(res);
          //修改
          this.ruleForm = {
            title: res.data.data.title, //广告名称
            beginDate: res.data.data.beginDate, //开始时间
            endDate: res.data.data.endDate, //结束时间
            url: res.data.data.url, //广告链接
            photo: res.data.data.path, //图片地址
            // adValue: res.data.data.isShowInt, //控制广告是否显示的开关
            adPosition: res.data.data.adpositionId, //广告位下拉选的值
            content: res.data.data.content //备注
          };
          if (res.data.data.isShowInt == 0) {
            this.ruleForm.adValue = false;
          } else {
            this.ruleForm.adValue = true;
          }
          //图片的回显
          if (res.data.data.path) {
            this.fileList.push({
              url: res.data.data.path
            });
          }
          if (this.fileList.length >= 1) {
            this.hideUpload = true;
          }
        } else {
          this.$message.warning(data.message);
        }
      });
    }
  },
  created() {
    this.getAliyunData();

    this.getAdSelectData(); //获取广告品位下拉选数据
    /* 添加 */
    this.typeFlag = this.$route.query.typeFlag;
    if (this.typeFlag == 1) {
      //新增
    } else if (this.typeFlag == 2) {
      this.getDeitorDetail();
      //修改
      // this.ruleForm = {
      //   title: this.$route.query.title, //广告名称
      //   beginDate: this.$route.query.beginDate, //开始时间
      //   endDate: this.$route.query.endDate, //结束时间
      //   url: this.$route.query.url, //广告链接
      //   photo: this.$route.query.path, //图片地址
      //   adValue: this.$route.query.isShow, //控制广告是否显示的开关
      //   adPosition: this.$route.query.adpositionId, //广告位下拉选的值
      //   content: this.$route.query.content //备注
      // };
      //图片的回显
      if (this.$route.query.path) {
        this.fileList.push({
          url: this.$route.query.path
        });
      }
      if (this.fileList.length >= 1) {
        this.hideUpload = true;
      }
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.hide .el-upload--picture-card {
  display: none;
}
.addCar {
  background-color: #ffffff;
  .tip {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 40px 40px 0;
    font-size: 12px;
    em {
      margin-right: 5px;
    }
  }
  .inner-box {
    // margin-left: 10%;
    width: 70%;
    .upload-box {
      width: 100%;
      height: 100%;
      position: relative;
      .icon-XZ {
        width: 92px;
        height: 92px;
        margin: 0 auto;
        background: url("../../../../assets/xiazai.png") no-repeat;
        background-size: 100% 100%;
      }
      .icon-word {
        width: 100%;
        height: 20px;
        line-height: 20px;
        font-size: 10px;
        position: absolute;
        bottom: 25px;
        left: 0px;
        color: #cccccc;
      }
    }
  }
}

.editor {
  margin-top: 30px;
}

.myQuillEditor {
  height: 400px;
}
.editCommit {
  margin: 100px;
}
</style>
