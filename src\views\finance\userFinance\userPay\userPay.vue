<template>
  <div class="app-container userPay">
    <div class="content">
      <el-tabs v-model="activeName">
        <el-tab-pane label="运费差" name="first"><FreightDifference/></el-tab-pane>
        <el-tab-pane label="服务费" name="second"><ServiceCharge/></el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import ServiceCharge from "./serviceCharge.vue";
import FreightDifference from "./freightDifference.vue";
export default {
  components: {
    ServiceCharge,
    FreightDifference
  },
  data() {
    return {
      activeName: "first"
    };
  },
  methods: {
  },
  activated() {
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.userPay {
  .content {
      background-color: white;
      padding: 20px;
  }
}
</style>
