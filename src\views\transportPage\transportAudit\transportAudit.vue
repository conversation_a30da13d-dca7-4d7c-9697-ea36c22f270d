<template>
  <div class="app-container">
    <el-form ref="searchForm" :model="search" :inline="true" class="search" label-width="110px" size="mini">
      <el-form-item label="订单号：" prop="orderSn">
        <el-input placeholder="请输入订单号" v-model="search.orderSn"></el-input>
      </el-form-item>
      <el-form-item label="运单号：" prop="orderItemSn">
        <el-input placeholder="请输入运单号" v-model="search.orderItemSn"></el-input>
      </el-form-item>
      <el-form-item label="货主名称：" prop="businessName">
        <el-input placeholder="请输入货主名称" v-model="search.businessName"></el-input>
      </el-form-item>
      <el-form-item label="货主手机号：" prop="businessPhone">
        <el-input placeholder="请输入货主手机号" v-model="search.businessPhone"></el-input>
      </el-form-item>
      <el-form-item label="司机姓名：" prop="driverName">
        <el-input placeholder="请输入司机姓名" v-model="search.driverName"></el-input>
      </el-form-item>
      <el-form-item label="司机手机号：" prop="driverPhone">
        <el-input placeholder="请输入司机手机号" v-model="search.driverPhone"></el-input>
      </el-form-item>
      <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label-width="200px" label="收货审核时间/导入时间：">
        <el-date-picker
          type="datetimerange"
          size="mini"
          v-model="date"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"></el-date-picker>
      </el-form-item>
      <el-form-item label="创建人：" prop="orderCreateUserName">
        <el-input placeholder="请输入创建人姓名" v-model="search.orderCreateUserName"></el-input>
      </el-form-item>
      <el-form-item label="合作平台主体:" prop="baseId">
        <el-select v-model="search.baseId" placeholder="请选择" size="mini" clearable>
          <el-option v-for="item in $store.state.user.baseInfo" :key="item.id" :value="item.id" :label="item.baseName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" @click="doSearch" type="primary">查询</el-button>
        <el-button size="mini" @click="reset">重置</el-button>
        <!-- <el-button size="mini" @click="isFreightLimitShow = true" type="primary">设置运费限制</el-button> -->
      </el-form-item>
    </el-form>

    <div class="wrapper">
      <el-tabs v-if="!$store.state.user.userInfo2.hasStandardModeFlag" v-model="tab" type="border-card" class="tab">
        <el-tab-pane label="实时延迟付单" name="0"></el-tab-pane>
        <el-tab-pane label="导入" name="1"></el-tab-pane>
      </el-tabs>

      <el-table
        :data="data"
        class="table"
        border
        cell-class-name="table_cell_gray"
        header-cell-class-name="table_header_cell_gray">
        <el-table-column type="selection"></el-table-column>
        <el-table-column label="订单号" prop="orderSn"></el-table-column>
        <el-table-column label="运单号" prop="orderItemSn"></el-table-column>
        <el-table-column label="货主名称" prop="businessName"></el-table-column>
        <el-table-column label="货主手机号" prop="businessPhone"></el-table-column>
        <el-table-column label="合作平台主体" prop="baseName"></el-table-column>
        <el-table-column label="接单司机" width="120">
          <template slot-scope="scope">
            {{ scope.row.driverName }}
            <!-- 除认证成功以外显示红色 -->
            <span v-if="scope.row.authStatusDTO.driverAuthStatus !== '2'" class="reject-text">（{{ scope.row.authStatusDTO.driverAuthStatusStr }}）</span>
          </template>
        </el-table-column>
        <el-table-column label="接单司机手机号" prop="driverPhone">
          <template slot-scope="scope">
            {{ scope.row.driverPhone }}
            <span v-if="scope.row.authStatusDTO.mobileAuthStatus !== '1'" class="mobile-warn">(<span>未验证</span>)</span>
          </template>
        </el-table-column>
        <el-table-column label="接单车辆" prop="plateNumber">
          <template slot-scope="scope">
            {{ scope.row.plateNumber }}
            <span v-if="scope.row.authStatusDTO.carAuthStatus !== '1'" class="reject-text">（{{ scope.row.authStatusDTO.carAuthStatusStr }}）</span>
          </template>
        </el-table-column>
        <el-table-column label="车辆所有人" width="120">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.licenseOwner === scope.row.businessName" placement="bottom">
              <i class="el-icon-warning-outline owner-icon" style="color: #ED970F"></i>
              <template slot="content">
                系统检测到“车辆所有人”与“货主名称”相同，请注意审核
              </template>
            </el-tooltip>
            {{ scope.row.licenseOwner }}
          </template>
        </el-table-column>
        <el-table-column label="司机运费" prop="amount"></el-table-column>
        <el-table-column label="创建人" prop="orderCreateUserName"></el-table-column>
        <el-table-column label="创建时间" prop="orderCreatedDate"></el-table-column>
        <el-table-column :label="tab === '1' ? '导入时间' : '收货审核时间'" prop="acceptDate">
          <template slot-scope="scope">
            <template v-if="tab === '1'">
              {{ scope.row.importDate }}
            </template>
            <template v-else>
              {{ scope.row.acceptDate }}
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" 
        fixed="right"
        min-width="100">
          <template slot-scope="scope">
            <el-button type="text" @click="audit(scope.row.orderItemId)">审核</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :total="total"
        :current-page.sync="page.pageNumber"
        @current-change="getList"
        @size-change="handleSizeChange">
      </el-pagination>
      <el-dialog
        :visible.sync="isFreightLimitShow"
        width="600px"
        @open="handleLimitDialogOpen">
        <template slot="title">
          运费限制
          <div class="log-btn" @click="isLogShow = true">修改日志</div>
        </template>
        <div class="limit">
          <el-form ref="limitForm" :rules="limitRules" :model="limitForm">
            <el-form-item label="按车付费：" prop="carLimit">
              每公里运费须 ≤ <el-input v-model="limitForm.carLimit"></el-input>元/车
            </el-form-item>
            <el-form-item label="按吨付费：" prop="tonLimit">
              每公里运费须 ≤ <el-input v-model="limitForm.tonLimit"></el-input>元/吨
            </el-form-item>
          </el-form>
          <div class="limit-btns">
            <el-button type="primary" @click="modifyLimit">确认修改</el-button>
            <el-button @click="isFreightLimitShow = false">取消</el-button>
          </div>
        </div>
      </el-dialog>
      <el-dialog
        :visible.sync="isLogShow"
        title="修改日志"
        width="900px"
        @open="handleLogOpen">
        <el-table :data="logData">
          <el-table-column label="修改时间" prop="createdDate"></el-table-column>
          <el-table-column label="修改前" prop="oriValue"></el-table-column>
          <el-table-column label="修改后" prop="value"></el-table-column>
          <el-table-column label="操作人" prop="createdUserName"></el-table-column>
        </el-table>
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          background
          :total="logTotal"
          :current-page.sync="logPage.pageNumber"
          @current-change="getLog"
          @size-change="handleLogSizeChange">
        </el-pagination>
      </el-dialog>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      page: {
        pageSize: 10,
        pageNumber: 1
      },
      isSearching: false,
      search: {
        orderSn: '',
        orderItemSn: '',
        businessName: '',
        businessPhone: '',
        driverName: '',
        driverPhone: '',
        orderCreateUserName: ''
      },
      date: '',
      data: [],
      total: 0,
      tab: '0',
      isFreightLimitShow: false,
      limitRules: {
        carLimit: [
          { required: true, message: '请输入运费限制', trigger: 'blur' }
        ],
        tonLimit: [
          { required: true, message: '请输入运费限制', trigger: 'blur' }
        ]
      },
      limitForm: {
        carLimit: null,
        tonLimit: null
      },
      isLogShow: false,
      logData: [],
      logTotal: 0,
      logPage: {
        pageSize: 10,
        pageNumber: 1
      }
    }
  },
  activated() {
    this.getList()

    if (!this.$store.state.user.userInfo2.hasStandardModeFlag) {
      this.tab = '0'
    }
  },
  watch: {
    tab(v) {
      this.page.pageNumber = 1
      this.getList()
    }
  },
  methods: {
    getList() {
      let params = {
        ...this.page,
        tab: this.tab
      }
      if (this.isSearching) {
        Object.assign(params, this.search)
        if (this.date !== null) {
          params.acceptDateStart = this.date[0]
          params.acceptDateEnd = this.date[1]
        }
      }
      this.$post('/admin-center-server/orderItem/getAuditVoPage', params)
        .then(res => {
          this.data = res.list
          this.total = Number(res.total)
        })
        .catch(() => {
          this.data = []
          this.total = 0
        })
    },
    handleSizeChange(v) {
      this.page.pageSize = v
      this.getList()
    },
    doSearch() {
      this.isSearching = true
      this.page.pageNumber = 1
      this.getList()
    },
    reset() {
      this.isSearching = false
      this.$refs.searchForm.resetFields()
      this.date = null
      this.page.pageNumber = 1
      this.getList()
    },
    handleLimitDialogOpen() {
      this.$get('/admin-center-server/base/getInfo', {
        group: 'freightLimit',
        code: 'vehicleKmMax'
      })
        .then(res => {
          this.limitForm.carLimit = res[0].value
        })
      this.$get('/admin-center-server/base/getInfo', {
        group: 'freightLimit',
        code: 'weightKmMax'
      })
        .then(res => {
          this.limitForm.tonLimit = res[0].value
        })
    },
    modifyLimit() {
      this.$refs.limitForm.validate(valid => {
        if (!valid) return
        this.$post('/admin-center-server/base/updateSetting', [
          {
            group: 'freightLimit',
            code: 'vehicleKmMax',
            value: this.limitForm.carLimit
          },
          {
            group: 'freightLimit',
            code: 'weightKmMax',
            value: this.limitForm.tonLimit
          }
        ])
          .then(() => {
            this.$message.success('修改成功')
            this.isFreightLimitShow = false
          })
      })
    },
    getLog() {
      this.$post('/admin-center-server/base/setting/logs', {
        ...this.logPage,
        group: 'freightLimit'
      })
        .then(res => {
          this.logData = res.list
          this.logTotal = Number(res.total)
        })
    },
    handleLogOpen() {
      this.getLog()
    },
    handleLogSizeChange(v) {
      this.logPage.pageSize = v
      this.getLog()
    },
    audit(id) {
      let routerData = this.$router.resolve('/transport/transportAuditDetail?orderItemId=' + id)
      let auditWindow = window.open(routerData.href)
      this.openTimers = this.openTimers || []
      let openTimer = setInterval(() => {
        if (auditWindow.closed) {
          this.getList()
          clearInterval(openTimer)
        }
      }, 200)
      this.openTimers.push(openTimer)
    }
  },
  deactivated() {
    this.openTimers&&this.openTimers.forEach(v => clearInterval(v))
  }
}
</script>

<style scoped lang="scss">
  .search {
    padding: 20px;
    background: #fff;
  }
  .wrapper {
    margin-top: 10px;
    padding: 0 20px;
    background: #fff;
  }
  .tab {
    margin: 0 -20px;
    box-shadow: none;
    border-width: 0;
    ::v-deep {
      .el-tabs__header .el-tabs__item.is-active {
        border-left-color: #F4F5FA;
        border-right-color: #F4F5FA;
      }
      .el-tabs__header {
        border-bottom: 0;
        background: #f2f2f2;
      }
    }
  }
  .el-pagination {
    margin: 10px 0;
    text-align: right;
  }
  .limit {
    ::v-deep .el-form-item__label {
      padding-right: 0;
      font-weight: normal;
    }
    .el-input {
      margin: 0 10px;
      width: 150px;
    }
  }
  .limit-btns {
    margin-top: 30px;
  }
  .log-btn {
    float: right;
    margin-right: 30px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
  }
  .mobile-warn span {
    color: #D9001B;
  }
  .reject-text {
    color: #D45353;
  }
</style>