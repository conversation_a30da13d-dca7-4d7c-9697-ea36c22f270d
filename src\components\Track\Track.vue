<template>
  <div class="track-wrapper">
    <div class="detail-panel">
      <div v-if="isMapFail">暂无轨迹数据</div>
      <div v-else class="amap-page-container">
        <div id="amap-show" class="amap-demo" style="height: 600px;"></div>
      </div>
      <div class="card">
        <div class="card-avatar">
          <img :src="waybillData.avatar">
        </div>
        <div class="card-content">
          <div class="card-info">{{ waybillData.driverName }} {{ waybillData.driverPhone }}</div>
          <div class="card-row">
            <div class="card-plate">
              <div class="card-plate-inner">{{ waybillData.plateNumber }}</div>
            </div>
            <div class="gps-status" :class="getGpsStatusClass(waybillData)">{{ getGpsStatusText(waybillData) }}</div>
          </div>
        </div>
      </div>
      <div v-if="$store.state.user.userInfo2.hasStandardModeFlag" class="alarm">
        <el-tabs v-model="alarmTab">
          <el-tab-pane label="超速报警" name="1"></el-tab-pane>
          <el-tab-pane label="疲劳驾驶" name="2"></el-tab-pane>
          <el-tab-pane label="离线报警" name="3"></el-tab-pane>
        </el-tabs>
        <div v-if="alarmList.length > 0" class="alarm-list">
          <div v-for="(item, index) in alarmList" :key="index" class="alarm-item">
            <div class="alarm-time">{{ item.time.slice(5) }}</div>
            <div class="alarm-level">{{ alarmTab === '3' ? '离线' : item.level }}</div>
            <div v-if="alarmTab !== '3'" class="alarm-text"><i class="el-icon-warning-outline"></i> {{ item.message }}</div>
          </div>
        </div>
        <div v-else class="alarm-empty">暂无数据</div>
      </div>
      <div v-if="isShowTab" class="tab">
        <el-radio-group v-model="tab" @change="handleTabChange">
          <el-radio-button :label="trackTypes.app">APP轨迹</el-radio-button>
          <el-radio-button :label="trackTypes.hfd">鸿飞达设备轨迹</el-radio-button>
          <el-radio-button :label="trackTypes.beidou">北斗轨迹</el-radio-button>
          <el-radio-button :label="trackTypes.third">三方轨迹</el-radio-button>
        </el-radio-group>
        <!-- <div class="tab-warn" v-if="tab === trackTypes.beidou">
          <i class="el-icon-warning-outline"></i> 暂不支持查看车辆当前位置及运输途中的轨迹
        </div> -->
      </div>
      <div v-if="isAuditDetail">
        <div class="address-wrapper">
          <div class="address">
          <div class="address-item address-item-unload">
            <div class="address-title">
              装货打卡<span>{{ loadingData.checkInTime }}</span>
            </div>
            <div class="address-text">{{ loadingData.checkInAddress }}</div>
          </div>
          <div class="address-item address-item-load">
            <div class="address-title">
              卸货打卡<span>{{ unloadingData.checkInTime }}</span>
            </div>
            <div class="address-text">{{ unloadingData.checkInAddress }}</div>
          </div>
        </div>
          <!-- <div class="address">
            <div class="address-item2 address-item-load" v-if="updateLoad">
              <div class="address-title">
                装货打卡
              </div>
              <div class="address-text">操作时间:{{ updateLoad.checkInTime || '--' }}</div>
              <div class="address-text">操作地址:{{ updateLoad.checkInAddress  || '--' }}</div>
            </div>
            <div class="address-item2 address-item-unload" v-if="updateUnload">
              <div class="address-title">
                卸货打卡
              </div>
              <div class="address-text">操作时间:{{ updateUnload.checkInTime || '--'  }}</div>
              <div class="address-text">操作地址:{{ updateUnload.checkInAddress || '--'  }}</div>
            </div>
          </div> -->
        </div>
      </div>
      <div v-else>
        <div class="address-wrapper">
          <div v-if="waybillData.dataFrom === '0' && clockInList && clockInList.length>0" class="address">
            <div class="address-item address-item-clock" v-for="item in clockInList" :key="item.id">
              <div class="address-title">
                {{ item.checkInTypeStr }}
                <el-tag v-if="(item.checkInTypeStr == '到达装货地' || item.checkInTypeStr == '到达卸货地') && item.isLate && item.lateDuration" size="mini" color="#fcd5d0" class="tag-class">晚点 {{ item.lateDuration }}分钟</el-tag>
              </div>
              <div class="address-text">操作时间:{{ item.checkInTime || '--'  }}</div>
              <div class="address-text">操作地址:{{ item.checkInAddress  || '--' }}</div>
            </div>
          </div>
        </div>
        <div class="address-wrapper2">
          <!-- 展示上报监管的装货点、卸货点信息 -->
          <div class="address">
            <div class="address-item2 address-item-load" v-if="updateLoad">
              <div class="address-title">
                修改后的装货点
              </div>
              <div class="address-text">操作时间:{{ updateLoad.checkInTime || '--' }}</div>
              <div class="address-text">操作地址:{{ updateLoad.checkInAddress  || '--' }}</div>
            </div>
            <div class="address-item2 address-item-unload" v-if="updateUnload">
              <div class="address-title">
                修改后的卸货点
              </div>
              <div class="address-text">操作时间:{{ updateUnload.checkInTime || '--'  }}</div>
              <div class="address-text">操作地址:{{ updateUnload.checkInAddress || '--'  }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div v-if="isPathInfoShow" @mouseleave="isPathInfoShow = false" class="info" :style="{left: infoLeft, top: infoTop}">
        <div class="info-inner info-width">
          <div @click="isPathInfoShow = false" class="info-close">
            <i class="el-icon-close"></i>
          </div>
          <div class="info-item">
            <div class="info-title">时间</div>
            <div class="info-content">{{ dayjs(pathInfo.time).format('YYYY-MM-DD HH:mm:ss') }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">位置</div>
            <div class="info-content">{{ pathInfo.address }}</div>
          </div>
          <!--  -->
          <div class="info-item" v-if="tab === trackTypes.hfd || tab === trackTypes.beidou || tab === trackTypes.app">
            <div class="info-title">速度</div>
            <div class="info-content">{{ pathInfo.speed ? pathInfo.speed + 'km/h' : '--' }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">经度</div>
            <div class="info-content">{{ pathInfo.coordinate[0] }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">纬度</div>
            <div class="info-content">{{ pathInfo.coordinate[1] }}</div>
          </div>
          
        </div>
      </div>
      <div v-if="isCurrentInfoShow" @mouseleave="isCurrentInfoShow = false" class="info" :style="{left: infoLeft, top: infoTop}">
        <div class="info-inner info-width">
          <div @click="isCurrentInfoShow = false" class="info-close">
            <i class="el-icon-close"></i>
          </div>
          <div class="info-item">
            <div class="info-title">节点</div>
            <div class="info-content">车辆位置</div>
          </div>
          <div class="info-item">
            <div class="info-title">时间</div>
            <div class="info-content">{{ currentInfo.time }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">位置</div>
            <div class="info-content">{{ currentInfo.address }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">速度</div>
            <div class="info-content">{{ currentInfo.speed ? currentInfo.speed + 'km/h' : '--' }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">里程</div>
            <div class="info-content">
              <span>{{ currentInfo.mileage?'全程'+currentInfo.mileage+'km':'--' }}</span> | 
              <span>{{ currentInfo.distance?'剩余'+currentInfo.distance+'km':'--' }}</span>
            </div>
          </div>
          <div class="info-item">
            <div class="info-title">预计到达</div>
            <div class="info-content">{{ currentInfo.estimatedArrival||'--' }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">预计时效</div>
            <div class="info-content">{{ currentInfo.estimatedTimeliness||'--' }}</div>
          </div>
          <!-- <div>当前位置：{{ currentInfo.address }}</div>
          <div>{{ currentInfo.time }}</div>
          <div class="distance">距离卸货地{{ currentInfo.distance }}km</div> -->
        </div>
      </div>
      <div v-if="isAddressInfoShow" @mouseleave="isAddressInfoShow = false" class="info" :style="{left: infoLeft, top: infoTop}">
        <div class="info-inner">
          <div @click="isAddressInfoShow = false" class="info-close">
            <i class="el-icon-close"></i>
          </div>
          {{ addressInfo.address }}
        </div>
      </div>
      <!-- 离线 -->
      <div v-if="isOfflineInfoShow" @mouseleave="isOfflineInfoShow = false" class="info" :style="{left: infoLeft, top: infoTop}">
        <div class="info-inner info-width">
          <div @click="isOfflineInfoShow = false" class="info-close">
            <i class="el-icon-close"></i>
          </div>
          <div class="info-item">
            <div class="info-title">节点</div>
            <div class="info-content">离线</div>
          </div>
          <div class="info-item">
            <div class="info-title">位置</div>
            <div class="info-content">{{ offlineInfo.lastLocation || '--' }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">开始</div>
            <div class="info-content">{{ offlineInfo.offlineStartTime || '--' }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">结束</div>
            <div class="info-content">{{ offlineInfo.offlineEndTime || '--' }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">时长</div>
            <div class="info-content">{{ offlineInfo.offlineDurationStr || '--' }}</div>
          </div>
        </div>
      </div>
       <!-- 停车 -->
      <div v-if="isStopInfoShow" @mouseleave="isStopInfoShow = false" class="info" :style="{left: infoLeft, top: infoTop}">
        <div class="info-inner info-width">
          <div @click="isStopInfoShow = false" class="info-close">
            <i class="el-icon-close"></i>
          </div>
          <div class="info-item">
            <div class="info-title">节点</div>
            <div class="info-content">停车</div>
          </div>
          <div class="info-item">
            <div class="info-title">位置</div>
            <div class="info-content">{{ stopInfo.parkAdr || '--' }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">开始</div>
            <div class="info-content">{{ stopInfo.parkBteAllStr || '--' }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">结束</div>
            <div class="info-content">{{ stopInfo.parkEteAllStr || '--' }}</div>
          </div>
          <div class="info-item">
            <div class="info-title">时长</div>
            <div class="info-content">{{ stopInfo.parkMinsStr || '--' }}</div>
          </div>
        </div>
      </div>
      <!-- 停车记录、离线记录 -->
      <div class="record-tab" v-if="tab === trackTypes.beidou || tab === trackTypes.app">
        <span class="record-tab-i" v-if="activeName=='1'"><i class="el-icon-warning-outline"></i>至少连续{{ offlineTime }}分钟未获取到{{ tab === trackTypes.app?'App':'北斗' }}定位</span>
        <span class="record-tab-i" v-else><i class="el-icon-warning-outline"></i>至少停车{{stopTime}}分钟</span>
        <el-tabs v-model="activeName">
          <el-tab-pane label="停车记录" name="2" v-if="trackTypes.beidou == tab">
            <div class="record-tab-content-all" v-if="stopList && stopList.length>0">
              <div class="record-tab-content" v-for="(item, index) in stopList" :key="index">
                <div class="record-tab-content-left"><img src="./images/stop.png" alt=""></div>
                <div class="record-tab-content-right">
                  <div>{{ item.parkAdr }}</div>
                  <div>停车开始时间：{{ item.parkBteStr }}</div>
                  <div>停车结束时间：{{ item.parkEteStr }}</div>
                  <div>停车时长：{{ item.parkMinsStr }}</div>
                </div>
              </div>
            </div>
            <div class="record-tab-content-all text-centent" v-else>
              暂无停车记录
            </div>
          </el-tab-pane>
          <el-tab-pane label="离线记录" name="1">
              <div class="record-tab-content-all" v-if="offlineList && offlineList.length>0">
                <div class="record-tab-content" v-for="(item, index) in offlineList" :key="index">
                  <div class="record-tab-content-left"><img src="./images/offlineImg.png" alt=""></div>
                  <div class="record-tab-content-right">
                    <div>{{ item.lastLocation }}</div>
                    <div>离线开始时间:{{ item.offlineStartTimeStr }}</div>
                    <div>离线结束时间:{{ item.offlineEndTimeTimeStr }}</div>
                    <div>离线时长:{{ item.offlineDurationStr }}</div>
                  </div>
                </div>
              </div>
              <div class="record-tab-content-all text-centent" v-else>
                暂无离线记录
              </div>
          </el-tab-pane>
        </el-tabs>
      </div> 
      <div class="scale">
        <div @click="track.zoomIn()" class="scale-item">
          <i class="el-icon-plus"></i>
        </div>
        <div @click="track.zoomOut()" class="scale-item">
          <div class="el-icon-minus"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Track } from './track'
import { trackTypes } from './trackConfig'
import dayjs from 'dayjs'
export default {
  name: 'Track',
  props: {
    isAuditDetail: { // 是否是审核详情
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isWaybillDetail: false,
      trackTypes,
      isMapFail: false,
      tab: '1',
      isPathInfoShow: false,
      infoLeft: 0,
      infoTop: 0,
      pathInfo: {
        coordinate: [],
        time: ''
      },
      isCurrentInfoShow: false,
      currentInfo: {
        
      },
      isAddressInfoShow: false,
      addressInfo: {
        address: ''
      },
      track: null,
      waybillData: {},
      alarmTab: '1',
      fatigueDriving: [],
      offLine: [],
      speed: [],
      list1: [],
      list2: [],
      list3: [],
      clockInList: [],
      updateLoad: null,
      updateUnload: null,
      unloadingData: {},
      loadingData: {},
      isOfflineInfoShow: false,// 离线
      activeName: '1', // 离线记录
      offlineList: [],// 离线列表
      stopList: [],// 停车列表
      stopTime: 0,// 停车时长
      offlineTime: 0,// 离线时长
      offlineInfo: {},// 悬浮离线信息
      isStopInfoShow: false,// 停车
      stopInfo: {},// 悬浮停车信息
    }
  },
  methods: {
    dayjs,
    /**
     * 
     * @param {Object} config - 轨迹配置项
     * @param {Object} config.waybillData - 运单信息
     * @param {Object} config.orderData - 订单信息
     * @param {Boolean} config.isWaybillDetail - 是否展示：装、卸水滴；装货地卸货地文字以及旗帜，默认展示（运单详情为true，运单审核为false）
     * @param {Boolean} config.isNavigation - 是否从高德获取轨迹，默认否
     * @param {Boolean} config.isShowTab - 是否展示地图切换
     */
     async init({
      waybillData,
      orderData,
      isWaybillDetail = true,
      isNavigation = false,
      isShowTab = false
    }) {
      // this.tab = trackTypes.app
      this.$post('/admin-center-server/app/orderItemGps/viewGps?orderItemId='+waybillData.orderItemId)
        .then(res => {
        let showTab = '1'
        if(res && res.routePathFrom){
          if(res.routePathFrom==='0'){
            showTab = '3'
          }else if(res.routePathFrom==='2' || res.routePathFrom==='3'){
            showTab = '2'
          }else if(res.routePathFrom==='4'){
            showTab = '1'
          }else if(res.routePathFrom==='5'){
            showTab = '4'
          }
        }
        this.tab = showTab
        this.handleTabChange(showTab)
      })
      this.waybillData = waybillData
      this.isWaybillDetail = isWaybillDetail
      this.isShowTab = isShowTab
      let resData = await this.$get('/admin-center-server/orderItem/getCheckInfoByOrderItemId?OrderItemId=' + waybillData.orderItemId)
      console.log('打卡信息', resData)
      // 处理上报的装货点、卸货点信息
      let clockInList = []
      if (resData && resData.length > 0) {
        resData.forEach(item => {
          // 运单审核详情展示点位信息
          if(this.isAuditDetail){
            if(item.checkInTypeStr==='装货地发车'){
              this.loadingData = item 
            }
            if(item.checkInTypeStr==='卸货完成'){
              this.unloadingData = item 
            }
          }
          if (item.checkInTypeStr.includes('修改')) {
            if(item.checkInTypeStr==='修改后的装货点'){
              this.updateLoad = item
              if(item.checkInAddress){
                this.loadingData = item 
              }
              
            }else if(item.checkInTypeStr==='修改后的卸货点'){
              this.updateUnload = item
              if(item.checkInAddress){
                this.unloadingData = item 
              }
            }
          }else{
            clockInList.push(item)
          }
        })
      }
      this.clockInList = clockInList

      let track = new Track({
        waybillData,
        orderData,
        isWaybillDetail,
        isNavigation,
        mapId: 'amap-show',
        clockInList: this.clockInList,
        updateLoad: this.updateLoad,
        updateUnload: this.updateUnload,
        isAuditDetail: this.isAuditDetail,
        loadingData: this.loadingData,
        unloadingData: this.unloadingData
      })
      track.on('pointHover', ({ event, pointInfo, address }) => {
        let position = event.originalEvent.pixel
        let infoLeft = position.x + 20 + 'px',
          infoTop = position.y + 20 + 'px'
        this.infoLeft = infoLeft
        this.infoTop = infoTop
        let data = pointInfo.pathData.path[pointInfo.pointIndex]
        let coordinate = data.slice(0, 2)
        let time = data[2]
        this.pathInfo = {
          coordinate,
          time,
          address,
          speed: data[3]
        }
        this.isPathInfoShow = true
      })
      track.on('positionHover', ({ event, addressInfo}) => {
        let position = event.originEvent.pixel
        let infoLeft = position.x + 20 + 'px',
          infoTop = position.y + 20 + 'px'
        this.infoLeft = infoLeft
        this.infoTop = infoTop
        this.addressInfo.address = addressInfo.address
        this.isAddressInfoShow = true
      })
      track.on('currentHover', ({ event, currentInfo}) => {
        let position = event.originEvent.pixel
        let infoLeft = position.x + 20 + 'px',
          infoTop = position.y + 20 + 'px'
        this.infoLeft = infoLeft
        this.infoTop = infoTop
        this.currentInfo = {
          address: currentInfo.address,
          time: currentInfo.time,
          speed: currentInfo.speed,
          distance: currentInfo.distance,
          estimatedArrival: currentInfo.estimatedArrival, // 预计到达时间
          estimatedTimeliness: currentInfo.estimatedTimeliness, // 预计时效
          mileage: currentInfo.mileage // 总里程
        }
        this.isCurrentInfoShow = true
      })
      track.on('error', () => {
        this.$emit('error')
      })
      track.on('source', (source) => {
        this.$emit('source', source)
      })
      track.showTrack(isNavigation)
       track.on('offline', (res) => {
        this.offlineList = res
      })
      track.on('stopList', (res) => {
        this.stopList = res
      })  
      track.on('offlineHover', ({ event, dataInfo}) => {
        let position = event.originEvent.pixel
        let infoLeft = position.x + 20 + 'px',
          infoTop = position.y + 20 + 'px'
        this.infoLeft = infoLeft
        this.infoTop = infoTop
        this.offlineInfo = dataInfo
        this.isOfflineInfoShow = true
      })
      track.on('stopOut', () => this.isStopInfoShow = false)
      track.on('stopHover', ({ event, dataInfo}) => {
        let position = event.originEvent.pixel
        let infoLeft = position.x + 20 + 'px',
          infoTop = position.y + 20 + 'px'
        this.infoLeft = infoLeft
        this.infoTop = infoTop
        this.stopInfo = dataInfo
        this.isStopInfoShow = true
      })
      track.on('offlineOut', () => this.isOfflineInfoShow = false)
      this.track = track

      this.$post('/admin-center-server/app/orderItemGps/gpsAlarm', {}, {
        params: {
          orderItemId: waybillData.orderItemId
        }
      })
        .then(res => {
          this.list1 = res.speed
          this.list2 = res.fatigueDriving
          this.list3 = res.offLine
        })
    },
    handleTabChange(v) {
      this.stopList = []
      this.offlineList = []
      this.isPathInfoShow = false
      this.isCurrentInfoShow = false
      this.isAddressInfoShow = false
      this.isOfflineInfoShow = false
      this.isStopInfoShow = false
      this.track.switchType(v)
      if(v === this.trackTypes.beidou) {
        this.getSettingBd()
      } else if (v === this.trackTypes.app) {
        this.getSettingApp()
      }
    
    },
    getGpsStatusClass(row) {
      if (row.deviceStatus === '-1') {
        return 'gps-status-unbind'
      } else if (row.deviceStatus === '0') {
        return 'gps-status-offline'
      } else {
        return 'gps-status-online'
      }
    },
    getGpsStatusText(row) {
      if (row.deviceStatus === '-1') {
        return '未绑定'
      } else if (row.deviceStatus === '0') {
        return '离线'
      } else {
        return '在线'
      }
    },
    auditSelect(type) {
      this.track.auditSelect(type)
    }, 
    // 获取北斗停车时长和离线时长 设置
    getSettingBd() {
      // 获取离线时长  北斗
      this.$post('/base-center-server/settings/get?group=ZhongJiaoServer&code=offlineTime')
      .then(res => {
        this.offlineTime = res.value
      })
      // 获取停车时长  北斗
      this.$post('/base-center-server/settings/get?group=ZhongJiaoServer&code=parkingTime')
      .then(res => {
        this.stopTime = res.value
      })
    },
    // 获取app离线时长 设置
    getSettingApp() {
       this.$post('/base-center-server/settings/get?group=Time&code=offlineTimeApp')
      .then(res => {
        this.offlineTime = res.value
      })
    }
  },
  computed: {
    alarmList() {
      return this[`list${this.alarmTab}`]
    }
  }
}
</script>

<style scoped src="@/assets/scss/detail.scss" lang="scss"></style>
<style scoped lang="scss">
.open-big {
  float: right;
}
.detail-panel {
  position: relative;
}
.detail-title {
  margin-top: 0;
}
.card {
  position: absolute;
  top: 10px;
  left: 20px;
  display: flex;
  width: 250px;
  height: 80px;
  box-sizing: border-box;
  padding: 10px;
  background-color: #fff;
  border-radius: 10px;
}
.card-avatar {
  width: 60px;
  height: 60px;
  margin-right: 10px;
  img {
    width: 100%;
    height: 100%;
  }
}
.card-info {
  margin-top: 10px;
  font-size: 14px;
}
.card-row {
  margin-top: 5px;
}
.card-plate {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 20px;
  margin-right: 10px;
  background-color: #FFBD1E;
  border-radius: 2px;
  vertical-align: middle;
}
.card-plate-inner {
  width: 60px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  border: 1px solid #fff;
  font-size: 10px;
  color: #fff;
}
.tab {
  position: absolute;
  top: 10px;
  left: 280px;
}
.tab-warn {
  margin-top: 10px;
  padding: 10px 0 10px 10px;
  width: 330px;
  background: #fff;
  border-radius: 3px;
  font-size: 13px;
  line-height: 13px;
  color: var(--theme-color);
}
.gps-status {
  display: inline-block;
  width: 54px;
  height: 22px;
  border-radius: 5px;
  text-align: center;
  line-height: 22px;
  font-size: 12px;
}
.gps-status-unbind {
  color: #606266;
  border: 1px solid #e9e9eb;
  background-color: #f4f4f5;
}
.gps-status-offline {
  border: 1px solid #fde2e2;
  color: rgb(245, 108, 108);
  background-color: #fef1f1;
}
.gps-status-online {
  border: 1px solid #e1f3d8;
  color: rgb(103, 194, 58);
  background-color: #f0f9ec;
}
.address-wrapper {
  position: absolute;
  top: 10px;
  right: 20px;
}
.address-wrapper2 {
  position: absolute;
  top: 10px;
  right: 280px;
}
.address {
  width: 240px;
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 10px;
}
.address-item {
  box-sizing: border-box;
  padding-left: 35px;
  padding-top: 15px;
  padding-bottom: 15px;
  width: 95%;
  margin: 0 auto;
  background: 8px 13px no-repeat;
}
.address-item2 {
  box-sizing: border-box;
  padding-left: 35px;
  padding-top: 15px;
  padding-bottom: 15px;
  width: 95%;
  margin: 0 auto;
  background: 10px 15px no-repeat;
}
.address-item-unload {
  background-image: url(./images/address1.png);
}
.address-item-load {
  border-top: 1px solid #f2f2f2;
  background-image: url(./images/address2.png);
}
.address-item-clock {
  background-image: url(./images/positionImg.png);
  background-size: 20px 20px;
}
.address-title {
  font-size: 14px;
  color: rgb(51, 51, 51);
  display: flex;
  justify-content: space-between;
  span {
    // margin-left: 10px;
    font-size: 12px;
  }
  .tag-class{
    margin-right: 0px;
  }
}
.address-text {
  margin-top: 10px;
  font-size: 13px;
  line-height: 24px;
}
.address-paly {
  color: rgb(51, 51, 51);
  font-size: 13px;
  text-align: right;
  padding-right: 10px;
}
.info {
  position: absolute;
  padding-top: 10px;
  padding-left: 10px;
  margin-top: -10px;
  margin-left: -10px;
  font-size: 14px;
  line-height: 24px;
}
.info-inner {
  box-sizing: border-box;
  width: 240px;
  padding: 20px;
  padding-bottom: 15px;
  background: #fff;
}
.info-close {
  position: absolute;
  right: 0;
  top: 10px;
  padding: 5px;
  cursor: pointer;
  i {
    font-size: 20px;
    color: #999;
  }
}
.info-item {
  display: flex;
}
.info-title {
  flex-shrink: 0;
  width: 65px;
}
.scale {
  position: absolute;
  bottom: 10px;
  right: 10px;
}
.scale-item {
  margin-top: 5px;
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  border: 1px solid #999;
  background-color: #fff;
}
.distance {
  color: var(--theme-color);
}
.alarm {
  position: absolute;
  top: 100px;
  left: 20px;
  width: 360px;
  height: 435px;
  padding-left: 5px;
  background: rgba(255, 255, 255, 0.6);
}
.alarm-list {
  overflow-y: auto;
  height: 380px;
}
.alarm-item {
  display: flex;
  font-size: 13px;
  line-height: 20px;
}
.alarm-time {
  width: 95px;
}
.alarm-level {
  margin-left: 10px;
  width: 80px;
  color: rgb(245, 108, 108);
}
.alarm-text {
  color: rgb(246, 160, 24);
}
.alarm-empty {
  padding-top: 20px;
  text-align: center;
  font-size: 14px;
  color: rgba(136, 136, 136, 0.898);
}
::v-deep .info-window-all{
    background-color: #fff;
    font-size: 14px;
    width: 300px;
    padding: 10px 10px 10px 0px;
    position: relative;
    // top: -90px;
    // top: -110px;
    top: -128px;
    left: -134px;
    border:1px solid #cccccc;
    border-radius: 5px;
    .info-item{
      line-height: 24px;
      margin: 0px;
    }
   .info-content{
      text-align: left;
    }
}
/* 主三角形 */
::v-deep .info-window-all::after {
    content: "";
    position: absolute;
    top: 100%; /* 让三角位于 div 底部 */
    left: 50%;
    transform: translateX(-50%); /* 水平居中 */
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #fff; /* 倒三角颜色与 div 背景色一致 */
    z-index: 2;
}
/* 模拟边框的三角形 */
::v-deep .info-window-all::before {
    content: "";
    position: absolute;
    top: calc(100% + 1px); /* 往下偏移 1px 以显示边框效果 */
    left: 50%;
    transform: translateX(-50%); /* 水平居中 */
    border-left: 11px solid transparent;
    border-right: 11px solid transparent;
    border-top: 11px solid #cccccc; /* 边框颜色 */
    z-index: 1;
}
::v-deep .position2{
  // top: -110px;
  // top: -122px;
  top: -165px;
  left: -142px;
}
.record-tab{
  position: absolute;
  width:350px;
  bottom: 30px;
  left: 20px;
  height: 350px;
  background: #fff;
  font-size: 14px;
  color: #333;
  padding: 5px;
  border-radius: 10px;
  ::v-deep .el-tabs__item{
    padding: 0 10px;
  }
  ::v-deep .el-tabs__nav-wrap:after{
    height: 0 !important;
  }
  &-i{
    font-size: 12px;
    position: absolute;
    right: 5px;
    top: 19px;
  }
  &-content-all{
    height: 280px;
    overflow-y: auto;
  }
  &-content{
    display: flex;
    justify-content: flex-start;
    margin-bottom: 10px;
    &-left{
      width: 35px;
      text-align: center;
      position: relative;
      img{
   
      }
    }
    &-right{
      div{
        margin-bottom: 10px;
        max-width: 315px;
      }
    }
  }
  img{
    width: 18px;
    height: 18px;
  }
}
.text-centent{
  display: flex;
  justify-content: center;
  align-items: center;
}
.tag-class{
  color: #cf2f2f;
  margin-right: 10px;
  position: relative;
  top: -2px;
}
.info-width{
  width: 300px!important;
}
</style>
<style>
.info-window-icon {
  position: absolute;
  right: 0;
  padding: 5px;
  top: 0px;
  cursor: pointer;
  
}
.info-window-icon i {
  font-size: 20px;
  color: #999;
}
.info-item {
  display: flex;
}
.info-window-title {
  flex-shrink: 0;
  width: 65px;
}
.tag-window-tag{
  height: 20px;
  padding: 0 5px;
  line-height: 19px;
  background-color: #fcd5d0;
  display: inline-block;
  border: 1px solid #cf2f2f;
  border-radius: 4px;
  box-sizing: border-box;
  white-space: nowrap;
  color: #cf2f2f;
}
.tag-window-tag-contain{
  display: flex;
  justify-content: flex-start;
  padding-left: 20px;
  margin-bottom: 5px;
}
.classTop{
  top: -152px !important;
}
.classTop2{
  top: -177px !important;
}
</style>