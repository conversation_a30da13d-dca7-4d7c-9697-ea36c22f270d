import { shapeTypes } from './FenceConfiguratorTools'
function createShape(type, data = {}, pointType, map) {
  let shape
  let center = data.center
  let style = pointType === 'start' ? {
    strokeColor: 'green',
    strokeWeight: 1,
    fillColor: 'green',
    fillOpacity: 0.3,
    strokeStyle: 'solid',
    bubble: true
  } : {
    strokeColor: 'green',
    strokeWeight: 1,
    fillColor: 'red',
    fillOpacity: 0.3,
    strokeStyle: 'solid',
    bubble: true
  }

  switch (type) {
    case shapeTypes.circle:
      shape = new AMap.Circle({
        center: data.center || center,
        radius: data.radius,
        ...style
      })
      break
    case shapeTypes.rectangle:
      let southWest, northEast
      if (data.paths) {
        southWest = new AMap.LngLat(data.paths[3][0], data.paths[3][1])
        northEast = new AMap.LngLat(data.paths[1][0], data.paths[1][1])
      } else {
        southWest = new AMap.LngLat(center[0] - 0.01, center[1] + 0.01)
        northEast = new AMap.LngLat(center[0] + 0.01, center[1] - 0.01)
      }
      let bounds = new AMap.Bounds(southWest, northEast)
      shape = new AMap.Rectangle({
        bounds,
        ...style
      })
      break
    case shapeTypes.polygon:
      let polygonCoordinates
      if (data.paths) {
        polygonCoordinates = data.paths.map(v => {
          return new AMap.LngLat(v[0], v[1])
        })
      } else {
        polygonCoordinates = [
          new AMap.LngLat(center[0] - 0.01, center[1] + 0.01),
          new AMap.LngLat(center[0] + 0.01, center[1] + 0.01),
          new AMap.LngLat(center[0] + 0.017, center[1]),
          new AMap.LngLat(center[0] + 0.01, center[1] - 0.01),
          new AMap.LngLat(center[0] - 0.01, center[1] - 0.01),
          new AMap.LngLat(center[0] - 0.017, center[1])
        ]
      }
      shape = new AMap.Polygon({
        path: polygonCoordinates,
        ...style
      })
  }
  shape.setMap(map)
  return shape
}
export const paintShape = async (map, data) => {
  [data.deliveryGpsFence, data.receiveGpsFence].map((v, i) => {
    let fenceData = v
    if (fenceData.centerLng === null || fenceData.centerLat === null) return
    let shapeType = fenceData.fenceShape
    let data = {}
    if (shapeType === shapeTypes.circle) {
      data.center = [fenceData.centerLng, fenceData.centerLat]
      data.radius = fenceData.radius
    } else {
      data.paths = JSON.parse(fenceData.points).map(v => [v.lng, v.lat])
    }
    let pointType = i === 0 ? 'start' : 'end'
    createShape(shapeType, data, pointType, map)
  })
}