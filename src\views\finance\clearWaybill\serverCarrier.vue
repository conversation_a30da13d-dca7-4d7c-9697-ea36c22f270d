<template>
  <div class="app-container">
    <el-form
      ref="searchForm"
      :model="search"
      :inline="true"
      class="search"
      size="mini"
    >
      <el-form-item label="上报时间:">
        <el-date-picker
          v-model="uploadDate"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="getbillTimeVal"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" @click="doSearch" type="primary">查询</el-button>
        <el-button size="mini" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="wrapper">
      <el-table
        v-if="isToB=='0'"
        :data="data"
        class="table"
        style="width: 100%"
        :cell-style="{ 'text-align': 'center', border: '0.5px solid #EAF0FB' }"
        :header-cell-style="{
          'text-align': 'center',
          border: '0.3px solid #EAF0FB',
          'background-color': '#F5F6F9',
          height: '60px',
        }"
        border
      >
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column label="服务方ID" prop="fwfuuid" width="100"></el-table-column>
        <el-table-column label="服务方姓名" prop="fwfxm" width="150">
        </el-table-column>        
        <el-table-column label="身份证号" prop="fwfsfzjhm" width="170"></el-table-column>
        <el-table-column label="手机号" prop="yddh" width="110"></el-table-column>
        <el-table-column label="驾驶证号码" prop="jszjhm"></el-table-column>
        <el-table-column label="行驶证档案编号" prop="xszjhm"></el-table-column>
        <el-table-column label="承运车牌号" prop="cycph" width="120">
          <template slot-scope="scope">
            <el-button type="text">{{scope.row.cycph}}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="承运车型" prop="cycx"></el-table-column>
        <el-table-column label="承运载重（吨）" prop="cyzz"></el-table-column>
        <el-table-column label="上报时间" prop="createTime"></el-table-column>
        <el-table-column label="操作" prop="">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="openDetailPage(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-table
        v-if="isToB=='1'"
        :data="data"
        class="table"
        style="width: 100%"
        :cell-style="{ 'text-align': 'center', border: '0.5px solid #EAF0FB' }"
        :header-cell-style="{
          'text-align': 'center',
          border: '0.3px solid #EAF0FB',
          'background-color': '#F5F6F9',
          height: '60px',
        }"
        border
      >
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column label="企业ID" prop="fwfuuid" width="100"></el-table-column>
        <el-table-column label="企业手机号" prop="yddh" width="110"></el-table-column>
        <el-table-column label="企业纳税人识别号" prop="nsrsbh" width="150">
        </el-table-column>
        <el-table-column label="企业名称（纳税人名称）" prop="nsrmc" width="150">
        </el-table-column>
        <el-table-column label="公司地址" prop="gsdz" width="150">
        </el-table-column>   
        <el-table-column label="企业注册时间" prop="ptzcsj" width="150">
        </el-table-column>    
        <el-table-column label="营业执照" prop="">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="openImage(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
        <el-table-column label="上报时间" prop="createTime"></el-table-column>
      </el-table>
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :total="total"
        :current-page.sync="page.pageNumber"
        @current-change="getList"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
    <el-image-viewer
      v-if="showedPic"
      :on-close="closePic"
      :url-list="showedPic"/>
  </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  components: { ElImageViewer },
  props: {
    // 传参
    // 是否为to b 端
    isToB: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      page: {
        pageSize: 10,
        pageNumber: 1,
      },
      isSearching: false,
      search: {
        driverName: '',
        driverPhone: '',
        plateNumber: '',
        startTime: '',
        endTime: ''
      },
      uploadDate: "",
      data: [],
      total: 0,
      showedPic: null
    };
  },
  // activated() {
  //   this.getList();
  // },
  created() {
    this.getList();
  },
  methods: {
    openImage(row){
      this.showedPic = row.zpfj
    },
    getbillTimeVal(val) {
      if (val) {
        this.search.startTime = val[0];
        this.search.endTime = val[1];
      } else {
        this.search.startTime = "";
        this.search.endTime = "";
      }
    },
    getList() {
      let params = {
        ...this.page,
        tab: "0",
        isToB: this.isToB,
      };
      if (this.isSearching) {
        Object.assign(params, this.search);
      }
      if (this.uploadDate) {
        params.startTime = this.uploadDate[0]
        params.endTime = this.uploadDate[1]
      }
      this.$post("/admin-center-server/tax/getFwfPage", params)
        .then((res) => {
          this.data = res.list;
          this.total = Number(res.total);
        })
        .catch(() => {
          this.data = [];
          this.total = 0;
        });
    },
    closePic() {
      this.showedPic = null
    },
    openDetailPage(row) {
      if(this.isToB=='0'){
        this.$router.push({
              path: 'serverCarrierDetail',
              query: {
                id: row.id,
                isToB: this.isToB
              }
            })
      }else{
        this.$router.push({path: 'serverCarrierDetail2B',query: {id: row.id,isToB: this.isToB}})
      }
    },
    handleSizeChange(v) {
      this.page.pageSize = v;
      this.getList();
    },
    doSearch() {
      this.isSearching = true;
      this.page.pageNumber = 1;
      this.getList();
    },
    reset() {
      this.isSearching = false;
      this.$refs.searchForm.resetFields();
      this.page.pageNumber = 1;
      this.uploadDate = ''
      this.search = {
        driverName: '',
        driverPhone: '',
        plateNumber: '',
        startTime: '',
        endTime: ''
      }
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
.search {
  padding: 20px 20px 10px 20px;
  background: #fff;
}
.wrapper {
  margin-top: 10px;
  background: #fff;
  padding: 20px 10px 10px 10px;
}
.el-pagination {
  margin: 10px 0;
  text-align: right;
}
</style>