<template>
    <div class="app-container">

        <div v-html="sysMessage">

        </div>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                sysMessage: '',
            }
        },
        methods: {
            getDetail() {
                let id = sessionStorage.getItem('cur_new_detail')
                this.$http.get('/admin-center-server/sys/getSysMessageInfo', {
                    params: {
                        id: id
                    }
                }).then(res => {
                    let data = res.data;

                    if(data.code==='200'){
                        let resData = data.data
                        let sysMessage = resData.sysMessage
                        this.sysMessage = sysMessage
                    }
                })
            }
        },
        mounted() {
            this.getDetail()
        },
    }
</script>

<style lang="scss" scoped>

</style>
