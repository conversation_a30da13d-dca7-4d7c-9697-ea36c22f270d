<template>
  <div class="app-container userWallet">
    <div class="base">
      当前平台主体：{{ currentBase.baseName }}
      <i @click="changeBase" class="el-icon-sort"></i>
    </div>
    <div class="info">
      <div class="info-head">
        <div class="title"></div>
        <div class="info-refresh" @click="refreshAllCount">
          <i
            class="el-icon-refresh-right"
            :class="{ refreshing: isRefreshing }"
          ></i>
          刷新
        </div>
      </div>
      <div class="info-wrapper">
        <div class="wallet-titleInfo">
          <div class="infoItem">
            <div class="item-img">
              <div>平台</div>
              <div>余额</div>
            </div>
            <div class="item-right">
              <div>{{ totalBalance }}</div>
            </div>
          </div>
          <div class="infoItem">
            <div class="item-img">
              <div>冻结</div>
              <div>金额</div>
            </div>
            <div class="item-right">
              <div>{{ guaranteeBalance }}</div>
            </div>
          </div>
          <div class="infoItem" style="border: none">
            <div class="item-img">
              <div>平台</div>
              <div>收益</div>
            </div>
            <div class="item-right">
              <div>{{ revenueBalance }}</div>
            </div>
          </div>

          <div class="platform-income" style="margin-left: -3%">
            <div class="income-title">其中油费账户</div>
            <div>{{ oilAmout }}</div>
            <div class="income-button" @click="showWithdraw(1)">提现</div>
          </div>
          <div class="platform-income">
            <div class="income-title">其中服务费账户</div>
            <div>{{ serviceCharge }}</div>
            <div class="income-button" @click="showWithdraw(2)">提现</div>
          </div>
          <div class="platform-income">
            <div class="income-title">其中垫款账户</div>
            <div>{{ makeAdvances }}</div>
            <div class="income-button" @click="showWithdraw(3)">提现</div>
          </div>
          <div class="platform-income">
            <div class="income-title">其中运费差额账户</div>
            <div>{{ freightDifference }}</div>
            <div class="income-button" @click="showWithdraw(4)">提现</div>
          </div>
        </div>
      </div>
    </div>
    <div class="list-box">
      <div class="select-info">
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          label-width="120px"
          size="mini"
        >
          <!-- <el-form-item label="银行交易流水号">
                        <el-input v-model="formInline.bankTransactionSn" placeholder="请输入银行交易流水号"></el-input>
                    </el-form-item> -->
          <el-form-item label="平台交易流水号">
            <el-input
              v-model="formInline.transactionSn"
              placeholder="请输入平台交易流水号"
            ></el-input>
          </el-form-item>
          <el-form-item label="提现类型">
            <el-select
              v-model="formInline.subType"
              @visible-change="handleTypeSelectorClick"
            >
              <el-option
                v-for="item in typeList"
                :value="item.value"
                :label="item.comment"
                :key="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="操作账号">
            <el-input
              v-model="formInline.nickName"
              placeholder="请输入操作账号"
            ></el-input>
          </el-form-item>
          <el-form-item label="操作人">
            <el-input
              v-model="formInline.realName"
              placeholder="请输入操作人"
            ></el-input>
          </el-form-item>
          <el-form-item label="交易时间">
            <el-date-picker
              v-model="searchDate"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="onSubmit"
              >查询</el-button
            >
            <el-button icon="el-icon-delete" type="danger" @click="clearForm"
              >清空筛选</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <div style="text-align: right">
        <el-dropdown trigger="click">
          <el-button type="primary">导出</el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <div @click="exportFile" type="text">导出Excel</div>
            </el-dropdown-item>
            <el-dropdown-item>
              <div @click="exportPdf" type="text">导出PDF电子回单</div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="list-main">
        <template>
          <el-table
            :data="tableData"
            border
            style="width: 100%"
            height="930px"
            cell-class-name="table_cell_gray"
            header-cell-class-name="table_header_cell_gray"
            @selection-change="handleSelectionChange"
          >
            <!-- <el-table-column type="selection" width="50"></el-table-column> -->
            <!-- <el-table-column prop="bankTransactionSn" label="银行交易流水号" width="120"></el-table-column> -->
            <el-table-column
              prop="transactionSn"
              label="平台交易流水号"
            ></el-table-column>
            <el-table-column prop="realName" label="操作人"></el-table-column>
            <el-table-column prop="nickName" label="操作账号"></el-table-column>
            <el-table-column
              prop="amount"
              label="提现金额（元）"
            ></el-table-column>
            <el-table-column prop="remark" label="备注"></el-table-column>
            <el-table-column label="提现类型">
              <template slot-scope="scope">
                {{ getSubType(scope.row.subType) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="optionTime"
              label="交易时间"
              width="180"
            ></el-table-column>
          </el-table>
        </template>
      </div>
      <div class="paging">
        <div class="block">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="isWithdrawShow"
      :title="withdrawType === 1 ? '油款提现' : '运费差额提现'"
      width="600px"
      @close="handleBeforeWithdrawClose"
    >
      <el-form
        label-width="120px"
        style="width: 400px"
        :model="withdrawForm"
        :rules="withdrawRules"
        ref="withdrawForm"
      >
        <el-form-item prop="bankId" label="提现至银行卡">
          <el-radio-group v-model="withdrawForm.bankId" class="widthdraw-card">
            <el-radio
              v-for="item in cardList"
              :key="item.bankId"
              :label="item.bankId"
              >{{ `${item.openingBank}（${item.accountNumber}）` }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="withdrawalAmount" label="提现金额">
          <el-input
            v-model="withdrawForm.withdrawalAmount"
            placeholder="请输入提现金额"
          ></el-input>
        </el-form-item>
        <el-form-item prop="passWord" label="提现密码">
          <el-input
            v-model="withdrawForm.passWord"
            type="password"
            auto-complete="new-password"
            placeholder="请输入提现密码"
            :readonly="isPasswordReadonly"
          ></el-input>
        </el-form-item>
        <el-form-item prop="remark" label="备注">
          <el-input
            v-model="withdrawForm.remark"
            type="textarea"
            placeholder="请输入备注，50字以内"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            @click="confirmWithdraw"
            :loading="isWidthdrawLoading"
            type="primary"
            >确定</el-button
          >
          <el-button @click="isWithdrawShow = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { checkLess3Month } from "@/utils/date";
export default {
  data() {
    return {
      title: "提现",
      showSet: true,
      setPwd: false,
      changePwd: false,
      hasPwd: false,
      gridData: [],
      totalBalance: "",
      guaranteeBalance: "",
      revenueBalance: "",
      total: 1,
      totalInner: 1,
      pageSize: 20,
      currentPage: 1,
      currentPageInner: 1,
      pageSizeInner: 20,

      loading: false,
      formInline: {},
      innerForm: {
        cashType: "",
        type: "",
        transactionSn: "",
      },
      tableData: [],
      changeId: "",
      isRefreshing: false,
      oilAmout: "",
      serviceCharge: "",
      makeAdvances: "",
      freightDifference: "",
      withdrawType: null,
      isWithdrawShow: false,
      cardList: [],
      withdrawForm: {},
      isPasswordReadonly: true,
      searchDate: [],
      withdrawRules: {
        bankId: [{ required: true, message: "不能为空", trigger: "change" }],
        withdrawalAmount: [
          { required: true, message: "不能为空", trigger: "blur" },
          {
            validator: (rule, value, cb) => {
              value = Number(value);
              if (Number.isNaN(value) || value <= 0) {
                cb("请输入正整数数字");
              }
              cb();
            },
            trigger: "blur",
          },
        ],
        passWord: [{ required: true, message: "不能为空", trigger: "blur" }],
        remark: [
          {
            validator: (rule, value, cb) => {
              if (typeof value === "string" && value.length > 50) {
                cb("长度不超过50");
              }
              cb();
            },
            trigger: "blur",
          },
        ],
      },
      selectedItems: [],
      typeList: [],
      isWidthdrawLoading: false,
      exportData: {},
      currentBase: {},
    };
  },
  methods: {
    tabUserType(row) {
      if (row.userType === "1") {
        return "客户";
      } else if (row.userType === "2") {
        return "调度员";
      } else if (row.userType === "3") {
        return "司机(包括车队长)";
      }
    },
    walletStatus(row) {
      if (row.status === "1") {
        return "正常";
      } else if (row.status === "2") {
        return "冻结";
      } else if (row.status === "3") {
        return "禁止提现";
      }
    },
    onSubmit() {
      this.currentPage = 1;
      this.getDataList();
    },
    /** 清空筛选 **/
    clearForm() {
      this.formInline = {};
      this.searchDate = [];
      this.getDataList();
    },
    /** 分页方法 **/
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.getDataList();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDataList();
    },
    getDataList() {
      let params = {
        pageNumber: this.currentPage,
        pageSize: this.pageSize,
        userSn: this.currentBase.platformUserSn,
        baseId: this.currentBase.id,
        ...this.formInline,
      };
      if (this.searchDate && this.searchDate.length !== 0) {
        params.startTime = this.searchDate[0];
        params.endTime = this.searchDate[1];
      }
      this.$get("/admin-center-server/finance/record/list", params).then(
        (res) => {
          this.exportData = {
            ...this.formInline,
            startTime:
              this.searchDate && this.searchDate.length !== 0
                ? this.searchDate[0]
                : "",
            endTime:
              this.searchDate && this.searchDate.length !== 0
                ? this.searchDate[1]
                : "",
          };
          this.tableData = res.list;
          this.total = Number(res.total);
        }
      );
    },

    getCountInfo() {
      return this.$http
        .get(
          "/admin-center-server/finance/getPlatformFinance?platformUserSn=" +
            this.currentBase.platformUserSn +
            "&baseId=" +
            this.currentBase.id
        )
        .then((res) => {
          let data = res.data;
          if (data.code === "200") {
            let totalBalance = data.data.totalBalance;
            let guaranteeBalance = data.data.guaranteeBalance;
            let revenueBalance = data.data.revenueBalance;
            if (totalBalance === null) {
              this.totalBalance = "0";
            } else {
              this.totalBalance = totalBalance;
            }
            if (guaranteeBalance === null) {
              this.guaranteeBalance = "0";
            } else {
              this.guaranteeBalance = guaranteeBalance;
            }
            if (revenueBalance === null) {
              this.revenueBalance = "0";
            } else {
              this.revenueBalance = revenueBalance;
              // this.formCash.withdrawalAmount = revenueBalance
            }

            this.oilAmout = data.data.oilAmout || 0;
            this.serviceCharge = data.data.serviceCharge || 0;
            this.makeAdvances = data.data.makeAdvances || 0;
            this.freightDifference = data.data.freightDifference || 0;
          }
        });
    },
    refreshAllCount() {
      this.isRefreshing = true;
      this.getCountInfo().finally(() => {
        this.isRefreshing = false;
      });
    },
    showWithdraw(type) {
      this.$post("/admin-center-server/platFormWallet/platFormBankList", {
        subType: type,
        baseId: this.currentBase.id,
      }).then((res) => {
        this.cardList = res;
        const lastItem = res.find((item) => item.lastUse === true);
        if (lastItem) this.$set(this.withdrawForm, "bankId", lastItem.bankId);
      });
      this.withdrawType = type;
      this.isWithdrawShow = true;
      setTimeout(() => {
        this.isPasswordReadonly = false;
      }, 500);
    },
    confirmWithdraw() {
      this.$refs.withdrawForm.validate().then((valid) => {
        if (valid) {
          this.isWidthdrawLoading = true;
          this.$post("/admin-center-server/platFormWallet/platFormWithdrawal", {
            ...this.withdrawForm,
            subType: this.withdrawType,
            baseId: this.currentBase.id,
          })
            .then(() => {
              this.getDataList();
              this.getCountInfo();
              this.isWithdrawShow = false;
              this.$message.success("操作成功");
            })
            .finally(() => {
              this.isWidthdrawLoading = false;
            });
        }
      });
    },
    handleBeforeWithdrawClose() {
      this.withdrawForm = {};
      this.$refs.withdrawForm.resetFields();
    },
    exportCheck() {
      //日期校验
      if (
        !this.exportData.startTime ||
        !checkLess3Month(this.exportData.startTime, this.exportData.endTime)
      ) {
        this.$message.warning("请按照“交易时间”筛选数据，时间范围最长为3个月");
        return false;
      }
      // if (this.selectedItems.length === 0) {
      //     this.$message.warning('至少选择一条')
      //     return false
      // }
      return true;
    },
    exportFile() {
      if (!this.exportCheck()) {
        return;
      }

      this.$post(
        "/admin-center-server/finance/record/exportCashList",
        this.exportData
      ).then((res) => {
        this.$message.success(
          "正在导出，稍后您可在【导出任务】中查看导出进度及导出文件"
        );
      });
    },
    exportPdf() {
      if (!this.exportCheck()) {
        return;
      }

      this.$post(
        "/admin-center-server/finance/record/exportPdfReceipt",
        this.exportData
      ).then((res) => {
        this.$message.success(
          "正在导出，稍后您可在【导出任务】中查看导出进度及导出文件"
        );
      });
    },
    handleSelectionChange(v) {
      this.selectedItems = v;
    },
    getTypeList() {
      return this.$post(
        "/admin-center-server/transaction/flow/getcashValueDetailSubType"
      ).then((res) => {
        this.typeList = res;
      });
    },
    handleTypeSelectorClick(type) {
      if (!type) return;
      this.getTypeList();
    },
    getSubType(code) {
      let item = this.typeList.find((v) => v.value === code);
      if (item) return item.comment;
    },
    changeBase() {
      let baseInfo = this.baseInfo;
      let currentIndex = baseInfo.findIndex(
        (v) => v.id === this.currentBase.id
      );
      let toggleIndex = null;
      if (currentIndex === baseInfo.length - 1) {
        toggleIndex = 0;
      } else {
        toggleIndex = currentIndex + 1;
      }
      this.currentBase = baseInfo[toggleIndex];
      localStorage.setItem("UserCurrentBaseId", this.currentBase.id);
      this.getDataList();
      this.getCountInfo();
      setTimeout(() => {
        this.$message.success("切换成功");
      }, 1000);
    },
  },
  activated() {
    let baseInfo = (this.baseInfo = this.$store.state.user.baseInfo);
    let currentBaseId = localStorage.getItem("UserCurrentBaseId");
    if (currentBaseId) {
      this.currentBase = baseInfo.find((v) => v.id == currentBaseId);
    } else {
      this.currentBase = baseInfo.find((v) => v.defaultFlag);
      localStorage.setItem("UserCurrentBaseId", this.currentBase.id);
    }
    this.getTypeList().then(() => {
      this.getDataList();
    });
    this.getCountInfo();
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.base {
  margin-bottom: 10px;
  font-size: 14px;
  i {
    transform: rotate(90deg);
    color: #f6a018;
    cursor: pointer;
  }
}
.info {
  padding: 20px;
  background: #fff;
}
.info-head {
  display: flex;
  justify-content: space-between;
}
.info-refresh {
  cursor: pointer;
}
@keyframes refresh {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.refreshing {
  animation: refresh 1s linear infinite;
}
.info-wrapper {
  display: flex;
  margin-top: 35px;
}
.item-img {
  box-sizing: border-box;
  padding-top: 13px;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  color: #fff;
  line-height: 26px;
  text-align: center;
}
.infoItem:nth-child(1) .item-img {
  background: #3d63ed;
}
.infoItem:nth-child(2) .item-img {
  background: #f68318;
}
.infoItem:nth-child(3) .item-img {
  background: #13c49d;
}
.info-btns {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  .el-button {
    margin-left: 0;
    margin-bottom: 15px;
    color: #f6a018;
    background-color: #fff;
  }
}
.item-right {
  margin-top: 10px;
  font-weight: 500;
  div {
    color: #333;
    font-size: 19px;
    word-break: break-all;
  }
}
.platform-income {
  width: 10%;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  margin-left: 2%;
  border: 1px solid rgb(207, 204, 204);
  border-radius: 8px;

  font-size: 14px;

  .income-title {
    font-size: 14px;
    font-weight: 500;
  }
  .income-button {
    color: #f68318;
    font-size: 16px;
    cursor: pointer;
    &:hover {
      color: darken($color: #f6a018, $amount: 15);
    }
  }
}
.userWallet {
  .wallet-titleInfo {
    display: flex;
    flex-grow: 1;

    .infoItem {
      width: 23%;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: left;
      border-right: 1px solid #dcdfe6;
    }
  }

  .select-box {
    margin-top: 10px;
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }
  }
  .select-info {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 20px;
    overflow: hidden;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }

    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }

    .paging {
      margin-top: 10px;
      float: right;
    }
  }
}

.mask-box {
  width: 500px;
  height: 250px;
  padding-top: 20px;
}
.widthdraw-card {
  .el-radio {
    display: block;
    margin-top: 10px;
  }
}
</style>
