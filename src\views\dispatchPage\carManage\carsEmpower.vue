<template>
    <div class="app-container carsEmpower">
        <div class="select-box">
            <div class="top-title">
                <div>筛选查询</div>
                <div class="button">
                    <el-button
                            class="left"
                            icon="el-icon-refresh-right"
                            size="mini"
                            type="success"
                            @click="()=>{this.$router.go(0)}"
                    >刷新
                    </el-button>
                    <el-button class="left"
                               icon="el-icon-search"
                               size="mini"
                               type="primary"
                               @click="onSubmit">查询
                    </el-button>
                    <el-button class="left"
                               icon="el-icon-delete"
                               size="mini"
                               type="danger"
                               @click="resetSubmit">清空筛选
                    </el-button>
                </div>
            </div>
            <div class="select-info">
                <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="150px" size="mini">
                    <el-form-item label="车牌号:">
                        <el-input v-model="formInline.plateNumber" :οnkeyup="formInline.plateNumber=formInline.plateNumber.replace(/\s/g, '')" placeholder="请输入车牌号"></el-input>
                    </el-form-item>
                    <el-form-item label="驾驶授权状态:">
                        <el-select v-model="formInline.region" placeholder="不限">
                            <el-option label="不限" value=""></el-option>
                            <el-option label="授权审核中" value="0"></el-option>
                            <el-option label="认证成功" value="1"></el-option>
                            <el-option label="认证已驳回" value="2"></el-option>
                            <el-option label="认证过期" value="5"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="临期状态">
                        <el-select v-model="formInline.nearExpirationStatus">
                            <el-option label="不限" value=""></el-option>
                            <el-option value="1" label="即将到期"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="申请司机:">
                        <el-input v-model="formInline.drivingName" :οnkeyup="formInline.drivingName=formInline.drivingName.replace(/\s/g, '')" placeholder="请输入车队长名称"></el-input>
                    </el-form-item>
                    <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="是否优先通过:">
                        <el-select v-model="formInline.priority" placeholder="不限" style="width: 80px">
                            <el-option label="不限" value=""></el-option>
                            <el-option label="否" value="0"></el-option>
                            <el-option label="是" value="1"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="150px"
                         style="margin-top:20px" size="mini">
                    <el-form-item label="申请司机手机号:">
                        <el-input v-model="formInline.mobile" :οnkeyup="formInline.mobile=formInline.mobile.replace(/\s/g, '')" placeholder="请输入司机手机号"></el-input>
                    </el-form-item>
                    <el-form-item label="道路运输证是否异常">
                        <el-select v-model="formInline.shippingCertWarnFlag">
                            <el-option value="" label="不限"></el-option>
                            <el-option value="1" label="是"></el-option>
                            <el-option value="0" label="否"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="操作人名称:">
                        <el-input v-model="formInline.updateUserName" :οnkeyup="formInline.updateUserName=formInline.updateUserName.replace(/\s/g, '')" placeholder="请输入操作人名称" style="width: 195px"></el-input>
                    </el-form-item>
                    <el-form-item label="操作人账号:">
                        <el-input v-model="formInline.updateUserNick" :οnkeyup="formInline.updateUserNick=formInline.updateUserNick.replace(/\s/g, '')" placeholder="请输入操作人账号"></el-input>
                    </el-form-item>
                </el-form>
                <el-form :inline="true" :model="formInline"  class="noneSelectForm"
                         style="margin-top: 20px;margin-left: 50px" size="mini">
                    <el-select v-model="formInline.timeType" size="mini" style="width: 100px;">
                        <el-option label="创建日期" value="0"></el-option>
                        <el-option label="操作日期" value="1"></el-option>
                    </el-select>
                    <el-form-item label="">
                        <el-date-picker
                                :clearable="false"
                                @blur="selectTime"
                                v-model="formInline.date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetimerange"
                                :default-time="['00:00:00', '23:59:59']"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="所属车队长名称:">
                        <el-input v-model="formInline.boss" :οnkeyup="formInline.boss=formInline.boss.replace(/\s/g, '')" placeholder="请输入所属车队长名称"></el-input>
                    </el-form-item>
                    <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="所属车队长手机号:">
                        <el-input v-model="formInline.ownerMobile" :οnkeyup="formInline.ownerMobile=formInline.ownerMobile.replace(/\s/g, '')" placeholder="请输入所属车队长手机号"></el-input>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="list-box">
            <div class="list-title">
                <div>数据列表</div>
            </div>
            <div class="list-main">
                <template>
                    <el-table
                            :data="tableData"
                            border
                            :row-class-name="tableRowClassName"
                            style="width: 100%">
                        <el-table-column
                                prop="num"
                                label="序号"
                                type="index"
                                width="50">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="plateNumber"
                                label="车牌号"
                        >
                        </el-table-column>
                        <el-table-column label="车长车型" prop="carLengthName">
                            <template scope="scope">
                                {{(scope.row.carLength || '') + (scope.row.carLength ? '米' : '') + (scope.row.carTypeName || '')}}
                            </template>
                        </el-table-column>
                        <el-table-column label="车辆类型" prop="carModelName"></el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="driverName"
                                label="申请司机"
                                width="120">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="driverMobile"
                                label="申请司机手机号"
                                width="120">
                        </el-table-column>
                        <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag"
                                show-overflow-tooltip
                                prop="ownerName"
                                label="所属车队长"
                                width="120">
                        </el-table-column>
                        <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag"
                                show-overflow-tooltip
                                prop="ownerMobile"
                                label="车队长手机号"
                                width="140">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="authStatus"
                                label="驾驶权认证状态"
                                :formatter="jiashiStatus"
                                width="120">
                        </el-table-column>
                        <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag"
                                show-overflow-tooltip
                                prop="authStatus"
                                label="归属权认证状态"
                                :formatter="belongStatus"
                                width="120">
                        </el-table-column>
                        <el-table-column label="道路运输证异常">
                            <template slot-scope="scope">
                                {{ scope.row.shippingCertWarnFlag === '1' ? '是' : '否' }}
                            </template>
                        </el-table-column>
                        <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag"
                                show-overflow-tooltip
                                prop="priority"
                                label="是否优先通过"
                                :formatter="carPriority"
                                width="120">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="updateUserName"
                                label="操作人名称"
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="updateUserNick"
                                label="操作人账号"
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="updateDateTime"
                                label="操作日期"
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="createdDate"
                                label="创建日期"
                        >
                        </el-table-column>
                        <el-table-column
                                fixed="right"
                                label="操作"
                                width="100">
                            <template slot-scope="scope">
                                <el-button type="text" size="small" @click="goAttestation(scope.row)">审核</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </div>
            <div class="pagination" style="float: right;margin-top: 20px">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 40,50,100,200]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total=total>
                </el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'CarsList',
        data() {
            return {
                formInline: {
                    boss: '',
                    ownerMobile: '',
                    priority: '',
                    updateUserName: '',
                    updateUserNick: '',
                    timeType: '0',
                    plateNumber: '',
                    drivingName: '',
                    mobile: '',
                    date: '',
                    region: '0',
                    nearExpirationStatus: '',
                    datehandle:'',
                },
                tableData: [],
                currentPage: 1,
                pageSize: 20,
                total: 1,

                drivingName:'',
                startTime:'',
                endTime:'',
                mobile:'',
                plateNumber:'',
                status:'',
                type:'0',
                startTimehandle:"",
                endTimehandle:"",
            }
        },
        methods: {
            jiashiStatus(row){
                if (row.type === '1') return ''
                if(row.status==='0'){
                    return '审核中'
                }else if(row.status==='1'){
                    return '认证成功'
                }else if(row.status==='2'){
                    return '认证失败'
                }else if(row.status==='3'){
                    return '认证中'
                }else if(row.status==='4'){
                    return '未认证'
                }else if(row.status==='5'){
                    return '认证过期'
                }
            },
            belongStatus(row) {
                if (row.type === '0') return ''
                if (row.status === '0') {
                    return '认证中'
                } else if (row.status === '1') {
                    return '认证成功'
                } else if (row.status === '2') {
                    return '认证失败'
                } else if (row.status === '3') {
                    return '认证中'
                }
            },
            carPriority(row){
                if(row.priority==='0'){
                    return '否'
                }else if(row.priority==='1'){
                    return '是'
                }else if(row.priority==='2'){
                    return ''
                }
            },
            tableRowClassName ({ row, rowIndex }) {
                if (row.status === '3'||row.status === '0') {
                    return 'inAudit-row '; //失败 和未
                } else if (row.status === '2') {
                    return 'success-row'; //成功
                } else if (row.status === '1') {
                    return 'warning-row'; //认证中
                }
                return '';
            },

            onSubmit() {
                this.currentPage =1;
                this.getCarList();
            },
            // /** 根据车驾驶授权状态搜索 **/
            // authStatusChange(value) {
            //     this.formInline.authStatus = value
            // },
            goAttestation(row){
                let routerData = this.$router.resolve({
                    path:'/carsEmpower/carsEmpowerExamine',
                    query:{
                        carId:row.id,
                        time:new Date().getTime()
                    }
                })
                let auditWindow = window.open(routerData.href)
                this.openTimers = this.openTimers || []
                let openTimer = setInterval(() => {
                    if (auditWindow.closed) {
                    this.getCarList()
                    clearInterval(openTimer)
                    }
                }, 200)
                this.openTimers.push(openTimer)
            },
            /** 清空搜索选项 **/
            resetSubmit() {
                this.formInline={
                    boss: '',
                    ownerMobile: '',
                    priority: '',
                    plateNumber: '',
                    drivingName: '',
                    mobile: '',
                    date: '',
                    region: '',
                    nearExpirationStatus: '',
                    datehandle: '',
                    updateUserName: '',
                    updateUserNick: '',
                    timeType: '0',
                },
                this.startTime = ''
                this.endTime = ''
                  this.getCarList();
            },
            /** 根据时间态搜索 创建日期 **/
            selectTime() {
                if(this.formInline.date!==null){
                    let startTime = this.formInline.date[0];
                    let endTime = this.formInline.date[1];
                    this.startTime = startTime;
                    this.endTime = endTime
                }else {
                    this.formInline.date=[];
                }
            },
            /** 分页方法 **/
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage =1;
                this.getCarList()
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getCarList()
            },
            getCarList(){
                this.$http.get('/admin-center-server/car/driving/list',{
                    params:{
                        boss:this.formInline.boss,
                        ownerMobile:this.formInline.ownerMobile,
                        priority:this.formInline.priority,
                        plateNumber:this.formInline.plateNumber,
                        drivingName:this.formInline.drivingName,
                        timeType:this.formInline.timeType,
                        updateUserName:this.formInline.updateUserName,
                        updateUserNick:this.formInline.updateUserNick,
                        mobile:this.formInline.mobile,
                        shippingCertWarnFlag: this.formInline.shippingCertWarnFlag,
                        startTime:this.startTime,
                        endTime:this.endTime,

                        pageNumber: this.currentPage,
                        pageSize: this.pageSize,

                        status:this.formInline.region,
                        nearExpirationStatus: this.formInline.nearExpirationStatus,
                        type:'',
                    }
                }).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                        this.total = Number(data.data.total);
                        let tableData = data.data.list
                        tableData = tableData.map(v => {
                            if (v.type === '1') {
                                v.ownerName = v.driverName
                                v.ownerMobile = v.driverMobile
                                v.driverName = ''
                                v.driverMobile = ''
                            }
                            return v
                        })
                        this.tableData = tableData
                    }
                })
            },
        },
        activated() {
            this.getCarList();
        },
        deactivated() {
            this.openTimers&&this.openTimers.forEach(v => clearInterval(v))
        }
    }
</script>
<style>
.noneSelectForm .el-select .el-input__inner {
    font-size: 14px;
    color: #606266;
    font-weight: 700;
    border: none !important;
}
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
.el-form-item {
    margin-bottom: 10px;
}
.carsEmpower {
    .select-box {
        background-color: #ffffff;

        .top-title {
            font-size: 16px;
            font-weight: 700;
            height: 40px;
            line-height: 40px;
            padding-left: 10px;
            border-bottom: 1px solid #cccccc;
            display: flex;
            justify-content: space-between;

            .button {
                margin-right: 20px;
            }
        }

        .select-info {
            padding-top: 10px;
            /*padding-bottom: 30px;*/
        }
    }

    .list-box {
        overflow: hidden;
        background-color: #ffffff;
        margin-top: 20px;
        padding: 10px;

        .list-title {
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            div {
                height: 38px;
                line-height: 38px;
            }
        }

        .list-main {
            width: 100%;
            border: 1px solid #cccccc;
            margin-top: 10px;
        }
    }
}
</style>
