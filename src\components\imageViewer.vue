
<script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
export default {
  extends: ElImageViewer,
  methods: {
    prev() {

      if (this.isFirst) {
        this.$message.warning("已经是第一张了");
        return;
      }
      if (this.isFirst && !this.infinite) return;
      const len = this.urlList.length;
      this.index = (this.index - 1 + len) % len;
    },
    next() {
      if (this.isLast) {
        this.$message.warning("已经是最后一张了");
        return;
      }
      if (this.isLast && !this.infinite) return;
      const len = this.urlList.length;
      this.index = (this.index + 1) % len;
    },
  },

}
</script>

<style lang="scss" scoped>
</style>