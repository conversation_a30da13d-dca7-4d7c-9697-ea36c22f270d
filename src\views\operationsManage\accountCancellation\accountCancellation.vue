<template>
    <div class="app-container accountCancellation">
        <div class="select-box">
            <div class="top-title">
                <div>
                    筛选查询
                </div>
                <div>
                    <el-button icon="el-icon-search" type="primary" @click="onSubmit" size="mini">查询</el-button>
                    <el-button icon="el-icon-delete" type="danger" @click="clearForm" size="mini">清空筛选</el-button>
                </div>
            </div>
            <div class="select-info">
                <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="140px" size="mini">
                    <el-form-item label="司机名称:">
                        <el-input v-model="formInline.userName"
                                  placeholder="请输入司机名称"></el-input>
                    </el-form-item>
                    <el-form-item label="司机手机号:">
                        <el-input v-model="formInline.userMobile"
                                  :οnkeyup="formInline.userMobile=formInline.userMobile.replace(/[^\w\.\/]/ig,'')"
                                  placeholder="请输入司机手机号"></el-input>
                    </el-form-item>
                    <el-form-item label="司机身份证号:">
                        <el-input v-model="formInline.idCard"
                                  :οnkeyup="formInline.idCard=formInline.idCard.replace(/[^\w\.\/]/ig,'')"
                                  placeholder="请输入司机身份证号"></el-input>
                    </el-form-item>
                </el-form>
                <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="140px" size="mini">
                    <el-form-item label="操作人名称:">
                        <el-input placeholder="请输入操作人名称" v-model="formInline.operatorName">
                        </el-input>
                    </el-form-item>
                    <el-form-item label="数据来源:">
                        <el-select v-model="formInline.dataSource" placeholder="不限" style="width: 180px">
                            <el-option label="不限" value=""></el-option>
                            <el-option label="用户申请" value="0"></el-option>
                            <el-option label="中台手动" value="1"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="销户状态:">
                        <el-select v-model="formInline.closeStatus" placeholder="不限" style="width: 180px">
                            <el-option label="不限" value=""></el-option>
                            <el-option label="销户成功" value="0"></el-option>
                            <el-option label="审核中" value="1"></el-option>
                            <el-option label="销户处理中" value="2"></el-option>
                            <el-option label="销户失败" value="3"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="140px" size="mini">
                    <el-form-item label="操作日期:">
                        <el-date-picker
                                :clearable="false"
                                @blur="selectTime"
                                v-model="date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetimerange"
                                :default-time="['00:00:00', '00:00:00']"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="list-box">
            <div class="list-title">
                <div>数据列表</div>
                <div>
                    <el-button type="primary" size="mini" @click="accountfn">去销户</el-button>
                </div>
            </div>
            <div class="list-main">
                <template>
                    <el-table
                            :data="tableData"
                            border
                            style="width: 100%"
                    >
                        <el-table-column
                                type="index"
                                label="序号"
                                width="50">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="userName"
                                label="司机名称"
                                width="100"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="userMobile"
                                label="司机手机号"
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="idCard"
                                label="司机身份证"
                                width="200"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="flag"
                                label="销户凭证"
                                width="100px"
                        >
                            <template slot-scope="scope">
                                <span style="color: blue;cursor: pointer" @click="handleRowClick(scope.row)">查看图片</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                                prop="closeRemark"
                                show-overflow-tooltip
                                label="销户备注"
                                width="180"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="dataSourceEnum"
                                label="数据来源"
                                width="100">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="auditRemark"
                                label="审核备注"
                                width="120">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="closeStatusEnum"
                                label="销户状态"
                                width="120">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="operatorName"
                                label="操作人名称"
                                width="100">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="operatorAcctNo"
                                label="操作人账号"
                                width="180">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="operationTime"
                                label="操作时间"
                        >
                        </el-table-column>
                        <el-table-column
                                fixed="right"
                                label="操作"
                                width="80">
                            <template slot-scope="scope">
                                <el-button v-if="scope.row.closeStatus==='0'" @click="handleLook(scope.row)" type="text"
                                           size="small">查看
                                </el-button>
                                <el-button v-if="scope.row.closeStatus!=='0'" @click="examineFn(scope.row)"
                                           type="text" size="small">审核
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>

            </div>
            <div class="paging">
                <div class="block">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="pageNumber"
                            :page-sizes="[10, 20, 30, 40,50,100]"
                            :page-size="pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total">
                    </el-pagination>
                </div>
            </div>
        </div>

        <!-- 多张图片弹窗 -->
        <el-dialog title="图片详情" :visible.sync="dialogPhoto">
            <ul class="photoList">
                <li v-for="(item,index) in imgArr" :key="index">
                    <img :src="item" alt/>
                </li>
            </ul>
        </el-dialog>
        <!-- 查看销户弹窗-->
        <el-dialog
                title="提示"
                :visible.sync="dialogVisible"
                :before-close="handleClose">
            <div>
                <el-form ref="form" :model="form" label-width="120px">
                    <el-form-item label="司机名称:" required>
                        <el-input :disabled="true" v-model="form.userName" placeholder="请输入司机名称"
                                  style="width: 300px"></el-input>
                    </el-form-item>
                    <el-form-item label="司机手机号:" required>
                        <el-input :disabled="true" v-model="form.userMobile" placeholder="请输入司机手机号"
                                  style="width: 300px"></el-input>
                    </el-form-item>
                    <el-form-item label="司机身份证号:" required>
                        <el-input :disabled="true" v-model="form.idCard" placeholder="请输入司机身份证号"
                                  style="width: 300px"></el-input>
                    </el-form-item>
                    <el-form-item label="销户凭证:" required>
                        <div style="display:flex">
                            <ul class="pictures" v-if="form.images && form.images.length >0">
                                <li v-for="(item,index) in form.images" :key="index">
                                    <img :src="item" alt/>
                                </li>
                            </ul>
                            <span
                                    @click="pictureDetail"
                                    class="largePicture"
                            >预览</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="销户备注:">
                        <el-input :disabled="true" type="textarea" v-model="form.closeRemark" placeholder="请输入销户备注"
                                  style="width: 300px"></el-input>
                    </el-form-item>
                </el-form>
            </div>
        </el-dialog>
        <!--查看功能 预览图片-->
        <el-image-viewer
                :on-close="closeViewer"
                :url-list="form.images"
                style="z-index:9999"
                v-if="dialogImg"
        />
        <!-- 审核销户弹窗-->
        <el-dialog
                title="提示"
                :visible.sync="examineMask"
                :before-close="handleClose">
            <div>
                <el-form ref="form" :model="form" label-width="120px">
                    <el-form-item label="司机名称:" required>
                        <el-input :disabled="true" v-model="examineForm.userName" placeholder="请输入司机名称"
                                  style="width: 300px"></el-input>
                    </el-form-item>
                    <el-form-item label="司机手机号:" required>
                        <el-input :disabled="true" v-model="examineForm.userMobile" placeholder="请输入司机手机号"
                                  style="width: 300px"></el-input>
                    </el-form-item>
                    <el-form-item label="司机身份证号:" required>
                        <el-input :disabled="true" v-model="examineForm.idCard" placeholder="请输入司机身份证号"
                                  style="width: 300px"></el-input>
                    </el-form-item>
                    <el-form-item label="销户凭证:" required>
                        <div style="display:flex">
                            <ul class="pictures" v-if="examineForm.images && examineForm.images.length >0">
                                <li v-for="(item,index) in examineForm.images" :key="index">
                                    <img :src="item" alt/>
                                </li>
                            </ul>
                            <span
                                    @click="examineImg=true"
                                    class="largePicture"
                            >预览</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="销户备注:">
                        <el-input :disabled="true" type="textarea" v-model="examineForm.closeRemark"
                                  placeholder="请输入销户备注"
                                  style="width: 300px"></el-input>
                    </el-form-item>
                    <el-form-item label="审核备注:">
                        <el-input type="textarea" v-model="examineForm.auditRemark" placeholder="请输入审核备注"
                                  style="width: 300px"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" size="mini" @click="agreeExamine">同意销户
                        </el-button>
                        <el-button size="mini" @click="refuseExamine">拒 绝</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
  </span>
        </el-dialog>
        <!-- 同意销户弹窗-->
        <el-dialog
                title="提示"
                :visible.sync="agreeMask"
                width="30%"
                :before-close="handleClose1">
            <div>
                请确认以下销户信息，此操作为 <span style="color: red">不可逆</span>
                操作，请谨慎操作。司机姓名：{{examineForm.userName}}，手机号{{examineForm.userMobile}}.司机身份证号{{examineForm.idCard}}，请问您还要继续销户吗？
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button @click="agreeMask = false">取 消</el-button>
    <el-button type="primary" :loading="loading" @click="ascertainExamine">确 定</el-button>
  </span>
        </el-dialog>
        <!--审核功能 预览图片-->
        <el-image-viewer
                :on-close="closeViewer"
                :url-list="examineForm.images"
                style="z-index:9999"
                v-if="examineImg"
        />
    </div>
</template>
<script>
    import ElImageViewer from "cf-element-ui/packages/image/src/image-viewer";

    export default {
        components: {ElImageViewer},
        data() {
            return {
                examineId: '',
                imgArr: [],
                loading: false,
                agreeMask: false,
                dialogImg: false,
                examineImg: false,
                dialogPhoto: false,
                dialogVisible: false,
                examineMask: false,
                form: {
                    userName: '',
                    userMobile: '',
                    idCard: '',
                    closeRemark: '',
                    images: [],
                },
                examineForm: {
                    userName: '',
                    userMobile: '',
                    idCard: '',
                    closeRemark: '',
                    auditRemark: '',
                    images: [],
                },
                formInline: {
                    userName: '',
                    userMobile: '',
                    idCard: '',
                    operatorName: '',
                    dataSource: '',
                    closeStatus: '',
                    operationTimeStart: '',
                    operationTimeEnd: '',
                },
                tableData: [],
                total: 0,
                pageSize: 20,
                pageNumber: 1,
                date: [],
            }
        },
        methods: {
            /** 查看销户详情 **/
            handleLook(row) {
                this.form = {
                    userName: row.userName,
                    userMobile: row.userName,
                    idCard: row.idCard,
                    closeRemark: row.closeRemark,
                    images: row.images,
                };
                this.dialogVisible = true;
            },
            /* 查看图大图 */
            pictureDetail() {
                this.dialogImg = true;
            },
            closeViewer() {
                this.dialogImg = false;
                this.examineImg = false;
            },
            /** 审核行 **/
            examineFn(row) {
                this.examineForm = {
                    userName: row.userName,
                    userMobile: row.userMobile,
                    idCard: row.idCard,
                    auditRemark: row.auditRemark,
                    closeRemark: row.closeRemark,
                    images: row.images,
                };
                this.examineId = row.id;
                this.examineMask = true;
            },
            /** 同意销户 **/
            agreeExamine() {
                if (this.examineForm.auditRemark === '') {
                   this.$message.warning('请填写审核备注')
                }else {
                    this.agreeMask = true;
                }
            },
            /** 确定销户 **/
            ascertainExamine(){
                this.loading=true;
                this.$http.post('/admin-center-server/acctChange/acctChangeRequesAuditAgree?id='+this.examineId+'&auditRemark='+this.examineForm.auditRemark).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                        this.loading=false;
                        this.$message.success(data.message);
                        this.agreeMask = false;
                        this.examineMask = false;
                        this.getDataList()
                    }else {
                        this.loading=false;
                        this.$message.warning(data.message)
                    }
                })
            },
            /** 拒绝销户 **/
            refuseExamine(){
                if (this.examineForm.auditRemark === '') {
                    this.$message.warning('请填写审核备注')
                }else {
                    this.$http.post('/admin-center-server/acctChange/acctChangeRequesAuditRefuse?id='+this.examineId+'&auditRemark='+this.examineForm.auditRemark).then(res=>{
                        let data = res.data;
                        if(data.code==='200'){
                            this.$message.success(data.message);
                            this.examineMask = false;
                            this.getDataList()
                        }else {
                            this.$message.warning(data.message)
                        }
                    })
                }
            },
            /** 点击去销户 **/
            accountfn() {
                this.$router.push("accountPage");
            },
            /** 清空筛选 **/
            clearForm() {
                this.formInline = {
                    userName: '',
                    userMobile: '',
                    idCard: '',
                    operatorName: '',
                    dataSource: '',
                    closeStatus: '',
                    operationTimeStart: '',
                    operationTimeEnd: '',
                };
                this.date = [];
                this.getDataList()
            },
            /** 根据时间态搜索 **/
            selectTime() {
                if(this.date!==null){
                    this.formInline.operationTimeStart = this.date[0];
                    this.formInline.operationTimeEnd = this.date[1];
                }else {
                    this.date=[]
                }
            },
            /** 按条件查询 **/
            onSubmit() {
                this.pageNumber = 1
                this.getDataList()
            },
            handleSizeChange(val) {
                this.pageSize = val;
                this.pageNumber = 1;
                this.getDataList()
            },
            handleCurrentChange(val) {
                this.pageNumber = val;
                this.getDataList()
            },
            getDataList() {
                let postData = {
                    pageNumber: this.pageNumber,
                    pageSize: this.pageSize,
                    userName: this.formInline.userName,
                    userMobile: this.formInline.userMobile,
                    idCard: this.formInline.idCard,
                    operatorName: this.formInline.operatorName,
                    dataSource: this.formInline.dataSource,
                    closeStatus: this.formInline.closeStatus,
                    operationTimeStart: this.formInline.operationTimeStart,
                    operationTimeEnd: this.formInline.operationTimeEnd,
                };
                this.$http.post('/admin-center-server/acctChange/getAcctChangeRequesPager', postData).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.total = Number(data.data.total);
                        let resData = data.data.list;
                        resData.map((item, index) => {
                            let str = [];
                            str = item.images.split(',');
                            item.images = str;
                        });
                        this.tableData = resData;
                    }
                })
            },
            /** 表格 点击查看图片列 **/
            handleRowClick(row) {
                this.dialogPhoto = true;
                this.imgArr = row.images
            },
            handleClose() {
                this.dialogVisible = false;
                this.examineMask = false;
                this.dialogPhoto = false;
            },
            handleClose1() {
                this.agreeMask = false;
            },
        },
        activated() {
            this.getDataList()
        }
    }
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .accountCancellation {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                padding-right: 10px;
                border-bottom: 1px solid #cccccc;
                display: flex;
                flex-flow: row nowrap;
                justify-content: space-between;
            }

            .select-info {
                padding-top: 30px;
                padding-bottom: 30px;
            }
        }

        .list-box {

            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;
            overflow: hidden;

            .list-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                div {
                    height: 38px;
                    line-height: 38px;
                }
            }

            .list-main {
                width: 100%;
                border: 1px solid #cccccc;
                margin-top: 10px;
            }

            .paging {
                margin-top: 10px;
                float: right;
            }
        }

        .pictures {
            padding: 0;
            margin: 0;

            li {
                display: inline-block;

                img {
                    width: 100px;
                    height: 100px;
                    margin: 10px;
                }
            }
        }

        .photoList {
            li {
                display: inline-block;

                img {
                    padding: 10px;
                    background: #cfcfcf;
                    width: 200px;
                    margin: 20px;
                }
            }
        }

        .largePicture {
            margin-left: 5px;
            color: blue;
            cursor: pointer;
        }
    }
</style>
