<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">{{title}}</div>

    </div>
    <div class="list-box">
      <div class="list-title">
        <div>全部{{title}}</div>
      </div>
      <div class="list-main">
        <template>
          <el-table ref="multipleTable"
                    :data="tableData"
                    :height="tableHeight"
                    tooltip-effect="dark"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                    border>
            <el-table-column show-overflow-tooltip
                             v-for="item in tableLabel"
                             :label="item.label"
                             :width='item.width?item.width:""'>
              <template slot-scope="scope">
                <span style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
              </template>
            </el-table-column>

          </el-table>

          <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page="pageNumber"
                         :page-sizes="[10,20, 40, 60, 80,100]"
                         :page-size="pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total"
                         class="pagination"></el-pagination>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
const list = '/admin-center-server/commonUser/drivingDescNumberList'//司机列表
const drivingDescCarList = '/admin-center-server/commonUser/drivingDescCarList'//车辆详情
export default {
  name: "CarsList",
  data () {
    return {
      title: '',
      tableHeight: null, //表格的高度
      id: '',
      type: '',
      tableLabel: [],
      pageSize: 10,
      total: 0,
      pageNumber: 1,
      tableData: []
    };
  },
  methods: {
    onSubmit () {
      this.pageNumber = 1
      this.getData()
    },
    handleSizeChange (val) {
      this.pageNumber = 1
      this.pageSize = val
      this.getData()
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange (val) {
      this.pageNumber = val
      this.getData()
      console.log(`当前页: ${val}`);
    },
    getData () {
      //1车辆  2司机
      var url = ''
      if (this.type == 2) {
        this.title = '司机详情'
        this.tableLabel = [
          {
            prop: 'driverName',
            label: '司机名称',
            width: 200
          },
          {
            prop: 'useCarNumber',
            label: '当前驾驶车辆',
            width: 140
          },
          {
            prop: 'mobile',
            label: '司机手机'
          },
          {
            prop: 'jobStataus',
            label: '工作状态'
          },
          {
            prop: 'usableCar',
            label: '当前司机可驾驶车辆'
          }
        ]
        url = list + `?pageNumber=${this.pageNumber}&pageSize=${this.pageSize}&id=${this.id}`
        this.$http.post(url).then(res => {
          console.log(res.data.data.total)
          this.tableData = res.data.data.list
          this.total = Number(res.data.data.total)
        })
      } else {
        this.title = '车辆详情'
        this.tableLabel = [
          {
            prop: 'plateNumber',
            label: '车牌号',
            width: 200
          },
          {
            prop: 'driverName',
            label: '当前驾驶司机',
            width: 140
          },
          {
            prop: 'isFreeTxt',
            label: '使用状态'
          },
          {
            prop: 'payeeAgentTxt',
            label: '收款方'
          }
        ]
        url = drivingDescCarList + `?pageNumber=${this.pageNumber}&pageSize=${this.pageSize}&id=${this.id}&driverType=${this.driverType}`
        this.$http.post(url).then(res => {
          console.log(res.data.data.total)
          this.tableData = res.data.data.list
          this.total = Number(res.data.data.total)
        })
      }


    }
  },
  activated () {
    this.id = this.$route.query.id
    this.type = this.$route.query.type
    this.driverType = this.$route.query.driverType
    this.getData()
    this.tableHeight =
      window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 160;
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
