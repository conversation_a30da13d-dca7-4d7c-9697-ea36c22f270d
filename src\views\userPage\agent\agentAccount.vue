<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">账户查询</div>
      <div class="select-info">
        <el-form :inline="true"
                 :model="formInline"
                 ref="formInline"
                 class="demo-form-inline"
                 size="mini"
                 label-width="100px">
          <el-form-item label="调度员名称:"
                        prop='name'>
            <el-input style='width:180px'
                      v-model="formInline.name"
                      placeholder="调度员公司名称或个人姓名"></el-input>
          </el-form-item>
          <el-form-item label="手机号:"
                        prop='tel'>
            <el-input v-model="formInline.tel"
                      oninput="value=value.replace(/[^\d]/g,'')"
                      maxlength="11"
                      placeholder="请输入注册手机号"></el-input>
          </el-form-item>
          <el-form-item label="数据来源："
                        label-width="100px"
                        prop='dataSources'>
            <el-select v-model="formInline.dataSources"
                       placeholder="请选择"
                       clearable
                       label-width="10px">
              <el-option v-for="(item, lan) in statusWrap1"
                         :label="item.name"
                         :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="认证状态："
                        label-width="100px"
                        prop='authenticateStatus'>
            <el-select v-model="formInline.authenticateStatus"
                       placeholder="请选择认证状态"
                       clearable
                       label-width="10px">
              <el-option v-for="(item, lan) in statusWrap"
                         :label="item.name"
                         :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="注册时间："
                        label-width="100px"
                        prop='date'>
            <el-col>
              <el-date-picker v-model="formInline.date"
                              value-format=""
                              type="datetimerange"
                              range-separator="至"
                              start-placeholder="选择开始时间"
                              end-placeholder="选择结束时间"
                              :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button class="left"
                       icon="el-icon-search"
                       @click="onSubmit">查询</el-button>
            <el-button class="left"
                       @click="resetForm('formInline')"
                       icon="el-icon-refresh-right">重置</el-button>
            <el-button class="left"
                       @click="refreshfn"
                       icon="el-icon-refresh-right">刷新</el-button>
            <!-- <el-button class="left"
                       icon="el-icon-plus"
                       @click="addnewfn">添加用户</el-button> -->

          </el-form-item>
        </el-form>

      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>账户列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table ref="multipleTable"
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%"
                    :height="tableHeight"
                    @selection-change="handleSelectionChange"
                    border>
            <el-table-column type="selection"
                             width="55"></el-table-column>
            <el-table-column type="index"
                             label="序号"
                             width="55">
            </el-table-column>
            <el-table-column show-overflow-tooltip
                             v-for="item in tableLabel"
                             :label="item.label"
                             :width='item.width?item.width:""'>
              <template slot-scope="scope">
                <span style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
              </template>
            </el-table-column>

            <el-table-column fixed="right"
                             min-width="150"
                             label="操作">
              <template slot-scope="scope">
                <el-button @click="goDetail(scope.row)"
                           type="text"
                           size="small">查看</el-button>
                <el-button type="text"
                           @click="editfn(scope.row)"
                           size="small">编辑</el-button>
                <el-button type="text"
                           @click="resetPassword(scope.row)"
                           size="small">重置密码</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page="pageNumber"
                         :page-sizes="[10,20, 40, 60, 80,100]"
                         :page-size="pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total"
                         class="pagination"></el-pagination>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
const list = '/admin-center-server/commonUser/list'//列表
const updateUserPwd = '/admin-center-server/commonUser/updateUserPwd'//重置密码
export default {
  name: "CarsList",
  data () {
    return {
      tableHeight: null, //表格的高度
      tableLabel: [
        {
          prop: 'name',
          label: '调度员名称',
          width: 200
        },
        {
          prop: 'mobile',
          label: '手机号（注册）',
          width: 120
        },
        {
          prop: 'createTime',
          label: '注册时间',
          width: 180
        },
        {
          prop: 'inviteCode',
          label: '邀请码'
        },
        {
          prop: 'brokerAuthType',
          label: '认证类型'
        },
        {
          prop: 'brokerAuthStatusName',
          label: '认证状态'
        },
        {
          prop: 'deleteFlag',
          label: '账户状态'
        },
        {
          prop: 'dataFrom',
          label: '数据来源'
        }
      ],
      statusWrap1: [
        {
          name: '注册',
          id: '0'
        },
        {
          name: '手动添加',
          id: '1'
        },
      ],
      statusWrap: [
        {
          name: '未认证',
          id: '1'
        },
        {
          name: '已认证',
          id: '2'
        },
        {
          name: '认证驳回',
          id: '3'
        },
        {
          name: '审核中',
          id: '4'
        },
        {
          name: '认证资质过期',
          id: '5'
        },
        {
          name: '补充资料认证中',
          id: '6'
        },
        {
          name: '补充资料驳回',
          id: '7'
        }
      ],
      pageSize: 10,
      total: 0,
      pageNumber: 1,
      formInline: {
        name: "",
        tel: "",
        dataSources: '',
        date: '',
        authenticateStatus: ''
      },
      tableData: []
    };
  },
  methods: {
    // 刷新当前页
    refreshfn () {
      this.$router.go(0)
    },
    //重置密码
    resetPassword (row) {
      var that = this
      this.$confirm('是否确认重置该账户密码？重置之后该账户登录密码即为a12345678？', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.post(updateUserPwd + '?pwd=' + this.$md5('a12345678').toUpperCase() + '&userId=' + row.id).then(res => {
          this.$message({
            type: 'success',
            message: '操作成功'
          });
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消重置'
        });
      });
    },
    //重置
    resetForm (formName) {
      this.$refs[formName].resetFields();
      this.formInline.date = ''
      this.pageNumber = 1
      this.getData()
    },
    addnewfn () {
      this.$router.push("/consignorAccount/addUser?urlType=2");
    },
    onSubmit () {
      this.pageNumber = 1
      this.getData()
    },
    editfn (row) {
      //编辑页面的条件
      // if (row.brokerAuthType == '个人') {
      //   row.driverType = 'personal'
      // } else if (row.brokerAuthType == '公司') {
      //   row.driverType = 'company'
      // }
      if (row.brokerAuthType == '个人') {
        row.driverType = 'personal'
      } else {
        row.driverType = 'company'
      }
      this.$router.push("/consignorAccount/addShipper?urlType=2&id=" + row.id + '&driverType=' + row.driverType);
    },
    goDetail (row) {
      if (row.brokerAuthType == '个人') {
        row.driverType = '1'
      } else {
        row.driverType = '2'
      }
      this.$router.push("/agentAccount/accountDetails?id=" + row.id + '&type=' + row.driverType);
    },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },
    handleSizeChange (val) {
      this.pageNumber = 1
      this.pageSize = val
      this.getData()
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange (val) {
      this.pageNumber = val
      this.getData()
      console.log(`当前页: ${val}`);
    },
    getData () {
      var v = this.formInline,
        data1 = '',
        data2 = '';
      if (v.date) {
        data1 = this.getDate(v.date[0])
        data2 = this.getDate(v.date[1])
      } else {
        data1 = ''
        data2 = ''
      }
      //差个调度员名称
      v.name = this.Trim(v.name, 'g')
      var url = list + `?userType=2&flag=0&mobile=${v.tel}&brokerName=${v.name}&dataFrom=${v.dataSources}&brokerAuthStatus=${v.authenticateStatus}&pageNumber=${this.pageNumber}&pageSize=${this.pageSize}&minCreateTime=${data1}&maxCreateTime=${data2}`
      this.$http.get(url).then(res => {
        var data = res.data.data
        this.tableData = data.list
        this.total = Number(data.total)
      })
    }
  },
  activated () {
    this.getData()
    this.tableHeight =
      window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 160;
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
