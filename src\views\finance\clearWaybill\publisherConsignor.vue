<template>
  <div class="app-container">
    <el-form
      ref="searchForm"
      :model="search"
      :inline="true"
      class="search"
      size="mini"
    >
      <el-form-item label="上报时间:">
        <el-date-picker
          v-model="uploadDate"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          @change="getbillTimeVal"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" @click="doSearch" type="primary">查询</el-button>
        <el-button size="mini" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="wrapper">
      <el-table
        :data="data"
        class="table"
        style="width: 100%"
        :cell-style="{ 'text-align': 'center', border: '0.5px solid #EAF0FB' }"
        :header-cell-style="{
          'text-align': 'center',
          border: '0.3px solid #EAF0FB',
          'background-color': '#F5F6F9',
          height: '60px',
        }"
        border
      >
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column label="企业ID" prop="fbfuuid" show-overflow-tooltip></el-table-column>
        <el-table-column label="企业手机号" prop="qyyddh" show-overflow-tooltip></el-table-column>
        <el-table-column label="企业纳税人识别号" prop="nsrsbh" show-overflow-tooltip> </el-table-column>
        <el-table-column label="企业名称（纳税人名称）" prop="nsrmc" show-overflow-tooltip></el-table-column>
        <el-table-column label="公司地址" prop="gsdz" show-overflow-tooltip></el-table-column>
        <el-table-column label="法定代表人" prop="fddbr" v-if="isToB==1" show-overflow-tooltip></el-table-column>
        <el-table-column label="注册资本" prop="zczb" v-if="isToB==1" show-overflow-tooltip></el-table-column>
        <el-table-column label="成立日期" prop="slrq" v-if="isToB==1" show-overflow-tooltip></el-table-column>
        <el-table-column label="经营范围" prop="jyfw" v-if="isToB==1" show-overflow-tooltip></el-table-column>
        <el-table-column label="主管税务机关" prop="zgswjg" v-if="isToB==1" show-overflow-tooltip></el-table-column>
        <el-table-column label="企业类型" prop="nsrlx" v-if="isToB==1">
          <template slot-scope="scope">
            <span v-if="scope.row.nsrlx == 0">一般人</span>
            <span v-if="scope.row.nsrlx == 1">小规模</span>
          </template>
        </el-table-column>
        <el-table-column label="企业注册时间" prop="ptzcsj" show-overflow-tooltip></el-table-column>
        <el-table-column label="企业营业执照照片" prop="">
          <template slot-scope="scope">
            <el-button type="text" @click="showImageUrl = scope.row.url">查看</el-button>
          </template>
        </el-table-column>
        <el-table-column label="上报时间" prop="createTime"></el-table-column>
      </el-table>
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :total="total"
        :current-page.sync="page.pageNumber"
        @current-change="getList"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
    <el-image-viewer style="z-index: 10000" v-if="showImageUrl" :on-close="closeOilViewer" :url-list="[showImageUrl]"></el-image-viewer>
  </div>
</template>

<script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer";

export default {
  components: { ElImageViewer },
  props: {
    // 传参
    // 是否为to b 端
    isToB: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      page: {
        pageSize: 10,
        pageNumber: 1,
      },
      isSearching: false,
      search: {
        name: "",
        startTime: '',
        endTime: '',
      },
      uploadDate: "",
      data: [],
      total: 0,
      showImageUrl: ''
    };
  },
  // activated() {
  //   this.getList();
  // },
  created() {
    this.getList();
  },
  methods: {
    getbillTimeVal(val) {
      if (val) {
        this.search.startTime = val[0];
        this.search.endTime = val[1];
      } else {
        this.search.startTime = "";
        this.search.endTime = "";
      }
    },
    getList() {
      let params = {
        ...this.page,
        tab: "0",
        isToB: this.isToB,
      };
      if (this.isSearching) {
        Object.assign(params, this.search);
      }
      if (this.uploadDate) {
        params.startTime = this.uploadDate[0]
        params.endTime = this.uploadDate[1]
      }
      this.$post("/admin-center-server/tax/getFbfPage", params)
        .then((res) => {
          this.data = res.list;
          this.total = Number(res.total);
        })
        .catch(() => {
          this.data = [];
          this.total = 0;
        });
    },
    handleSizeChange(v) {
      this.page.pageSize = v;
      this.getList();
    },
    doSearch() {
      this.isSearching = true;
      this.page.pageNumber = 1;
      this.getList();
    },
    reset() {
      this.isSearching = false;
      this.$refs.searchForm.resetFields();
      this.uploadDate = ''
      this.search = {
        name: '',
        startTime: '',
        endTime: ''
      }
      this.page.pageNumber = 1;
      this.getList();
    },
    closeOilViewer() {
      this.showImageUrl = ""
    },
  },
};
</script>

<style scoped lang="scss">
.search {
  padding: 20px 20px 10px 20px;
  background: #fff;
}
.wrapper {
  margin-top: 10px;
  background: #fff;
  padding: 20px 10px 10px 10px;
}
.el-pagination {
  margin: 10px 0;
  text-align: right;
}
</style>