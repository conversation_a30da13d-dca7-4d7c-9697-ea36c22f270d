<template>
  <div class="invoice">
    <el-button @click="$router.push('/finance/invoice/invoice')" class="back-btn">返回</el-button>
    <FullElecInvoice v-if="info.billType === '2'" :info="info" :data="data" :invoiceInfoList="invoiceInfoList" :isSimplification="isSimplification" :originData="originData" type="detail"></FullElecInvoice>
    <template v-else>
      <div class="headline">
        <div class="headline-outer">
          <div class="headline-inner">
            <template v-if="info.billType === '0'">天津增值税电子专用发票</template>
            <template v-else-if="info.billType === '1'">天津增值税专用发票</template>
          </div>
        </div>
      </div>
      <div class="main">
        <table class="table1">
          <tr>
            <td>
              <div class="title vertical-title">购买方</div>
            </td>
            <td>
              <div class="table1-row1">
                <div><span class="title">名称</span>： {{ info.buyName }}</div>
                <div><span class="title">纳税人识别号</span>： {{ info.buyDutyNum }}</div>
                <div><span class="title">地址、电话</span>： {{ info.buyAddressPhone }}</div>
                <div><span class="title">开户行及账号</span>： {{ info.buyBankAccount }}</div>
              </div>
            </td>
            <td>
              <div class="title vertical-title">密码区</div>
            </td>
            <td>
              <div class="table1-row2"></div>
            </td>
          </tr>
        </table>
        <table class="table2">
          <tr class="list-row">
            <th class="vertical-td">
              <div class="title vertical-title">
                <input type="checkbox">
              </div>
            </th>
            <th>
              <div class="title">货物或应税劳务名称</div>
            </th>
            <th>
              <div class="title">规格型号</div>
            </th>
            <th>
              <div class="title">单位</div>
            </th>
            <th>
              <div class="title">数量</div>
            </th>
            <th>
              <div class="title">单价（不含税）</div>
            </th>
            <th>
              <div class="title">金额（不含税）</div>
            </th>
            <th>
              <div class="title">税率</div>
            </th>
            <th>
              <div class="title">税额</div>
            </th>
          </tr>
          <tr
            v-for="(item, index) in data"
            :key="item.id" class="list-row"
            :class="{'list-row-last': index === 8}">
            <td class="vertical-td">
              <div class="title vertical-title">
                <input type="checkbox">
              </div>
            </td>
            <td>
              <div class="content">{{ item.hwysName }}</div>
            </td>
            <td>
              <div class="content">{{ item.specs }}</div>
            </td>
            <td>
              <div class="content">
                <template v-if="item.freightCalcType === '0'">吨</template>
                <template v-else-if="item.freightCalcType === '1'">车</template>
              </div>
            </td>
            <td>
              <div class="content">{{ item.ton }}</div>
            </td>
            <td>
              <div class="content">{{ item.freightkNotax }}</div>
            </td>
            <td>
              <div class="content">{{ item.amountNotax }}</div>
            </td>
            <td>
              <div class="content">{{ item.taxRate }}%</div>
            </td>
            <td>
              <div class="content">{{ item.taxMoney }}</div>
            </td>
          </tr>
          <tr
            v-for="item in 8 - data.length"
            :key="item"
            class="list-row"
            :class="{'list-row-last': item + data.length === 8}">
            <td>
              <div class="content"></div>
            </td>
            <td>
              <div class="content"></div>
            </td>
            <td>
              <div class="content"></div>
            </td>
            <td>
              <div class="content"></div>
            </td>
            <td>
              <div class="content"></div>
            </td>
            <td>
              <div class="content"></div>
            </td>
            <td>
              <div class="content"></div>
            </td>
            <td>
              <div class="content"></div>
            </td>
            <td>
              <div class="content"></div>
            </td>
          </tr>
          <tr class="sum">
            <td colspan="2">
              <div class="title">合计</div>
            </td>
            <td colspan="7">
              <div class="sum-content">
                <div class="sum-item">
                  <div class="title">金额：</div>{{ info.sumAmount }}
                </div>
                <div class="sum-item">
                  <div class="title">税额：</div>{{ info.sumTaxMoney }}
                </div>
              </div>
            </td>
          </tr>
          <tr class="sum">
            <td colspan="2">
              <div class="title">价税合计（大写）</div>
            </td>
            <td colspan="7">
              <div class="sum-content">
                <div class="sum-item">
                  {{ info.amountCN }}
                </div>
                <div class="sum-item">
                  <div class="title">小写：</div>{{ info.allAmount }}
                </div>
              </div>
            </td>
          </tr>
        </table>
        <table class="table1">
          <tr>
            <td>
              <div class="title vertical-title">销售方</div>
            </td>
            <td>
              <div class="table1-row1">
                <div><span class="title">名称</span>： {{ info.sellName }}</div>
                <div><span class="title">纳税人识别号</span>： {{ info.sellDutyNum }}</div>
                <div><span class="title">地址、电话</span>： {{ info.sellAddressPhone }}</div>
                <div><span class="title">开户行及账号</span>： {{ info.sellBankAccount }}</div>
              </div>
            </td>
            <td>
              <div class="title vertical-title">备注</div>
            </td>
            <td class="remark-td">
              <div class="remark-wrapper">
                <div class="remark-inner">
                  <!-- <textarea class=""></textarea> -->
                  {{ info.remark }}
                </div>
              </div>
            </td>
          </tr>
        </table>
        <div class="detail-btns">
          <el-button
            type="primary"
            size="mini"
            v-for="item in data"
            :key="item.id"
            @click="detail(item.id)">查看详情</el-button>
        </div>
      </div>
    </template>
    
    <!-- <div class="bottom-line"></div> -->
    <div class="tool">
      <div class="tool-info">
        <template v-if="mailInfo">
          <div class="tool-address">
            邮寄地址：{{ mailInfo.provinceName }} {{ mailInfo.cityName }} {{ mailInfo.countyName }} {{ mailInfo.address }}
          </div>
          <div class="tool-name">收件人：{{ mailInfo.name }}</div>
          <div class="tool-tel">联系电话：{{ mailInfo.phone }}</div>
          <div class="tool-email">邮箱：{{ mailInfo.email }}</div>
        </template>
      </div>
      <div class="tool-info2">
        <span>
          <template v-if="info.billType === '0'">电子专票</template>
          <template v-else-if="info.billType === '1'">纸质专票</template>
          <template v-else-if="info.billType === '2'">全电票</template>
        </span>
        <span>{{ info.printIf === '1' ? '需要' : '不需要' }}打印清单</span>
        <template v-if="type === 'audit'">
          <el-button type="primary" size="mini" @click="audit('reject')">驳回</el-button>
          <el-button type="primary" size="mini" @click="audit('pass')">审核通过</el-button>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { objToFormData } from '@/utils/tools'
import FullElecInvoice from './FullElecInvoice'
export default {
  components: { FullElecInvoice },
  data() {
    return {
      type: null,
      data: [],
      info: {},
      isSimplification: false,
      mailInfo: {},
      invoiceInfoList: []
    }
  },
  activated() {
    let query = this.$route.query
    let id = this.id = query.id
    this.type = query.type
    this.$http.post('/admin-center-server/bill/getInvoiceScheduleBillDetail', objToFormData({
      id
    }))
      .then(res => {
        let data = res.data
        if (data.code === '200') {
          data = data.data
          this.originData = data.scheduleList || [] //用于超过8条时，获取每一条的规格
          this.data = data.scheduleList || []
          this.info = data.invoiceScheduleBill || {}
          this.mailInfo = data.billMailRO

          let isSimplification = this.isSimplification = this.data.length > 8 ? true : false
          if (isSimplification) {
            let info = this.info
            this.data = [{
              hwysName: '详情见附件清单',
              freightCalcType: info.sumFreightCalcType,
              ton: info.sumTon,
              freightkNotax: info.sumFreightkNotax,
              amountNotax: info.sumAmount,
              taxRate: this.data[0].taxRate,
              taxMoney: info.sumTaxMoney,
              specs: info.sumSpecs
            }]
          }
        } else {
          this.$message.error(data.message)
        }

        this.$post('/admin-center-server/invoiceScheduleOrder/getinvoiceScheduleOrderItem', {
          invoiceNumber: data.invoiceScheduleBill.invoiceNumber,
          billInfoFlag: true
        })
          .then(res => {
            this.invoiceInfoList = res
          })
      })
  },
  methods: {
    audit(type) {
      let p
      let params = {
        id: this.id
      }
      if (type === 'reject') {
        p = this.$prompt('请输入驳回原因', {
          inputValidator(v) {
            if (v === null || v.trim() === '') return '驳回原因不能为空'
            return true
          }
        })
        params.status = '2'
      } else {
        p = this.$confirm('确定要通过吗')
        params.status = '3'
      }
      p.then(v => {
        if (type === 'reject') params.rejectReason = v.value
        this.$http.post('/admin-center-server/bill/updateStatus', params)
          .then(res => {
            let data = res.data
            if (data.code === '200') {
              this.$message.success('审核成功')
              this.$router.push('/finance/invoice/invoice')
            } else {
              this.$message.error(data.message)
            }
          })
      })
    },
    detail(id) {
      if (this.isSimplification) {
        this.$router.push({
          name: 'attachmentInventory',
          params: {
            data: this.originData
          }
        })
      } else {
        this.$router.push('/finance/invoice/waybill?id=' + id)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.back-btn {
  float: right;
}
.invoice {
  padding: 10px;
  padding-bottom: 80px;
  background: #fff;
}
.main,
.headline {
  font-family: 'Kaiti';
}
.headline,
.title {
  color: #A06F11;
}
.headline {
  text-align: center;
}
.headline-outer {
  padding-bottom: 4px;
  display: inline-block;
  border-bottom: 2px solid #A06F11;
}
.headline-inner {
  padding: 0 40px 5px 40px;
  font-size: 26px;
  border-bottom: 2px solid #A06F11;
}
.title {
  text-align: center;
  font-size: 14px;
}
.vertical-title {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 41px;
  writing-mode: vertical-rl;
}
.vertical-td {
  width: 41px;
}
.main {
  box-sizing: content-box;
  position: relative;
  margin: 24px auto 0 auto;
  padding-right: 93px;
  padding-bottom: 20px;
  width: 1037px;
}
table {
  box-sizing: border-box;
  margin: 0 auto;
  margin-bottom: -2px;
  border-collapse: collapse;
  td, th {
    padding: 0;
    border: 2px solid #A06F11;
    vertical-align: middle;
    font-size: 14px;
  }
}
.table1-row1 {
  box-sizing: border-box;
  padding: 5px 10px;
  width: 551px;
  min-height: 98px;
  line-height: 22px;
  .title {
    display: inline-block;
    width: 100px;
    text-align-last: justify;
  }
}
.table1-row2 {
  width: 394px;
}
.table2 {
  width: 1037px;
  border: 2px solid #A06F11;
}
.table2 .list-row th {
  padding: 5px 0;
}
.table2 .content {
  height: 20px;
  line-height: 20px;
  text-align: center;
}
.unit-select {
  width: 60px;
}
.table2 .list-row {
  td, th {
    border-top: 0;
    border-bottom: 0;
  }
}
.table2 .list-row-last {
   .content, .title {
    margin-bottom: 7px;
  }
}
.table2 .sum {
  height: 28px;
}
.sum-content {
  margin-left: 60px;
}
.sum-item {
  display: inline-block;
  width: 50%;
}
.sum-content .title {
  display: inline-block;
}
table input,
table textarea {
  border: 1px solid #DCDFE6;
  outline: none;
}
.spec-input {
  box-sizing: border-box;
  width: 130px;
  height: 28px;
}
.remark-td {
  position: relative;
}
.remark-wrapper {
  width: 394px;
}
.remark-inner {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 5px;
}
.remark-area {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  resize: none;
  ::v-deep textarea {
    height: 100%;
  }
}
.detail-btns {
  position: absolute;
  top: 170px;
  right: 0;
  .el-button {
    display: block;
    margin-bottom: 6px;
    margin-left: 0;
  }
}
.bottom-line {
  margin-bottom: 53px;
  margin-left: -50px;
  margin-right: -50px;
  height: 10px;
  background: #f2f2f2;
}
.tool {
  box-sizing: border-box;
  position: fixed;
  left: 180px;
  bottom: 0;
  width: calc(100% - 200px);
  height: 80px;
  padding: 12px 20px 0 20px;
  background: #fff;
  font-size: 12px;
  color: #333;
  text-align: right;
}
.tool-address,
.tool-name,
.tool-tel,
.tool-email {
  display: inline-block;
  margin-right: 5%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tool-address {
  max-width: 44%;
}
.tool-name {
  max-width: 15%;
}
.tool-tel {
  max-width: 15%;
}
.tool-email {
  max-width: 15%;
}
.tool-set-address-warn {
  margin-right: 30px;
}
.tool-info2 {
  margin-top: 8px;
  text-align: right;
  .el-radio-group {
    margin-right: 20px;
  }
  .el-radio {
    margin-right: 10px;
  }
  span {
    margin-right: 20px;
    color: #f6a018;
  }
}
</style>