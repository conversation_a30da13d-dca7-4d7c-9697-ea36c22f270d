<template>
    <div :class="classObj" class="app-wrapper">
        <navbar/>
        <div class="main-container">
            <sidebar class="sidebar-container"/>
            <div class="right-container">
                <tags-view/>
                <app-main/>
                <div id="failSound" clickMusic='true'></div>
            </div>
        </div>
    </div>
</template>

<script>
    import {Navbar, Sidebar, AppMain, TagsView} from './components'
    import ResizeMixin from './mixin/ResizeHandler'

    export default {
        name: 'Layout',
        components: {
            Navbar,
            Sidebar,
            AppMain,
            TagsView,
        },
        mixins: [ResizeMixin],
            computed: {
                sidebar() {
                    return this.$store.state.app.sidebar
                },
            device() {
                return this.$store.state.app.device
            },
            classObj() {
                return {
                    hideSidebar: !this.sidebar.opened,
                    openSidebar: this.sidebar.opened,
                    withoutAnimation: this.sidebar.withoutAnimation,
                    mobile: this.device === 'mobile'
                }
            }
        },
        data() {
            return {
                isTrue: false
            }
        },
        methods: {
            handleClickOutside() {
                this.$store.dispatch('closeSideBar', {withoutAnimation: false})
            },

            /** type 类型消息类型  id 对应权限ID **/
            getMenu(type, num) {
                this.$store.state.permission.addRouters.map((item, index) => {
                    if (item.meta) {
                        if (item.meta.id === 22) {
                            let changeNum = item.children[0].children;
                            changeNum.map((item, index) => {
                                if (!item.hidden && item.meta.id !== 24) {
                                    if (item.meta.id === 25 && type === 0) {
                                        item.meta.num = num
                                    } else if (item.meta.id === 26 && type === 1) {
                                        item.meta.num = num
                                    } else if (item.meta.id === 27 && type === 2) {
                                        item.meta.num = num
                                    }
                                }
                            })
                        } else if (item.meta.id === 1) {
                            let clientNum = item.children.find(v => v.meta.id === 2).children
                            let brokerNum = (item.children.find(v => v.meta.id === 6) || {}).children || []
                            let driverNum = item.children.find(v => v.meta.id === 11).children
                            clientNum.map((item, index) => {
                                if (!item.hidden) {
                                    if (item.meta.id === 5 && type === 10) {
                                        item.meta.num = num
                                    }
                                }
                            });
                            brokerNum.map((item, index) => {
                                if (!item.hidden) {
                                    if (item.meta.id === 10 && type === 20) {
                                        item.meta.num = num
                                    }
                                }
                            });
                            driverNum.map((item, index) => {
                                if (!item.hidden) {
                                    if (item.meta.id === 13 && type === 30) {
                                        item.meta.num = num
                                    }
                                }
                            })
                        } else if (item.meta.id === 34) {
                            let curItem = item.children[4];
                            if (curItem.meta.id === 81 && type === 100) {
                                item.meta.num = num;
                            }
                        } else if (item.meta.id === 82) {
                            let curItem = item.children[0];
                            if (curItem.meta.id === 82 && type === 888) {
                                curItem.meta.num = num;
                            }
                        }
                    }
                });
            },

            getStatusNum() {
                let video = document.getElementById("failSound");
                let _this = this;
                //客户认证频道
                this.$goEasy.subscribe({
                    channel: "client_auth_apply_channel",
                    onMessage: function (message) {
                        _this.getClientauthapplychannel();
                        video.click();
                        // let time = new Date();
                        // let mytime=time.toLocaleTimeString();
                        // console.log('货主消息',mytime);
                    }
                });
                //调度员认证
                this.$goEasy.subscribe({
                    channel: "broker_auth_apply_channel",
                    onMessage: function (message) {
                        _this.getBrokerauthapplychannel();
                        video.click();
                        // let time = new Date();
                        // let mytime=time.toLocaleTimeString();
                        // console.log('调度员消息',mytime);
                    }
                });
                //司机认证
                this.$goEasy.subscribe({
                    channel: "driver_auth_apply_channel",
                    onMessage: function (message) {
                        _this.getDriverauthapplychannel();
                        video.click();
                        // let time = new Date();
                        // let mytime=time.toLocaleTimeString();
                        // console.log('司机消息',mytime);
                    }
                });
                //车辆认证0
                this.$goEasy.subscribe({
                    channel: "car_auth_apply_channel",
                    onMessage: function (message) {
                        _this.getCarauthapplychannel();
                        video.click();
                        // let time = new Date();
                        // let mytime=time.toLocaleTimeString();
                        // console.log('车辆认证消息',mytime);
                    }
                });
                //司机申用车辆消息1
                this.$goEasy.subscribe({
                    channel: "driving_apply_channel",
                    onMessage: function (message) {
                        _this.getdrivingapplychannel();
                        video.click();
                        // let time = new Date();
                        // let mytime=time.toLocaleTimeString();
                        // console.log('驾驶授权',mytime);
                    }
                });
                //司机申用车辆消息2
                this.$goEasy.subscribe({
                    channel: "escheatage_apply_channel",
                    onMessage: function (message) {
                        _this.getescheatageapplychannel();
                        video.click();
                        // let time = new Date();
                        // let mytime=time.toLocaleTimeString();
                        // console.log('归属授权',mytime);
                    }
                });
                //司机销户管理
                this.$goEasy.subscribe({
                    channel: "cancel_bind_card",
                    onMessage: function (message) {
                        _this.driverCancellation();
                        video.click();
                        // let time = new Date();
                        // let mytime=time.toLocaleTimeString();
                        // console.log('司机销户',mytime);
                    }
                });
            },

            // 司机销户
            driverCancellation() {
                this.$http.post('/admin-center-server/acctChange/getAcctChangeAuditingCount').then(res => {
                    let data = Number(res.data.data);
                    let type = 100;
                    this.getMenu(type, data)
                })
            },


            //货主  10
            getClientauthapplychannel() {
                this.$http.get('/admin-center-server/verify/getVerifySumMsgByType', {
                    params: {
                        userType: 1
                    }
                }).then(res => {
                    let data = Number(res.data.data);
                    let type = 10;
                    this.getMenu(type, data)
                })
            },
            //调度员  20
            getBrokerauthapplychannel() {
                this.$http.get('/admin-center-server/verify/getVerifySumMsgByType', {
                    params: {
                        userType: 2
                    }
                }).then(res => {
                    let data = Number(res.data.data);
                    let type = 20;
                    this.getMenu(type, data)
                })
            },
            //司机 30
            getDriverauthapplychannel() {
                this.$http.get('/admin-center-server/verify/getVerifySumMsgByType', {
                    params: {
                        userType: 3
                    }
                }).then(res => {
                    let data = Number(res.data.data);
                    let type = 30;
                    this.getMenu(type, data)
                })
            },

            //车辆管理
            getCarauthapplychannel() {
                this.$http.get('/admin-center-server/car/auth/manager/getSumMsgByType', {
                    params: {
                        type: 0
                    }
                }).then(res => {
                    let data = Number(res.data.data);
                    let type = 0;
                    this.getMenu(type, data)
                })
            },
            //驾驶审核页面
            getdrivingapplychannel() {
                this.$http.get('/admin-center-server/car/auth/manager/getSumMsgByType', {
                    params: {
                        type: ''
                    }
                }).then(res => {
                    let data = Number(res.data.data);
                    let type = 1;
                    this.getMenu(type, data)
                })
            },
            //归属权限
            getescheatageapplychannel() {
                this.$http.get('/admin-center-server/car/auth/manager/getSumMsgByType', {
                    params: {
                        type: 2
                    }
                }).then(res => {
                    let data = Number(res.data.data);
                    let type = 2;
                    this.getMenu(type, data)
                })
            },

            //预警
            warningNum() {
                this.$http.get('/admin-center-server/earningsWarning/getnewNum').then(res => {
                    let data = Number(res.data.data);
                    let type = 888;
                    this.getMenu(type, data)
                })
            },
        },

        mounted() {
            this.getClientauthapplychannel();
            this.getBrokerauthapplychannel();
            this.getDriverauthapplychannel();
            this.getCarauthapplychannel();
            this.getdrivingapplychannel();
            this.getescheatageapplychannel();
            this.driverCancellation();
            // this.getStatusNum();
            this.warningNum();
        }
    }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
    @import "~@/styles/mixin.scss";

    .app-wrapper {
        @include clearfix;
        display: flex;
        flex-direction: column;
        position: relative;
        height: 100%;
        width: 100%;

        &.mobile.openSidebar {
            position: fixed;
            top: 0;
        }
    }

    .drawer-bg {
        background: #000;
        opacity: 0.3;
        width: 100%;
        top: 0;
        height: 100%;
        position: absolute;
        z-index: 999;
    }

    .right-container {
        flex-grow: 1;
        width: 0;
        display: flex;
        flex-direction: column;
    }
</style>
