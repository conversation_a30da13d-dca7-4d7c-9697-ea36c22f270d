<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">筛选查询--广告位</div>
      <div class="select-info">
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="120px">
          <el-form-item label="广告名称:">
            <el-input v-model="formInline.name" placeholder="请输入广告名称"></el-input>
          </el-form-item>
          <el-form-item label="广告位置:">
            <el-select v-model="formInline.positionType" placeholder="请选择">
              <el-option
                v-for="item in positons"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-form>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          label-width="150px"
          style="margin-top: 30px"
        ></el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
        <div>
          <el-button class="releaseMessage" @click="addAdvertise">添加</el-button>
        </div>
      </div>
      <div class="list-main">
        <template>
          <el-table
            ref="table"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            :height="tableHeight"
            border
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="name" label="名称"></el-table-column>
            <el-table-column prop="positionTypeName" label="位置"></el-table-column>
            <el-table-column prop="height" label="长度"></el-table-column>
            <el-table-column prop="width" label="宽度"></el-table-column>
            <el-table-column prop="description" label="描述"></el-table-column>

            <el-table-column fixed="right" label="操作" width="160">
              <template slot-scope="scope">
                <!-- <el-button @click="goDetail(scope.row)" type="text" size="small">查看</el-button> -->
                <el-button type="text" size="small" @click="editor(scope.row)">编辑</el-button>
                <el-button type="text" size="small" @click="delItem(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="flex">
            <div class="delAll">
              <el-button @click="deleteAll">批量删除</el-button>
            </div>
            <div class="pageSize">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="formInline.pageNumber"
                :page-sizes="[20, 40, 60, 80,100]"
                :page-size="formInline.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                style="margin: 10px auto"
              ></el-pagination>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CarsList",
  comments: {},
  data() {
    return {
      value1: "",
      currentPage4: 4,
      total: null,
      positons: [
        {
          id: "0",
          name: "banner-货主端"
        },
        {
          id: "1",
          name: "启动页-货主端"
        },
        {
          id: "2",
          name: "启动页-调度员端"
        },
        {
          id: "3",
          name: "启动页-司机端"
        }
      ],
      tableHeight: null, //表格的高度
      types: [
        {
          value: "0",
          label: "图片"
        },
        {
          value: "1",
          label: "文本"
        }
      ],
      value: "",

      formInline: {
        name: "", //广告名称
        positionType: "", //广告位置 传id
        order: "asc",
        pageNumber: 1,
        pageSize: 20
      },
      date: "",
      tableData: [],
      multipleSelection: [] //多选的值
    };
  },
  activated() {
    this.getDataList(); //获取列表数据
    this.tableHeight =
      window.innerHeight - this.$refs.table.$el.offsetTop - 240;
    console.log(this.tableHeight);
  },
  methods: {
    //查询
    onSubmit() {
      console.log(this.formInline);
      this.formInline.pageNumber=1;
      this.getDataList();
    },
    /* 重置查询条件 */
    reset() {
      this.formInline = {
        name: "", //广告名称
        positionType: "", //广告位置 传id
        order: "asc",
        pageNumber: 1,
        pageSize: 20
      };
      this.getDataList();
    },

    /* 多选的值 */
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    /* 选取每页多少条数据 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.formInline.pageNumber =1;
      this.formInline.pageSize = JSON.parse(`${val}`);
      console.log(this.formInline.pageSize);
      this.getDataList();
    },
    /* 翻页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.formInline.pageNumber = JSON.parse(`${val}`);
      console.log(this.formInline.pageNumber);
      this.getDataList();
    },
    /* 获取数据 */
    getDataList() {
      var postData = this.formInline;

      console.log(postData, "postData");
      this.$http
        .post("/admin-center-server/ad/queryAdpositionList", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            console.log(data);
            this.tableData = data.data.list;
            this.total = JSON.parse(data.data.total);
            console.log(this.tableData, "tableData----");
          } else {
            this.$message.warning(data.message);
          }
        });
    },

    /* 添加广告 */
    addAdvertise() {
      this.$router.push({
        path: "addPositionAdvertise",
        query: {
          typeFlag: 1
        }
      });
    },
    /* 单个删除功能 */
    delItem(row) {
      console.log(row.id);
      let id = row.id;
      console.log(row.id, "-----id");
      let ids = [];
      ids[0] = Number(row.id);

      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http
            .post("/admin-center-server/ad/deleteByAdpositionIds", ids)
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
              }
              this.getDataList();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },

    /* 批量删除 */
    deleteAll() {
      var deleteAllData = this.multipleSelection; //批量删除的数据
      let ids = deleteAllData.map(obj => {
        return obj.id;
      });
      console.log(ids, "----");
      this.dialogVisible = true;
      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http
            .post("/admin-center-server/ad/deleteByAdpositionIds", ids)
            .then(res => {
              let data = res.data;
              if (data.code == "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
              }
              this.getDataList();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    /* 表格中的开关 */
    change: function(index, row) {
      console.log(index, row);

      var postData = {
        id: row.id,
        isShow: row.isShow
      };
      this.$http
        .post("/admin-center-server/ad/isShow", this.$qs.stringify(postData))
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.getDataList();
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /*  编辑 */
    editor(row) {
      this.$router.push({
        path: "addPositionAdvertise",
        query: {
          typeFlag: 2,
          id: row.id
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }
    .flex {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
