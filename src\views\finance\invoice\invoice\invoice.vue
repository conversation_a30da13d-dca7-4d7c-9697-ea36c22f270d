<template>
  <div class="app-container">
    <div class="select-box">
      <div class="top-title">
        <div>筛选查询</div>
        <div class="button">
          <el-button
            class="left"
            icon="el-icon-refresh-right"
            size="mini"
            type="success"
            @click="()=>{this.$router.go(0)}"
            >刷新
          </el-button>
          <el-button
            class="left"
            icon="el-icon-search"
            size="mini"
            type="primary"
            @click="doSearch"
            >查询
          </el-button>
          <el-button
            class="left"
            icon="el-icon-delete"
            size="mini"
            type="danger"
            @click="searchReset"
            >清空筛选
          </el-button>
        </div>
      </div>
      <div class="select-info">
        <el-form
          class="demo-form-inline"
          :inline="true"
          :model="searchForm"
          label-width="90px"
          size="mini"
        >

          <el-form-item label="开票编号:">
            <el-input
              class="form-item-content-width"
              v-model="searchForm.invoiceNumber"
              placeholder="请输入开票编号"
            ></el-input>
          </el-form-item>
          <el-form-item label="运单号:">
            <el-input
              class="form-item-content-width"
              v-model="searchForm.orderItemSn"
              placeholder="请输入运单号"
            ></el-input>
          </el-form-item>
          <el-form-item label="申请时间:">
            <el-date-picker
              :clearable="false"
              v-model="date"
              value-format="yyyy-MM-dd"
              type="daterange"
              :default-time="['00:00:00', '00:00:00']"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="开票时间:">
            <el-date-picker
              :clearable="false"
              v-model="invoiceDate"
              value-format="yyyy-MM-dd"
              type="daterange"
              :default-time="['00:00:00', '00:00:00']"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-form>
        <el-form
          :inline="true"
          :model="searchForm"
          class="demo-form-inline"
          label-width="90px"
          size="mini"
        >
          <el-form-item label="状态:">
            <el-select
              class="form-item-content-width"
              v-model="searchForm.status"
              placeholder="全部"
            >
              <el-option value="" label="全部"></el-option>
              <el-option :value="1" label="待审核"></el-option>
              <el-option :value="3" label="审核通过"></el-option>
              <el-option :value="2" label="已驳回"></el-option>
              <el-option :value="4" label="已开票"></el-option>
              <el-option :value="5" label="已作废"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发票类型:">
            <el-select
              class="form-item-content-width"
              v-model="searchForm.billType"
              placeholder="全部">
              <el-option value="" label="全部"></el-option>
              <el-option value="0" label="电子专票"></el-option>
              <el-option value="1" label="纸质专票"></el-option>
              <el-option value="2" label="全电票"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属项目:">
            <el-input
              class="form-item-content-width"
              v-model="searchForm.projectName"
              placeholder="请输入所属项目"
            ></el-input>
          </el-form-item>
          <el-form-item label="公司名称:">
            <el-input
              class="form-item-content-width"
              v-model="searchForm.buyName"
              placeholder="请输入公司名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="合作业务主体:" label-width="120px">
            <el-select
              class="form-item-content-width"
              v-model="searchForm.baseId"
              placeholder="请选择"
            >
              <el-option v-for="item in $store.state.user.baseInfo" :key="item.id" :value="item.id" :label="item.baseName"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div></div>
        <div>
          <div v-if="info" class="info">
            <span>开票金额</span>合计<span>{{  info.amountTotal }}</span>元，<span>开票税额</span>合计<span>{{ info.taxMoneyTotal }}</span>元，<span>平台服务费</span>合计<span>{{ info.taxAmountTotal }}</span>元
          </div>
          <el-button @click="countTotal" type="primary">计算合计金额</el-button>
          <el-button type="primary" @click="batchMarkInvoice" :loading="batchLoading">批量标记开票</el-button>
          <el-button type="primary" @click="generateInvoiceDocument" :loading="generateLoading">生成发票申领文件</el-button>
        </div>
        
      </div>
      <div class="list-main">
        <el-table
        :data="data"
        cell-class-name="table_cell_gray"
        header-cell-class-name="table_header_cell_gray"
        border
        >
          <el-table-column label="序号" type="index"></el-table-column>
          <el-table-column label="开票编号" prop="invoiceNumber"></el-table-column>
          <el-table-column label="发票类型" prop="billTypeStr"></el-table-column>
          <el-table-column label="公司名称" prop="buyName"></el-table-column>
          <el-table-column label="合作平台主体" prop="baseName"></el-table-column>
          <el-table-column label="所属项目" prop="projectName">
            <template slot-scope="scope">
              {{scope.row.projectName ? scope.row.projectName : '-'}}
            </template>
          </el-table-column>
          <el-table-column label="开票金额" prop="amount"></el-table-column>
          <el-table-column label="开票税额" prop="taxMoney"></el-table-column>
          <el-table-column label="平台服务费" prop="taxAmount"></el-table-column>
          <el-table-column label="状态" prop="statusStr"></el-table-column>
          <el-table-column label="申请时间" prop="createTime"></el-table-column>
          <el-table-column label="开票时间" prop="kpTime"></el-table-column>
          <el-table-column label="操作" prop="name" width="325" fixed="right">
            <template slot-scope="scope">
              <el-button v-if="scope.row.status === '1'" type="text" @click="toDetail('audit', scope.row.id)">审核</el-button>
              <el-button v-if="scope.row.status === '2' || scope.row.status === '3' || scope.row.status === '4'|| scope.row.status === '5'" type="text" @click="toDetail('detail', scope.row.id)">详情</el-button>
              <el-button v-if="scope.row.status === '3'" type="text" @click="openMark('edit', scope.row)">标记开票</el-button>
              <el-button v-if="scope.row.status === '4' || scope.row.status === '5'" type="text" @click="openMark('detail', scope.row)">开票信息</el-button>
              <el-button v-if="scope.row.status === '3' || scope.row.status === '4' || scope.row.status === '5'" type="text" @click="showMailInfo(scope.row.id)">邮寄地址</el-button>
              <el-button v-if="scope.row.status === '4'" type="text" @click="voidInvoice(scope.row.id)">作废</el-button>
              <!-- 开票详情、作废 4 -->
              <el-button @click="exportDetail(scope.row, true)" type="text">导出全部运输明细</el-button>
              <el-button @click="exportDetail(scope.row, false)" type="text">导出票面运输明细</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="page">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="getList"
            :current-page.sync="page.pageNumber"
            :page-sizes="[20, 40, 60, 80, 100]"
            :page-size="page.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="isMailShow" title="邮寄地址" width="500px">
      <el-form label-width="120px" size="mini" class="company-form">
        <el-form-item label="收件人：">{{ mailInfo.name }}</el-form-item>
        <el-form-item label="收件人电话：">{{ mailInfo.phone }}</el-form-item>
        <el-form-item label="地区：">{{ mailInfo.provinceName }} {{ mailInfo.cityName }} {{ mailInfo.countyName }} {{ mailInfo.address }}</el-form-item>
        <el-form-item label="详细地址：">{{ mailInfo.address }}</el-form-item>
        <el-form-item label="邮箱：">{{ mailInfo.email }}</el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog
      :visible.sync="isMarkShow"
      width="500px"
      title="标记开票">
      <el-form label-width="100px" :model="markForm" :rules="markRules" ref="markForm">
        <template v-if="markType === 'edit'">
          <el-form-item v-if="markInfo.billType === '1'" label="发票代码" prop="fpdm">
            <el-input v-model="markForm.fpdm"></el-input>
          </el-form-item>
          <el-form-item label="发票号码" prop="fphm">
            <el-input v-model="markForm.fphm"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitMark" :loading="isSubmitMarkLoading">确定</el-button>
            <el-button @click="isMarkShow = false">取消</el-button>
          </el-form-item>
        </template>
        <template v-else-if="markType === 'detail'">
          <el-form-item v-if="markInfo.billType === '1'" label="发票代码">
            {{ markInfo.fpdm }}
          </el-form-item>
          <el-form-item label="发票号码">
            {{ markInfo.fphm }}
          </el-form-item>
        </template>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { objToFormData } from '@/utils/tools'
import { selectFile } from '@/utils/file'
export default {
  data() {
    return {
      searchForm: {
        invoiceNumber: '',
        orderItemSn: '',
        status: '',
        billType: '',
        buyName: '',
        startDate: '',
        endDate: '',
        baseId: ''
      },
      date: [],
      invoiceDate: null,
      data: [],
      total: 0,
      page: {
        pageNumber: 1,
        pageSize: 20,
      },
      isMailShow: false,
      mailInfo: {},
      isMarkShow: false,
      isSubmitMarkLoading: false,
      markInfo: {},
      markForm: {},
      markType: null,
      markRules: {
        fpdm: [
          { required: true, message: '请输入发票代码', trigger: 'blur' }
        ],
        fphm: [
          { required: true, message: '请输入发票号码', trigger: 'blur' }
        ]
      },
      batchLoading: false,
      generateLoading: false,
      info: null
    }
  },
  created() {
    this.searchDefault = { ...this.searchForm }
    this.searchFinal = { ...this.searchForm }
  },
  activated() {
    this.getList()
  },
  methods: {
    getList() {
      let params = {
        ...this.page,
        ...this.searchFinal
      }
      this.$http.post('/admin-center-server/bill/getInvoiceScheduleBillList', objToFormData(params))
        .then(res => {
          let data = res.data
          if (data.code === '200') {
            data = data.data
            this.data = data.list
            this.total = Number(data.total)
          } else {
            this.$message.error(data.message)
          }
        })
    },
    doSearch() {
      this.searchFinal = {
        ...this.searchForm,
        startDate: this.date && this.date[0] || '',
        endDate: this.date && this.date[1] || '',
        billsStartDate: this.invoiceDate ? this.invoiceDate[0] : '',
        billEndDate: this.invoiceDate ? this.invoiceDate[1] : '',
      }
      this.getList()
    },
    searchReset() {
      this.searchForm = { ...this.searchDefault }
      this.date = []
      this.invoiceDate = null
      this.searchFinal = { ...this.searchDefault }
      this.page.pageNumber = 1
      this.getList()
    },
    handleSizeChange(v) {
      this.page.pageSize = v
      this.getList()
    },
    openMark(type, row) {
      this.markType = type
      this.markInfo = row
      if (type === 'edit') {
        this.markForm = {
          id: row.id
        }
      }
      this.isMarkShow = true
      this.$nextTick(() => {
        this.$refs.markForm.clearValidate()
      })
    },
    submitMark() {
      this.$refs.markForm.validate(valid => {
        if (!valid) return
        let markForm = this.markForm
        let params = {
          id: markForm.id,
          status: '4',
          fpdm: markForm.fpdm,
          fphm: markForm.fphm
        }
        this.isSubmitMarkLoading = true
        this.$http.post('/admin-center-server/bill/updateStatus', params)
          .then(res => {
            let data = res.data
            if (data.code === '200') {
              this.$message.success('审核成功')
              this.isMarkShow = false
              this.getList()
            } else {
              this.$message.error(data.message)
            }
          })
          .finally(() => {
            this.isSubmitMarkLoading = false
          })
      })
    },
    showMailInfo(id) {
      this.$http.get('/admin-center-server/bill/getBillMail?id=' + id)
        .then(res => {
          let data = res.data
          if (data.code === '200') {
            data = data.data
            this.mailInfo = data
            this.isMailShow = true
          } else {
            this.$message.error(data.message)
          }
        })
    },
    toDetail(type, id) {
      this.$router.push(`/finance/invoice/invoiceDetail?type=${type}&id=${id}`)
    },
    voidInvoice(id) {
      this.$confirm('确定要作废吗')
        .then(() => {
          this.$post('/admin-center-server/bill/cancelBill?id=' + id)
          .then(() => {
            this.$message('操作成功')
            this.getList()
          })
        })
    },
    batchMarkInvoice() {
      selectFile({accept: '.xls,.xlsx'}).then(
        (file) => {
          
          // if (file.type != "xls" && file.type != "xlsx") {
          //   this.$message({
          //       showClose: true,
          //       message:"请选择正确的文件类型",
          //       type: 'warning',
          //     })
          //     return
          // }
          this.batchLoading = true
          let formData = new FormData()
          formData.append('file', file)
          this.$post("/admin-center-server/bill/batchMarkInvoice", formData)
            .then(res => {
              this.batchLoading = false
              this.getList()
              this.$message({
                showClose: true,
                message:res,
                type: 'warning',
                duration: 0
              })
            })
        }
      )
    },
    generateInvoiceDocument() {
      if (!this.searchForm.baseId) {
        this.$message.warning('请选择合作业务主体')
        return
      }
      this.generateLoading = true
      this.$get("/admin-center-server/bill/exportInvoiceApply?baseId=" + this.searchForm.baseId).then(
        (res) => {
          this.generateLoading = false
          this.$message({
                message:'正在生成，稍后您可在【导出任务】中查看生成进度及下载文件',
                type: 'success',
              })
        }
      )
    },
    exportDetail(row, isAll) {
      if (isAll) {
        this.$post("/admin-center-server/bill/getNSRSupportingExcel", {
          invoiceNumber: row.invoiceNumber,
          scheduleSn: row.scheduleSn
        })
          .then(() => {
            this.$message({
              message:'正在生成，稍后您可在【导出任务】中查看生成进度及下载文件',
              type: 'success',
            })
        })
      } else {
        this.$post("/admin-center-server/bill/getNSRSupportingExcel", {
          invoiceNumber: row.invoiceNumber,
          scheduleSn: row.scheduleSn,
          billInfoFlag: true
        })
          .then(() => {
            this.$message({
              message:'正在生成，稍后您可在【导出任务】中查看生成进度及下载文件',
              type: 'success',
            })
        })
      }
    },
    countTotal() {
      this.$post('/admin-center-server/bill/statisticsInvoiceScheduleBill', objToFormData(this.searchFinal))
        .then(res => {
          this.info = res
        })
    }
  }
}
</script>

<style src="@/assets/scss/list.scss" scoped lang="scss"></style>
<style scoped lang="scss">
.page {
  text-align: right;
  .el-pagination {
    margin: 10px 0;
  }
}
.info {
  display: inline-block;
  margin-right: 10px;
  font-size: 14px;
  span {
    color: #f13545;
  }
}
</style>