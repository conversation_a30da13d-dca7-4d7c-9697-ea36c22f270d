<template>
    <el-dialog
        :close-on-click-modal="false"
        title="开通平安账户"
        :visible.sync="open"
        destroy-on-close
        @close="cancel"
        width="800px"
    >
        <el-form
            ref="form"
            label-width="100px"
            :model="form"
            :rules="rules"
            v-loading="loading"
        >
            <el-row class="global-div-search">
                <el-col :span="24">
                    <el-form-item label="名称" prop="channelName">
                        <el-input
                            v-model="form.channelName"
                            clearable
                            placeholder="请输入渠道名称"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="姓名" prop="name">
                        <el-input
                            v-model="form.name"
                            clearable
                            placeholder="请输入开户人姓名"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="手机号" prop="mobile">
                        <el-input
                            @input="
                                () => {
                                    form.mobile = form.mobile.replace(
                                        /[^\d]/g,
                                        ''
                                    );
                                }
                            "
                            v-model="form.mobile"
                            clearable
                            placeholder="请输入开户人手机号"
                            maxlength="11"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <el-form-item label="身份证号" prop="idCard">
                        <el-input
                            v-model="form.idCard"
                            clearable
                            placeholder="请输入开户人身份证号"
                            maxlength="18"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: "openCardModel",
    props: {},
    data() {
        return {
            open: true,
            loading: false,
            form: {
                channelName: undefined,
                name: undefined,
                mobile: undefined,
                idCard: undefined,
            },
            rules: {
                channelName: [
                    {
                        required: true,
                        validator: async (rule, value, callback) => {
                            if (!value) {
                                callback(new Error("请输入渠道名称"));
                            } else {
                                callback();
                            }
                        },
                        trigger: "blur",
                    },
                ],
                name: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (!value) {
                                callback(new Error("请输入开户人姓名"));
                            } else if (
                                !/^([\u4e00-\u9fa5]{2,20}|[a-zA-Z.\s]{2,20})$/.test(
                                    value
                                )
                            ) {
                                callback(
                                    new Error("只允许输入2~20位中文或英文")
                                );
                            } else {
                                callback();
                            }
                        },
                        trigger: "blur",
                    },
                ],
                mobile: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (!value) {
                                callback(new Error("请输入开户人手机号"));
                            } else if (
                                !/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)
                            ) {
                                callback(new Error("请输入正确格式的手机号"));
                            } else {
                                callback();
                            }
                        },
                        trigger: "blur",
                    },
                ],
                idCard: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (!value) {
                                callback(new Error("请输入身份证号"));
                            } else if (
                                !/^[1-9]\d{5}(18|19|20|(3\d))\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(
                                    value
                                )
                            ) {
                                callback(new Error("请输入正确格式的身份证号"));
                            } else {
                                callback();
                            }
                        },
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    /** 事件监听 */
    watch: {},

    /** 计算属性 */
    computed: {},

    /** 生命周期 -- 实例创建后调用 */
    created() {},

    /** 生命周期 -- 实例挂载后调用 */
    mounted() {},

    /** 生命周期 -- 实例销毁后调用 */
    destroyed() {},

    methods: {
        edit() {
            this.reset(2);
        },

        cancel() {
            this.$emit("cancel");
        },

        // 表单重置
        reset(status) {
            this.form = {
                channelName: undefined,
                name: undefined,
                mobile: undefined,
                idCard: undefined,
            };
            this.$refs.form.resetFields();
            this.status = status;
            switch (status) {
                case 1: // 新增
                    break;
                case 2: // 编辑
                    break;
                case 3: // 详情
                    break;
                case 4: // 取消
                    break;
            }
        },

        /** 提交按钮 */
        submitForm: function () {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    this.loading = true;
                    this.$post(
                        "/admin-center-server/commonUser/add/channelUser",
                        this.form
                    )
                        .then((res) => {
                            this.loading = false;
                            this.$message.success(`${this.form.channelName}的平安账户开通成功`);
                            this.$emit("success");
                        })
                        .catch((err) => {
                            this.loading = false;
                        });
                }
            });
        },
    },
};
</script>
<style scoped type="scss">
.warning {
    color: #ff0000;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
}
.global-div-search {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    /* font-size: 14px; */
    /* color: #555; */
    position: relative;
}

.el-select {
    width: 100% !important;
}

.el-date-editor.el-input,
.el-date-editor.el-input__inner {
    width: 100% !important;
}
.el-input-number--medium {
    width: 100%;
}
</style>
