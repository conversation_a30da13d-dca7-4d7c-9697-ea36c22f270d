<template>
  <div class="app-container accountCancellation">
    <el-tabs v-model="activeName"
             @tab-click="handleClick">
      <el-tab-pane label="退款管理"
                   name="first">
        <div class="select-box">
          <div class="top-title">
            <div>
              筛选查询
            </div>
            <div>
              <el-button icon="el-icon-search"
                         type="primary"
                         @click="onSubmit"
                         size="mini">查询</el-button>
              <el-button icon="el-icon-delete"
                         type="danger"
                         @click="clearForm"
                         size="mini">清空筛选</el-button>
            </div>
          </div>
          <div class="select-info">
            <el-form :inline="true"
                     :model="formInline"
                     class="demo-form-inline"
                     label-width="140px"
                     size="mini">
              <el-form-item label="平台名称:">
                <el-input v-model="formInline.userName"
                          placeholder="请输入平台名称"></el-input>
              </el-form-item>
              <el-form-item label="订单状态："
                            label-width="100px"
                            prop='authenticateStatus'>
                <el-select v-model="formInline.authenticateStatus"
                           placeholder="请选择订单状态"
                           clearable
                           label-width="10px">
                  <el-option v-for="(item, lan) in statusWrap"
                             :label="item.name"
                             :value="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="创建日期:">
                <el-col>
                  <el-date-picker v-model="formInline.date"
                                  value-format=""
                                  type="datetimerange"
                                  range-separator="至"
                                  start-placeholder="选择开始时间"
                                  end-placeholder="选择结束时间">
                  </el-date-picker>
                </el-col>
              </el-form-item>
              <el-form-item label="司机名称:">
                <el-input v-model="formInline.driverName"
                          placeholder="请输入司机名称"></el-input>
              </el-form-item>
              <el-form-item label="司机电话:">
                <el-input v-model="formInline.driverTel"
                          oninput="value=value.replace(/[^\d]/g,'')"
                          maxlength="11"
                          placeholder="请输入司机电话"></el-input>
              </el-form-item>

            </el-form>
          </div>
        </div>
        <div class="list-box">
          <div class="list-title">
            <div>明细列表</div>
            <div>当前搜索结果总计：订单合计：{{allnum}}元</div>
          </div>
          <div class="list-main">
            <template>
              <el-table :data="tableData"
                        border
                        style="width: 100%">
                <el-table-column type="index"
                                 label="序号"
                                 width="50">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="driverName"
                                 label="司机姓名"
                                 width="140">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="driverPhone"
                                 label="司机手机号">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="platformName"
                                 label="消费平台">
                </el-table-column>
                <el-table-column prop="gasName"
                                 show-overflow-tooltip
                                 label="油站名称">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="orderAmount"
                                 width="130"
                                 label="订单金额（元）">
                </el-table-column>
                <el-table-column prop="payTypeText"
                                 show-overflow-tooltip
                                 label="订单状态">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="isStatementText"
                                 width="130"
                                 label="是否加入对账单">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="orderId"
                                 label="油气订单号">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="transactionSn"
                                 width="130"
                                 label="平台油气流水号">
                </el-table-column>
                <el-table-column prop="operateTime"
                                 show-overflow-tooltip
                                 label="操作日期">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="createTime"
                                 label="创建日期">
                </el-table-column>
              </el-table>
            </template>

          </div>
          <div class="paging">
            <div class="block">
              <el-pagination @size-change="handleSizeChange"
                             @current-change="handleCurrentChange"
                             :current-page="pageNumber"
                             :page-sizes="[10, 20, 30, 40,50,100]"
                             :page-size="pageSize"
                             layout="total, sizes, prev, pager, next, jumper"
                             :total="total">
              </el-pagination>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>

const listUserOilRefund = '/admin-center-server/userOilTransaction/listUserOilRefund'//油气退款
export default {
  data () {
    return {
      examineId: '',
      allnum: '',
      imgArr: [],
      statusWrap: [{
        name: '退款中',
        id: '5'
      }, {
        name: '已退款',
        id: '7'
      }, {
        name: '退款失败',
        id: '6'
      }],
      activeName: 'first',
      loading: false,
      agreeMask: false,
      dialogImg: false,
      examineImg: false,
      dialogPhoto: false,
      dialogVisible: false,
      examineMask: false,
      formInline: {
        userName: '',
        driverName: '',
        driverTel: '',
        authenticateStatus: "",
        date: ''
      },
      tableData: [],
      total: 0,
      pageSize: 10,
      pageNumber: 1,
      date: [],
    }
  },
  methods: {
    handleClick (tab, event) {
      console.log(tab, event);
    },
    closeViewer () {
      this.dialogImg = false;
      this.examineImg = false;
    },
    /** 清空筛选 **/
    clearForm () {
      this.formInline = {
        userName: '',
        driverName: '',
        driverTel: '',
        authenticateStatus: "",
        date: ''
      };
      this.date = [];
      this.getDataList()
    },
    /** 根据时间态搜索 **/
    selectTime () {
      this.formInline.operationTimeStart = this.date[0];
      this.formInline.operationTimeEnd = this.date[1];
    },
    /** 按条件查询 **/
    onSubmit () {
      this.pageNumber = 1
      this.getDataList()
    },
    handleSizeChange (val) {
      this.pageSize = val;
      this.pageNumber = 1;
      this.getDataList()
    },
    handleCurrentChange (val) {
      this.pageNumber = val;
      this.getDataList()
    },
    getDataList () {
      var v = this.formInline,
        data1 = '',
        data2 = '';
      if (v.date) {
        data1 = this.getDate(v.date[0])
        data2 = this.getDate(v.date[1])
      } else {
        data1 = ''
        data2 = ''
      }
      const url = `${listUserOilRefund}?pageNumber=${this.pageNumber}&pageSize=${this.pageSize}&creatEndTime=${data2}&creatStartTime=${data1}&driverName=${v.driverName}&driverPhone=${v.driverTel}&orderStatus=${v.authenticateStatus}&platformName=${v.userName}`
      this.$http.post(url).then(res => {
        let data = res.data;
        if (data.code === '200') {
          this.total = Number(data.data.listUserOilConsumerVO.total);
          let resData = data.data.listUserOilConsumerVO.list;
          this.allnum = data.data.totalConsumer
          this.tableData = resData;
        }
      })
    },
    /** 表格 点击查看图片列 **/
    handleRowClick (row) {
      this.dialogPhoto = true;
      this.imgArr = row.images
    },
    handleClose () {
      this.dialogVisible = false;
      this.examineMask = false;
      this.dialogPhoto = false;
    },
    handleClose1 () {
      this.agreeMask = false;
    },
  },
  activated () {
    this.getDataList()
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.accountCancellation {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      padding-right: 10px;
      border-bottom: 1px solid #cccccc;
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;
    overflow: hidden;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }

    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }

    .paging {
      margin-top: 10px;
      float: right;
    }
  }

  .pictures {
    padding: 0;
    margin: 0;

    li {
      display: inline-block;

      img {
        width: 100px;
        height: 100px;
        margin: 10px;
      }
    }
  }

  .photoList {
    li {
      display: inline-block;

      img {
        padding: 10px;
        background: #cfcfcf;
        width: 200px;
        margin: 20px;
      }
    }
  }

  .largePicture {
    margin-left: 5px;
    color: blue;
    cursor: pointer;
  }
}
</style>
