<template>
    <div class="app-container receipt">
        <div class="select-box">
            <div class="top-title">
                <div>筛选查询</div>
                <div class="button">
                    <el-button
                            class="left"
                            icon="el-icon-refresh-right"
                            size="mini"
                            type="success"
                            @click="()=>{this.$router.go(0)}"
                    >刷新
                    </el-button>
                </div>
            </div>
            <div class="select-info">
                <el-form
                        :inline="true"
                        :model="formInline"
                        class="demo-form-inline"
                        label-width="120px"
                        size="mini"
                >
                    <el-form-item label="代收人用户类型:">
                        <el-select v-model="formInline.agentType" placeholder="不限" style="width: 177px">
                            <el-option label="不限" value=""></el-option>
                            <el-option label="司机" value="3"></el-option>
                            <el-option label="调度员" value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="代收人:">
                        <el-input v-model="formInline.agentName" placeholder="请输入代收人姓名"></el-input>
                    </el-form-item>
                    <el-form-item label="代收人手机号:">
                        <el-input v-model="formInline.agentPhone" placeholder="请输入代收人手机号"></el-input>
                    </el-form-item>
                    <el-form-item label="运单编号:">
                        <el-input v-model="formInline.sn" placeholder="请输入运单编号"></el-input>
                    </el-form-item>
                </el-form>
                <el-form
                        :inline="true"
                        :model="formInline"
                        class="demo-form-inline"
                        label-width="120px"
                        size="mini"
                >
                    <el-form-item label="车牌号:">
                        <el-input v-model="formInline.carNumber" placeholder="请输入车牌号"></el-input>
                    </el-form-item>
                    <el-form-item label="司机姓名:">
                        <el-input v-model="formInline.driverName" placeholder="请输入司机姓名"></el-input>
                    </el-form-item>
                    <el-form-item label="订单编号:">
                        <el-input v-model="formInline.orderBusinessSn" placeholder="请输入订单编号"></el-input>
                    </el-form-item>
                    <el-form-item label="调度员单号:">
                        <el-input v-model="formInline.orderBrokerSn" placeholder="请输入调度员单号"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button class="left"
                                icon="el-icon-search"
                                size="mini"
                                type="primary"
                                @click="onSubmit">查询
                        </el-button>
                        <el-button class="left"
                                icon="el-icon-delete"
                                size="mini"
                                type="danger"
                                @click="clearForm">清空筛选
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="list-box">
            <div class="list-title">
                <div>数据列表</div>
            </div>
            <div class="list-main">
                <template>
                    <el-table
                            :data="tableData"
                            border
                            style="width: 100%">
                        <el-table-column
                                type="index"
                                label="序号"
                                width="50"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="agentName"
                                label="代收人"
                                width="120"
                                show-overflow-tooltip
                        >
                        </el-table-column>
                        <el-table-column
                                prop="agentTypeName"
                                label="代收人用户类型"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="agentPhone"
                                label="代收人手机号"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="sn"
                                label="运单编号"
                                show-overflow-tooltip
                                width="120">
                        </el-table-column>
                        <el-table-column
                                prop="carNumber"
                                label="车牌号"
                                show-overflow-tooltip
                                width="100">
                        </el-table-column>
                        <el-table-column
                                prop="driverName"
                                label="司机姓名"
                                show-overflow-tooltip
                                width="120">
                        </el-table-column>
                        <el-table-column
                                prop="agentAmount"
                                label="代收运费金额"
                                show-overflow-tooltip
                                width="120"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="orderBusinessSn"
                                label="关联订单"
                                show-overflow-tooltip
                        >
                        </el-table-column>
                        <el-table-column
                                prop="orderBrokerSn"
                                label="关联调度员单"
                                width="120"
                                show-overflow-tooltip
                        >
                        </el-table-column>
                        <el-table-column
                                fixed="right"
                                label="操作"
                                width="80">
                            <template slot-scope="scope">
                                <el-button @click="goDetail(scope.row)" type="text" size="small">查看</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
                <el-pagination @size-change="handleSizeChange"
                               @current-change="handleCurrentChange"
                               :current-page="pageNumber"
                               :page-sizes="[10,20, 40, 60, 80,100]"
                               :page-size="pageSize"
                               layout="total, sizes, prev, pager, next, jumper"
                               :total="total"
                               class="pagination"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                formInline: {
                    agentType: '',
                    agentName: '',
                    agentPhone: '',
                    sn: '',
                    carNumber: '',
                    driverName: '',
                    orderBusinessSn: '',
                    orderBrokerSn: '',
                },
                total: 1,
                pageNumber: 1,
                pageSize: 20,
                tableData: []
            }
        },
        methods: {
            goDetail(row) {
                let id  = row.id
                this.$router.push({
                    path: 'collectionDeatil',
                    query: {
                        id: id,
                    }
                })
            },
            selectTime() {

            },
            onSubmit() {
                this.pageNumber = 1
                this.getDataList()
            },
            clearForm() {
                this.formInline={
                    agentType: '',
                        agentName: '',
                        agentPhone: '',
                        sn: '',
                        carNumber: '',
                        driverName: '',
                        orderBusinessSn: '',
                        orderBrokerSn: '',
                }
                this.pageNumber=1;
                this. pageSize=20;
            },
            handleSizeChange(val) {
                this.pageNumber = 1;
                this.pageSize = val;
                this.getDataList()
            },
            handleCurrentChange(val) {
                this.pageNumber = val
                this.getDataList()
            },
            getDataList() {
                this.$http.get('/admin-center-server/orderItem/agentItemList', {
                    params: {
                        pageSize: this.pageSize,
                        pageNumber: this.pageNumber,
                        agentType: this.formInline.agentType,
                        agentName: this.formInline.agentName,
                        agentPhone: this.formInline.agentPhone,
                        sn: this.formInline.sn,
                        carNumber: this.formInline.carNumber,
                        driverName: this.formInline.driverName,
                        orderBusinessSn: this.formInline.orderBusinessSn,
                        orderBrokerSn: this.formInline.orderBrokerSn,
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.total = Number(data.data.total);
                        this.tableData = data.data.list;
                    }
                })
            }
        },
        activated() {
            this.getDataList()
        }
    }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
    .receipt {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
                display: flex;
                justify-content: space-between;

                .button {
                    margin-right: 20px;
                }
            }

            .select-info {
                padding-top: 30px;
                padding-bottom: 30px;
            }
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;

            .list-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                div {
                    font-size: 14px;
                    height: 30px;
                    line-height: 30px;
                }
            }

            .list-main {
                width: 100%;
                //   border: 1px solid #cccccc;
                margin-top: 10px;
            }

            .releaseMessage {
                margin-right: 20px;
            }

            .pagination {
                text-align: right;
                margin-top: 10px;
            }
        }
    }
</style>
