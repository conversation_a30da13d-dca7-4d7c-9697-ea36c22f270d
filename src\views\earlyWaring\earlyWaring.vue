<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">预警筛选查询</div>
      <div class="select-info">
        <el-form
          :inline="true"
          :model="formInline"
          ref="formInline"
          class="demo-form-inline"
          size="mini"
          label-width="90px"
        >
          <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="角色类型：">
            <el-select v-model="formInline.userType" placeholder="请选择" clearable>
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户名称:" prop="name">
            <el-input v-model="formInline.userName" placeholder="请输入用户名称"></el-input>
          </el-form-item>
          <el-form-item label="身份证号:" prop="tel">
            <el-input
              v-model="formInline.idCard"
              oninput="value=value.replace(/[^\d]/g,'')"
              maxlength="18"
              placeholder="请输入用户身份证号"
            ></el-input>
          </el-form-item>

          <el-form-item label="手机号:" prop="tel">
            <el-input
              v-model="formInline.mobile"
              oninput="value=value.replace(/[^\d]/g,'')"
              maxlength="11"
              placeholder="请输入手机号"
            ></el-input>
          </el-form-item>

          <el-form-item>
            <el-button class="left" icon="el-icon-search" @click="onSubmit">查询</el-button>
            <el-button class="left" @click="resetForm" icon="el-icon-refresh-right">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>账户列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table :data="tableData" border style="width: 100%">
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" prop="userTypeName" label="角色类型" width="200"></el-table-column>
            <el-table-column prop="userName" label="用户名称" width="200"></el-table-column>
            <el-table-column prop="idCard" label="身份证号" width="300"></el-table-column>
            <el-table-column prop="mobile" label="手机号"></el-table-column>
            <el-table-column prop="amount" label="年累计收入" width="200"></el-table-column>
            <el-table-column label="账户状态">
              <template slot-scope="scope">
                {{ scope.row.deleteFlag === '0' ? '启用中' : '停止'}}
              </template>
            </el-table-column>
            <el-table-column label="邮件预警">
              <template slot-scope="scope">
                {{ scope.row.emailWarningStatus === '0' ? '启用中' : '停止'}}
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="200">
              <template slot-scope="scope">
                <el-button @click="view(scope.row)" type="text">查看</el-button>
                <el-button v-if="scope.row.emailWarningStatus === '0'" @click="changeWarning(scope.row.id, 'stop')" type="text">停止邮件预警</el-button>
                <el-button v-else @click="changeWarning(scope.row.id, 'start')" type="text">开启邮件预警</el-button>
                <!-- <el-button @click="earlyWarningFun(scope.row)" type="text" size="small">
                  提醒
                  <span>(</span>
                  {{scope.row.remindNumber}}
                  <span>)</span>
                </el-button>
                <el-button
                  @click="forbidCars(scope.row)"
                  type="text"
                  size="small"
                  v-if="scope.row.isSendsomeone ==0&&scope.row.userType==2"
                >禁止派车</el-button>
                <el-button
                  @click="reCoverCars(scope.row)"
                  type="text"
                  size="small"
                  v-if="scope.row.isSendsomeone ==1&&scope.row.userType==2"
                >恢复派车</el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="forbidReceiveOrder(scope.row)"
                  v-if="scope.row.isOrder == 0"
                >禁止接单</el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="reCovereReceiveOrder(scope.row)"
                  v-if="scope.row.isOrder == 1"
                >恢复接单</el-button> -->
              </template>
            </el-table-column>
          </el-table>
        </template>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageNumber"
          :page-sizes="[10,20, 40, 60, 80,100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CarsList",
  data() {
    return {
      tableHeight: null, //表格的高度
      options: [
        {
          value: "2",
          label: "调度员"
        },
        {
          value: "3",
          label: "司机"
        },
        {
          value: "4",
          label: "车队长"
        }
      ],
      pageSize: 10,
      total: 0,
      pageNumber: 1,
      formInline: {
        userName: "", //用户名字
        idCard: "", //身份证号
        mobile: "", //手机号
        userType: "" //用户类型
      },
      tableData: []
    };
  },
  methods: {
    //重置
    resetForm(formName) {
      this.formInline = {
        userName: "", //用户名字
        idCard: "", //身份证号
        mobile: "", //手机号
        userType: "" //用户类型
      };
      this.pageNumber = 1;
      this.getData();
    },
    // 刷新当前页
    refreshfn() {
      this.$router.go(0);
    },

    onSubmit() {
      this.pageNumber = 1;
      this.getData();
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSizeChange(val) {
      this.pageNumber = 1;
      this.pageSize = val;
      this.getData();
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.getData();
      console.log(`当前页: ${val}`);
    },
    /* 获取列表数据 */
    getData() {
      let postData = this.formInline;
      this.$http
        .post(
          "/admin-center-server/earningsWarning/list",
          {
            pageNumber: this.pageNumber,
            pageSize: this.pageSize,
            idCard: this.formInline.idCard,
            mobile: this.formInline.mobile,
            userName: this.formInline.userName,
            userType: this.formInline.userType
          }
        )
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.tableData = data.data.list;
            this.total = JSON.parse(data.data.total);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 禁止派车 */
    forbidCars(row) {
      this.$confirm("是否对当前角色执行禁止派车?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })

        .then(() => {
          this.$http
            .post(
              "/admin-center-server/earningsWarning/isSendSomeOne?id=" +
                row.id +
                "&userId=" +
                row.userId +
                "&sendSomeOne=1"
            )
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message.success("操作成功");
                this.getData();
                this.warningNum(); //获取预警最新数据
              } else {
                this.$message.warning(data.message);
              }
            });

          //   this.$message({
          //     type: 'success',
          //     message: '删除成功!'
          //   });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消"
          });
        });
    },
    /* 恢复派车 */
    reCoverCars(row) {
      this.$confirm("是否对当前角色禁止接单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })

        .then(() => {
          this.$http
            .post(
              "/admin-center-server/earningsWarning/isSendSomeOne?id=" +
                row.id +
                "&userId=" +
                row.userId +
                "&sendSomeOne=0"
            )
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message.success("操作成功");
                this.getData();
                this.warningNum(); //获取预警最新数据
              } else {
                this.$message.warning(data.message);
              }
            });

          //   this.$message({
          //     type: 'success',
          //     message: '删除成功!'
          //   });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消"
          });
        });
    },
    /* 禁止接单 */
    forbidReceiveOrder(row) {
      this.$confirm("是否对当前角色执行恢复派车?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })

        .then(() => {
          this.$http
            .post(
              "/admin-center-server/earningsWarning/isOrder?id=" +
                row.id +
                "&userId=" +
                row.userId +
                "&isOrder=1"
            )
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message.success("操作成功");
                this.getData();
                this.warningNum(); //获取预警最新数据
              } else {
                this.$message.warning(data.message);
              }
            });

          //   this.$message({
          //     type: 'success',
          //     message: '删除成功!'
          //   });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消"
          });
        });
    },
    /* 恢复接单 */
    reCovereReceiveOrder(row) {
      this.$confirm("是否对当前角色执行恢复接单?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })

        .then(() => {
          this.$http
            .post(
              "/admin-center-server/earningsWarning/isOrder?id=" +
                row.id +
                "&userId=" +
                row.userId +
                "&isOrder=0"
            )
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message.success("操作成功");
                this.getData();
                this.warningNum(); //获取预警最新数据
              } else {
                this.$message.warning(data.message);
              }
            });

          //   this.$message({
          //     type: 'success',
          //     message: '删除成功!'
          //   });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消"
          });
        });
    },
    /* 预警 */
    earlyWarningFun(row) {
      this.$confirm("是否对当前角色发送提醒信息?", "提醒", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })

        .then(() => {
          this.$http
            .post("/admin-center-server/earningsWarning/remind?id=" + row.id)
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message.success("操作成功");
                this.getData();
                this.warningNum(); //获取预警最新数据
              } else {
                this.$message.warning(data.message);
              }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消"
          });
        });
    },

    /* 获取预警最新数量 */
    warningNum() {
      this.$http
        .get("/admin-center-server/earningsWarning/getnewNum")
        .then(res => {
          let data = Number(res.data.data);
          let type = 888;
          // this.getMenu(type, data);
        });
    },
    view(item) {
      let { userType, userId } = item //userType 3司机 4车队长
      let type = userType === '3' ? 1 : 2
      this.$router.push(`/driverAccount/accountDetails?id=${userId}&type=${type}`)
    },
    changeWarning(id, type) {
      let status = type === 'start' ? 0 : 1
      let text = type === 'start' ? '开启' : '停止'
      this.$confirm(`确定要${text}邮件预警吗`)  
        .then(() => {
          this.$http.post(`/admin-center-server/earningsWarning/updateEmailWarningStatus/${status}/${id}`)
            .then(res => {
              let data = res.data
              if (data.code === "200") {
                this.getData()
              } else {
                this.$message.error(data.message)
              }
            })
        })
    }
  },
  activated() {
    this.getData();
    this.warningNum(); //获取预警最新数据
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
