<template>
  <div class="app-container carDetail">
    <template>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="运单信息" name="first">
          <div class="main-box">
            <div class="base-info">
              <div class="title-box">
                <div>
                  <p>基本信息</p>
                </div>
                <div>
                  <el-button
                    size="mini"
                    type="primary"
                    @click="editOrder"
                    v-if="tableData[0].ydStatus == '21' || tableData[0].ydStatus == '22'"
                  >修改运单</el-button>
                  <el-button
                    size="mini"
                    type="primary"
                    @click="frozenOrder"
                    v-if="freezeStatus == 0"
                  >冻结运单</el-button>
                  <el-button v-if="isShowReject" @click="isRejectDialogShow = true" size="mini" type="primary">驳回运单</el-button>
                  <el-button
                    size="mini"
                    type="primary"
                    @click="dialogVisibleCancel= true"
                    v-if="freezeStatus == 1"
                  >操作</el-button>
                </div>
              </div>
              <div class="list-box">
                <detail-row>
                  <detail-col v-if="!$store.state.user.userInfo2.hasStandardModeFlag" :span="6" label="调度单号">
                    <span class="list-order-dispatch" v-if="tableData[0].orderDispatchSn" @click="openDispatchDetail">{{tableData[0].orderDispatchSn}}</span>
                    <span v-else>-</span>
                  </detail-col>
                  <detail-col :span="6" label="运单号" :value="tableData[0].sn"></detail-col>
                  <detail-col :span="6" label="创建人" :value="tableData[0].orderCreateUserName"></detail-col>
                  <detail-col :span="6" label="创建日期" :value="tableData[0].ydCreatedDate"></detail-col>
                  <detail-col :span="6" label="结束日期" :value="tableData[0].finishDate || '-'"></detail-col>
                  <detail-col v-if="!$store.state.user.userInfo2.hasStandardModeFlag" :span="6" label="订单类型" :value="tableData[0].typeParams"></detail-col>
                  <detail-col :span="6" label="接单司机">
                    {{ tableData[0].driverName }}
                    <!-- 除认证成功以外显示红色 -->
                    <span v-if="tableData[0].authStatusDTO && tableData[0].authStatusDTO.driverAuthStatus !== '2'" class="reject-text">（{{ tableData[0].authStatusDTO.driverAuthStatusStr }}）</span>
                  </detail-col>
                  <detail-col :span="6" label="接单司机电话" :value="tableData[0].driverPhone"></detail-col>
                  <detail-col :span="6" label="接单车辆">
                    {{ tableData[0].carNumber }}
                    <span v-if="tableData[0].authStatusDTO && tableData[0].authStatusDTO.carAuthStatus !== '1'" class="reject-text">（{{ tableData[0].authStatusDTO.carAuthStatusStr }}）</span>
                    <template v-if="tableData[0].authStatusDTO && tableData[0].authStatusDTO.secondCarNumber">
                      -
                      {{ tableData[0].authStatusDTO.secondCarNumber }}
                      <span v-if="tableData[0].authStatusDTO.secondCarAuthStatus !== '1'" class="reject-text">（{{ tableData[0].authStatusDTO.secondCarAuthStatusStr }}）</span>
                    </template>
                  </detail-col>
                  <detail-col :span="6" label="调度员" :value="tableData[0].dispatcherName || '-'"></detail-col>
                  <detail-col :span="6" label="调度员手机" :value="tableData[0].dispatcherPhone || '-'"></detail-col>
                  <detail-col :span="6" label="运力供应商" :value="tableData[0].brokerMasterName || '-'"></detail-col>

                  <detail-col :span="6" label="车辆类型" :value="tableData[0].carTypeStr"></detail-col>
                  <detail-col :span="6" label="车辆所有人">
                    {{ tableData[0].licenseOwner }} 
                    <el-tooltip v-if="tableData[0].licenseOwner === tableData[0].businessName" placement="bottom">
                      <i class="el-icon-warning-outline owner-icon"></i>
                      <template slot="content">
                        系统检测到“车辆所有人”与“货主名称”相同，请注意审核
                      </template>
                    </el-tooltip>
                  </detail-col>
                  <detail-col :span="6" label="车辆载重">
                    <template v-if="tableData[0].authStatusDTO && tableData[0].authStatusDTO.secondCapacityTonnage">
                      {{ tonFormatter(tableData[0].authStatusDTO.secondCapacityTonnage) }}
                    </template>
                    <template v-else>
                      {{ tonFormatter(tableData[0].capacityTonnage) }}
                    </template>
                  </detail-col>
                  <!-- <detail-col :span="6" label="含税运费单价" :value="unitMoneyFormatter(tableData[0].freightCalcType, tableData[0].taxFreight)"></detail-col> -->
                  <detail-col :span="6" label="运费单价" :value="unitMoneyFormatter(tableData[0].freightCalcType, tableData[0].freight)"></detail-col>
                  <detail-col :span="6" label="运单状态" :value="tableData[0].ydStatusEnum"></detail-col>
                  <detail-col :span="6" label="运输距离" :value="kmFormatter(tableData[0].actualDistance)"></detail-col>
                  <!-- <detail-col :span="6" label="最久待装车" :value="tableData[0].overTimeStr"></detail-col> -->
                  <detail-col :span="6" label="货物名称">
                    {{ tableData[0].cargoTypeClassificationValue + ' / ' + tableData[0].cargoType }}
                  </detail-col>
                  <!-- <detail-col :span="6" label="与货主约定运费" v-if="tableData[0].operationalPeoFlag === '1'" :value="unitMoneyFormatter(tableData[0].freightCalcType, tableData[0].platformConsignorFreight)"></detail-col> -->
                  <detail-col :span="6" label="在线签约状态">
                    {{ tableData[0].onLineSigningStatus }} <el-button type="text" v-if="tableData[0].signingUrl" @change="openUrl">（电子合同）</el-button>
                  </detail-col>
                  <detail-col :span="6" label="合作平台主体" :value="(tableData[0].baseName)"></detail-col>
                  <detail-col  :span="6" label="运力专员" v-if="tableData[0].operationalPeoFlag=='1'" :value="tableData[0].capacityPersonName || '-'"></detail-col>
                  <detail-col  :span="6" label="销售人员" v-if="tableData[0].operationalPeoFlag=='1'" :value="tableData[0].salePersonName || '-'"></detail-col>
                  <detail-col :span="6" label="交易凭证">
                    <template>
                      <el-button v-if="tableData[0].paymentStatementUrl" type="text" @click="() => showedPic =tableData[0].paymentStatementUrl">查看</el-button>
                      <span v-else>-</span>
                    </template>
                  </detail-col>
                  <detail-col  :span="24" label="运输合同" value="">
                    <template>
                      <el-button v-if="tableData[0].transportationContractUrl" @click="openFn(tableData[0].transportationContractUrl)" type="text" size="small">{{ tableData[0].transportationContractName }}</el-button>
                      <span v-else>-</span>
                    </template>
                  </detail-col>
                </detail-row>
              </div>
            </div>
            <div class="drive-info">
              <div class="title-box">
                <div>收发信息</div>
              </div>
              <div v-if="tableData[0].operationalPeoFlag != 1" class="list-box">
                <detail-row>
                  <!-- <detail-col :span="6" label="装车点" :value="tableData[0].deliveryPlace"></detail-col> -->
                  <detail-col :span="6" label="装车点" :value="tableData[0].orderCommonDeliveryPlace"></detail-col> 
                  <detail-col :span="6" label="发货联系人" :value="tableData[0].consignerName"></detail-col>
                  <detail-col :span="6" label="发货人电话" :value="tableData[0].consignerPhone"></detail-col>
                  <detail-col :span="6" label="装货时间" :value="tableData[0].gpsStartTime">
                    {{tableData[0].gpsStartTime}}{{tableData[0].loadingCheckInType ? '(' + tableData[0].loadingCheckInType + ')' : ''}}
                  </detail-col>
                  <!-- <detail-col :span="6" label="卸货点" :value="tableData[0].receivePlace"></detail-col> -->
                  <detail-col :span="6" label="卸货点" :value="tableData[0].orderCommonReceivePlace"></detail-col> 
                  <detail-col :span="6" label="收货联系人" :value="tableData[0].consigneeName"></detail-col>
                  <detail-col :span="6" label="收货人电话" :value="tableData[0].consigneePhone"></detail-col>
                  <detail-col :span="6" label="卸货时间" :value="tableData[0].gpsEndTime">
                    {{tableData[0].gpsEndTime}}{{tableData[0].unloadCheckInType ? '(' + tableData[0].unloadCheckInType + ')' : ''}}
                  </detail-col>
                </detail-row>
              </div>
              <OperaterItemDetail v-else :tableData="tableData"></OperaterItemDetail>
            </div>

            <div class="driver-info">
              <div class="title-box">
                <div>费用信息</div>
              </div>
              <costInfo v-if="tableData[0].operationalPeoFlag !== '1'" :tableData="tableData" :ruleData="ruleData"></costInfo>
              <cost-operation-info v-else :tableData="tableData" :ruleData="ruleData" :orderDetail='orderDetail'></cost-operation-info>

              <!-- 未开启标准模式&&不是运营单 -->
              <div v-if="!$store.state.user.userInfo2.hasStandardModeFlag && tableData[0].statusBd == 1 && tableData[0].operationalPeoFlag != 1" class="driver-oil">
                <!-- <el-button @click="dialogDriverBuyOil = true">补足司机油费</el-button> -->
                <el-button class="return-oil" @click="returnOilCostDialog = true">退还货主油费</el-button>
              </div>
            </div>
            <div class="other-info" v-if="tableData[0].operationalPeoFlag != 1">
              <div class="title-box">
                <div>收货信息</div>
              </div>
              <div class="list-box">
                <detail-row>
                  <detail-col :span="12" :label="tableData[0].freightCalcType === '0' ? '装货磅单' : '装货凭证'" class="img-view">
                    <img v-for="item in loadUrlList" :src="item" @click="() => showedPic = item" class="thumbnail">
                  </detail-col>
                  <detail-col :span="12" :label="tableData[0].freightCalcType === '0' ? '卸货磅单' : '卸货凭证'" class="img-view">
                    <img v-for="item in unloadUrlList" :src="item" @click="() => showedPic = item" class="thumbnail">
                  </detail-col>
                </detail-row>
                <detail-row>
                  <detail-col label="装货吨数" :value="tableData[0].originalTon?tableData[0].originalTon + '吨':'-'"></detail-col>
                  <detail-col label="卸货吨数" :value="tableData[0].currentTon?tableData[0].currentTon+'吨':'-'"></detail-col>
                  <detail-col v-if="tableData[0].freightCalcType == 0" label="结算吨数" :value="tableData[0].payTon?tableData[0].payTon + '吨':'-'"></detail-col>
                </detail-row>
              </div>
            </div>

            <div class="driver-info" v-if="tableData[0].operationalPeoFlag != 1">
              <div class="title-box">
                <div>运单备注</div>
              </div>
              <div class="list-box">
                <detail-row>
                  <detail-col :span="24" label="运单备注" :value="tableData[0].memo"></detail-col>
                </detail-row>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="订单信息" name="second">
          <OrderDetail :tableData="orderDetail" :ruleData="ruleData" :showCustomerName="true"></OrderDetail>
        </el-tab-pane>
        <el-tab-pane label="调度员单信息" name="dispatch" v-if="tableData[0].type === '1'">
          <Dispatch :tableData="orderBrokerList" :showCustomerName="true"></Dispatch>
        </el-tab-pane>
        <el-tab-pane v-if="false" label="电子路单信息" name="third">
          <div class="main-box">
            <div class="base-info">
              <div class="title-box">
                <div>当前运单状态：{{statusEnum}}</div>
              </div>

              <div class="list-box">
                <el-table :data="tableData" border style="width: 100%">
                  <el-table-column prop label="电子路单状态" width="50"></el-table-column>
                  <el-table-column prop label="发送时间"></el-table-column>
                  <el-table-column prop label="报文信息"></el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="交易明细" name="fourth">
          <div class="main-box">
            <div class="base-info">
              <div class="transaction-detail">
                <span class="detail-value">应收货主：{{orderItemTransactionInfo.shipperReceivable}}</span>
                <span class="detail-value">实收货主：{{orderItemTransactionInfo.shipperReceipts}}</span>
                <span class="detail-value">应付司机：{{orderItemTransactionInfo.driverPayable}}</span>
                <span class="detail-value">实付司机：{{orderItemTransactionInfo.driverActualPayment}}</span>
              </div>
              <div class="list-box">
                <template>
                  <el-table 
                  :data="orderItemTransactionInfo.orderItemTransactionInfoVOList" 
                  border 
                  style="width: 100%"
                  cell-class-name="table_cell_gray"
                  header-cell-class-name="table_header_cell_gray"
                  :header-cell-style="{height:'35px'}">
                    <el-table-column prop="time" label="交易时间"></el-table-column>
                    <el-table-column prop="typeName" label="交易类型"></el-table-column>
                    <el-table-column prop="amount" label="交易金额" min-width="100">
                      <template slot-scope="scope">
                          <span v-if="scope.row.type=='4' && orderItemTransactionInfo.operationalPeoFlag!='1'">{{ amountAllFn(scope.row) }}</span>
                          <span v-else>{{scope.row.amount}}</span>
                          <div v-if="scope.row.type=='4' && orderItemTransactionInfo.operationalPeoFlag!='1'">(含服务费:{{scope.row.serviceCharge}})</div> 
                      </template>
                    </el-table-column>
                    <el-table-column prop="payerName" label="付款人"></el-table-column>
                    <el-table-column prop="outUserSn" label="付款账号"></el-table-column>
                    <el-table-column prop="payerBankName" label="开户行">
                    </el-table-column>
                    <el-table-column prop="payeeName" label="收款人"></el-table-column>
                    <el-table-column prop="inUserSn" label="收款账号">
                    </el-table-column>
                    <el-table-column prop="payeeBankName" label="开户行">
                    </el-table-column>
                    <el-table-column prop="transactionSn" label="流水号"></el-table-column>
                  </el-table>
                </template>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="运单日志" name="fifth">
          <div class="main-box">
            <div class="base-info">
              <div class="title-box">
                <div>当前运单状态：{{statusEnum}}</div>
              </div>
              <div class="list-box">
                <template>
                  <el-table :data="orderItemLogList" border style="width: 100%" 
                    tooltip-effect="dark"
                    cell-class-name="table_cell_gray"
                    header-cell-class-name="table_header_cell_gray"
                  >
                    <el-table-column prop="statusEnum" label="运单状态"></el-table-column>
                    <el-table-column prop="operator" label="操作人"></el-table-column>
                    <el-table-column prop="memo" label="备注"></el-table-column>
                    <el-table-column prop="createdDate" label="操作日期"></el-table-column>
                  </el-table>
                </template>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="行驶轨迹" name="sixth">
          <Track ref="track"></Track>
          <!-- <div class="main-box">
            <div class="base-info">
              <div class="title-box">
                <div>当前运单状态：{{statusEnum}}</div>
                <div class="source">轨迹来源：{{ source }}</div>
              </div>
              <div>
                <div class="amap-page-container">
                  <div id="amap-show" class="amap-demo" style="height: 600px;"></div>
                </div>
              </div>
            </div>
          </div> -->
        </el-tab-pane>
      </el-tabs>

      <!--  冻结运单弹窗 -->
      <el-dialog :title="reasonText" :visible.sync="dialogForCancel">
        <el-form :model="form">
          <el-form-item :label="reasonText" :label-width="formLabelWidth" required>
            <el-select v-model="reasonId" placeholder="请选择原因">
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.content"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="其他原因" :label-width="formLabelWidth">
            <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="reasonMemo"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogForCancel = false">取 消</el-button>
          <el-button type="primary" @click="fronzeSure" v-if="type == 0">确 定</el-button>
          <el-button type="primary" @click="cancelSure" v-if="type ==1">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 运单被冻结后 -->
      <el-dialog
        title="操作选择"
        :visible.sync="dialogVisibleCancel"
        width="30%"
        :before-close="handleClose"
      >
        <div>
          <el-form ref="handleCancelform" :model="handleCancelform">
            <el-form-item label="请选择对运单的操作" required>
              <br />
              <el-radio-group v-model="handleCancelform.resource">
                <el-radio label="1">解冻运单</el-radio>
                <el-radio label="2">取消运单</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="备注" required>
              <el-input type="textarea" v-model="handleCancelform.desc"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisibleCancel = false">取 消</el-button>
          <el-button type="primary" @click="cancelClick">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 修改时间的弹窗 -->
      <el-dialog title="修改时间" :visible.sync="dialogForTime" :before-close="handleClose">
        <div>
          <el-form>
            <div v-if="createdDate">
              <el-form-item label="当前运单创建日期：">
                <span>{{createdDate}}</span>
              </el-form-item>
              <el-form-item label="修改后运单创建日期：">
                <el-date-picker
                  v-model="changeCreatedDate"
                  type="datetime"
                  placeholder="选择日期时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
              </el-form-item>
            </div>

            <div v-if="gpsStartTime">
              <el-form-item label="当前运单装货日期：">
                <span>{{gpsStartTime}}</span>
              </el-form-item>
              <el-form-item label="修改后运单装货日期">
                <el-date-picker
                  v-model="changeGpsStartTime"
                  type="datetime"
                  placeholder="选择日期时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
              </el-form-item>
            </div>

            <div v-if="gpsEndTime">
              <el-form-item label="当前运单卸货日期：">
                <span>{{gpsEndTime}}</span>
              </el-form-item>
              <el-form-item label="修改后运单卸货日期：">
                <el-date-picker
                  v-model="changeGpsEndTime"
                  type="datetime"
                  placeholder="选择日期时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
              </el-form-item>
            </div>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogForTime = false">取 消</el-button>
          <el-button type="primary" @click="changeTimeSure">确 定</el-button>
        </span>
      </el-dialog>

      <el-image-viewer v-if="showedPic" :url-list="[showedPic]" :on-close="() => showedPic = null"/>

      <!-- 撤销 收货的弹窗-->
      <el-dialog
        title="提示"
        :visible.sync="cancelReceiveDialog"
        :before-close="handleClose"
        width="30%"
      >
        <span>{{cancelText}}</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="cancelReceiveDialog = false">取 消</el-button>
          <el-button type="primary" @click="cancelReceive(1)" v-if="cancelText == '确认撤销货主收货?'">确 定</el-button>
          <el-button type="primary" @click="cancelReceive(2)" v-if="cancelText == '确认撤销调度员收货?'">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 查看分油凭证的弹框 -->
      <el-dialog title="分油凭证" :visible.sync="dialogOilStatus">
        <div class="oil_images" v-for="(image, index) of oilImageUrls" :key="index">
          <div>分油凭证{{index + 1}}.jpg</div>
          <div @click="checkOilImage(image)" style="color:#ED970F; margin-left: 10px">查看</div>
        </div>
       
      </el-dialog>
      <el-dialog
        :visible.sync="isRejectDialogShow"
        title="驳回原因"
        width="700px">
        <el-form :model="form" label-width="120px">
          <el-form-item label="请选择驳回原因">
            <el-checkbox-group v-model="rejectSelectedReason">
              <div v-for='item in rejectReasonList' :key="item.id">
                <el-checkbox :label="item" >{{ item.content }}</el-checkbox>
              </div>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="其他原因">
            <el-input
              type="textarea"
              :rows="3"
              placeholder="请输入其他驳回原因"
              v-model="rejectOtherReason"
              resize="none">
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="isRejectDialogShow = false">取消</el-button>
            <el-button type="primary" @click="reject">确认驳回</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
      <!-- 扣除司机油费 -->
      <el-dialog title="补足司机油费" :visible.sync="dialogDriverBuyOil" width="30%">
        <div>
          <div class="driver-oil-hint">ⓘ用于将“运单收款方”钱包的金额转账至“平台油品账户”，以便增加当前运单的油费，操作完成后需平台分油专员到【油管理 - 油分配】为当前司机分油</div>
          <div>运单收款方信息</div>
          <div class="driver-oil-item">{{'姓名：' + tableData[0].agentName + " "}}</div>
          <div class="driver-oil-item">{{'手机号：' + tableData[0].agentMobile}}</div>
          <div class="driver-oil-item">平安子账户号：{{tableData[0].agentSubAcctNo}}</div>
        </div>
        <div class="dirver-oil-value">
          <span>转账银行（元）：</span>
          <el-input 
            style="width:200px"
            v-model="driverOilValue"
            placeholder="请输入金额"
            type="number"
            oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>1000000)value=1000000;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);">
          ></el-input>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogDriverBuyOil = false">取 消</el-button>
          <el-button type="primary" :disabled="driverOilConfrimButtonDisabled" @click="driverBuyOil">确 定</el-button>
        </span>
      </el-dialog>
      <el-dialog
        title="退还货主油费"
        :visible.sync="returnOilCostDialog"
        width="30%">
        <span>退还油费后，运单会逻辑删除，确定将货主已支付的{{payTableData[0] ? payTableData[0].oilCost : ''}}元油费退还至货主钱包？</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="returnOilCostDialog = false">取 消</el-button>
          <el-button type="primary" @click="confirmReturnOilCost">确 定</el-button>
        </span>
      </el-dialog>
      <el-dialog
        title="修改运单"
        :visible.sync="editDialog"
        width="500px">
        <el-form :model="editForm" ref="editForm" label-width="120px" :rules="editFormRules">
          <el-form-item label="订单创建时间">
            {{ editForm.createdDate }}
          </el-form-item>
          <el-form-item label="运单创建时间" prop="orderCreatedDate">
            <el-date-picker
              v-model="editForm.orderCreatedDate"
              type="datetime"
              placeholder="选择日期时间"
              style="width: 100%;"
              value-format="yyyy-MM-dd HH:mm:ss"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="装货打卡时间" prop="gpsStartTime">
            <el-date-picker
              v-model="editForm.gpsStartTime"
              type="datetime"
              placeholder="选择日期时间"
              style="width: 100%;"
              value-format="yyyy-MM-dd HH:mm:ss"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="装货地址" prop="originalTonLocationName">
            <el-input v-model="editForm.originalTonLocationName" placeholder="请输入修改后的装货打卡地址"></el-input>
          </el-form-item>
          <el-form-item label="卸货打卡时间" prop="gpsEndTime">
            <el-date-picker
              v-model="editForm.gpsEndTime"
              type="datetime"
              placeholder="选择日期时间"
              style="width: 100%;"
              value-format="yyyy-MM-dd HH:mm:ss"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="卸货地址" prop="unloadLocationName">
            <el-input v-model="editForm.unloadLocationName" placeholder="请输入修改后的卸货打卡地址"></el-input>
          </el-form-item>
          <el-form-item label="货主收货时间">
            {{ editForm.receivingTime }}
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="editDialog = false;editForm={}">取 消</el-button>
          <el-button type="primary" @click="editSubmit" :loading="editLoading">确 定</el-button>
        </span>
      </el-dialog>
    </template>
    <el-image-viewer style="z-index: 10000" v-if="showImageUrl" :on-close="closeOilViewer" :url-list="[showImageUrl]"></el-image-viewer>

  </div>
</template>
<script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import { Big } from 'big.js'
import Track from '@/components/Track/Track.vue'
import costInfo from '../components/costInfo.vue'
import costOperationInfo from '../components/costOperationInfo.vue';
import OperaterItemDetail from '@/components/OrderDetail/OperaterItemDetail.vue';
export default {
  components: { 
    ElImageViewer,
    Track,
    costInfo,
    costOperationInfo,
    OperaterItemDetail
  },
  data() {
    return {
      itemSn: "", //运单编号
      originalTonImageUrl: "", //装货磅单地址
      dischargeCargoImageUrl: "", //卸货磅单地址
      cancelReceiveDialog: false, //撤销收货的弹窗

      handleCancelform: {
        resource: "1",
        desc: ""
      },
      createdDate: "", //但前运单创建日期
      gpsStartTime: "", //当前运单装货时间
      gpsEndTime: "", //但前运单卸货时间
      ydStatus: "", //运单状态
      //修改后
      changeCreatedDate: "",
      changeGpsStartTime: "",
      changeGpsEndTime: "",
      dialogVisibleCancel: false,
      dialogForTime: false, //修改时间
      reasonText: "", //原因文本
      activeName: "first",
      dialogForCancel: false, // 取消订单
      statusEnum: "", //运单状态
      textarea: "", //其他原因
      baseInfo: {},
      form: {
        name: "",
        region: "",
        date1: "",
        date2: "",
        delivery: false,
        type: [],
        resource: "",
        desc: ""
      },
      options: [],
      dialogImageUrl: "",
      driverId: "", //司机id
      reasonId: "", //原因ID
      formLabelWidth: "120px",
      tableData: [{}],
      orderDetail: [{}], //订单信息
      orderItemLogList: [], //运单日志
      orderBrokerList: [{}],
      reasonMemo: "", //其他原因
      freezeStatus: "", // 冻结的状态
      status: "", //是否取消的状态
      type: "", //点击的是取消活着冻结
      cancelText:'',
      source: '',
      payTableData: [],
      dialogOilStatus: false,
      showImageUrl: '',
      oilImageUrls: [],

      isRejectDialogShow: false,
      rejectReasonList: [],
      rejectOtherReason: '',
      rejectSelectedReason: [],

      showedPic: null,
      ruleData: [],
      returnOilCostDialog: false,
      dialogDriverBuyOil: false,
      driverOilValue: '',
      driverOilConfrimButtonDisabled: false,
      orderItemTransactionInfo: {},
      editDialog: false,
      editForm: {},
      editLoading: false,
      editFormRules: {
        originalTonLocationName: [
          { required: true, message: "请输入装货地址", trigger: "blur" }
        ],
        unloadLocationName: [
          { required: true, message: "请输入卸货地址", trigger: "blur" }
        ],
        orderCreatedDate: [
          {
            validator: (rule, value, callback) => {
              if (this.editForm.orderCreatedDate && new Date(this.editForm.orderCreatedDate).getTime() < new Date(this.editForm.createdDate).getTime()) {
                callback(new Error("不可早于订单创建时间  "+this.editForm.createdDate));
              }else if (this.editForm.orderCreatedDate && new Date(this.editForm.gpsStartTime).getTime() < new Date(this.editForm.orderCreatedDate).getTime()) {
                callback(new Error("不可晚于装货打卡时间  "+this.editForm.gpsStartTime));
              }else  {
                callback();
              }
            },
            trigger: "blur"
          }
        ],
        gpsStartTime: [
          {
            validator: (rule, value, callback) => {
              if(this.editForm.gpsStartTime){
                if (this.editForm.orderCreatedDate && new Date(this.editForm.gpsStartTime).getTime() < new Date(this.editForm.orderCreatedDate).getTime()) {
                  callback(new Error("不可早于运单创建时间  "+this.editForm.orderCreatedDate));
                }else if(this.editForm.oilPayTime && new Date(this.editForm.gpsStartTime).getTime() > new Date(this.editForm.oilPayTime).getTime()) {
                  callback(new Error("不可晚于油费付时间  "+this.editForm.oilPayTime));
                }else {
                  callback();
                }
              }else{
                callback();
              }
            },
            trigger: "blur"
          }
        ],
        gpsEndTime: [
          {
            validator: (rule, value, callback) => {
              if(this.editForm.gpsEndTime){
                //if (new Date(this.editForm.gpsEndTime).getTime() < new Date(this.editForm.oilPayTime).getTime()) {
                //callback(new Error("不可早于油费付时间  "+this.editForm.oilPayTime));
                //}else 
                if(this.editForm.receivingTime && new Date(this.editForm.gpsEndTime).getTime() > new Date(this.editForm.receivingTime).getTime()) {
                  callback(new Error("不可早于货主收货时间  "+this.editForm.receivingTime));
                }else{
                  callback();
                }
              }else{
                callback();
              }
            
            },
            trigger: "blur"
          }
        ]
      }
    };
  },
  activated() {
    this.getDetail();
  },
  methods: {
    openFn(url){
      window.open(url)
    },
    amountAllFn(row){
      return Big(Number(row.amount)).plus(Number(row.serviceCharge)).toFixed(2)
    },
    handleClose() {
      this.dialogVisibleCancel = false;
      this.dialogForTime = false;
      this.cancelReceiveDialog = false;
    },
    cancelClick() {
      let type = this.handleCancelform.resource;
      if (type === "1") {
        let freezeStatus = false;
        let orderItemId = this.$route.query.orderItemId;
        let reasonMemo = this.handleCancelform.desc;
        if (reasonMemo == "") {
          this.$message.warning("请填写备注");
          return;
        }
        this.$http
          .post(
            "/admin-center-server/orderItem/freezeOrder?freezeStatus=" +
              freezeStatus +
              "&orderItemId=" +
              orderItemId +
              "&reasonMemo=" +
              reasonMemo +
              "&reasonId=-1"
          )
          .then(res => {
            let data = res.data;
            if (data.code == 200) {
              this.$message.success("解冻成功");
              this.dialogVisibleCancel = false;
              this.getDetail(); //请求详情更新状态
            } else {
              this.$message.warning(res.data.message);
            }
          });
      } else if (type === "2") {
        let freezeStatus = false;
        let orderItemId = this.$route.query.orderItemId;
        let reasonMemo = this.handleCancelform.desc;
        if (reasonMemo == "") {
          this.$message.warning("请填写备注");
          return;
        }
        this.$http
          .post(
            "/admin-center-server/orderItem/cancelOrderItem?freezeStatus=" +
              freezeStatus +
              "&orderItemId=" +
              orderItemId +
              "&reasonMemo=" +
              reasonMemo +
              "&reasonId=-1"
          )
          .then(res => {
            let data = res.data;
            if (data.code == 200) {
              this.$message.success("取消成功");
              this.dialogVisibleCancel = false;
              this.getDetail(); //请求详情更新状态
              this.$router.go(-1);
            } else {
              this.$message.warning(res.data.message);
            }
          });
      }
    },
    // 修改运单
    editOrder() {
      console.log('this.tableData',this.tableData[0]);
      console.log('this.orderDetail',this.orderDetail[0]);
      this.editForm = {
        id: this.tableData[0].orderItemId,
        sn: this.tableData[0].sn,
        createdDate: this.orderDetail[0].createdDate,
        orderCreatedDate: this.tableData[0].ydCreatedDate,
        gpsStartTime:this.tableData[0].gpsStartTime,
        originalTonLocationName:this.tableData[0].originalTonLocationName,
        gpsEndTime:this.tableData[0].gpsEndTime,
        unloadLocationName:this.tableData[0].unloadLocationName,
        receivingTime:this.tableData[0].receivingTime,
        oilPayTime:this.tableData[0].oilPayTime,
      }
      console.log('this.editForm',this.editForm);
      this.editDialog = true;
      this.$nextTick(()=>{
        this.$refs.editForm.clearValidate();
      })
    },
    editSubmit() {
      console.log(this.editForm);
      this.$refs.editForm.validate(vaild => {
        if(!vaild) return;
        this.editLoading=true;
        let data = {
          id:this.editForm.id,
          sn:this.editForm.sn,
          orderCreateTime:this.editForm.createdDate,
          itemCreateTime:this.editForm.orderCreatedDate,
          loadingTime:this.editForm.gpsStartTime,
          loadingAddress:this.editForm.originalTonLocationName,
          unLoadingTime:this.editForm.gpsEndTime,
          unLoadingAddress:this.editForm.unloadLocationName,
          receivingTime:this.editForm.receivingTime,
          oilPayTime:this.editForm.oilPayTime,
        }
        this.$http
          .post("/admin-center-server/orderItem/editItemLoadingAndUnloadingInfo", data)
          .then(res => {
              let data = res.data;
              this.editLoading = false;
              if (data.code == 200) {
                this.$message.success("修改成功");
                this.editDialog = false;
                this.getDetail(); //请求详情更新状态
              } else {
                this.$message.warning(res.data.message);
              }
            }).catch(()=>{
              this.editLoading = false;
            })
      })
    },
    /* 冻结运单 */
    frozenOrder() {
      this.reasonText = "冻结运单原因";
      this.dialogForCancel = true;
      this.type = 0;
      this.reasonlist(); //获取冻结原因
    },

    /* 取消订单 */
    cancelOrder() {
      this.reasonText = "取消运单原因";
      this.dialogForCancel = true;
      this.type = 1;
      this.getReasonList(); //获取取消原因列表
    },

    /* 冻结订单的原因列表 */
    reasonlist() {
      this.dialogForCancel = true;
      this.$http
        .get("/admin-center-server/order/reasonlist", {
          params: {
            reasonType: 1
          }
        })
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.options = data.data;
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },
    /* 获取运单详情 */
    getDetail() {
      let orderItemId = this.$route.query.orderItemId;
      this.$http
        .get("/admin-center-server/orderItem/getOrderItemDetail", {
          params: {
            OrderItemId: orderItemId //后台给的key 首字母是大写
          }
        })
        .then(res => {
          let data = res.data;
          if (data.code == 200) {
            this.tableData = data.data.orderItemByOrderBrokerDetail;
            this.orderDetail = data.data.orderItemByOrderBusinessDetail;
            this.orderBrokerList = data.data.orderBrokerList
            this.statusEnum =
              data.data.orderItemByOrderBrokerDetail[0].ydCurrentStatusParams;
            this.orderItemLogList = data.data.orderItemLogList; //运单日志
            this.freezeStatus =
              data.data.orderItemByOrderBrokerDetail[0].freezeStatus;
            this.ydStatus = data.data.orderItemByOrderBrokerDetail[0].ydStatus; //运单状态
            this.driverId = data.data.orderItemByOrderBrokerDetail[0].driverId; //司机ID
            this.itemSn = data.data.orderItemByOrderBrokerDetail[0].sn; //运单编号
            this.originalTonImageUrl =
              data.data.orderItemByOrderBrokerDetail[0].originalTonImageUrl; //装货磅单
            this.dischargeCargoImageUrl =
              data.data.orderItemByOrderBrokerDetail[0].dischargeCargoImageUrl; //卸货磅单

            if (!this.tableData[0].invoiceOrderItemDetails) return
            let payTableData = [...this.tableData[0].invoiceOrderItemDetails]

            //按照装货、卸货、结算排序
            let _payTableData = []
            ;['20', '3', '6'].forEach(v => {
              let item = payTableData.find(item => item.orderItemStatus === v)
              if (!item) return
              _payTableData.push(item)
            })
            payTableData = _payTableData
            
            let countSum = key => {
              let sum = payTableData.reduce((prev, current) => {
                return Big(prev).plus(current[key] || 0).toNumber()
              }, 0)
              return '合计：' + String(sum)
            }
            payTableData.push({
              amountToDriver: countSum('amountToDriver'),
              oilCost: countSum('oilCost'),
              actualPlatformServiceAmount: countSum('actualPlatformServiceAmount'),
              paySum: countSum('paySum')
            })
            this.payTableData = payTableData

            if (this.tableData[0].allocationOilProofUrl != null) {
              this.oilImageUrls = this.tableData[0].allocationOilProofUrl.split(',')
            }
          } else {
            this.$message.warning(data.message);
          }

          let rejectCode = data.data.orderItemByOrderBrokerDetail[0].dataFrom === '0' ? 8 : 9
          this.$post('/order-center-server/app/reason/queryReasonByType?type=' + rejectCode)
            .then(res => {
              this.rejectReasonList = res
            })
          this.$get(`/admin-center-server/rule/getRuleRormulatianById?id=${this.tableData[0].ruleId}&isOperated=${this.tableData[0].operationalPeoFlag}`).then(
            res => {
              this.ruleData = res
            }
          )
          this.$get('/admin-center-server/settlement/queryOrderItemTransactionInfo', {}, {params: {orderItemId: orderItemId, orderItemSn: this.tableData[0].sn}}).then(
            res => {
              this.orderItemTransactionInfo = res
            }
          )

        });
    },
    handleClick(row) {
      if (row.label == "行驶轨迹") {
        this.$refs.track.init({
          waybillData: this.tableData[0],
          orderData: this.orderDetail[0],
          isWaybillDetail: true,
          isNavigation: false,
          isShowTab:true
        })
      }
    },
    /* 获取原因 */
    getReasonList() {
      this.$http
        .get("/admin-center-server/order/reasonlist?reasonType=2")
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.options = data.data;
          } else {
            this.$message.warning(data.message);
          }
        });
    },

    /* 冻结确定 */
    fronzeSure() {
      var freezeStatus;
      if (this.reasonId == "") {
        this.$message.warning("请选择原因");
        return;
      }
      if (this.freezeStatus == 0) {
        freezeStatus = true;
      } else {
        freezeStatus = false;
      }
      let postData = {
        orderItemId: Number(this.$route.query.orderItemId),
        reasonId: Number(this.reasonId),
        reasonMemo: this.reasonMemo,
        freezeStatus: freezeStatus
      };
      this.$http
        .post(
          "/admin-center-server/orderItem/freezeOrder",
          this.$qs.stringify(postData)
        )
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.dialogForCancel = false;
            this.getDetail();
            // this.$router.go(-1);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 取消的确定 */
    cancelSure() {
      if (this.reasonId == "") {
        this.$message.warning("请选择原因");
        return;
      }
      let postData = {
        orderItemId: Number(this.$route.query.orderItemId),
        reasonId: Number(this.reasonId),
        reasonMemo: this.reasonMemo
      };
      this.$http
        .post(
          "/admin-center-server/orderItem/cancelOrderItem",
          this.$qs.stringify(postData)
        )
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "取消成功!"
            });
            this.$router.go(-1);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 修改时间 */
    changeTime() {
      this.dialogForTime = true;
      //获取时间回显数据
      let orderItemId = this.$route.query.orderItemId;
      this.$http
        .post(
          "/admin-center-server/orderItem/showTime?orderItemId=" + orderItemId
        )
        .then(res => {
          console.log(res);
          if (res.data.code == "200") {
            this.createdDate = res.data.data.createdDate; //当前运单创建时间
            this.gpsStartTime = res.data.data.gpsStartTime; //当前运单装货时间
            this.gpsEndTime = res.data.data.gpsEndTime; //当前运单卸货时间
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },

    /*提交修改时间 */
    changeTimeSure() {
      let orderItemId = this.$route.query.orderItemId;
      let postData = {
        status: this.ydStatus,
        createdDate: this.changeCreatedDate,
        gpsStartTime: this.changeGpsStartTime,
        gpsEndTime: this.changeGpsEndTime,
        orderItemId: orderItemId //运单id
      };
      if ((this.changeCreatedDate == "")) {
        this.$message.warning("请选要修改的创建时间");
        return;
      }

      this.$http
        .post("/admin-center-server/orderItem/updateOrderItemTime", postData)
        .then(res => {
          console.log(res);
          if (res.data.code == "200") {
            this.$message.success("修改成功");
            this.dialogForTime = false;
            this.$router.go(-1); //成功之后返回
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },

    /* 撤销收货1：货主；2：调度员 */
    cancelReceive(val) {
      let orderItemId = this.$route.query.orderItemId;

      this.$http
        .post(
          "/admin-center-server/orderItem/withdrawalOfUnsettledReceipts?id=" +
            orderItemId +
            "&type=" +
            val
        )
        .then(res => {
          console.log(res);
          if (res.data.code == "200") {
            this.$message.success("撤销成功");
            this.cancelReceiveDialog = false;
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },
    valueFormatter(row, column) {
      let value = row[column.property]
      return value === null ? '-' : value
    },
    percentFormatter(row, column) {
      let value = row[column.property]
      if (value === undefined) return ''
      if (value === null) return '-'
      value = Number(value)
      return value.toFixed(2) + '%'
    },
    checkOilStatus() {
      this.dialogOilStatus = true
    },
    checkOilImage(url) {
      this.showImageUrl = url
    },
    closeOilViewer() {
      this.showImageUrl = ""
    },
    driverBuyOil() {
     if(!this.driverOilValue) {
       this.$message.warning('请输入金额')
       return
     }
     this.driverOilConfrimButtonDisabled = true
     let params = {
       oilAmt: this.driverOilValue,
       orderItemSn: this.tableData[0].sn
     }
     this.$post("/admin-center-server/oilSale/driverBuyOilFromHfd", params).then(
       res => {
         this.dialogDriverBuyOil = false
         this.driverOilConfrimButtonDisabled = false
         this.driverOilValue = ''
         this.$message.success('操作成功')
       },
       error => {
         this.driverOilConfrimButtonDisabled = false
       }
     )
    },
    openUrl() {
      window.open(this.tableData[0].signingUrl) 
    },
    reject() {
      let params = {
        orderItemSn: this.tableData[0].sn,
        otherReason: this.rejectOtherReason,
        reasonList: this.rejectSelectedReason.map(v => {
          return {
            id: v.id,
            content: v.content
          }
        })
      }
      this.$post("/admin-center-server/orderItem/auditPassReview", params)
        .then(res => {
          this.$message.success('操作成功')
        })
        .finally(() => {
          this.isRejectDialogShow = false
        })
    },

    // 查看投保单
    checkInsure() {
      if (typeof this.tableData[0].policyOfInsuranceUrl === 'string' && this.tableData[0].policyOfInsuranceUrl !== '') {
        // location.href = 
         window.open(this.tableData[0].policyOfInsuranceUrl)
      }
    },
    openDispatchDetail() {
      this.$router.push({
        path: "/order/agentOrder/agentOrderDetail",
        query: {
          orderBrokerId: this.tableData[0].orderDispatchId //经济单主键
        }
      })
    },
    confirmReturnOilCost() {
      this.$post('/admin-center-server/oilSale/oilCostPayBack?orderItemSn=' + this.tableData[0].sn).then(
        () => {
          this.$message.success('操作成功')
          this.returnOilCostDialog = false
          this.getDetail()
        }
      ) 
    }
  },
  computed: {
    received() {
      return this.tableData[0].statusBd === '6' || this.tableData[0].statusBd === '21' || this.tableData[0].statusBd === '22'
    },
    isShowReject() {
      return this.tableData[0].ydStatus === '6' && this.tableData[0].reportStatus === '0' && this.tableData[0].settlementStatus !== '1'    },
    loadUrlList() {
      return this.tableData[0].originalTonImageUrl ? this.tableData[0].originalTonImageUrl.split(';') : []
    },
    unloadUrlList() {
      return this.tableData[0].dischargeCargoImageUrl ? this.tableData[0].dischargeCargoImageUrl.split(';') : []
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss">
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
::v-deep input[type="number"] {
  -moz-appearance: textfield;
}
ul li {
  list-style: none;
}
.hide .el-upload--picture-card {
  display: none;
}
.upload-box {
  width: 100%;
  height: 100%;
  position: relative;
  .icon-XZ {
    width: 92px;
    height: 92px;
    margin: 0 auto;
    background: url("../../../assets/xiazai.png") no-repeat;
    background-size: 100% 100%;
  }
  .icon-word {
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 10px;
    position: absolute;
    bottom: 25px;
    left: 0px;
    color: #cccccc;
  }
}
.carDetail {
  .main-box {
    background-color: white;

    .title-box {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }

    .transaction-detail {
      display: flex;
      .detail-value {
        font-size: 14px;
        width: 17%;
      }
    }

    .list-box {
      margin-top: 20px;
      // border: 1px solid #cccccc;
      border-left: none;

      .item-title {
        display: flex;
        flex-direction: row;

        div {
          width: 500px;
          height: 50px;
          font-weight: bold;
          font-size: 16px;
          line-height: 50px;
          border: 1px solid #cccccc;
          border-top: none;
          border-right: none;
          background-color: rgb(249, 252, 250);
          text-align: center;
        }
      }

      .item-info {
        display: flex;
        flex-direction: row;

        div {
          font-size: 14px;
          width: 500px;
          height: 50px;
          line-height: 50px;
          text-align: center;
          border: 1px solid #cccccc;
          border-top: none;
          border-right: none;
          border-bottom: none;
        }
      }
    }

    .base-info,
    .drive-info,
    .other-info,
    .driver-info {
      padding: 20px;
    }
  }
}
.check_oil_status {
  margin-left: 10px;
  line-height: 36px;
  font-size: 14px;
  font-weight: bold;
  color: #666;
  word-break: break-all;
  white-space: normal;
}
.oil_images {
  display: flex;
  margin-bottom: 10px;
}
</style>
<style scoped>
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
::v-deep input[type="number"] {
  -moz-appearance: textfield;
}
.source {
  font-size: 14px;
  font-weight: normal;
}
.reject-text {
  color: #D45353;
}
.owner-icon {
  color: #f6a018;
}
.driver-oil {
  margin-top: 10px;
  display: flex;
  flex-direction: row-reverse;
}
.return-oil {
  margin-right: 10px;
}
.driver-oil-item {
  margin: 10px;
}
.driver-oil-hint {
  color: #f6a018;
  font-size: 14px;
  margin-bottom: 20px;
}
.dirver-oil-value {
  margin-top: 20px;
  display: flex;
  align-items: center;
}
.check-insure-button {
  padding: 1px;
  margin-left: 3px;
  border: solid 0.5px #f6a018;
  color: #f6a018;
  cursor: pointer;
  font-size: 13px;
  border-radius: 2px;
}
.reject-text {
  color: #D45353;
}
.warn {
  color: rgb(221, 32, 66);
}
.list-order-dispatch {
  color: #f6a018;
  cursor: pointer;
}

</style>