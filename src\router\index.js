import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router);
/** Layout **/
import Layout from '@/views/layout/Layout'
import dispatchPageNested from './modules/dispatchPageNested'

/** 基础路由设置 **/
export const constantRouterMap = [{
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/errorPage/404'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: 'dashboard',
    children: [{
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'Dashboard',
        meta: {
          title: '首页',
          icon: 'nav-shouye',
          affix: true
        }
      },
      {
        path: '/dashboard/modify',
        component: () => import('@/views/dashboard/modify'),
        name: 'Modify',
        hidden: true,
        meta: {
          title: '常用导航设置'
        }
      }
    ]
  },
  {
    component: Layout,
    path: '/exportTask',
    redirect: '/exportTask/exportTask',
    hidden: true,
    children: [
      {
        path: 'exportTask',
        component: () => import('@/views/exportTask/exportTask'),
        name: 'exportTask',
        meta: {
          id: 90,
          keepAlive: true,
          title: '导出任务'
        }
      }
    ]
  },
];
export default new Router({
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRouterMap
})
export const asyncRouterMap = [
  /* 用户 */
  {
    path: '/user',
    component: Layout,
    redirect: 'noredirect',
    name: 'UserNav',
    meta: {
      title: '用户',
      icon: 'nav-yonghu',
      id: 1,
    },
    children: [{
        path: 'consignorIndex',
        component: () => import('@/views/userPage/consignor/index'),
        name: 'ConsignorIndex',
        meta: {
          title: '货主管理',
          noCache: false,
          id: 2,
        },
        children: [{
            path: 'consignorAccount',
            component: () => import('@/views/userPage/consignor/consignorAccount'),
            name: 'ConsignorAccount',
            meta: {
              id: 3,
              title: '主账号管理',
              keepAlive: true,
            }
          },
          {
            path: '/consignorAccount/accountDetails',
            component: () => import('@/views/userPage/consignor/accountDetails'),
            name: 'ConsignorAccountDetails',
            hidden: true,
            meta: {
              id: 3,
              title: '账户详情',
              keepAlive: true,
            }
          },
          {
            path: '/consignorAccount/addDriver',
            component: () => import('@/views/userPage/consignor/addDriver'),
            name: 'AddDriver',
            hidden: true,
            meta: {
              id: 3,
              title: '认证',
              keepAlive: true,
            }
          },
          {
            path: '/consignorAccount/addShipper',
            component: () => import('@/views/userPage/consignor/addShipper'),
            name: 'AddShipper',
            hidden: true,
            meta: {
              id: 3,
              title: '编辑货主',
              keepAlive: false,
            }
          },
          {
            path: '/consignorAccount/addUser',
            component: () => import('@/views/userPage/consignor/addUser'),
            name: 'addUser',
            hidden: true,
            meta: {
              id: 3,
              title: '添加',
              keepAlive: false,
            }
          },
          {
            path: 'consignorSubAccount',
            component: () => import('@/views/userPage/consignor/consignorSubAccount'),
            name: 'ConsignorSubAccount',
            meta: {
              id: 4,
              title: '子账号管理',
              keepAlive: true,
            }
          },
          // 子账号管理-账户详情
          {
            path: '/consignorSubAccount/brokerDetails',
            component: () => import('@/views/userPage/consignor/brokerDetails'),
            name: 'BrokerDetails',
            hidden: true,
            meta: {
              id: 4,
              title: '详情',
              keepAlive: true,
            }
          },
          // 子账号管理-角色管理
          {
            path: '/consignorSubAccount/roleManagement',
            component: () => import('@/views/userPage/consignor/roleManagement'),
            name: 'RoleManagement',
            hidden: true,
            meta: {
              id: 4,
              title: '角色管理',
              keepAlive: true,
            }
          },
          // 货主管理-子账号管理-添加角色
          {
            path: '/consignorSubAccount/addrole',
            component: () => import('@/views/userPage/consignor/addrole'),
            name: 'Addrole',
            hidden: true,
            meta: {
              id: 4,
              title: '添加角色',
              keepAlive: true,
            }
          },
          {
            path: 'consignorExamine',
            component: () => import('@/views/userPage/consignor/consignorExamine'),
            name: 'ConsignorExamine',
            meta: {
              id: 5,
              title: '货主审核',
              num: 0,
              keepAlive: true,
            }
          },
          //货主管理-审核管理-审核详情
          {
            path: '/consignorExamine/auditDetails',
            component: () => import('@/views/userPage/consignor/auditDetails'),
            name: 'AuditDetails',
            hidden: true,
            meta: {
              id: 5,
              title: '审核详情',
              keepAlive: true,

            }
          },
          //货主管理-审核管理-驳回原因
          {
            path: '/consignorExamine/rejectReason',
            component: () => import('@/views/userPage/consignor/rejectReason'),
            name: 'RejectReason',
            hidden: true,
            meta: {
              id: 5,
              title: '驳回原因管理',
              keepAlive: true,
            }
          }
        ]
      },
      {
        path: 'agentIndex',
        component: () => import('@/views/userPage/agent/index'),
        name: 'AgentIndex',
        meta: {
          id: 6,
          title: '调度员管理',
        },
        children: [{
            path: 'agentAccount',
            component: () => import('@/views/userPage/agent/agentAccount'),
            name: 'AgentAccount',
            meta: {
              id: 8,
              title: '主账号管理',
              keepAlive: true,
            }
          },
          //调度员管理-主账号-添加调度员
          {
            path: '/agentAccount/addShipper',
            component: () => import('@/views/userPage/agent/addShipper'),
            name: 'AgentAddShipper',
            hidden: true,
            meta: {
              id: 8,
              title: '添加调度员',
              keepAlive: true,
            }
          },
          //调度员管理-主账号-账户详情
          {
            path: '/agentAccount/accountDetails',
            component: () => import('@/views/userPage/agent/accountDetails'),
            name: 'AgentAccountDetails',
            hidden: true,
            meta: {
              id: 8,
              title: '账户详情',
              keepAlive: true,
            }
          },
          {
            path: 'agentSubAccount',
            component: () => import('@/views/userPage/agent/agentSubAccount'),
            name: 'AgentSubAccount',
            meta: {
              id: 9,
              title: '子账号管理',
              keepAlive: true,
            }
          },
          {
            path: 'agentExamine',
            component: () => import('@/views/userPage/agent/agentExamine'),
            name: 'AgentExamine',
            meta: {
              id: 10,
              title: '调度员审核',
              num: 0,
              keepAlive: true,
            }
          }
        ]
      },
      {
        path: 'driverIndex',
        component: () => import('@/views/userPage/driver/index'),
        name: 'DriverIndex',
        meta: {
          id: 11,
          title: '司机管理',
        },
        children: [{
            path: 'driverAccount',
            component: () => import('@/views/userPage/driver/driverAccount'),
            name: 'DriverAccount',
            meta: {
              id: 12,
              title: '账户管理',
              keepAlive: true,
            }
          },
          {
            path: 'driverSubAccount',
            component: () => import('@/views/userPage/driver/driverSubAccount'),
            name: 'DriverSubAccount',
            meta: {
              id: 12,
              title: '子帐户管理',
              keepAlive: true,
            }
          },
          {
            path: '/driverSubAccount/brokerDetails',
            component: () => import('@/views/userPage/driver/brokerDetails'),
            name: 'DriverBrokerDetails',
            hidden: true,
            meta: {
              id: 12,
              title: '详情',
              keepAlive: true
            }
          },
          //司机账号管理-账户详情
          {
            path: '/driverAccount/accountDetails',
            component: () => import('@/views/userPage/driver/accountDetails'),
            name: 'DriverAccountDetails',
            hidden: true,
            meta: {
              id: 12,
              title: '账户详情',
              keepAlive: true,
            }
          },
          {
            path: '/driverAccount/keyMore',
            component: () => import('@/views/userPage/driver/keyMore'),
            name: 'keyMore',
            hidden: true,
            meta: {
              id: 12,
              title: '查看详情',
              keepAlive: true,
            }
          },

          {
            path: 'driverExamine',
            component: () => import('@/views/userPage/driver/driverExamine'),
            name: 'DriverExamine',
            meta: {
              id: 13,
              num: 0,
              title: '司机审核',
              keepAlive: true,
            }
          },
          {
            path: '/driverExamine/auditDetails',
            component: () => import('@/views/userPage/driver/auditDetails'),
            name: 'DriverAuditDetails',
            hidden: true,
            meta: {
              id: 13,
              title: '司机审核',
              keepAlive: true,
            }
          },
          {
            path: '/driverExamine/supplierAuditDetail',
            component: () => import('@/views/userPage/driver/supplierAuditDetail'),
            name: 'SupplierAuditDetail',
            hidden: true,
            meta: {
              id: 13,
              title: '司机审核'
            }
          }
        ]
      },
      {
        path: 'operater',
        component: () => import('@/views/userPage/operater/index'),
        name: 'operater',
        meta: {
          id: 1003,
          title: '运营人员管理',
        },
        children: [{
            path: 'operaterManager',
            component: () => import('@/views/userPage/operater/operaterManager'),
            name: 'operaterManager',
            meta: {
              id: 1003,
              title: '账户管理',
              keepAlive: true,
            }
          },
          {
            path: 'roleManager',
            component: () => import('@/views/userPage/operater/roleManager'),
            name: 'roleManager',
            meta: {
              id: 1003,
              title: '角色管理',
              keepAlive: true,
            }
          },
          {
            path: '/roleManager/roleFunctionSetting',
            component: () => import('@/views/userPage/operater/roleFunctionSetting'),
            name: 'roleFunctionSetting',
            hidden: true,
            meta: {
              id: 1003,
              title: '功能权限设置',
              keepAlive: false,
            }
          },
          {
            path: '/roleManager/roleDataSetting',
            component: () => import('@/views/userPage/operater/roleDataSetting'),
            name: 'roleDataSetting',
            hidden: true,
            meta: {
              id: 1003,
              title: '数据权限设置',
              keepAlive: true,
            }
          }
        ]
      },
      {
        path: 'unsubscribe',
        component: () => import('@/views/userPage/unsubscribe/unsubscribe'),
        name: 'unsubscribe',
        meta: {
          title: '注销审核',
          keepAlive: true
        }
      }
    ]
  },
  dispatchPageNested,
  /* 订单 */
  {
    path: '/order',
    component: Layout,
    redirect: 'noredirect',
    name: 'OrderNav',
    meta: {
      id: 14,
      title: 'orderNav',
      icon: 'nav-dingdan'
    },
    children: [
      //订单管理
      {
        path: 'orderManage',
        component: () => import('@/views/orderPage/orderManageList/orderManage'),
        name: 'orderManage',
        meta: {
          id: 15,
          title: '订单管理',
          keepAlive: true,
        }
      },
      {
        path: 'orderManage/orderDetail',
        component: () => import('@/views/orderPage/orderManageList/orderDetail'),
        name: 'OrderDetail',
        meta: {
          id: 15,
          title: '订单详情',
          keepAlive: true,
        },
        hidden: true
      },
      {
        path: '/orderManage/changeFee',
        component: () => import('@/views/orderPage/changeFee/changeFee'),
        name: 'orderManage/changeFee',
        meta: {
          id: 15,
          title: '修改运费',
          keepAlive: true,
        },
        hidden: true
      },
      //调度员单管理
      {
        path: 'agentOrder',
        component: () => import('@/views/orderPage/agentOrderList/agentOrder'),
        name: 'AgentOrder',
        meta: {
          id: 17,
          title: '调度单管理',
          keepAlive: true,
        }
      },
      {
        path: 'agentOrder/agentOrderDetail',
        component: () => import('@/views/orderPage/agentOrderList/agentOrderDetail'),
        name: 'agentOrder/agentOrderDetail',
        meta: {
          id: 17,
          title: '调度员单详情',
          keepAlive: true,
        },
        hidden: true
      },
      {
        path: 'orderAudit',
        component: () => import('@/views/orderPage/orderAudit/orderAudit'),
        name: 'orderAudit',
        meta: {
          title: '订单审核',
          keepAlive: true
        }
      },
      {
        path: 'orderAuditDetail',
        component: () => import('@/views/orderPage/orderAudit/orderAuditDetail'),
        name: 'orderAuditDetail',
        meta: {
          title: '订单审核详情',
          keepAlive: true
        },
        hidden: true
      },
      {
        path: 'orderRejectReason',
        component: () => import('@/views/orderPage/orderAudit/orderRejectReason'),
        name: 'orderRejectReason',
        meta: {
          title: '驳回原因管理',
          keepAlive: true
        },
        hidden: true
      }
    ]
  },
  /* 运单 */
  {
    path: '/transport',
    component: Layout,
    redirect: 'noredirect',
    name: 'TransportNav',
    meta: {
      id: 19,
      title: 'transportNav',
      icon: 'nav-yundan'
    },
    children: [
      //运单管理
      {
        path: 'transportList',
        component: () => import('@/views/transportPage/transport/transportList'),
        name: 'transportList',
        meta: {
          id: 19,
          title: '运单',
          keepAlive: true,
        }
      },
      {
        path: 'transportListDetail',
        component: () => import('@/views/transportPage/transport/transportListDetail'),
        name: 'TransportListDetail',
        meta: {
          id: 19,
          title: '运单',
          keepAlive: true,
        },
        hidden: true
      },
      {
        path: 'transportAudit',
        component: () => import('@/views/transportPage/transportAudit/transportAudit'),
        name: 'transportAudit',
        meta: {
          id: 19,
          title: '运单审核',
          keepAlive: true
        }
      },
      {
        path: 'transportAuditDetail',
        component: () => import('@/views/transportPage/transportAudit/transportAuditDetail'),
        name: 'transportAuditDetail',
        meta: {
          id: 19,
          title: '审核详情',
          keepAlive: true
        },
        hidden: true
      }
    ]
  },
  /* 调度 */
  /* 财务 */
  {
    path: '/finance',
    component: Layout,
    redirect: '/finance/userFinance',
    name: 'finance',
    meta: {
      id: 60,
      title: '财务',
      icon: 'nav-caiwu'
    },
    children: [
      {
        path: 'withdraw',
        component: () => import('@/views/finance/userFinance/withdraw/withdraw'),
        name: 'withdraw',
        meta: {
          id: 103,
          keepAlive: true,
          title: '提现管理'
        }
      },
      {
        path: 'userDeal',
        component: () => import('@/views/finance/userFinance/userDeal/userDeal'),
        name: 'userDeal',
        meta: {
          id: 63,
          keepAlive: true,
          title: '收支记录'
        }
      },
      {
        path: 'dealDetail',
        component: () => import('@/views/finance/userFinance/userDeal/dealDetail'),
        name: 'dealDetail',
        meta: {
          id: 63,
          keepAlive: true,
          title: '交易详情',
          
        },
        hidden: true
      },
      {
        path: 'userPay',
        component: () => import('@/views/finance/userFinance/userPay/userPay'),
        name: 'userPay',
        meta: {
          id: 64,
          keepAlive: true,
          title: '收益明细'
        }
      },
      {
        path: 'userBalance',
        component: () => import('@/views/finance/userFinance/userBalance/userBalance'),
        name: 'userBalance',
        meta: {
          id: 100,
          keepAlive: true,
          title: '货主余额'
        }
      },
      {
        path: 'clearWaybill',
        component: () => import('@/views/finance/clearWaybill/clearWaybill'),
        name: 'clearWaybill',
        meta: {
          id: 101,
          keepAlive: true,
          title: '税务上报'
        }
      },
      {
        path: 'serverCarrierDetail',
        component: () => import('@/views/finance/clearWaybill/serverCarrierDetail'),
        name: 'serverCarrierDetail',
        meta: {
          id: 101,
          title: '2C-服务方详情'
          // title: '服务方详情'
        },
        hidden: true
      },
      {
        path: 'serverCarrierDetail2B',
        component: () => import('@/views/finance/clearWaybill/serverCarrierDetail2B'),
        name: 'serverCarrierDetail',
        meta: {
          id: 101,
          title: '2B-服务方详情'
          // title: '服务方详情'
        },
        hidden: true
      },
      {
        path: 'taxDetail',
        component: () => import('@/views/finance/taxDetail/taxDetail'),
        name: 'taxDetail',
        meta: {
          keepAlive: true,
          // title: '税务上报信息'
          title: '2C-业务订单信息'
        },
        hidden: true
      },
      {
        path: 'taxDetail2B',
        component: () => import('@/views/finance/taxDetail/taxDetail2B'),
        name: 'taxDetail2B',
        meta: {
          keepAlive: true,
          // title: '税务上报信息'
          title: '2B-业务订单信息'
        },
        hidden: true
      },
      {
        path: 'userFlowing',
        component: () => import('@/views/finance/userFinance/userFlowing/userFlowing'),
        name: 'userFlowing',
        meta: {
          id: 71,
          keepAlive: true,
          title: '用户充值提现流水'
        }
      },
      {
        path: 'userWallet',
        component: () => import('@/views/finance/userFinance/userWallet/userWallet'),
        name: 'userWallet',
        meta: {
          id: 62,
          keepAlive: true,
          title: '用户钱包'
        }
      },
      {
        path: 'channelManagement',
        component: () => import('@/views/finance/userFinance/channelManagement/index'),
        name: 'channelManagement',
        meta: {
          id: 102,
          keepAlive: true,
          title: '渠道账户管理',
          hidden: false,
        }
      },
      // {
      //   path: 'ticketFinance',
      //   component: () => import('@/views/finance/ticketFinance/index'),
      //   name: 'ticketFinance',
      //   meta: {
      //     id: 68,
      //     title: '开票管理'
      //   },
      //   children: [{
      //       path: 'statements',
      //       component: () => import('@/views/finance/ticketFinance/statements/statements'),
      //       name: 'Statements',
      //       meta: {
      //         id: 69,
      //         title: '结算单管理',
      //         keepAlive: true,
      //       },
      //     },
      //     {
      //       path: 'addStatement',
      //       component: () => import('@/views/finance/ticketFinance/statements/addStatement'),
      //       name: 'addStatement',
      //       hidden: true,
      //       meta: {
      //         id: 69,
      //         keepAlive: true,
      //         title: '新增结算单'
      //       }
      //     },
      //     {
      //       path: 'lookList',
      //       component: () => import('@/views/finance/ticketFinance/statements/lookLIst'),
      //       name: 'lookList',
      //       hidden: true,
      //       meta: {
      //         id: 69,
      //         keepAlive: true,
      //         title: '结算单-查看'
      //       }
      //     },
      //     {
      //       path: 'statementEdit',
      //       component: () => import('@/views/finance/ticketFinance/statements/statementEdit'),
      //       name: 'statementEdit',
      //       hidden: true,
      //       meta: {
      //         id: 69,
      //         keepAlive: true,
      //         title: '编辑结算单'
      //       }
      //     },
      //     {
      //       path: 'statementList',
      //       component: () => import('@/views/finance/ticketFinance/statements/statementList'),
      //       name: 'StatementList',
      //       hidden: true,
      //       meta: {
      //         id: 69,
      //         keepAlive: true,
      //         title: '结算单-清单'
      //       }
      //     },
      //     /** 结算单下的回单 2020-6-12 **/
      //     {
      //       path: 'statements_receipt',
      //       component: () => import('@/views/finance/ticketFinance/statements/statements_receipt'),
      //       name: 'StatementsReceipt',
      //       hidden: true,
      //       meta: {
      //         id: 69,
      //         keepAlive: true,
      //         title: '结算单-回单'
      //       }
      //     },
      //     {
      //       path: 'statements_electronic',
      //       component: () => import('@/views/finance/ticketFinance/statements/statements_electronic'),
      //       name: 'StatementsElectronic',
      //       hidden: true,
      //       meta: {
      //         id: 69,
      //         keepAlive: true,
      //         title: '结算单-回单-详情'
      //       }
      //     },
      //     // {
      //     //     path: 'lookDd',
      //     //     component: () => import('@/views/finance/ticketFinance/statements/lookDd'),
      //     //     name: 'lookDd',
      //     //     hidden: true,
      //     //     meta: {
      //     //         title: '订单查看'
      //     //     }
      //     // },
      //     {
      //       path: 'editLook',
      //       component: () => import('@/views/finance/ticketFinance/statements/editLook'),
      //       name: 'editLook',
      //       hidden: true,
      //       meta: {
      //         id: 69,
      //         title: '结算单-编辑-查看'
      //       }
      //     },
      //     {
      //       path: 'detailedList',
      //       component: () => import('@/views/finance/ticketFinance/detailedList/detailedList'),
      //       name: 'detailedList',
      //       meta: {
      //         id: 70,
      //         keepAlive: true,
      //         title: '清单管理'
      //       }
      //     },
      //     {
      //       path: 'detailedListLook',
      //       component: () => import('@/views/finance/ticketFinance/detailedList/detailedListLook'),
      //       name: 'detailedListLook',
      //       meta: {
      //         id: 70,
      //         keepAlive: true,
      //         title: '清单管理-查看'
      //       },
      //       hidden: true
      //     }
      //   ]
      // },
      {
        path: 'invoice',
        component: () => import('@/views/finance/invoice/index'),
        name: 'invoice',
        meta: {
          title: '开票管理'
        },
        children: [
          {
            path: 'invoice',
            component: () => import('@/views/finance/invoice/invoice/invoice'),
            name: 'invoice',
            meta: {
              title: '开票管理',
              keepAlive: true
            }
          },
          {
            path: 'invoiceDetail',
            component: () => import('@/views/finance/invoice/invoice/invoiceDetail'),
            name: 'invoiceDetail',
            meta: {
              title: '开票详情',
              keepAlive: true
            },
            hidden: true
          },
          {
            path: 'attachmentInventory',
            component: () => import('@/views/finance/invoice/invoice/attachmentInventory'),
            name: 'attachmentInventory',
            meta: {
              title: '清单附件列表',
              keepAlive: true
            },
            hidden: true
          },
          {
            path: 'inventory',
            component: () => import('@/views/finance/invoice/inventory/inventory'),
            name: 'inventory',
            meta: {
              title: '清单管理',
              keepAlive: true
            }
          },
          {
            path: 'waybill',
            component: () => import('@/views/finance/invoice/inventory/waybill'),
            name: 'waybill',
            meta: {
              title: '运单列表',
              keepAlive: true
            },
            hidden: true
          }
        ]
      },
      {
        path: 'Withholding',
        component: () => import('@/views/finance/Withholding/index'),
        name: 'Withholding',
        meta: {
          id: 65,
          title: '代扣代缴'
        },
        children: [{
            path: 'paying',
            component: () => import('@/views/finance/Withholding/Withholding/Withholding'),
            name: 'paying',
            meta: {
              id: 65,
              keepAlive: true,
              title: '代扣代缴'
            }
          },
          {
            path: '/paying/Record_payment',
            component: () => import('@/views/finance/Withholding/Withholding/Record_payment'),
            name: 'Record_payment',
            meta: {
              id: 65,
              keepAlive: true,
              title: '代扣代缴查询'
            },
            hidden: true
          },
          {
            path: '/paying/withholding_record',
            component: () => import('@/views/finance/Withholding/Withholding/withholding_record'),
            name: 'withholding_record',
            meta: {
              id: 65,
              keepAlive: true,
              title: '代扣代缴记录'
            },
            hidden: true
          },
          {
            path: '/paying/tax_certificate',
            component: () => import('@/views/finance/Withholding/Withholding/TaxCertificate'),
            name: 'tax_certificate',
            meta: {
              id: 65,
              keepAlive: true,
              title: '上传完税证明'
            },
            hidden: true
          }
        ]
      },
      // {
      //   path: 'ele_receiptPage',
      //   component: () => import('@/views/finance/electronic_receipt/index'),
      //   name: 'Ele_receiptPage',
      //   meta: {
      //     id: 78,
      //     title: '电子回单'
      //   },
      //   children: [{
      //       path: 'electronic_receipt',
      //       component: () => import('@/views/finance/electronic_receipt/ElectronicReceipt'),
      //       name: 'Electronic_receipt',
      //       meta: {
      //         id: 78,
      //         keepAlive: true,
      //         title: '电子回单'
      //       }
      //     },
      //     {
      //       path: 'receiptDeatil',
      //       component: () => import('@/views/finance/electronic_receipt/receiptDeatil'),
      //       name: 'ReceiptDeatil',
      //       meta: {
      //         id: 78,
      //         keepAlive: true,
      //         title: '电子回单详情'
      //       },
      //       hidden: true,
      //     },
      //   ]
      // },
      
      {
        path: 'collectionMoney',
        component: () => import('@/views/finance/electronic_receipt/index'),
        name: 'CollectionMoney',
        meta: {
          id: 79,
          title: '运费代收'
        },
        children: [{
            path: 'collectionMoney',
            component: () => import('@/views/finance/collectionMoney/collectionMoney'),
            name: 'Collection',
            meta: {
              id: 79,
              keepAlive: true,
              title: '运费代收'
            }
          },
          {
            path: 'collectionDeatil',
            component: () => import('@/views/finance/collectionMoney/collectionDeatil'),
            name: 'CollectionDeatil',
            meta: {
              id: 79,
              keepAlive: true,
              title: '运费代收详情'
            },
            hidden: true,
          },
        ]
      },
      {
        path: 'associated',
        component: () => import('@/views/finance/associated/index'),
        name: 'associated',
        meta: {
          id: 84,
          title: '油管理'
        },
        children: [
          {
            path: 'allocation',
            component: () => import('@/views/finance/associated/allocation/allocation'),
            name: 'allocation',
            meta: {
              id: 85,
              keepAlive: true,
              title: '油分配'
            }
          },
          {
            path: 'associatedBalance',
            component: () => import('@/views/finance/associated/balance'),
            name: 'associatedBalance',
            meta: {
              id: 85,
              keepAlive: true,
              title: '余额管理'
            }
          },
          {
            path: 'recharge',
            component: () => import('@/views/finance/associated/recharge'),
            name: 'recharge',
            meta: {
              id: 85,
              keepAlive: true,
              title: '充值记录'
            },
            hidden: true
          },
          {
            path: 'expend',
            component: () => import('@/views/finance/associated/expend'),
            name: 'expend',
            meta: {
              id: 85,
              keepAlive: true,
              title: '消费详情'
            },
            hidden: true
          },
          {
            path: 'accountBalance',
            component: () => import('@/views/finance/associated/accountBalance'),
            name: 'accountBalance',
            hidden: true,
            meta: {
              id: 85,
              keepAlive: true,
              title: '账户余额'
            }
          },
      //     // {
      //     //   path: 'incomeExpenditure',
      //     //   component: () => import('@/views/finance/associated/incomeExpenditure'),
      //     //   name: 'incomeExpenditure',
      //     //   meta: {
      //     //     id: 86,
      //     //     keepAlive: true,
      //     //     title: '收支查询'
      //     //   }
      //     // },
      //     // {
      //     //   path: 'associatedRefund',
      //     //   component: () => import('@/views/finance/associated/refund'),
      //     //   name: 'associatedRefund',
      //     //   meta: {
      //     //     id: 87,
      //     //     keepAlive: true,
      //     //     title: '退款管理'
      //     //   }
      //     // },
      //     // {
      //     //   path: 'checkBill',
      //     //   component: () => import('@/views/finance/associated/checkBill'),
      //     //   name: 'CheckBill',
      //     //   meta: {
      //     //     id: 88,
      //     //     keepAlive: true,
      //     //     title: '对账单管理'
      //     //   },
      //     // },
          {
            path: 'billDetail',
            component: () => import('@/views/finance/associated/billDetail'),
            name: 'BillDetail',
            hidden: true,
            meta: {
              id: 88,
              keepAlive: true,
              title: '对账单详情 '
            },
          },
        ]
      },
      {
        path: 'transferAccounts',
        component: () => import('@/views/finance/transferAccounts/index'),
        name: 'transferAccounts',
        meta: {
          id: 1007,
          title: '手动转账'
        }
      }
      
    ]
  },
  /* 数据 */
  {
    path: '/dataInformation',
    component: Layout,
    redirect: '/dataInformation/dataBaseManage',
    name: 'DataBaseManage',
    meta: {
      id: 28,
      title: '数据',
      icon: 'nav-shuju'
    },
    children: [
      {
        path: 'dataBaseManage/regulatoryReporting',
        component: () => import('@/views/dataInformation/regulatoryReporting/index'),
        name: 'regulatoryReporting',
        meta: {
          id: 29,
          keepAlive: true,
          title: '监管上报'
        },
        hidden: true,
      },
      {
        path: 'dataBaseManage',
        component: () => import('@/views/dataInformation/dataBaseManage/dataBaseManage'), // Parent router-view
        name: 'dataBaseManage1',
        meta: {
          id: 29,
          keepAlive: true,
          title: '车辆库管理'
        },
      },
      {
        path: 'dataBaseManage/addCarInfo',
        component: () => import('@/views/dataInformation/dataBaseManage/addCarInfo'), // Parent router-view
        name: 'addCarInfo',
        meta: {
          id: 29,
          keepAlive: true,
          title: '新增车辆信息'
        },
        hidden: true
      },
      {
        path: 'reportFormManage',
        component: () => import('@/views/dataInformation/reportFormManage/index'), // Parent router-view
        name: 'reportFormManage',
        meta: {
          id: 31,
          title: '报表管理'
        },
        children: [{
            path: 'driverEvalute',
            component: () => import('@/views/dataInformation/reportFormManage/driverEvalute/driverEvalute'),
            name: 'driverEvalute',
            meta: {
              id: 32,
              keepAlive: true,
              title: '司机评价表'
            }
          },
          {
            path: 'serviceEvalute',
            component: () => import('@/views/dataInformation/reportFormManage/serviceEvalute/serviceEvalute'),
            name: 'serviceEvalute',
            meta: {
              id: 92,
              keepAlive: true,
              title: '客服评价表'
            }
          },
          {
            path: 'addressBase',
            component: () => import('@/views/dataInformation/reportFormManage/addressBase/addressBase'),
            name: 'addressBase',
            meta: {
              id: 33,
              keepAlive: true,
              title: '地址库管理'
            }
          }

        ]
      }

    ]
  },
  /* 运营 */
  {
    path: '/operationsManage',
    component: Layout,
    redirect: '/operationsManage/messageCenter',
    name: 'operationsManage',
    meta: {
      id: 34,
      title: '运营',
      icon: 'nav-yunying'
    },
    children: [
      {
        path: 'insureManagement',
        component: () => import('@/views/operationsManage/insureManagement/index'),
        name: 'insureManagement',
        meta: {
          id: 1005, // 待修改
          keepAlive: true,
          title: '保单管理'
        },
      },
      {
        path: 'evaluateManagement',
        component: () => import('@/views/operationsManage/evaluateManagement/evaluateManagement'),
        name: 'evaluateManagement',
        meta: {
          id: 1004,
          keepAlive: true,
          title: '评价管理'
        },
      },
      {
        path: 'consultManagement',
        component: () => import('@/views/operationsManage/consultManagement/consultManagement'),
        name: 'consultManagement',
        meta: {
          id: 1006,
          keepAlive: true,
          title: '咨询投诉'
        }
      },
      {
        path: 'depositRefund',
        component: () => import('@/views/operationsManage/depositRefund/depositRefund'),
        name: 'depositRefund',
        meta: {
          id: 1008,
          keepAlive: true,
          title: '押金退还申请'
        }
      },
      {
        path: 'driverAppeal',
        component: () => import('@/views/operationsManage/driverAppeal/driverAppeal'),
        name: 'driverAppeal',
        meta: {
          id: 1012,
          keepAlive: true,
          title: '司机申诉'
        }
      },
      {
        path: 'messageCenter',
        component: () => import('@/views/operationsManage/messageCenter/index'), // Parent router-view
        name: 'messageCenter',
        meta: {
          id: 35,
          title: '消息中心'
        },
        children: [{
            path: 'systemMessage',
            component: () => import('@/views/operationsManage/messageCenter/systemMessage/systemMessage'),
            name: 'systemMessage',
            meta: {
              id: 36,
              keepAlive: true,
              title: '系统消息'
            }
          },
          {
            path: 'eventManagement',
            component: () => import('@/views/operationsManage/messageCenter/eventManagement/eventManagement'),
            name: 'eventManagement',
            meta: {
              id: 37,
              keepAlive: true,
              title: '活动管理'
            }
          },
          {
            path: 'sendMessage',
            component: () => import('@/views/operationsManage/messageCenter/sendMessage/sendMessage'),
            name: 'sendMessage',
            meta: {
              id: 36,
              keepAlive: true,
              title: '发布消息'
            },
            hidden: true
          },
          {
            path: 'addEvent',
            component: () => import('@/views/operationsManage/messageCenter/eventManagement/addEvent'),
            name: 'addEvent',
            meta: {
              id: 37,
              keepAlive: true,
              title: '添加活动'
            },
            hidden: true
          }

        ]
      },
      {
        path: 'cmsManage',
        component: () => import('@/views/operationsManage/cmsManage/index'), // Parent router-view
        name: 'cmsManage',
        meta: {
          id: 38,
          title: 'cms管理'
        },
        children: [{
            path: 'customerService',
            component: () => import('@/views/operationsManage/cmsManage/customerService/customerService'),
            name: 'CustomerService',
            meta: {
              id: 39,
              keepAlive: true,
              title: '客服中心'
            }
          },
          {
            path: 'classify',
            component: () => import('@/views/operationsManage/cmsManage/customerService/classify'),
            name: 'classify',
            meta: {
              id: 39,
              keepAlive: true,
              title: '分类管理'
            },
            hidden: true
          },
          {
            path: 'addArticle',
            component: () => import('@/views/operationsManage/cmsManage/customerService/addArticle'),
            name: 'addArticle',
            meta: {
              id: 39,
              keepAlive: true,
              title: '编辑文章'
            },
            hidden: true
          },

          {
            path: 'adManagement',
            component: () => import('@/views/operationsManage/cmsManage/adManagement/adManagement'),
            name: 'adManagement',
            meta: {
              id: 40,
              keepAlive: true,
              title: '广告管理'
            }
          },
          {
            path: 'addAdvertise',
            component: () => import('@/views/operationsManage/cmsManage/adManagement/addAdvertise'),
            name: 'addAdvertise',
            meta: {
              id: 40,
              keepAlive: false,
              title: '添加广告'
            },
            hidden: true
          },
          {
            path: 'adPositionManagement',
            component: () => import('@/views/operationsManage/cmsManage/adPositionManagement/adPositionManagement'),
            name: 'adPositionManagement',
            meta: {
              id: 73,
              keepAlive: true,
              title: '广告位管理'
            }
          },
          {
            path: 'addPositionAdvertise',
            component: () => import('@/views/operationsManage/cmsManage/adPositionManagement/addPositionAdvertise'),
            name: 'addPositionAdvertise',
            meta: {
              id: 73,
              keepAlive: true,
              title: '添加广告位'
            },
            hidden: true
          },
        ]
      },
      {
        path: 'expenseManage',
        component: () => import('@/views/operationsManage/expenseManage/customerServiceFee/customerServiceFee'),
        name: 'customerService',
        meta: {
          id: 58,
          keepAlive: true,
          title: '客户服务费'
        }
      },
      {
        path: 'freezing_amountIndex',
        component: () => import('@/views/finance/freezing_amount/index'),
        name: 'freezing_amountIndex',
        meta: {
          id: 66,
          title: '调度员冻结金额'
        },
        children: [{
          path: 'freezing_amount',
          component: () => import('@/views/finance/freezing_amount/freezing_amount'),
          name: 'freezing_amount',
          meta: {
            id: 66,
            keepAlive: true,
            title: '调度员冻结金额'
          }
        }, ]
      },
      {
        path: 'car_owners_freezeIndex',
        component: () => import('@/views/finance/car_owners_freeze/index'),
        name: 'car_owners_freezeIndex',
        meta: {
          id: 67,
        },
        children: [{
          path: 'car_owners_freeze',
          component: () => import('@/views/finance/car_owners_freeze/car_owners_freeze'),
          name: 'car_owners_freeze',
          meta: {
            id: 67,
            keepAlive: true,
            title: '司机车主冻结金额'
          }
        }, ]
      },
      {
        path: 'accountCancellation',
        component: () => import('@/views/operationsManage/accountCancellation/accountCancellation'), // Parent router-view
        name: 'accountCancellation',
        meta: {
          id: 81,
          num: 0,
          keepAlive: true,
          title: '司机销户管理'
        }
      },
      {
        path: 'accountPage',
        component: () => import('@/views/operationsManage/accountCancellation/accountPage'), // Parent router-view
        name: 'accountPage',
        hidden: true,
        meta: {
          id: 81,
          keepAlive: true,
          title: '手动销户'
        }
      },
      {
        path: 'cardBindHistory',
        component: () => import('@/views/dataInformation/cardInfo/cardBindHistory'),
        name: 'cardBindHistory',
        meta: {
          id: 83,
          keepAlive: true,
          title: '用户绑卡历史'
        }
      },
      {
        path: 'contractManage',
        component: () => import('@/views/dataInformation/contractManage/contractManage'),
        name: 'contractManage',
        meta: {
          id: 89,
          keepAlive: true,
          title: '合同协议管理'
        }
      },
      {
        path: 'editContract',
        component: () => import('@/views/dataInformation/editContract/editContract'),
        name: 'editContract',
        hidden: true,
        meta: {
          id: 89,
          keepAlive: false,
          title: '协议编辑'
        }
      },
      {
        path: 'earlyWarningEmail',
        component: () => import('@/views/dataInformation/earlyWarningEmail/earlyWarningEmail'),
        name: 'earlyWarningEmail',
        meta: {
          id: 89,
          keepAlive: true,
          title: '预警邮箱管理'
        }
      },
    ]
  },
  //销售
  {
    path: '/sale',
    component: Layout,
    redirect: 'noredirect',
    name: 'SaleNav',
    meta: {
      id: 41,
      title: '销售',
      icon: 'nav-xiaoshou'
    },
    children: [{
        path: 'saleIndex',
        component: () => import('@/views/sale/sale'),
        name: 'SaleIndex',
        meta: {
          id: 43,
          keepAlive: true,
          title: '客户管理',
          noCache: false
        }
      },
      {
        path: 'saleDetails',
        component: () => import('@/views/sale/details'),
        name: 'SaleDetails',
        hidden: true,
        meta: {
          id: 43,
          keepAlive: true,
          title: '客户管理详情',
          noCache: false
        }
      },
      {
        path: 'add',
        component: () => import('@/views/sale/add'),
        name: 'Add',
        hidden: true,
        meta: {
          id: 43,
          keepAlive: true,
          title: '新增客户',
          noCache: false
        }
      },
      {
        path: 'contract',
        component: () => import('@/views/sale/contract'),
        name: 'Contract',
        meta: {
          id: 44,
          keepAlive: true,
          title: '合同管理',
          noCache: false
        }
      },
      {
        path: '/contract/addContract',
        component: () => import('@/views/sale/addContract'),
        name: 'AddContract',
        hidden: true,
        meta: {
          id: 44,
          keepAlive: false,
          title: '编辑合同',
          noCache: false
        }
      },
      {
        path: '/contract/contractDetiles',
        component: () => import('@/views/sale/contractDetiles'),
        name: 'ContractDetiles',
        hidden: true,
        meta: {
          id: 44,
          keepAlive: true,
          title: '查看合同',
          noCache: false
        }
      }
    ]
  },
  //系统设置
  {
    path: '/systemPage',
    component: Layout,
    redirect: '/systemPage/systemSetting/framework',
    name: 'systemPage',
    meta: {
      id: 45,
      title: '系统',
      icon: 'nav-xitong'
    },
    children: [{
      path: 'systemSetting',
      component: () => import('@/views/systemPage/systemSetting/index'),
      name: 'systemSetting',
      meta: {
        id: 46,
        title: '系统设置',
        icon: 'guide'
      },
      children: [{
          path: 'serviceCharge',
          component: () => import('@/views/systemPage/systemSetting/serviceCharge/serviceCharge'),
          name: 'serviceCharge',
          meta: {
            id: 74,
            keepAlive: true,
            title: '平台手续费设置'
          }
        },
        {
          path: 'dictionariesManage',
          component: () => import('@/views/systemPage/systemSetting/dictionariesManage/dictionariesManage'),
          name: 'DictionariesManage',
          meta: {
            id: 47,
            keepAlive: true,
            title: '字典库管理'
          }
        },
        {
          path: 'regionManage',
          component: () => import('@/views/systemPage/systemSetting/regionManage/regionManage'),
          name: 'regionManage',
          meta: {
            id: 75,
            keepAlive: true,
            title: '地区管理'
          }
        },
        //组织架构
        {
          path: 'framework',
          component: () => import('@/views/systemPage/systemSetting/framework/framework'),
          name: 'framework',
          meta: {
            id: 48,
            keepAlive: true,
            title: '组织架构管理'
          }
        },
        //新增组织架构
        {
          path: 'addFramework',
          component: () => import('@/views/systemPage/systemSetting/framework/addFramework/addFramework'),
          hidden: true,
          name: 'addFramework',
          meta: {
            id: 48,
            keepAlive: true,
            title: '新增组织架构'
          }
        },
        //系统用户管理
        {
          path: 'systemUser',
          component: () => import('@/views/systemPage/systemSetting/systemUser/systemUser'),
          name: 'systemUser',
          meta: {
            id: 49,
            keepAlive: true,
            title: '系统用户管理'
          }
        },
        //新增系统用户管理
        {
          path: 'addSystemUser',
          component: () => import('@/views/systemPage/systemSetting/systemUser/addSystemUser/addSystemUser'),
          hidden: true,
          name: 'addSystemUser',
          meta: {
            id: 49,
            keepAlive: true,
            title: '新增系统用户'
          }
        },
        //系统用户角色管理
        {
          path: 'systemUserRole',
          component: () => import('@/views/systemPage/systemSetting/systemUserRole/systemUserRole'),
          name: 'systemUserRole',
          meta: {
            id: 50,
            keepAlive: true,
            title: '系统用户角色管理'
          }
        },
        {
          path: 'operationLog',
          component: () => import('@/views/systemPage/systemSetting/operationLog/operationLog'),
          name: 'operationLog',
          meta: {
            id: 51,
            keepAlive: true,
            title: '操作日志'
          }
        },
        {
          path: 'systemBase',
          component: () => import('@/views/systemPage/systemSetting/systemBase/systemBase'),
          name: 'systemBase',
          meta: {
            id: 52,
            keepAlive: true,
            title: '系统基础设置'
          }
        },

        {
          path: 'stationMessage',
          component: () => import('@/views/systemPage/systemSetting/stationMessage/stationMessage'),
          name: 'stationMessage',
          meta: {
            id: 54,
            keepAlive: true,
            title: '站内消息管理'
          }
        },
        {
          path: 'withholdingSettings',
          component: () => import('@/views/systemPage/systemSetting/withholdingSettings/withholdingSettings'),
          name: 'withholdingSettings',
          meta: {
            id: 76,
            keepAlive: true,
            title: '代扣代缴设置'
          },
        },
        {
          path: '/withholdingSettings/modify',
          component: () => import('@/views/systemPage/systemSetting/withholdingSettings/modify'),
          name: 'modify',
          meta: {
            id: 76,
            keepAlive: true,
            title: '代扣代缴编辑'
          },
          hidden: true
        },
        {
          path: 'freezingGold',
          component: () => import('@/views/systemPage/systemSetting/withholdingSettings/freezingGold'),
          name: 'freezingGold',
          meta: {
            id: 77,
            keepAlive: true,
            title: '冻结金额设置'
          },
        },
        {
          path: 'addStationMessage',
          component: () => import('@/views/systemPage/systemSetting/addStationMessage/addStationMessage'),
          name: 'addStationMessage',
          meta: {
            id: 54,
            keepAlive: true,
            title: '新增站内消息'
          },
          hidden: true
        },
      ]
    },
    {
      path: 'cargoOwnerSetting',
      component: () => import('@/views/systemPage/CargoOwner/index'),
      redirect: 'cargoOwnerSetting/menu',
      meta: {
        title: '货主系统设置'
      },
      alwaysShow: true,
      children: [
        {
          path: 'CargoOwnerMenu',
          name: 'cargoOwnerMenu',
          component: () => import('@/views/systemPage/CargoOwner/CargoOwnerMenu'),
          meta: {
            title: '菜单设置'
          }
        },
        {
          path: 'whiteList',
          name: 'whiteList',
          component: () => import('@/views/systemPage/CargoOwner/WhiteList/WhiteList'),
          meta: {
            title: '白名单设置'
          }
        }
      ]
    }]
  },
  /* 系统消息 */
  {
    path: '/messagePage',
    component: Layout,
    redirect: '/messagePage/messageType/productNews',
    name: 'messagePage',
    meta: {
      title: '消息',
      id: 0,
      icon: 'nav-xiaoxi'
    },
    children: [{
      path: 'centerMessage',
      component: () => import('@/views/messagePage/messageType/index'),
      name: 'centerMessage',
      meta: {
        title: '消息中心',
        icon: 'message'
      },
      children: [

        {
          path: 'messageDeatil',
          component: () => import('@/views/messagePage/messageType/messageDeatil/messageDeatil'),
          name: 'messageDeatil',
          hidden: true,
          meta: {
            title: '消息详情',
          }
        },
        {
          path: 'productNews',
          component: () => import('@/views/messagePage/messageType/productNews/productNews'),
          name: 'productNews',
          meta: {
            title: '产品消息'
          }
        },
        {
          path: 'securigyNews',
          component: () => import('@/views/messagePage/messageType/securigyNews/securigyNews'),
          name: 'securigyNews',
          meta: {
            title: '安全消息'
          }
        },
        {
          path: 'serviceNews',
          component: () => import('@/views/messagePage/messageType//serviceNews/serviceNews'),
          name: 'serviceNews',
          meta: {
            title: '服务消息'
          }
        },
        {
          path: 'activityNews',
          component: () => import('@/views/messagePage/messageType/activityNews/activityNews'),
          name: 'activityNews',
          meta: {
            title: '活动消息'
          }
        },
        {
          path: 'historicalNews',
          component: () => import('@/views/messagePage/messageType/historicalNews/historicalNews'),
          name: 'historicalNews',
          meta: {
            title: '历史消息'
          }
        },
        {
          path: 'faultNews',
          component: () => import('@/views/messagePage/messageType/faultNews/faultNews'),
          name: 'dictionariesManage',
          meta: {
            title: '故障消息'
          }
        }
      ]
    }]
  },


  /* 运单 */
  {
    path: '/earlyWaring',
    component: Layout,
    redirect: 'noredirect',
    name: 'earlyWaring',
    meta: {
      id: 82,
      title: '预警/报警',
      icon: 'nav-yujing'
    },
    children: [
      {
        path: 'certificateWaring',
        component: () => import('@/views/earlyWaring/certificateWarning/certificateWarning'),
        name: 'certificateWaring',
        meta: {
          id: 82,
          title: '证件到期预警',
          keepAlive: true
        }
      },
      {
        path: 'transportAlarm',
        component: () => import('@/views/transportPage/transportAlarm/transportAlarm'),
        name: 'transportAlarm',
        meta: {
          id: 82,
          title: '违规行为预警',
          keepAlive: true
        }
      },
      //运单管理
      {
        path: 'earlyWaring0',
        component: () => import('@/views/earlyWaring/earlyWaring'),
        name: 'earlyWaring0',
        meta: {
          id: 82,
          keepAlive: true,
          title: '收益预警',
        }
      },
      {
        path: '',
        component: () => import('@/views/earlyWaring/earlyWaring'),
        name: '',
        redirect: 'earlyWaring0',
        meta: {
          id: 82,
          keepAlive: true,
          title: '',
        }
      },
    ]
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true
  },
]