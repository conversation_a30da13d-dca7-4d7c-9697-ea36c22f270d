<template>
    <div class="app-container carDetail">
        <div class="main-box" style="overflow: hidden">
            <div class="base-info">
                <div class="title-box">
                    <div>基本信息</div>
                    <div>
                        <el-button size="small" type="primary" @click="goEditCar">修改车辆信息</el-button>
                        <el-button v-if="dongjie" size="small" type="danger" @click="Frozen">冻结</el-button>
                        <el-button v-if="jiedong" size="small" type="success" @click="relieveFrozen">解冻</el-button>
                    </div>
                </div>
                <detail-row>
                    <detail-col :span="6" label="车牌号" :value="data.plateNumber"></detail-col>
                    <detail-col :span="6" label="车型" :value="data.carModelName"></detail-col>
                    <detail-col :span="6" label="车牌颜色" :value="data.plateColorMessage"></detail-col>
                    <detail-col v-if="!$store.state.user.userInfo2.hasStandardModeFlag" :span="6" label="是否挂靠车辆">
                        <template v-if="data.affiliatedFlag === '0'">否</template>
                        <template v-else-if="data.affiliatedFlag === '1'">是</template>
                    </detail-col>
                    <detail-col :span="6" label="使用性质" :value="data.licensesUseCharacter"></detail-col>
                    <detail-col :span="6" label="车辆识别代号" :value="data.licensesVin"></detail-col>
                    <detail-col :span="6" label="发证机关" :value="data.licensesIssueOrg"></detail-col>
                    <detail-col :span="6" label="注册日期">
                        {{ typeof data.licensesRegisterDate === 'string' && data.licensesRegisterDate.slice(0, 10) }}
                    </detail-col>
                    <detail-col :span="6" label="发证日期">
                        {{ typeof data.licensesIssueDate === 'string' && data.licensesIssueDate.slice(0, 10) }}
                    </detail-col>
                    <detail-col :span="6" label="整备质量">
                        {{ data.curbWeight && Number(data.curbWeight) + 'KG' }}
                    </detail-col>
                    <detail-col :span="6" label="总重量" :value="data.sumCapacityTonnage && Number(data.sumCapacityTonnage) + 'KG'"></detail-col>
                    <detail-col :span="6" label="核定载质量" :value="data.capacityTonnage && Number(data.capacityTonnage) + 'KG'"></detail-col>
                    <detail-col :span="6" label="准牵引总质量" :value="data.tractionMass && data.tractionMass + 'KG'"></detail-col>
                    <detail-col :span="6" label="外廓尺寸">
                        {{ `${data.carLong} * ${data.carWeight} * ${data.carHigh} mm` }}
                    </detail-col>
                    <detail-col :span="6" label="行驶证所有人" :value="data.licenseOwner"></detail-col>
                    <detail-col :span="6" label="行驶证档案编号" :value="data.licenseNumber"></detail-col>
                    <detail-col :span="6" label="行驶证有效期">
                        {{ typeof data.licenseExpire === 'string' && data.licenseExpire.slice(0, 10) }}
                        <span class="expire-text">
                            <template v-if="licenseExpireStatus === -1">（已过期）</template>
                            <template v-else-if="licenseExpireStatus !== -2">（{{ licenseExpireStatus }}天后到期）</template>
                        </span>
                    </detail-col>
                    <detail-col :span="6" label="车辆品牌型号" :value="data.cypp"></detail-col>
                    <detail-col :span="12" label="能源类型">
                        {{ getEnergy(data.energyType) }}
                    </detail-col>
                    <detail-col :span="6" label="车辆道路运输许可证号" :value="data.shippingCert"></detail-col>
                    <detail-col :span="6" label="道路运输证有效期">
                        {{ data.shippingCertExpire }}
                        <span class="expire-text">
                            <template v-if="shippingCertExpireStatus === -1">（已过期）</template>
                            <template v-else-if="shippingCertExpireStatus !== -2">（{{ shippingCertExpireStatus }}天后到期）</template>
                        </span>
                    </detail-col>
                    <detail-col :span="6" label="从业资格证号" :value="data.employmentCert"></detail-col>
                    <detail-col :span="6" label="从业资格证有效期">
                        {{ data.employmentCertExpireDate }}
                        <span class="expire-text">
                            <template v-if="employmentCertExpireDateStatus === -1">（已过期）</template>
                            <template v-else-if="employmentCertExpireDateStatus !== -2">（{{ employmentCertExpireDateStatus }}天后到期）</template>
                        </span>
                    </detail-col>
                    <template v-if="$store.state.user.userInfo2.hasStandardModeFlag">
                        <detail-col :span="6" label="行驶证住址" :value="data.xszAddress"></detail-col>
                        <detail-col :span="6" label="行驶证发动机号码" :value="data.fdjhm"></detail-col>
                    </template>
                </detail-row>
            </div>
            <div class="drive-info">
                <div class="title-box">
                    <div>驾驶信息</div>
                </div>
                <detail-row>
                    <detail-col :span="6" label="使用状态" :value="currentCarState"></detail-col>
                    <detail-col :span="6" label="当前驾驶司机" :value="driverName"></detail-col>
                    <template v-if="!$store.state.user.userInfo2.hasStandardModeFlag">
                        <detail-col :span="6" label="收款方" :value="payeeAgent"></detail-col>
                        <detail-col :span="6" label="所属车队长" :value="ownerName"></detail-col>
                    </template>
                    <detail-col :span="6" label="认证信息所属" :value="data.memberName"></detail-col>
                </detail-row>
            </div>
            <div class="other-info">
                <div class="title-box">
                    <div>车辆资质照片</div>
                </div>
                <detail-row>
                    <detail-col :span="6" label="车辆行驶证-正页">
                        <div class="view-btn" @click="viewPic(licenseImage)"> 查看</div>
                    </detail-col>
                    <detail-col :span="6" label="车辆行驶证-副页">
                        <div class="view-btn" @click="viewPic(data.licenseBackImage)"> 查看</div>
                    </detail-col>
                    <detail-col :span="6" label="车辆行驶证-有效期页">
                        <div class="view-btn" @click="viewPic(data.licenseExpireImage)"> 查看</div>
                    </detail-col>
                    <detail-col :span="6" label="车辆道路运输证">
                        <div class="view-btn" @click="viewPic(shippingCertImage)">查看</div>
                    </detail-col>
                    <detail-col v-if="!$store.state.user.userInfo2.hasStandardModeFlag" :span="6" label="车主声明">
                        <div class="view-btn" @click="viewPic(data.statementImage)"> 查看</div>
                    </detail-col>
                    <detail-col :span="6" label="人车合照">
                        <div class="view-btn" @click="viewPic(data.carImage)"> 查看</div>
                    </detail-col>
                    <detail-col :span="6" label="从业资格证">
                        <div class="view-btn" @click="viewPic(data.employmentCertImage)"> 查看</div>
                    </detail-col>
                </detail-row>
            </div>
            <div class="driver-info">
                <div class="title-box">
                    <div>可驾驶司机</div>
                </div>
                <div class="list-box">
                    <div class="item-title">
                        <div>司机姓名</div>
                        <div>手机号</div>
                        <div>身份证号</div>
                    </div>
                    <div class="item-info" v-for="dirveItem in carDrivingList">
                        <div>{{dirveItem.driverName}}</div>
                        <div>{{dirveItem.driverMobile}}</div>
                        <div>{{dirveItem.idCard}}</div>
                    </div>
                </div>
            </div>
            <div style="float: right;margin-right: 20px;margin-bottom: 20px">
                <el-button size="mini" @click="lookMore">查看更多</el-button>
            </div>
            <div v-if="!$store.state.user.userInfo2.hasStandardModeFlag" class="transfer-info">
                <div class="title-box">
                    <div>车辆转让记录</div>
                </div>
                <div>
                    <el-table
                            :data="carTransferDescList"
                            style="width: 100%">
                        <el-table-column
                                prop="userName"
                                label="转让车队长名称"
                               >
                        </el-table-column>
                        <el-table-column
                                prop="userMobile"
                                label="转让车队长手机号"
                                >
                        </el-table-column>
                        <el-table-column
                                prop="toUserName"
                                label="被转让车队长名称">
                        </el-table-column>
                        <el-table-column
                                prop="toUserNameMobile"
                                label="被转让车队长手机号">
                        </el-table-column>
                        <el-table-column
                                prop="createDate"
                                label="转让时间">
                        </el-table-column>
                    </el-table>
                </div>
            </div>
            <div v-if="!$store.state.user.userInfo2.hasStandardModeFlag" style="float: right;margin-right: 20px;margin-bottom: 20px">
                <el-button size="mini" @click="lookMorecarTransfer">查看更多</el-button>
            </div>
        </div>
        <el-image-viewer
            v-if="showedPic"
            :on-close="closePic"
            :url-list="[showedPic]"/>
    </div>
</template>
<script>
    import ElImageViewer from 'cf-element-ui/packages/image/src/image-viewer'
    import { checkExpire } from '@/utils/date'
    export default {
        components: {ElImageViewer},
        name: "CarDetail",
        data() {
            return {
                carTransferDescList: [],
                canClick: true, //控制是否可以点击
                canClick1: true, //控制是否可以点击
                licenseImage: '',
                shippingCertImage: '',
                dongjie: true,
                jiedong: false,
                curCarId: "",
                /** 基本信息 **/
                carDrivingListTotalSize: "", //
                plateNumber: "", //车牌号
                sumCapacityTonnage: "", //总重量
                capacityTonnage: "", //载重量
                axleNumber: "", //轴数
                carModelName: "", //车型
                /** 驾驶信息 **/
                currentCarState: "",
                driverName: "",
                authStatus: "",
                payeeAgent: "",
                status: "",
                ownerName: "",
                /** 驾驶信息 **/
                shippingCert: "",
                // shippingCertExpire: "",

                /** 可驾驶司机列表 **/
                carDrivingList: [],
                showedPic: null,
                data: {},
                licenseExpireStatus: null,
                shippingCertExpireStatus: null,
                employmentCertExpireDateStatus: null
            };
        },
        methods: {
            goEditCar() {
                this.$router.push({
                    path:'/carsList/carRevise',
                    query:{
                        carId:this.curCarId,
                    }
                });
            },
            /** 获取车辆详情 **/
            getCarId() {
                let id = this.$route.query.carId;
                this.curCarId = id;
                this.$http.post("/admin-center-server/car/carDesc?carId=" + id).then(res => {
                    let data = res.data;
                    if (data.code === "200") {
                        let deatilCar = data.data;
                        this.data = deatilCar
                        this.carDrivingListTotalSize = deatilCar.carDrivingListTotalSize;
                        this.plateNumber = deatilCar.plateNumber;
                        this.sumCapacityTonnage = deatilCar.sumCapacityTonnage;
                        this.capacityTonnage = deatilCar.capacityTonnage;
                        this.axleNumber = deatilCar.axleNumber;
                        this.carModelName = deatilCar.carModelName;
                        this.ownerName = deatilCar.ownerName;
                        this.licenseImage = deatilCar.licenseImage;
                        this.shippingCertImage = deatilCar.shippingCertImage;
                        // this.shippingCertFrontSideImage = deatilCar.shippingCertFrontSideImage;

                        let status = deatilCar.status;
                        if (status === "0") {
                            this.status = "认证中";
                        } else if (status === "1") {
                            this.status = "认证成功";
                        } else if (status === "2") {
                            this.status = "认证失败";
                        }

                        let currentCarState = deatilCar.currentCarState;

                        if (currentCarState === "0") {
                            this.currentCarState = "空闲";
                            this.dongjie = true;
                            this.jiedong = false;
                        } else if (currentCarState === "1") {
                            this.currentCarState = "使用";
                            this.dongjie = true;
                            this.jiedong = false;
                        } else if (currentCarState === "2") {
                            this.currentCarState = "停用";
                            this.dongjie = true;
                            this.jiedong = false;
                        } else if (currentCarState === "3") {
                            this.currentCarState = "冻结";
                            this.dongjie = false;
                            this.jiedong = true;
                        }
                        this.driverName = deatilCar.driverName;
                        let authStatus = deatilCar.authStatus;
                        if (authStatus === "0") {
                            this.authStatus = "未认证";
                        } else if (authStatus === "1") {
                            this.authStatus = "认证成功";
                        } else if (authStatus === "2") {
                            this.authStatus = "认证失败";
                        } else if (authStatus === "3") {
                            this.authStatus = "认证中";
                        }
                        this.shippingCert = deatilCar.shippingCert;
                        let payeeAgent = deatilCar.payeeAgent;
                        if (payeeAgent === "0") {
                            this.payeeAgent = "司机";
                        } else if (payeeAgent === "1") {
                            this.payeeAgent = "车队长";
                        }
                        let carDrivingList = deatilCar.carDrivingList.slice(0, 2);
                        this.carDrivingList = carDrivingList;

                        this.carTransferDescList = deatilCar.carTransferDescList
                    }
                });
            },
            relieveFrozen() {
                if(!this.canClick1) return;
                this.canClick1=false;
                let id = this.curCarId;
                this.$http
                    .post(
                        "/admin-center-server/car/freeze?freezeStatus=" + false + "&id=" + id
                    )
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.$message.success("解冻成功");
                            this.dongjie = true;
                            this.jiedong = false;
                            this.canClick1=true;
                            this.getCarId();
                        } else if (data.code === "422") {
                            this.canClick1=true;
                            this.$message.warning(data.message);
                        }
                    });
            },
            Frozen() {
                if(!this.canClick) return;
                this.canClick=false;
                let id = this.curCarId;
                this.$http
                    .post(
                        "/admin-center-server/car/freeze?freezeStatus=" + true + "&id=" + id
                    )
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.$message.success("冻结成功");
                            this.dongjie = false;
                            this.jiedong = true;
                            this.canClick=true;
                            this.getCarId();
                        } else if (data.code === "422") {
                            this.canClick=true;
                            this.$message.warning(data.message);
                        }
                    });
            },
            lookMore() {
                let plateNumber = this.plateNumber;
                let total = this.carDrivingListTotalSize;
                this.$router.push({
                    path: "/carsList/lookMore",
                    query: {
                        plateNumber: plateNumber,
                        total: total
                    }
                });
            },
            lookMorecarTransfer(){
                let carId = this.$route.query.carId;
                this.$router.push({
                    path: "/carsList/lookMorecarTransfer",
                    query: {
                        carId: carId,
                    }
                });
            },
            viewPic(url) {
                this.showedPic = url
            },
            closePic() {
                this.showedPic = null
            },
            getEnergy(code) {
                let energyList = [
                    { value: 'A', label: '汽油' },
                    { value: 'B', label: '柴油' },
                    { value: 'C', label: '电' },
                    { value: 'D', label: '混合油' },
                    { value: 'E', label: '天然气' },
                    { value: 'F', label: '液化石油气' },
                    { value: 'L', label: '甲醇' },
                    { value: 'M', label: '乙醇' },
                    { value: 'N', label: '太阳能' },
                    { value: 'O', label: '混合动力' },
                    { value: 'Y', label: '无' },
                    { value: 'Z', label: '其他' },
                ]
                let item = energyList.find(item => item.value === code)
                return item && item.label
            }
        },
        activated() {
            this.getCarId();

            this.$watch('data.licenseExpire', {
                deep: true,
                handler(v) {
                    checkExpire(v,  true)
                    .then(res => this.licenseExpireStatus = res)
                    
                }
            })

            this.$watch('data.shippingCertExpire', {
                deep: true,
                handler(v) {
                    checkExpire(v)
                    .then(res => this.shippingCertExpireStatus = res)
                    
                }
            })

            this.$watch('data.employmentCertExpireDate', {
                deep: true,
                handler(v) {
                    checkExpire(v)
                    .then(res => this.employmentCertExpireDateStatus = res)
                    
                }
            })
        }
    };
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    ul li {
        list-style: none;
    }

    .carDetail {
        .main-box {
            background-color: white;

            .title-box {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
            }

            .list-box {
                margin-top: 20px;
                border: 1px solid #cccccc;
                border-left: none;

                .item-title {
                    display: flex;
                    flex-direction: row;

                    div {
                        width: 600px;
                        height: 50px;
                        font-weight: bold;
                        font-size: 16px;
                        line-height: 50px;
                        border: 1px solid #cccccc;
                        border-top: none;
                        border-right: none;
                        background-color: rgb(249, 252, 250);
                        text-align: center;
                    }
                }

                .item-info {
                    display: flex;
                    flex-direction: row;

                    div {
                        font-size: 14px;
                        width: 600px;
                        height: 50px;
                        line-height: 50px;
                        text-align: center;
                        border: 1px solid #cccccc;
                        border-top: none;
                        border-right: none;
                        border-bottom: none;
                    }
                }
            }

            .base-info,
            .drive-info,
            .other-info,
            .transfer-info,
            .driver-info {
                padding: 20px;
            }
        }
    }
    .view-btn {
        cursor: pointer;
        color: blue;
    }
    .expire-text {
        color: #D92929;
    }
</style>
