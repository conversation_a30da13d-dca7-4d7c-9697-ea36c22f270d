<template>
  <div>
    <div class="dispatchInfo-all">
      <div class="dispatchInfo-item">
        <div class="dispatchInfo-left">
          <div class="label-all">
            <div class="zhuang-div">装</div>
            <div class="line2"></div>
          </div>
        </div>
        <div class="dispatchInfo-right">
          <div class="dispatchInfo-right-top">
            <div>
              {{
                tableData[0].deliveryPlaceShortName &&
                tableData[0].deliveryPlaceShortName + "-"
              }}
              {{ tableData[0].deliveryPlace }}
            </div>
          </div>
          <div class="dispatchInfo-right-bottom">
            <div>
              <i class="el-icon-user"></i> 发货人:{{tableData[0].consignerName}} / {{ tableData[0].consignerPhone }}
              <span v-if="tableData[0].planLoadingTime" style="margin-left:10px;"><i class="el-icon-time"></i>{{ getDay(tableData[0].planLoadingTime) }} <span style="color: red;">{{ getHourAndminute(tableData[0].planLoadingTime) }}  </span>装货</span> 
            </div>
          </div>
        </div>
      </div>
      <div
        class="dispatchInfo-item"
        v-for="(item, index) in tableData[0].orderStops"
        :key="index"
      >
        <div class="dispatchInfo-left">
          <div class="label-all">
            <div class="jing-div">经</div>
            <div class="line2"></div>
          </div>
        </div>
        <div class="dispatchInfo-right">
          <div class="dispatchInfo-right-top nomarbottom">
            <div>{{ item.stopPlace }}</div>
          </div>
        </div>
      </div>
      <div class="dispatchInfo-item">
        <div class="dispatchInfo-left">
          <div class="label-all">
            <div class="xie-div">卸</div>
          </div>
        </div>
        <div class="dispatchInfo-right">
          <div class="dispatchInfo-right-top">
            <div>
              {{
                tableData[0].receivePlaceShortName &&
                tableData[0].receivePlaceShortName + "-"
              }}
              {{ tableData[0].receivePlace }}
            </div>
          </div>
          <div class="dispatchInfo-right-bottom">
            <div>
              <i class="el-icon-user"></i> 收货人:{{
                tableData[0].consigneeName
              }}
              / {{ tableData[0].consigneePhone }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="dispatchInfo-info">
      <detail-row class="order-row">
        <detail-col label="在途时效">
          <template>
            <span
              v-if="tableData[0].transportDurationMinutes"
              style="margin-right: 3px"
              >{{ convertMinutesToHoursAndMinutes(tableData[0].transportDurationMinutes) }}</span
            ><span style="color: #ccc" v-if="tableData[0].transportDurationMinutes"> | </span>
            <span>{{ tableData[0].distance + "公里" }}</span>
          </template>
        </detail-col>
        <detail-col label="货物信息">
          <template>
            <span style="margin-right: 3px" v-if="tableData[0].singleTon">{{
             handleNum(tableData[0].singleTon) + "吨"
            }}</span>
            <span style="color: #ccc" v-if="tableData[0].singleTon"> | </span>
            <span v-if="tableData[0].goodsVolume">{{ tableData[0].goodsVolume +'方' }}</span>
            <span style="color: #ccc" v-if="tableData[0].goodsVolume"> | </span>
            <span>{{
              tableData[0].cargoTypeClassificationValue +
              " / " +
              tableData[0].cargoType
            }}</span>
          </template>
        </detail-col>
        <detail-col label="三方打卡" :value="tableData[0].needThirdPartyCheckinPictures ? '是':'否'"></detail-col>
      </detail-row>
      <detail-row class="order-row">
        <detail-col label="运输要求" :value="tableData[0].memo"></detail-col>
      </detail-row>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tableData: Array,
  },
  methods: {
    convertMinutesToHoursAndMinutes(totalMinutes) {
      const hours = Math.floor(totalMinutes / 60); // 获取小时数
      const minutes = totalMinutes % 60; // 获取剩余的分钟数

      return `${hours}小时${minutes}分钟`;
    },
    handleNum(num) {
      if (num) {
        num = Number(num);
        return num.toFixed(3);
      }else{
        return ''
      }
    },
     // 获取小时和分钟
     getHourAndminute(time){
        if(time){
            return time.split(' ')[1].substring(0,5)
        }
    },
    // 获取时间的日期
    getDay(time){
        if(time){
            return time.split(' ')[0]
        }
    }
  }
};
</script>

<style scoped lang="scss">
.detail-row {
  margin-bottom: 0px !important;
  .detail-col {
    margin-bottom: 10px !important;
  }
}
.dispatchInfo-all {
  margin-top: 20px;
  font-size: 14px;
  color: #333;
  .dispatchInfo-item {
    display: flex;
    margin-bottom: 20px;
    height: 40px;
  }
  .dispatchInfo-left {
    width: 70px;
    display: flex;
    justify-content: end;
  }
  .dispatchInfo-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 20px;
    .dispatchInfo-right-top {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    .dispatchInfo-right-bottom {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.dispatchInfo-info {
//   margin-left: 20px;
}
.label-all {
  height: 100%;
  width: 70px;
  display: inline-block;
  position: relative;
  text-align: center;
  left: 20px;
  top: 4px;
  .zhuang-div {
    width: 28px;
    height: 28px;
    background-color: #3fba91;
    border-radius: 5px;
    display: inline-block;
    text-align: center;
    line-height: 28px;
    color: #fff;
  }
  .xie-div {
    width: 28px;
    height: 28px;
    background-color: #ed970d;
    border-radius: 5px;
    display: inline-block;
    text-align: center;
    line-height: 28px;
    color: #fff;
  }
  .jing-div {
    width: 28px;
    height: 28px;
    background-color: #4A83F6;
    border-radius: 5px;
    display: inline-block;
    text-align: center;
    line-height: 28px;
    color: #fff;
  }
  .line2 {
    height: 35px;
    left: 34px;
    position: absolute;
    width: 2px;
    top: 28px;
    border-left: 1px solid transparent;
    background: linear-gradient(white, white) padding-box,
      repeating-linear-gradient(-45deg, #ccc 0, #ccc 3px, white 0, white 5px);
  }
}
.nomarbottom{
    margin-bottom: 0px !important;
}
</style>