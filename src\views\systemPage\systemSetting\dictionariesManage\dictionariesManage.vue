<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">字典库查询</div>
    </div>
    <div class="list-box flex">
      <div style="margin-right:50px">
        <div class="list-title">
          <div>字典类型</div>
        </div>
        <div class="list-main">
          <template>
            <el-table :data="tableData" cell-class-name="table_cell_gray">
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column prop="name" label="枚举名称" width="260"></el-table-column>
              <el-table-column fixed="right" label="操作" width="100">
                <template slot-scope="scope">
                  <div class="dic_button">
                    <el-button style="border: none" @click="findDdic(scope.row)" type="text" size="small">查看字典值</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </div>
      </div>

      <div>
        <div class="list-title">
          <div>
            <span>字典值</span>
            <span>{{valTitle}}</span>
          </div>
          <div>
            <el-button @click="addType" v-if="type!=''">添加</el-button>
          </div>
        </div>
        <!-- 获取类型 -->
        <div class="list-main" v-if="type ==1">
          <template>
            <el-table border :data="dicData" cell-class-name="table_cell_gray">
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column prop="name" label="名称"></el-table-column>
              <el-table-column prop="code" label="编码"></el-table-column>
              <el-table-column fixed="right" label="操作" width="100">
                <template slot-scope="scope">
                  <el-button @click="handleClick(scope.row)" type="text" size="small">修改</el-button>
                  <el-button type="text" size="small" @click="deleteCarge(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </div>
        <!-- 车辆类型 -->
        <div class="list-main" v-if="type ==2">
          <template>
            <el-table border :data="carData" cell-class-name="table_cell_gray">
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column prop="name" label="名称"></el-table-column>
              <el-table-column prop="capacityTonnage" label="载重量"></el-table-column>
              <el-table-column fixed="right" label="操作" width="100">
                <template slot-scope="scope">
                  <el-button @click="carTypeClick(scope.row)" type="text" size="small">修改</el-button>
                  <el-button type="text" size="small" @click="deleteCarType(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </div>
        <!-- 地区编码 -->
        <div class="list-main" v-if="type ==3">
          <template>
            <el-table border :data="codeData" cell-class-name="table_cell_gray">
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column prop="name" label="区域名称"></el-table-column>
              <el-table-column prop="code" label="编码"></el-table-column>
              <el-table-column fixed="right" label="操作" width="100">
                <template slot-scope="scope">
                  <el-button @click="handleCodeClik(scope.row)" type="text" size="small">修改</el-button>
                  <el-button type="text" size="small" @click="deleteCodeType(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </div>

        <!-- 油气比例 -->
        <div class="list-main" v-if="type ==4">
          <template>
            <el-table border :data="oilData" cell-class-name="table_cell_gray">
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column prop="ratio" label="油气比例"></el-table-column>
              <el-table-column prop="code" label="编码"></el-table-column>
              <el-table-column fixed="right" label="操作" width="100">
                <template slot-scope="scope">
                  <el-button @click="handleOilEdit(scope.row)" type="text" size="small">修改</el-button>
                  <el-button type="text" size="small" @click="deleteOilType(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </div>
        <!-- 补贴扣款 -->
        <div class="list-main" v-if="type==5 || type==6 || type==7 || type==12">
          <template>
            <el-table border :data="costData" cell-class-name="table_cell_gray">
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column prop="label" min-width="120" label="名称"></el-table-column>
              <el-table-column prop="value" label="编码"></el-table-column>
              <el-table-column fixed="right" label="操作" width="100">
                <template>
                  -
                  <!-- <el-button @click="handleCostEdit(scope.row)" type="text" size="small">修改</el-button>
                  <el-button type="text" size="small" @click="deleteCostType(scope.row)">删除</el-button> -->
                </template>
              </el-table-column>
            </el-table>
          </template>
        </div>
        <!-- 投保货物类型 -->
        <div class="list-main" v-if="type==8 || type==11">
          <template>
            <el-table :data="costData" cell-class-name="table_cell_gray">
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column prop="label" :label="type==8 ? '货物类型' : '异常类型'"></el-table-column>
              <el-table-column prop="value" :label="type==8 ? '货物类型编码' : '异常类型编码'"></el-table-column>
              <el-table-column fixed="right" label="操作" width="100">
                <template scope="scope">
                  <el-button @click="handleInsureTypeEdit(scope.row)" type="text" size="small">修改</el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </div>
        <!-- 车长车型 -->
        <div class="list-main" v-if="type==9||type==10">
          <template>
            <el-table :data="costData" cell-class-name="table_cell_gray">
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column prop="name" label="名称"></el-table-column>
              <el-table-column prop="usageFrequency" label="是否常用">
                <template scope="scope">
                  {{scope.row.usageFrequency == 1 ? '常用' : '不常用'}}
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="100">
                <template scope="scope">
                  <el-button @click="handleCarModelEdit(scope.row)" type="text" size="small">修改</el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </div>
      </div>
    </div>

    <!-- 按货物类型添加的弹窗 -->
    <el-dialog title="货物类型" :visible.sync="dialogForCargoType" class="dialog">
      <el-form label-width="100px" class="demo-ruleForm"  :rules="rules" ref="cargotype" :model="cargotype">
        <el-form-item label="名称">
          <el-input
            v-model="cargotype.label"
            style="width: 260px"
            placeholder="请输入货物类型名称"
            @input="change($event)"
          ></el-input>
        </el-form-item>
        <el-form-item label="编码">
          <el-input
            v-model="cargotype.value"
            style="width: 260px"
            placeholder="请输入货物类型编码"
            @input="change($event)"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogForCargoType = false">取 消</el-button>
        <el-button type="primary" @click="updateCargoTypeSure" v-if="isAddFlag">确定添加</el-button>
        <el-button type="primary" @click="updateCargoTypeSure" v-if="!isAddFlag">确定更改</el-button>
      </div>
    </el-dialog>

    <!-- 按车辆类型添加的弹窗 -->
    <el-dialog title="车辆类型" :visible.sync="dialogForCarType">
      <el-form :model="carType" label-width="100px" class="demo-ruleForm">
        <el-form-item label="名称" required>
          <el-input v-model="carType.name" style="width: 260px"></el-input>
        </el-form-item>
        <el-form-item label="载重量" required>
          <el-input v-model="carType.capacityTonnage" style="width: 260px"></el-input>
        </el-form-item>
        <el-form-item label="编码" required>
          <el-input v-model="carType.code" style="width: 260px"></el-input>
        </el-form-item>
        <!-- <el-form-item label="排序" prop="name">
          <el-input v-model="ruleForm.name" style="width: 260px"></el-input>
        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogForCarType = false">取 消</el-button>
        <el-button type="primary" @click="addCartype" v-if="isAddFlag">确定添加</el-button>
        <el-button type="primary" @click="upDateCartype" v-if="!isAddFlag">确定更改</el-button>
      </div>
    </el-dialog>
    <!-- 按地区编码添加的弹窗 -->
    <el-dialog title="地区编码" :visible.sync="dialogForRegonCode">
      <el-form :model="codeType" label-width="100px" class="demo-ruleForm">
        <el-form-item label="区域名称" required>
          <el-input v-model="codeType.name" style="width: 260px"></el-input>
        </el-form-item>
        <el-form-item label="区域编码" required>
          <el-input v-model="codeType.code" style="width: 260px"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogForRegonCode = false">取 消</el-button>
        <el-button type="primary" @click="addCodeype" v-if="isAddFlag">确定添加</el-button>
        <el-button type="primary" @click="updateCodeType" v-if="!isAddFlag">确定更改</el-button>
      </div>
    </el-dialog>

    <!-- 油气比例添加的弹窗 -->
    <el-dialog title="油气比例" :visible.sync="dialogForOil">
      <el-form :model="carType" label-width="100px" class="demo-ruleForm">
        <el-form-item label="名称" required>
          <el-input
            v-model="oilType.ratio"
            style="width: 260px"
            @input="change($event)"
            oninput="value=value.match(/\d+\.?\d{0,2}/,'')"
          >
            <template slot="append">%</template>
          </el-input>
        </el-form-item>
        <el-form-item label="编码" required>
          <el-input v-model="oilType.code" style="width: 260px" @input="change($event)"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogForOil = false">取 消</el-button>
        <el-button type="primary" @click="addOilType" v-if="isAddFlag">确定添加</el-button>
        <el-button type="primary" @click="updateOilType" v-if="!isAddFlag">确定更改</el-button>
      </div>
    </el-dialog>
    <!-- 费用补贴 -->
    <el-dialog :title="((type == 5 || type == 6 || type == 7 || type ==12) ? ('费用类型' + valTitle) : (type == 9 ? '车长' : '车型'))" :visible.sync="dialogForCost">
      <el-form v-if="(type == 5 || type == 6 || type == 7 || type ==12)" :model="costType" label-width="100px" class="demo-ruleForm" :rules="rules" ref="costRuleForm">
        <el-form-item label="名称" prop="label">
          <el-input v-model="costType.label" style="width: 260px"></el-input>
        </el-form-item>
        <el-form-item label="编码" prop="value">
          <el-input v-model="costType.value" style="width: 260px"></el-input>
        </el-form-item>
      </el-form>
      <el-form v-else :model="costType" label-width="100px" class="demo-ruleForm" :rules="rules" ref="costRuleForm">
        <el-form-item label="名称" prop="label">
          <el-input v-model="costType.label" style="width: 260px"></el-input>
        </el-form-item>
        <el-form-item label="是否常用" prop="value">
          <el-select v-model="costType.value" placeholder="请选择">
            <el-option label="常用" value="1"> </el-option>
            <el-option label="不常用" value="2"> </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogForCost = false">取 消</el-button>
        <el-button type="primary" @click="handleCostAdd('costRuleForm')">确定添加</el-button>
      </div>
    </el-dialog>
     <!-- 投保货物类型 -->
    <el-dialog :title="(type == 11 ? '司机异常报备类型': '投保货物类型')" :visible.sync="dialogForInsureType">
      <el-form :model="insureTypeForm" label-width="100px" class="demo-ruleForm" :rules="rules" ref="insureTypeRuleForm">
        <el-form-item label="名称" prop="label">
          <el-input v-model="insureTypeForm.label" style="width: 260px"></el-input>
        </el-form-item>
        <el-form-item label="编码" prop="value">
          <el-input v-model="insureTypeForm.value" style="width: 260px"></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogForInsureType = false">取 消</el-button>
        <el-button type="primary" @click="handleInsureTypeAdd('insureTypeRuleForm')">确定添加</el-button>
      </div>
    </el-dialog>
    <!-- 车型车长编辑 -->
    <el-dialog :title="(type == 9 ? '车长' : '车型')" :visible.sync="dialogForCarModel">
      <el-form :model="costType" label-width="100px" class="demo-ruleForm" :rules="rules" ref="costRuleForm">
        <el-form-item label="名称" prop="label">
          <el-input v-model="costType.label" style="width: 260px"></el-input>
        </el-form-item>
        <el-form-item label="是否常用" prop="value">
          <el-select v-model="costType.value" placeholder="请选择">
            <el-option label="常用" value="1"> </el-option>
            <el-option label="不常用" value="2"> </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogForCarModel = false">取 消</el-button>
        <el-button type="primary" @click="clickEditCarModel('costRuleForm')">确定更改</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "CarsList",
  data() {
    return {
      formLabelWidth: "100px",
      dialogForCargoType: false, //货物类型的弹窗
      dialogForCarType: false, //车辆类型的弹窗
      dialogForRegonCode: false, //  地区编码的弹窗
      dialogForOil: false, //油气比例
      dialogForCost: false,
      dialogForInsureType: false,
      type: "", //1 货物类型 ；2 车辆类型；3 地区编码；4油气比例
      valTitle: "",
      isAddFlag: true, //添加的flag
      tableData: [
        {
          type: "1",
          name: "货物类型"
        },
        {
          type: "2",
          name: "车辆类型"
        },
        {
          type: "9",
          name: "车长"
        },
        {
          type: "10",
          name: "车型"
        },
        {
          type: "11",
          name: "司机异常报备类型"
        },
        {
          type: "3",
          name: "地区编码"
        },
        {
          type: "4",
          name: "油气费用占运费比例"
        },
        {
          type: "5",
          name: "上游补贴费用类型（运营端）"
        },
        {
          type: "6",
          name: "上游扣款费用类型（运营端）"
        },
        {
          type: "7",
          name: "下游补贴费用类型（运营端）"
        },
        {
          type: "12",
          name: "下游扣款费用类型（运营端）"
        },
        {
          type: "8",
          name: "投保货物类型"
        }
      ],//  投保货物类型与司机异常报备；如果以后新增字典，都是名称与编码，那么都用此逻辑
      dicData: [],
      carData: [], //车量字典值
      codeData: [], //编码值
      oilData: [], //油气编码
      costData: [],
      cargotype: {
        //货物类型
        name: "",
        code: ""
      },
      carType: {
        //车辆类型
        name: "",
        code: "",
        capacityTonnage: "" //载重量
      },
      oilType: {
        ratio: "",
        code: ""
      },
      oilTypeId: "",
      oilTypeCode: "",

      cargoTypeId: "",
      carTypeId: "",
      codeTypeId: "",
      codeType: {
        //地区编码
        name: "",
        code: ""
      },
      costType: {
        label:'',
        value: ''
      },
      rules: {
        label: [
          { required: true, message: '请填写名称', trigger: 'blur' }
        ],
        value: [
          { required: true, message: '请填写编码', trigger: 'blur' }
        ]
      },
      insureTypeForm: {
        label:'',
        value: ''
      },
      dic: {
        total: null
      },
      ruleForm: {},
      editCarModel: {},
      dialogForCarModel: false
    };
  },
  activated() {},
  methods: {
    /* 获取 字典类型列表*/
    // getDicList() {
    //   let postData = {
    //     pageNumber: 0,
    //     pageSize: 20000
    //   };
    //   this.$http
    //     .get(
    //       "/admin-center-server/cargotype/list",
    //       this.$qs.stringify(postData)
    //     )
    //     .then(res => {
    //       let data = res.data;
    //       if (data.code == "200") {
    //         // this.tableData = data.data;
    //         this.dic.total = data.total;
    //       } else {
    //         this.$message.warning(data.message);
    //       }
    //     });
    // },
    onSubmit() {
      // console.log("submit!");
    },

    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row);
        });
      } else {
        this.$refs.multipleTable.clearSelection();
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    /* 添加 */
    addType() {
      // 货物类型
      this.cargotype={}
      // 油气
      this.oilType.ratio = "";
      this.oilType.code = "";

      this.isAddFlag = true;
      // 车辆类型
      this.carType = {};
      // 地区编码
      this.codeType={}
      if (this.type == 1) {
        //货物类型的弹窗
        this.dialogForCargoType = true;
      } else if (this.type == 2) {
        //车辆类型的弹窗
        this.dialogForCarType = true;
      } else if (this.type == 3) {
        //地区编码的弹窗
        this.dialogForRegonCode = true;
      } else if (this.type == 4) {
        //油气比例
        this.dialogForOil = true;
      } else if (this.type == 5 || this.type == 6 || this.type == 7 || this.type == 9 || this.type == 10 ||this.type == 12) {
        this.dialogForCost = true
      } else {
        this.dialogForInsureType = true
      }
    },
    /* 修改货物类型 */
    handleClick(row) {
      // console.log(row);

      this.cargoTypeId = row.id;

      // this.cargotypeDetail(row); //回显
      this.cargotype = {...row}
      this.dialogForCargoType = true;

      this.isAddFlag = false;
    },

    /* 查看字典值 */
    findDdic(row) {
      switch (row.type) {
        case '1':
          this.type = 1;
          this.valTitle = "（货物类型）";
          this.gitTypeDic();
          break
        case '2':
          this.valTitle = "（车辆类型）";
          this.type = 2;
          this.gitCarDic();
          break
        case '3':
          this.valTitle = "（地区编码）";
          this.type = 3;
          this.gitCodeDic();
          break
        case '4':
          this.valTitle = "（油气占比）";
          this.type = 4;
          this.getOilDic();
          break
        case '5':
          this.valTitle = "（上游补贴）";
          this.type = 5;
          this.getCostDic();
          break
        case '6':
          this.valTitle = "（上游扣款）";
          this.type = 6;
          this.getCostDic();
          break
        case '7':
          this.valTitle = "（下游补贴）";
          this.type = 7;
          this.getCostDic();
          break
        case '8':
          this.valTitle = "（投保货物类型）";
          this.type = 8;
          this.getCostDic();
        break
        case '9':
          this.valTitle = "（车长）";
          this.type = 9;
          this.getCostDic();
        break
        case '10':
          this.valTitle = "（车型）";
          this.type = 10;
          this.getCostDic();
          break
        case '11':
          this.valTitle = "（司机异常报备类型）";
          this.type = 11;
          this.getCostDic();
          break
        case '12':
          this.valTitle = "（下游扣款）";
          this.type = 12;
          this.getCostDic();
          break
        default:
          break
      }
    },

    /* 获取类型字典 */
    gitTypeDic() {
       this.$http
        .get("/order-center-server/order/dict/findDictByType?dictType=cargoType")
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            let arr = data.data
            arr.forEach(ele=>{
              ele.name = ele.label
              ele.code = ele.value
            })
            this.dicData = arr
            // this.dic.total = data.data.total;
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 获取 车辆管理字典*/
    gitCarDic() {
      this.$http
        .get("/admin-center-server/carmodel/list?pageNumber=1&pageSize=5000")
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.carData = data.data.list;
            this.dic.total = data.data.total;
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 获取 地区编码字典*/
    gitCodeDic() {
      this.$http
        .get("/admin-center-server/area_code/list?pageNumber=1&pageSize=5000")
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.codeData = data.data.list;
            this.dic.total = data.data.total;
          } else {
            this.$message.warning(data.message);
          }
        });
    },

    /* 油气比例字典 */
    getOilDic() {
      this.$http.get("/admin-center-server/oilRatio/listOilRatio").then(res => {
        let data = res.data;
        if (data.code == "200") {
          this.oilData = data.data;
          this.dic.total = data.data.total;
        } else {
          this.$message.warning(data.message);
        }
      });
    },

    /* 获取补贴扣款字典*/
    getCostDic() {
      let dictType
      switch (this.type) {
        case 5:
          dictType = 'subsidyUp'
          break;
        case 6:
          dictType = 'deductionUp'
          break;
        case 7:
          dictType = 'subsidyDown'
          break;
        case 8:
          dictType = 'insuranceGoodType'
          break;
        case 9:
          dictType = '1'
          break;
        case 10:
          dictType = '2'
          break
        case 11:
          dictType = 'driverExceptionRep'
          break;
        case 12:
          dictType = 'deductionDown'
          break;
      }
      if (this.type == 9 || this.type == 10) {
        this.$http.get("/admin-center-server/carmodel/getCarModelListByCategory?category=" + dictType).then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.costData = data.data;
            this.dic.total = data.data.total;
          } else {
            this.$message.warning(data.message);
          }
        });
      } else {
        this.$http.get("/admin-center-server/order/dict/findDictByType?dictType=" + dictType).then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.costData = data.data;
            this.dic.total = data.data.total;
          } else {
            this.$message.warning(data.message);
          }
        });
      }
      
    },

    handleCostAdd(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let dictType
          switch (this.type) {
            case 5:
              dictType = 'subsidyUp'
              break;
            case 6:
              dictType = 'deductionUp'
              break;
            case 7:
              dictType = 'subsidyDown'
              break;
            case 9:
              dictType = '1'
              break;
            case 10:
              dictType = '2'
              break;
            case 12:
              dictType = 'deductionDown'
              break;  
          }
          if (this.type == 9 || this.type == 10) {
            let params = {
              usageFrequency: this.costType.value,
              name: this.costType.label,
              category: dictType
            }
            this.$post('admin-center-server/carmodel/addCarModelByCategory', params).then(
              res => {
                this.dialogForCost = false
                this.$message.success('添加成功')
                this.costType = {
                  label: '',
                  value: ''
                }
                this.getCostDic()
              }
            )
          } else {
            this.$post('/admin-center-server/order/dict/addDictByType', {...this.costType, dictType}).then(
              res => {
                this.dialogForCost = false
                this.$message.success('添加成功')
                this.costType = {
                  label: '',
                  value: ''
                }
                this.getCostDic()
              }
            )
          }
          
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleCarModelEdit(model) {
      this.editCarModel = model
      this.costType = {label: model.name, value: model.usageFrequency}
      this.dialogForCarModel = true
    },
    clickEditCarModel(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let dictType
          switch (this.type) {
            case 9:
              dictType = '1'
              break;
            case 10:
              dictType = '2'
              break;
          }
          let params = {
            usageFrequency: this.costType.value,
            name: this.costType.label,
            category: dictType
          }
          params = {...this.editCarModel, ...params}
          this.$post('admin-center-server/carmodel/editCarModel', params).then(
            res => {
              this.dialogForCarModel = false
              this.$message.success('更改成功')
              this.costType = {
                label: '',
                value: ''
              }
              this.getCostDic()
            }
          )
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleInsureTypeAdd(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {

          let dictType
          switch (this.type) {
            case 8:
              dictType = 'insuranceGoodType'
              break;
            case 11:
              dictType = 'driverExceptionRep'
              break;
          }
          let url = this.insureTypeForm.id ? '/admin-center-server/order/dict/updateDictById' : '/admin-center-server/order/dict/addDictByType'
          this.$post(url, {...this.insureTypeForm, dictType}).then(
            res => {
              this.dialogForInsureType = false
              this.$message.success('添加成功')
              this.insureTypeForm = {
                label: '',
                value: ''
              }
              this.getCostDic()
            }
          )
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleInsureTypeEdit(item) {
      this.dialogForInsureType = true
      this.insureTypeForm = item
    },
    /* 编辑油气比例字典 */
    handleOilEdit(row) {
      this.oilTypeId = row.id;
      this.oilTypeCode = row.code;

      this.isAddFlag = false;
      this.dialogForOil = true;
      this.oilType.ratio = parseFloat(row.ratio);
      this.oilType.code = row.code;
    },
    /* 编辑油气比例 */
    updateOilType() {
      if (this.oilType.ratio == "") {
        this.$message({
          type: "warning",
          message: "请输入油气比例"
        });
        return;
      }
      if (this.oilType.ratio > 100) {
        this.$message({
          type: "warning",
          message: "油气比例不能超过100"
        });
        return;
      }
      if (this.oilType.code == "") {
        this.$message({
          type: "warning",
          message: "请输入油气编码"
        });
        return;
      }
      let postData = {
        id: this.oilTypeId,
        code: this.oilType.code,
        ratio: this.oilType.ratio + "%"
      };
      this.$http
        .post("/admin-center-server/oilRatio/editOilRatio", postData)
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.$message.success("更改成功");
            this.dialogForOil = false;
            this.getOilDic();
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 删除货物类型 */
    deleteCarge(row) {
      // console.log(row);
      let id = row.id;
      this.$http
        .post("/admin-center-server/order/dict/deleteDictById?dictDataId=" + id)
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.$message({
              type: "success",
              message: "删除成功"
            });
            this.gitTypeDic(); //刷新
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 输入时的事件 */
    change(e) {
      this.$forceUpdate();
    },
    /* 添加车辆类型 */
    addCartype() {
      if (this.carType.name == "") {
        this.$message({
          type: "warning",
          message: " 请输入车辆名称"
        });
        return;
      }
      if (this.carType.capacityTonnage == "") {
        this.$message({
          type: "warning",
          message: " 请输入载重量"
        });
        return;
      }
      if (this.carType.code == "") {
        this.$message({
          type: "warning",
          message: " 请输入编码"
        });
        return;
      }
      let postData = {
        name: this.carType.name,
        code: this.carType.code,
        capacityTonnage: this.carType.capacityTonnage
      };
      this.$http
        .post(
          "/admin-center-server/carmodel/save",
          this.$qs.stringify(postData)
        )
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.$message({
              type: "success",
              message: "添加成功"
            });
            this.dialogForCarType = false;
            this.carType = {};
            this.gitCarDic();
          } else {
            this.$message.warning(data.message);
          }
        });
    },

    /* 添加编码类型 */
    addCodeype() {
      if (this.codeType.name == "") {
        this.$message({
          type: "warning",
          message: "请输入区域名称"
        });
        return;
      }
      if (this.codeType.code == "") {
        this.$message({
          type: "warning",
          message: "请输入区域编码"
        });
        return;
      }

      let postData = this.codeType; //编码
      this.$http
        .post(
          "/admin-center-server/area_code/save",
          this.$qs.stringify(postData)
        )
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.$message({
              type: "success",
              message: "添加成功"
            });
            this.dialogForRegonCode = false;
            this.codeType = {};
            this.gitCodeDic();
          } else {
            this.$message.warning(data.message);
          }
        });
    },

    /* 添加油气比例 */
    addOilType() {
      if (this.oilType.ratio == "") {
        this.$message({
          type: "warning",
          message: "请输入油气比例"
        });
        return;
      }
      if (this.oilType.ratio > 100) {
        this.$message({
          type: "warning",
          message: "油气比例不能超过100"
        });
        return;
      }
      if (this.oilType.code == "") {
        this.$message({
          type: "warning",
          message: "请输入油气编码"
        });
        return;
      }

      let postData = {
        ratio: this.oilType.ratio + "%",
        code: this.oilType.code
      }; //编码

      this.$http
        .post(
          "/admin-center-server/oilRatio/editOilRatio", //id 传null  代表 增加
          postData
        )
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.$message({
              type: "success",
              message: "添加成功"
            });
            this.dialogForOil = false;
            this.oilType = {};
            this.getOilDic();
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 确定更改货物类型 */
    updateCargoTypeSure() {
      console.log("***",this.$refs['cargotype'])
      this.$refs['cargotype'].validate((valid) => {
        if (valid) {
          let url = this.cargotype.id ? '/admin-center-server/order/dict/updateDictById' : '/admin-center-server/order/dict/addDictByType'
          this.$post(url, {...this.cargotype, dictType: 'cargoType'}).then(
            res => {
              this.dialogForCargoType = false
              if(this.cargotype.id){
                this.$message.success('修改成功')
              }else{
                this.$message.success('添加成功')
              }
              this.cargotype = {
                label: '',
                value: ''
              }
              this.gitTypeDic()
            }
          )
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    /* 修改车辆类型*/
    carTypeClick(row) {
      // console.log(row);
      this.isAddFlag = false;
      this.dialogForCarType = true;
      this.carTypeId = row.id;
      this.carTypeDetail();
    },
    /* 修改车辆类型的回显数据 */
    carTypeDetail() {
      this.$http
        .get("/admin-center-server/carmodel/descByid?id=" + this.carTypeId)
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.carType.name = data.data.name;
            this.carType.code = data.data.code;
            this.carType.capacityTonnage = data.data.capacityTonnage;
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 修改车辆类型 */
    upDateCartype() {
      let postData = {
        capacityTonnage: this.carType.capacityTonnage,
        name: this.carType.name,
        code: this.carType.code,
        id: this.carTypeId
      };
      this.$http
        .post(
          "/admin-center-server/carmodel/update",

          this.$qs.stringify(postData)
        )
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.$message({
              type: "success",
              message: "更改成功"
            });
            this.dialogForRegonCode = false;
            this.gitCarDic();
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 删除车辆类型 */
    deleteOilType(row) {
      let id = row.id;
      this.$http
        .post("/admin-center-server/oilRatio/deleteOilRatio/" + id)
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.$message({
              type: "success",
              message: "删除成功"
            });
            this.getOilDic(); //刷新
          } else {
            this.$message.warning(data.message);
          }
        });
    },

    /* 地区编码的回显*/
    codeTypeDetail() {
      this.$http
        .get("/admin-center-server/area_code/descByid?id=" + this.codeTypeId)
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.carType.name = data.data.name;
            this.carType.code = data.data.code;
            this.carType.capacityTonnage = data.data.capacityTonnage;
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /*  修改地区编码*/
    handleCodeClik(row) {
      this.dialogForRegonCode = true;
      this.isAddFlag = false;
      this.codeTypeId = row.id;
      this.codeTypeDetail();
    },
   // 提交地区编码
    updateCodeType(row){
      if (this.codeType.name == "") {
        this.$message({
          type: "warning",
          message: "请输入区域名称"
        });
        return;
      }
      if (this.codeType.code == "") {
        this.$message({
          type: "warning",
          message: "请输入区域编码"
        });
        return;
      }
      let data = {
        id: this.codeTypeId,
        name: this.codeType.name,
        code: this.codeType.code
      }
      this.$http
        .post(
          "/admin-center-server/area_code/update",
          this.$qs.stringify(data)
        )
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.$message({
              type: "success",
              message: "更改成功"
            });
            this.dialogForRegonCode = false;
            this.gitCodeDic();
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 删除地区编码 */
    deleteCodeType(row) {
      let id = row.id;
      this.$http
        .post("/admin-center-server/area_code/delete?id=" + id)
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.$message({
              type: "success",
              message: "删除成功"
            });
            this.gitCodeDic(); //刷新
          } else {
            this.$message.warning(data.message);
          }
        });
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-table__fixed-right::before, .el-table__fixed::before {
  background-color: white;
  width: 0;
}
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }

    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }
  }
}

.flex {
  display: flex;
  // justify-content: space-between;
  // justify-content: center;
}
.dic_button {
  border: none;
  height:35px;
  line-height: 35px;
}

.table_cell_center {
  text-align: center;
  border: 1px solid red;
}
</style>
