<template>
  <div class="upload-wrapper" :style="{width: trimSize[0] + 'px'}">
    <div class="upload-box" ref="upload" :style="{height: trimSize[1] + 'px'}">
      <div v-if="url" class="uploaded" @click="viewPic">
        <img :src="url" class="upload-pic" key="pic1">
      </div>
      <div v-else-if="type === 'upload'" class="not-upload">
        <img src="@/assets/images/upload/cloud.png" @click="upload" class="upload-btn" key="pic2">
      </div>
    </div>
    <div class="operate" v-if="type === 'upload'">
      <template v-if="url">
        <el-button class="del" type="text" @click="del">删除，重新上传</el-button>
      </template>
      <template v-else>
        <el-button type="text" @click="upload">点击上传</el-button>文件，或拖拽上传
      </template>
    </div>
    <el-image-viewer v-if="isViewerShow"
      :on-close="handlePicClose"
      :url-list="[url]"/>
  </div>
</template>

<script>
import { selectAndUpload, FileDragger } from '@/utils/file'
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
export default {
  components: {
    ElImageViewer
  },
  props: {
    type: {
      default: 'viewer' // viewer upload
    },
    defaultUrl: String,
    size: {
      default: 'small' //small mudium large [width, height]
    }
  },
  data() {
    return {
      url: '',
      isViewerShow: false,
      sizeInfo: {
        small: [270, 174],
        medium: [330, 210],
        large: [400, 261]
      },
      trimSize: []
    }
  },
  mounted() {
    let dragger = new FileDragger(this.$refs.upload, {
      upload: true,
      accept: '.png,.jpg'
    })
    dragger.startDragUpload()
    dragger.on('upload', data => {
      this.handleUpload(data.url)
    })
  },
  methods: {
    upload() {
      selectAndUpload({
        accept: '.png,.jpg'
      })
        .then(data => {
          this.handleUpload(data.url)
        })
    },
    handleUpload(url) {
      this.url = url
      this.$emit('change', url)
    },
    viewPic() {
      this.isViewerShow = true
      
      //为了避免图片被挡在对话框后面
      this.$nextTick(() => {
        let image = document.querySelector('.el-image-viewer__wrapper')
        let imageZindex = Number(getComputedStyle(image).zIndex)
        let dialogs = document.querySelectorAll('.el-dialog__wrapper')
        if (!dialogs) return
        let maxZIndex = 0
        Array.from(dialogs).forEach(v => {
          let zIndex = Number(getComputedStyle(v).zIndex)
          if (maxZIndex < zIndex) maxZIndex = zIndex
        })
        if (imageZindex < maxZIndex) image.style.zIndex = maxZIndex + 1
      })
    },
    handlePicClose() {
      this.isViewerShow = false
    },
    del() {
      this.url = ''
      this.$emit('change', this.url)
    }
  },
  watch: {
    defaultUrl: {
      immediate: true,
      handler(v) {
        if (!v) {
          this.url = ''
          return
        }
        this.url = this.defaultUrl
      }
    },
    size: {
      immediate: true,
      handler(v) {
        if (typeof v === 'string') {
          this.trimSize = this.sizeInfo[v]
        } else {
          this.trimSize = v
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .upload-wrapper {
    display: inline-block;
  }
  .upload-box {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px dashed #DCDFE6;
    background: center center no-repeat;
  }
  .operate {
    height: 40px;
    margin-top: 5px;
    text-align: center;
    color: #333;
    font-size: 14px;
    line-height: 14px;
  }
  .del {
    margin-left: 10px;
  }
  .uploaded {
    position: relative;
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url(~@/assets/images/big.png) no-repeat center center;
      cursor: pointer;
    }
  }
  .upload-pic {
    width: 90%;
    height: 90%;
  }
  .upload-btn {
    -webkit-user-drag: none;
    cursor: pointer;
  }
</style>