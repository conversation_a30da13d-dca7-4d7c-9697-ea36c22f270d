<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">筛选查询</div>
      <div class="select-info">
        <el-form :inline="true" :model="ruleForm" class="demo-form-inline" label-width="150px">
          <el-form-item label="客户名称:">
            <el-input v-model="form.name" placeholder="请输入客户名称"></el-input>
          </el-form-item>

          <el-form-item label="客户手机号:">
            <el-input v-model="form.mobile" placeholder="请输入客户手机号" maxlength="11"></el-input>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="queryFun">查询</el-button>
          </el-form-item>
        </el-form>
        <el-form
          :inline="true"
          :model="ruleForm"
          class="demo-form-inline"
          label-width="150px"
          style="margin-top: 30px"
        ></el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
        <div>
          <!-- <el-button class="releaseMessage" @click="addFeePercent">新增服务费比例</el-button> -->
        </div>
      </div>
      <div class="list-main">
        <template>
          <el-table
            ref="multipleTable"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            border
            :cell-style="{
              'text-align': 'center'
            }"
          >
            <el-table-column type="index" width="50" label="序号"></el-table-column>
            <el-table-column prop="name" label="客户名称"></el-table-column>
            <el-table-column prop="mobile" label="客户手机号"></el-table-column>
            <!-- <el-table-column prop="oilRatio" label="油费比例"></el-table-column> -->
            <el-table-column prop="gradeStr" label="油耗来源"></el-table-column>
            <el-table-column prop="taxThreshold" label="服务费比例"></el-table-column>
            <el-table-column prop="statusStr" label="状态"></el-table-column>
            <el-table-column prop="operateName" label="操作人姓名"></el-table-column>
            <el-table-column prop="operateTime" label="操作时间"></el-table-column>
            <el-table-column fixed="right" label="操作" width="160">
              <template slot-scope="scope">
                <!-- <el-button @click="goDetail(scope.row)" type="text" size="small">查看</el-button> -->
                <el-button type="text" size="small" @click="modify(scope.row)">修改</el-button>
                <!-- <el-button type="text" size="small" @click="deleteItem(scope.row)">删除</el-button> -->
              </template>
            </el-table-column>
          </el-table>
          <div class="page">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="form.pageNumber"
              :page-sizes="[20, 40, 60, 80,100]"
              :page-size="form.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              style="margin: 10px auto"
            ></el-pagination>
          </div>

          <!-- 新增服务费的弹窗 -->
          <el-dialog title="服务费比例" :visible.sync="addFeePercentDialog" class="addFeePercentDialog">
            <el-form :model="ruleForm" status-icon label-width="130px" class="demo-ruleForm">
              <el-form-item label="客户名称" required>
                <el-autocomplete
                  class="inline-input mediaInput"
                  v-model="selectAccount"
                  :fetch-suggestions="querySearch"
                  placeholder="输入关键字进行查询"
                  @select="handleSelect"
                  :trigger-on-focus="false"
                  :disabled="true"
                ></el-autocomplete>
              </el-form-item>

              <!-- <el-form-item label="客户手机号" required>
                <el-input
                  v-model="mobile"
                  style="width: 200px"
                  :disabled="phoneFlag"
                  maxlength="11"
                ></el-input>
              </el-form-item> -->
              <el-form-item label="油耗来源" required>
                <el-input
                  v-model="gradeStr"
                  style="width: 200px"
                  :disabled="true"
                ></el-input>
              </el-form-item>
              <el-form-item label="服务费比例" required>
                <el-input
                  v-model="ruleForm.taxThreshold"
                  style="width: 300px"
                  @keyup.native="keyupEvent($event,ruleForm.taxThreshold)"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
              <el-form-item label="允许使用自有油耗" v-if="grade == 5">
                <el-switch
                  v-model="ruleForm.status"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  active-value="1"
                  inactive-value="0">
                </el-switch>
              </el-form-item>
              <el-form-item>
                <!-- <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button> -->
                <el-button type="primary" @click="submitInfo">提交</el-button>
                <!-- <el-button @click="resetForm">重置</el-button> -->
              </el-form-item>
            </el-form>
          </el-dialog>

          <!-- 自由油耗修改的弹框 -->
          <el-dialog title="服务费比例" :visible.sync="addPersonalOilDiaLog" class="addPersonalOilDiaLog">
            <el-form :model="ownUserTaxForm" status-icon label-width="10px" class="demo-ruleForm" label-position="right">
              <el-form-item label="" required label-width="10px">
                <span>客户名称 </span>
                <el-autocomplete
                  class="inline-input mediaInput"
                  v-model="selectAccount"
                  :fetch-suggestions="querySearch"
                  placeholder="输入关键字进行查询"
                  @select="handleSelect"
                  :trigger-on-focus="false"
                  :disabled="true"
                ></el-autocomplete>
                <span style="margin-left: 20px; margin-right: 10px">油耗来源</span>
                <el-input
                  v-model="gradeStr"
                  style="width: 100px"
                  :disabled="true"
                ></el-input>
              </el-form-item>
              <el-form-item label="" required>
                <span>油费比例下限 </span>
                <el-input
                v-model="ownUserTaxForm.oilRatioLowerLimit"
                style="width: 68px;"
                type="number"
                oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>100)value=100;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                >
                </el-input>
                <span>%，低于此比例时，服务费比例 = </span>
                <el-input
                type="number"
                v-model="ownUserTaxForm.oilRatioLowerLimitTaxThreshold"
                style="width: 68px"
                oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>100)value=100;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                >
                </el-input>
                <span>%</span>
              </el-form-item>
              <el-form-item label="" required>
                <span>油费比例上限 </span>
                <el-input
                type="number"
                v-model="ownUserTaxForm.oilRatioUpperLimit"
                style="width: 68px"
                oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>100)value=100;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                >
                </el-input>
                <span>%，高于此比例时，服务费比例 = 此比例的服务费比例</span>
              </el-form-item>
              <el-form-item label="" required>
                <span>油费比例处于下限和上限之间时(包含上限和下限)，服务费比例 = </span>
                <el-input
                type="number"
                v-model="ownUserTaxForm.freightDifferentialBasis"
                style="width: 68px"
                oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>100)value=100;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                >
                </el-input>
                <span>%- </span>
                <el-input
                type="number"
                v-model="ownUserTaxForm.freightDifferentialReductionFactor"
                style="width: 68px"
                oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>100)value=100;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                >
                </el-input>
                <span>% * 实际油费比例</span>
              </el-form-item>
              <el-form-item label="">
                <span>佣金调整比例 </span>
                <el-input
                type="number"
                v-model="ownUserTaxForm.commissionAdjustmentRatio"
                style="width: 68px"
                oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');if(value>100)value=100;if(value<0)value=null;if(value<0)value=null;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);"
                >
                </el-input>
                <span>%</span>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="submitOwnerOil">提交</el-button>
              </el-form-item>
            </el-form>
          </el-dialog>
          <!-- end -->

        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      phoneFlag: true, //手机是否可编辑的 flag
      form: {
        name: "",
        mobile: "",
        pageNumber: 1,
        pageSize: 20,
        sort: "desc"
      },
      selectAccount: "", //选中的客户账号
      ruleForm: {
        id: "",
        taxThreshold: "",
        status: "",
      },
      mobile: "",
      gradeStr: '',
      grade: '',
      status: '',
      total: null,

      value: "",

      addFeePercentDialog: false, //新增服务弹窗
      currentPage4: 4,

      tableData: [],

      addPersonalOilDiaLog: false, // 自由油耗调整
      ownUserTaxForm: {
        commissionAdjustmentRatio: "",
        freightDifferentialBasis: "",
        freightDifferentialReductionFactor: "",
        id: "",
        oilRatioLowerLimit: "",
        oilRatioLowerLimitTaxThreshold: "",
        oilRatioUpperLimit: "",
        }
    };
  },
  activated() {
    this.getDataList();
  },
  methods: {
    onSubmit() {
      console.log("submit!");
    },
    goDetail(row) {
      console.log(row);
      // window.location.href=''
      this.$router.push("/carsList/carDetail");
      // this.$router.push('/dispatch/page/carsAttestation')
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    /* 选取每页多少条数据 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.form.pageNumber =1;
      this.form.pageSize = JSON.parse(`${val}`);
      console.log(this.form.pageSize);
      this.getDataList();
    },
    /* 翻页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.form.pageNumber = JSON.parse(`${val}`);
      console.log(this.form.pageNumber);
      this.getDataList();
    },
    //搜索选中
    handleSelect(item) {
      this.ruleForm.id = Number(item.name);
      this.mobile = item.mobile;
    },
    /* 新增服务费 */
    addFeePercent() {
      this.addFeePercentDialog = true;
      this.mobile = "";
      this.selectAccount = "";
      this.ruleForm.taxThreshold = "";
    },
    /* 查询 */
    queryFun() {
      let postData = this.form;
      this.form.pageNumber =1
      this.getDataList();
    },

    /* 删除 */
    deleteItem(row) {
      let id = row.id;

      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http
            .post("/admin-center-server/sys/deleteCustomTax?id=" + id)
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
              }
              this.getDataList();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    handleClose() {},
    /* 修改的 操作 */
    modify(row) {
      console.log(row);
      if (row.grade == '10') {
        this.phoneFlag = true;
        this.addFeePercentDialog = true;
        this.selectAccount = row.name;
        this.mobile = row.mobile;
        this.gradeStr = row.gradeStr
        this.grade = row.grade
        this.ruleForm.taxThreshold = row.taxThreshold.replace('%', '');
        this.ruleForm.id = row.id
        this.ruleForm.status = row.status
      } else {
        this.addPersonalOilDiaLog = true;

        this.selectAccount = row.name;
        this.mobile = row.mobile;
        this.gradeStr = row.gradeStr

        this.ownUserTaxForm.commissionAdjustmentRatio = row.commissionAdjustmentRatio
        this.ownUserTaxForm.freightDifferentialBasis = row.freightDifferentialBasis
        this.ownUserTaxForm.freightDifferentialReductionFactor = row.freightDifferentialReductionFactor
        this.ownUserTaxForm.id = row.id
        this.ownUserTaxForm.oilRatioLowerLimit = row.oilRatioLowerLimit
        this.ownUserTaxForm.oilRatioLowerLimitTaxThreshold = row.oilRatioLowerLimitTaxThreshold
        this.ownUserTaxForm.oilRatioUpperLimit = row.oilRatioUpperLimit
      }
      
    },
    /* 重置 */
    resetForm() {
      this.selectAccount = "";
      this.mobile = "";
      this.gradeStr = ""
      this.grade = ""
      this.ruleForm.status = ""
      this.ruleForm.taxThreshold = "";
    },
    /* 数据列表 */
    getDataList() {
      let postData = this.form;
      this.$http
        .get("/admin-center-server/operations/getCustomTaxList", {
          params: postData
        })
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.tableData = data.data.list;
            this.total = Number(data.data.total);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    //(模糊搜索)
    querySearch(queryString, cb) {
      if (queryString != "") {
        this.getPlanTypeData(queryString, data => {
          let results = "";
          if (queryString && !data[0].noId) {
            //输入框有值且有匹配数据时
            results = data.filter(this.createFilter(queryString));
          } else {
            //没有匹配数据时显示自定义的字段
            results = data;
          }
          cb(results);
        });
      }
    },
    createFilter(queryString) {
      return restaurant => {
        // return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase())> -1);
        //后台已做筛选,不需再过滤
        return restaurant.value;
      };
    },
    //获取模糊搜索的数据
    getPlanTypeData(val, fun) {
      let dataArr = [];
      let name = val;
      let mobile = val;
      this.$http
        .get("/admin-center-server/operations/getCustomList?name=" + name)
        .then(res => {
          var data = res.data;
          if (data.code === "200") {
            // if (!res) return false;
            if (data.data.length > 0) {
              data.data.forEach((item, index) => {
                dataArr.push({
                  value: item.name,
                  name: item.id,
                  mobile: item.mobile
                });
              });
            } else {
              dataArr.push({
                value: "无搜索结果",
                noId: "无搜索结果"
              });
            }
            fun(dataArr);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 提交 */
    submitInfo() {
      if (this.selectAccount == "") {
        this.$message({
          type: "warning",
          message: "客户名称不能为空"
        });
        return;
      }

      if (this.ruleForm.taxThreshold.trim() == "") {
        this.$message({
          type: "warning",
          message: "请输入服务费比例"
        });
        return;
      }

      let postData = this.ruleForm;
      this.$http
        .post(
          "/admin-center-server/operations/updateUserTaxThreshold",
          this.$qs.stringify(postData)
        )
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "成功!"
            });
            this.addFeePercentDialog = false;
            this.getDataList(); //刷新列表
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    submitOwnerOil() {
      if (this.ownUserTaxForm.freightDifferentialBasis.trim() == "" 
      || this.ownUserTaxForm.freightDifferentialReductionFactor.trim() == "" 
      || this.ownUserTaxForm.id.trim() == ""
      || this.ownUserTaxForm.oilRatioLowerLimit.trim() == ""
      || this.ownUserTaxForm.oilRatioLowerLimitTaxThreshold.trim() == ""
      || this.ownUserTaxForm.oilRatioUpperLimit.trim() == "") {
        this.$message({
          type: "warning",
          message: "必填选项不能为空，请填写完整的信息"
        });
        return;
      }
      let postData = this.ownUserTaxForm
      if (postData.commissionAdjustmentRatio.trim() == "") {
        postData.commissionAdjustmentRatio = "0"
      }
      this.$http.post("/admin-center-server/operations/updateOwnUserTaxThreshold",
      postData)
      .then(
        (res) => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "成功!"
            });
            this.addPersonalOilDiaLog = false;
            this.getDataList(); //刷新列表
          } else {
            this.$message.warning(data.message);
          }
        }
      )
    },
    /*  */
    bindInput() {
      let price = this.ruleForm.taxThreshold;
      this.ruleForm.taxThreshold = this.astrictImport(price, 9, 2);
    },
    /* 校验 */
    astrictImport(price, int, float) {
      //限制输入的字数为int位整数float位小数
      if (price.indexOf(".") != -1) {
        var index = price.indexOf(".");
        var len = price.substring(index).length - 1;
        if (len > 2) {
          price = price.replace(price, price.substring(0, index + float + 1));
        }
        for (var i = 1; i <= float; i++) {
          if (price.charAt(index + i) == ".") {
            price = price.replace(price, price.substring(0, index + i));
          }
        }
      } else {
        // console.log("else")
        price = price.substring(0, int);
      }
      if (price.length > 1) {
        //如果有两位以及以上的数字，则首位不能输0
        if (price.substring(1, 2) != "." && price.substring(0, 1) == 0) {
          price = "";
        }
      }
      if (price.length == 1) {
        //首位不能输点
        if (price.substring(0, 1) == "." || price.substring(0, 1) == "-") {
          price = "";
        }
      }
      return price;
    },
    /* 输入的校验 */
    keyupEvent(e, input) {
      e.target.value = e.target.value.replace(/[^\d.]/g, "");
      e.target.value = e.target.value.replace(/\.{2,}/g, ".");
      e.target.value = e.target.value.replace(/^\./g, "0.");
      e.target.value = e.target.value.replace(
        /^\d*\.\d*\./g,
        e.target.value.substring(0, e.target.value.length - 1)
      );
      e.target.value = e.target.value.replace(/^0[^\.]+/g, "0");
      e.target.value = e.target.value.replace(/^(\d+)\.(\d\d).*$/, "$1.$2");
      this.input = e.target.value;
      console.log(this.input);
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.el-form-item {
  margin-bottom: 0;
}
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
::v-deep input[type="number"] {
  -moz-appearance: textfield;
}
.carsList {
  .page {
    text-align: right;
  }
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
    .el-form-item {
      margin: 30px auto;
    }
  }
}
</style>
