<template>
  <div class="app-container">
    <div class="base">
      当前平台主体：{{ currentBase.baseName }}<i @click="changeBase" class="el-icon-sort"></i>
    </div>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="2C-业务订单" name="first">
        <div>
          <div class="search">
            <el-form size="mini" :inline="true" label-width="120px">
              <el-form-item label="结算日期:">
                <el-date-picker
                  v-model="time"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  @change="getTimeVal"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="上报日期:">
                <el-date-picker
                  v-model="reportTime"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  @change="getReportTime"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="开票日期:">
                <el-date-picker
                  v-model="billTime"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  @change="getbillTimeVal"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="运单号:">
                <el-input
                  placeholder="请输入运单号"
                  v-model="searchForm.orderItemSn"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="货主名称:">
                <el-input
                  placeholder="请输入客户名称"
                  v-model="searchForm.buyName"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="税务上报:">
                <el-select v-model="searchForm.status" placeholder="请选择" clearable>
                  <el-option
                    v-for="item in statusList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  size="mini"
                  class="button"
                  type="primary"
                  @click="onSearch"
                  >查询</el-button
                >
                <el-button
                  icon="el-icon-delete"
                  size="mini"
                  class="button"
                  type="danger"
                  @click="resetSearch"
                  >清空筛选</el-button
                >
              </el-form-item>
            </el-form>
          </div>
        </div>

        <div class="list">
          <!-- 财务暂时隐藏导出按钮 之后再重新打开 -->
          <div style="margin-bottom: 20px; text-align: right">
            <span class="count-text">
             <span class="error-color">开票金额</span> 
              合计
              <span class="error-color">{{ invoicingNum }} </span>元，
              <span class="error-color">货主运费</span>
              合计
              <span class="error-color">{{ cargoNum }} </span>元，
              <span class="error-color">司机运费</span>
              合计
              <span class="error-color">{{ driverNum }} </span>元
            </span>
            <el-button @click="countFn" size="mini">计算合计金额</el-button>
            <el-button @click="exportFile" type="primary" size="mini" :loading="loading">导出Excel</el-button>
          </div>
          <el-table border :data="data">
            <!-- <el-table-column type="selection"></el-table-column> -->
            <el-table-column
              prop="settleTime"
              label="结算时间"
            ></el-table-column>
            <el-table-column
              prop="reportTime"
              label="上报时间"
            ></el-table-column>
            <el-table-column prop="kpTime" label="开票时间"></el-table-column>
            <el-table-column prop="buyName" label="货主名称"></el-table-column>
            <el-table-column prop="sn" label="运单号">
              <template slot-scope="scope">
                <el-button @click="toDetail(scope.row.sn)" type="text">{{
                  scope.row.sn
                }}</el-button>
              </template>
            </el-table-column>
            <el-table-column
              prop="amount"
              label="开票金额（元）"
            ></el-table-column>
            <el-table-column
              prop="fbfddje"
              label="货主运费（元）"
            ></el-table-column>
            <el-table-column
              prop="fwfddje"
              label="司机运费（元）"
            ></el-table-column>
            <el-table-column
              prop="statusEnum"
              label="税务上报"
            ></el-table-column>
          </el-table>
          <div class="pagin">
            <el-pagination
              :total="total"
              :current-page.sync="page.pageNumber"
              :page-size="page.pageSize"
              @current-change="getList"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
            ></el-pagination>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="2C-发布方（货主）" name="second">
        <PublisherConsignor :isToB='0' v-if="activeName=='second'" :baseId="currentBase.id"></PublisherConsignor>
      </el-tab-pane>
      <el-tab-pane label="2C-服务方（承运人）" name="third">
        <ServerCarrier :isToB='0' v-if="activeName=='third'" :baseId="currentBase.id"></ServerCarrier>
      </el-tab-pane>
      <el-tab-pane label="2B-业务订单" name="four">
        <div>
          <div class="search">
            <el-form size="mini" :inline="true" label-width="120px">
              <el-form-item label="结算日期:">
                <el-date-picker
                  v-model="time"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  @change="getTimeVal"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="上报日期:">
                <el-date-picker
                  v-model="reportTime"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  @change="getReportTime"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="开票日期:">
                <el-date-picker
                  v-model="billTime"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  @change="getbillTimeVal"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="运单号:">
                <el-input
                  placeholder="请输入运单号"
                  v-model="searchForm.orderItemSn"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="货主名称:">
                <el-input
                  placeholder="请输入客户名称"
                  v-model="searchForm.buyName"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="税务上报:">
                <el-select v-model="searchForm.status" placeholder="请选择" clearable>
                  <el-option
                    v-for="item in statusList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  size="mini"
                  class="button"
                  type="primary"
                  @click="onSearch"
                  >查询</el-button
                >
                <el-button
                  icon="el-icon-delete"
                  size="mini"
                  class="button"
                  type="danger"
                  @click="resetSearch"
                  >清空筛选</el-button
                >
              </el-form-item>
            </el-form>
          </div>
        </div>

        <div class="list">
          <!-- 财务暂时隐藏导出按钮 之后再重新打开 -->
          <div style="margin-bottom: 20px; text-align: right">
            <span class="count-text">
             <span class="error-color">开票金额</span> 
              合计
              <span class="error-color">{{ invoicingNum }} </span>元，
              <span class="error-color">货主运费</span>
              合计
              <span class="error-color">{{ cargoNum }} </span>元，
              <span class="error-color">司机运费</span>
              合计
              <span class="error-color">{{ driverNum }} </span>元
            </span>
            <el-button @click="countFn" size="mini">计算合计金额</el-button>
            <el-button @click="exportFile" type="primary" size="mini" :loading="loading">导出Excel</el-button>
          </div>
          <el-table border :data="data">
            <!-- <el-table-column type="selection"></el-table-column> -->
            <el-table-column
              prop="settleTime"
              label="结算时间"
            ></el-table-column>
            <el-table-column
              prop="reportTime"
              label="上报时间"
            ></el-table-column>
            <el-table-column prop="kpTime" label="开票时间"></el-table-column>
            <el-table-column prop="buyName" label="货主名称"></el-table-column>
            <el-table-column prop="sn" label="运单号">
              <template slot-scope="scope">
                <el-button @click="toDetail2B(scope.row.taxId)" type="text">{{
                  scope.row.sn
                }}</el-button>
              </template>
            </el-table-column>
            <el-table-column
              prop="amount"
              label="开票金额（元）"
            ></el-table-column>
            <el-table-column
              prop="fbfddje"
              label="货主运费（元）"
            ></el-table-column>
            <el-table-column
              prop="fwfddje"
              label="司机运费（元）"
            ></el-table-column>
            <el-table-column
              prop="statusEnum"
              label="税务上报"
            ></el-table-column>
          </el-table>
          <div class="pagin">
            <el-pagination
              :total="total"
              :current-page.sync="page.pageNumber"
              :page-size="page.pageSize"
              @current-change="getList"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
            ></el-pagination>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="2B-发布方（货主）" name="five">
        <PublisherConsignor :isToB='1' v-if="activeName=='five'" :baseId="currentBase.id"></PublisherConsignor>
      </el-tab-pane>
      <el-tab-pane label="2B-服务方（企业）" name="six">
        <ServerCarrier :isToB='1' v-if="activeName=='six'" :baseId="currentBase.id"></ServerCarrier>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { checkLess6Month } from "@/utils/date";
import PublisherConsignor from "./publisherConsignor.vue";
import ServerCarrier from "./serverCarrier.vue";
export default {
  components: {
    PublisherConsignor,
    ServerCarrier,
  },
  data() {
    return {
      activeName: "first",
      searchForm: {
        billStartTime: "",
        billEndTime: "",
        buyName: "",
        startTime: "",
        endTime: "",
        reportStartTime: "",
        reportEndTime: "",
        status: "0",
        isToB: 0
      },
      page: {
        pageSize: 20,
        pageNumber: 1,
      },
      total: 0,
      data: [],
      selectedItems: [],
      time: "",
      billTime: "",
      reportTime: [],
      exportData: {},
      statusList: [
        {
          value: "0",
          label: "成功",
        },
        {
          value: "1",
          label: "失败",
        },
      ],
      invoicingNum: 0, //开票金额合计
      cargoNum: 0, //货主运费合计
      driverNum: 0, //司机运费合计
      loading: false, //导出loading
      currentBase: {},
    };
  },
  methods: {
      changeBase() {
      let baseInfo = this.baseInfo;
      let currentIndex = baseInfo.findIndex(
        (v) => v.id === this.currentBase.id
      );
      let toggleIndex = null;
      if (currentIndex === baseInfo.length - 1) {
        toggleIndex = 0;
      } else {
        toggleIndex = currentIndex + 1;
      }
      this.currentBase = baseInfo[toggleIndex];
      localStorage.setItem("UserCurrentBaseId", this.currentBase.id);

      setTimeout(() => {
        this.$message.success("切换成功");
      }, 1000);
    },
    handleClick() { 
      if (this.activeName == "first" ||this.activeName == "four" ) {
        this.getList()
      }
    },
    onSearch() {
      this.page.pageNumber = 1;
      this.getList();
    },
    resetSearch() {
      this.time = "";
      this.billTime = "";
      this.searchForm = {};
      this.page.pageNumber = 1;
      this.getList();
    },
    getTimeVal(val) {
      console.log(val);
      if (val) {
        this.searchForm.startTime = val[0];
        this.searchForm.endTime = val[1];
      } else {
        this.searchForm.startTime = "";
        this.searchForm.endTime = "";
      }
    },
    getReportTime(val) {
      console.log(val);
      if (val) {
        this.searchForm.reportStartTime = val[0];
        this.searchForm.reportEndTime = val[1];
      } else {
        this.searchForm.reportStartTime = "";
        this.searchForm.reportEndTime = "";
      }
    },
    getbillTimeVal(val) {
      if (val) {
        this.searchForm.billStartTime = val[0];
        this.searchForm.billEndTime = val[1];
      } else {
        this.searchForm.billStartTime = "";
        this.searchForm.billEndTime = "";
      }
    },
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.page.pageNumber = 1;
      this.getList();
    },
    getList() {
      let params = {
        ...this.searchForm,
        ...this.page,
        baseId: this.currentBase.id
      };
      if(this.activeName=='first'||this.activeName=='second' || this.activeName=='third'){
        params.isToB = 0
      }else{
        params.isToB= 1
      }
      if(this.reportTime && this.reportTime.length > 0){
        params.reportStartTime = this.reportTime[0];
        params.reportEndTime = this.reportTime[1];
      }else{
        params.reportStartTime = "";
        params.reportEndTime = "";
      }
      this.countFn();
      this.$post("/admin-center-server/finance/order/orderList", params).then(
        (res) => {
          this.exportData = { ...this.searchForm };
          this.data = res.list;
          this.total = Number(res.total);
        }
      );
    },
    handleSelectionChange(v) {
      this.selectedItems = v;
    },
    exportFile() {
      // if (!this.searchForm.startTime && !this.searchForm.reportStartTime && !this.searchForm.billStartTime) {
      //   this.$message.warning("请选择结算日期、上报日期或开票日期");
      //   return;
      // }
      // if (
      //   (this.searchForm.startTime &&
      //     !checkLess6Month(
      //       this.searchForm.startTime,
      //       this.searchForm.endTime
      //     )) ||
      //   (this.searchForm.reportStartTime &&
      //     !checkLess6Month(
      //       this.searchForm.reportStartTime,
      //       this.searchForm.reportEndTime
      //     )) ||
      //   (this.searchForm.billStartTime &&
      //     !checkLess6Month(
      //       this.searchForm.billStartTime,
      //       this.searchForm.billEndTime
      //     ))
      // ) {
      //   this.$message.warning("请选择结算日期、上报日期或开票日期，跨度最长6个月");
      //   return;
      // }
      this.loading = true;
      let data = {
        ...this.searchForm,
        isToB: this.activeName == 'four' ? 1 : 0,
        baseId: this.currentBase.id
      }
      this.$http
        .post(
          "/admin-center-server/finance/order/orderListExport",
          // this.exportData
          data
        )
        .then((res) => {
           let data = res.data;
            if (data.code === 200) {
                this.loading = false;
                this.$message.success(
                  "正在导出，稍后您可在【导出任务】中查看导出进度及导出文件"
                );
                // window.location.href = data.data;
            } else {
                this.loading = false;
                this.$message.warning(data.message)
            }
          
        });
    },
    //计算合计金额
    countFn(){
       let params = {
        ...this.searchForm,
        // ...this.page,
        baseId: this.currentBase.id
      };
      if(this.activeName=='first'||this.activeName=='second' || this.activeName=='third'){
        params.isToB = 0
      }else{
        params.isToB= 1
      }
      if(this.reportTime && this.reportTime.length > 0){
        params.reportStartTime = this.reportTime[0];
        params.reportEndTime = this.reportTime[1];
      }else{
        params.reportStartTime = "";
        params.reportEndTime = "";
      }
      this.$post("/admin-center-server/finance/order/calculateTotalAmount", params).then(
        (res) => {
          this.invoicingNum = res.kpAmount; //开票金额合计
          this.cargoNum = res.fbfddje; //货主运费合计
          this.driverNum = res.fwfddje; //司机运费合计
        }
      );
    },
    computedDate(date1, date2) {
      console.log(new Date(date1).getTime());
      let time1 = new Date(date1).getTime();
      let time2 = new Date(date2).getTime();
      let iDays = parseInt(Math.abs(time2 - time1) / 1000 / 60 / 60 / 24);
      return iDays;
    },
    toDetail(sn) {
      this.$router.push("/finance/taxDetail?sn=" + sn);
    },
    toDetail2B(taxId) {
      this.$router.push("/finance/taxDetail2B?taxId=" + taxId);
    },
    timeFormat(date) {
      var Y = date.getFullYear() + '-'
      var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-'
      var D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()) + ' '
      var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
      var m = (date.getMinutes() <10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
      var s = (date.getSeconds() <10 ? '0' + date.getSeconds() : date.getSeconds())
      return Y+M+D+h+m+s
    },
  },
  activated() {
    // url获取开票时间
    let {baseId} = this.$route.query
    let currentBaseId = ''
    if(baseId){
      currentBaseId = baseId
    }else{
      currentBaseId = localStorage.getItem("UserCurrentBaseId");
    }
    let baseInfo = (this.baseInfo = this.$store.state.user.baseInfo);
    if (currentBaseId) {
      this.currentBase = baseInfo.find((v) => v.id == currentBaseId);
    } else {
      this.currentBase = baseInfo.find((v) => v.defaultFlag);
      localStorage.setItem("UserCurrentBaseId", this.currentBase.id);
    }
    let activeName = this.$route.query.activeName;
    if (activeName && activeName == "first"){
      this.activeName = 'first'
    }else if (activeName && activeName == "four") {
      this.activeName = 'four'
    }

 
    let currentDate = Date.now()
    let endDate = new Date(new Date(new Date(currentDate).toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
    let startDate = new Date(new Date(new Date(currentDate-90*24*60*60*1000).toLocaleDateString()).getTime())
    this.reportTime = [this.timeFormat(startDate), this.timeFormat(endDate)]
       // url获取开票时间
    let {reportStartTime,reportEndTime} = this.$route.query
    if(reportStartTime && reportEndTime){
      // this.billTime = [reportStartTime,reportEndTime]
      this.reportTime = [reportStartTime,reportEndTime]
    }
    this.searchForm.reportStartTime =reportStartTime?reportStartTime: this.reportTime[0];
    this.searchForm.reportEndTime = reportEndTime ?reportEndTime: this.reportTime[1];
    this.getList();
  },
};
</script>

<style scoped lang="scss">
.top-title {
  font-size: 16px;
  font-weight: 700;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  border-bottom: 1px solid #cccccc;
  display: flex;
  justify-content: space-between;
  background: #fff;
  .button {
    margin-right: 20px;
  }
}
.el-table ::v-deep .cell {
  text-align: center;
}
.search,
.list {
  padding: 30px 0;
  background: #fff;
}
.search {
  padding-bottom: 10px;
}
.list {
  margin-top: 20px;
  padding: 20px 10px;
}
.pagin {
  margin-top: 10px;
  text-align: right;
}
.wrapper {
  margin-top: 10px;
  background: #fff;
  padding: 20px 10px 10px 10px;
}
.count-text{
  margin-right: 10px;
  position: relative;
  top: 2px;
  color: #606266;
  font-size: 14px;
}
.base {
  margin-bottom: 10px;
  font-size: 14px;
  i {
    transform: rotate(90deg);
    color: #f6a018;
    cursor: pointer;
  }
}
</style>