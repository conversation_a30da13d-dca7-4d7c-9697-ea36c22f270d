<template>
  <div class="app-container">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="2C-业务订单" name="first">
        <div>
          <div class="search">
            <el-form size="mini" :inline="true" label-width="120px">
              <el-form-item label="结算日期:">
                <el-date-picker
                  v-model="time"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  @change="getTimeVal"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="开票日期:">
                <el-date-picker
                  v-model="billTime"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  @change="getbillTimeVal"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="货主名称:">
                <el-input
                  placeholder="请输入客户名称"
                  v-model="searchForm.buyName"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="税务上报:">
                <el-select v-model="searchForm.status" placeholder="请选择">
                  <el-option
                    v-for="item in statusList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  size="mini"
                  class="button"
                  type="primary"
                  @click="onSearch"
                  >查询</el-button
                >
                <el-button
                  icon="el-icon-delete"
                  size="mini"
                  class="button"
                  type="danger"
                  @click="resetSearch"
                  >清空筛选</el-button
                >
              </el-form-item>
            </el-form>
          </div>
        </div>

        <div class="list">
          <!-- 财务暂时隐藏导出按钮 之后再重新打开 -->
          <!-- <div style="margin-bottom: 20px; text-align: right">
        <el-button @click="exportFile" type="primary" size="mini">导出Excel</el-button>
      </div> -->
          <el-table border :data="data">
            <!-- <el-table-column type="selection"></el-table-column> -->
            <el-table-column
              prop="settleTime"
              label="结算时间"
            ></el-table-column>
            <el-table-column prop="kpTime" label="开票时间"></el-table-column>
            <el-table-column prop="buyName" label="货主名称"></el-table-column>
            <el-table-column prop="sn" label="运单号">
              <template slot-scope="scope">
                <el-button @click="toDetail(scope.row.sn)" type="text">{{
                  scope.row.sn
                }}</el-button>
              </template>
            </el-table-column>
            <el-table-column
              prop="amount"
              label="开票金额（元）"
            ></el-table-column>
            <el-table-column
              prop="fbfddje"
              label="货主运费（元）"
            ></el-table-column>
            <el-table-column
              prop="statusEnum"
              label="税务上报"
            ></el-table-column>
          </el-table>
          <div class="pagin">
            <el-pagination
              :total="total"
              :current-page.sync="page.pageNumber"
              :page-size="page.pageSize"
              @current-change="getList"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
            ></el-pagination>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="2C-发布方（货主）" name="second">
        <PublisherConsignor :isToB='0' v-if="activeName=='second'"></PublisherConsignor>
      </el-tab-pane>
      <el-tab-pane label="2C-服务方（承运人）" name="third">
        <ServerCarrier :isToB='0' v-if="activeName=='third'"></ServerCarrier>
      </el-tab-pane>
      <el-tab-pane label="2B-业务订单" name="four">
        <div>
          <div class="search">
            <el-form size="mini" :inline="true" label-width="120px">
              <el-form-item label="结算日期:">
                <el-date-picker
                  v-model="time"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  @change="getTimeVal"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="开票日期:">
                <el-date-picker
                  v-model="billTime"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  @change="getbillTimeVal"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="货主名称:">
                <el-input
                  placeholder="请输入客户名称"
                  v-model="searchForm.buyName"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item label="税务上报:">
                <el-select v-model="searchForm.status" placeholder="请选择">
                  <el-option
                    v-for="item in statusList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  size="mini"
                  class="button"
                  type="primary"
                  @click="onSearch"
                  >查询</el-button
                >
                <el-button
                  icon="el-icon-delete"
                  size="mini"
                  class="button"
                  type="danger"
                  @click="resetSearch"
                  >清空筛选</el-button
                >
              </el-form-item>
            </el-form>
          </div>
        </div>

        <div class="list">
          <!-- 财务暂时隐藏导出按钮 之后再重新打开 -->
          <!-- <div style="margin-bottom: 20px; text-align: right">
        <el-button @click="exportFile" type="primary" size="mini">导出Excel</el-button>
      </div> -->
          <el-table border :data="data">
            <!-- <el-table-column type="selection"></el-table-column> -->
            <el-table-column
              prop="settleTime"
              label="结算时间"
            ></el-table-column>
            <el-table-column prop="kpTime" label="开票时间"></el-table-column>
            <el-table-column prop="buyName" label="货主名称"></el-table-column>
            <el-table-column prop="sn" label="运单号">
              <template slot-scope="scope">
                <el-button @click="toDetail2B(scope.row.taxId)" type="text">{{
                  scope.row.sn
                }}</el-button>
              </template>
            </el-table-column>
            <el-table-column
              prop="amount"
              label="开票金额（元）"
            ></el-table-column>
            <el-table-column
              prop="fbfddje"
              label="货主运费（元）"
            ></el-table-column>
            <el-table-column
              prop="statusEnum"
              label="税务上报"
            ></el-table-column>
          </el-table>
          <div class="pagin">
            <el-pagination
              :total="total"
              :current-page.sync="page.pageNumber"
              :page-size="page.pageSize"
              @current-change="getList"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
            ></el-pagination>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="2B-发布方（货主）" name="five">
        <PublisherConsignor :isToB='1' v-if="activeName=='five'"></PublisherConsignor>
      </el-tab-pane>
      <el-tab-pane label="2B-服务方（企业）" name="six">
        <ServerCarrier :isToB='1' v-if="activeName=='six'"></ServerCarrier>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { checkLess3Month } from "@/utils/date";
import PublisherConsignor from "./publisherConsignor.vue";
import ServerCarrier from "./serverCarrier.vue";
export default {
  components: {
    PublisherConsignor,
    ServerCarrier,
  },
  data() {
    return {
      activeName: "first",
      searchForm: {
        billStartTime: "",
        billEndTime: "",
        buyName: "",
        startTime: "",
        endTime: "",
        status: "",
        isToB: 0
      },
      page: {
        pageSize: 20,
        pageNumber: 1,
      },
      total: 0,
      data: [],
      selectedItems: [],
      time: "",
      billTime: "",
      exportData: {},
      statusList: [
        {
          value: "0",
          label: "成功",
        },
        {
          value: "1",
          label: "失败",
        },
      ],
    };
  },
  methods: {
    handleClick() { 
      if (this.activeName == "first" ||this.activeName == "four" ) {
        this.getList()
      }
    },
    onSearch() {
      this.page.pageNumber = 1;
      this.getList();
    },
    resetSearch() {
      this.time = "";
      this.billTime = "";
      this.searchForm = {};
      this.page.pageNumber = 1;
      this.getList();
    },
    getTimeVal(val) {
      console.log(val);
      if (val) {
        this.searchForm.startTime = val[0];
        this.searchForm.endTime = val[1];
      } else {
        this.searchForm.startTime = "";
        this.searchForm.endTime = "";
      }
    },
    getbillTimeVal(val) {
      if (val) {
        this.searchForm.billStartTime = val[0];
        this.searchForm.billEndTime = val[1];
      } else {
        this.searchForm.billStartTime = "";
        this.searchForm.billEndTime = "";
      }
    },
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.page.pageNumber = 1;
      this.getList();
    },
    getList() {
      let params = {
        ...this.searchForm,
        ...this.page,
      };
      if(this.activeName=='first'||this.activeName=='second' || this.activeName=='third'){
        params.isToB = 0
      }else{
        params.isToB= 1
      }
      this.$post("/admin-center-server/finance/order/orderList", params).then(
        (res) => {
          this.exportData = { ...this.searchForm };
          this.data = res.list;
          this.total = Number(res.total);
        }
      );
    },
    handleSelectionChange(v) {
      this.selectedItems = v;
    },
    exportFile() {
      if (
        (!this.exportData.startTime && !this.exportData.billStartTime) ||
        (this.exportData.startTime &&
          !checkLess3Month(
            this.exportData.startTime,
            this.exportData.endTime
          )) ||
        (this.exportData.billStartTime &&
          !checkLess3Month(
            this.exportData.billStartTime,
            this.exportData.billEndTime
          ))
      ) {
        this.$message.warning("请选择结算日期或开票日期，跨度最长3个月");
        return;
      }

      // if (this.selectedItems.length === 0) {
      //   this.$message.warning('至少选择一条')
      //   return
      // }
      this.$http
        .post(
          "/admin-center-server/finance/order/exportOrderList",
          this.exportData
        )
        .then((res) => {
          this.$message.success(
            "正在导出，稍后您可在【导出任务】中查看导出进度及导出文件"
          );
        });
    },
    computedDate(date1, date2) {
      console.log(new Date(date1).getTime());
      let time1 = new Date(date1).getTime();
      let time2 = new Date(date2).getTime();
      let iDays = parseInt(Math.abs(time2 - time1) / 1000 / 60 / 60 / 24);
      return iDays;
    },
    toDetail(sn) {
      this.$router.push("/finance/taxDetail?sn=" + sn);
    },
    toDetail2B(taxId) {
      this.$router.push("/finance/taxDetail2B?taxId=" + taxId);
    },
  },
  activated() {
    this.getList();

  },
};
</script>

<style scoped lang="scss">
.top-title {
  font-size: 16px;
  font-weight: 700;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  border-bottom: 1px solid #cccccc;
  display: flex;
  justify-content: space-between;
  background: #fff;
  .button {
    margin-right: 20px;
  }
}
.el-table ::v-deep .cell {
  text-align: center;
}
.search,
.list {
  padding: 30px 0;
  background: #fff;
}
.search {
  padding-bottom: 10px;
}
.list {
  margin-top: 20px;
  padding: 20px 10px;
}
.pagin {
  margin-top: 10px;
  text-align: right;
}
.wrapper {
  margin-top: 10px;
  background: #fff;
  padding: 20px 10px 10px 10px;
}
</style>