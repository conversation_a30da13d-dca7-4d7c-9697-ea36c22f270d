<template>
  <div>
    <div class="wrap">
      <p>
        <span class='span-1'>合同名称：</span>
        <span class='span-2'>{{data.name}}</span>
      </p>
      <p>
        <span class='span-1'>合同编号：</span>
        <span class='span-2'>{{data.no}}</span>
      </p>
      <p>
        <span class='span-1'>合同有效期：</span>
        <span class='span-2'>{{data.startExpirationDate}}-{{data.expirationDate}}</span>
      </p>
      <p>
        <span class='span-1'>甲方名称：</span>
        <span class='span-2'>{{data.partyA}}</span>
      </p>
      <p>
        <span class='span-1'>乙方名称：</span>
        <span class='span-2'>{{data.partyB}}</span>
      </p>
      <p>
        <span class='span-1'>所属公司：</span>
        <span class='span-2'>{{data.company}}</span>
      </p>
      <p>
        <span class='span-1'>合同录入人：</span>
        <span class='span-2'>{{data.entryPerson}}</span>
      </p>
      <p>
        <span class='span-1'>合同录入时间：</span>
        <span class='span-2'>{{data.entryDate}}</span>
      </p>
      <p>
        <span class='span-1'>审核状态：</span>
        <span class='span-2'>{{data.reviewStatusEnum}}</span>
      </p>
      <p>
        <span class='span-1'>合同附件：</span>
        <el-button v-if='data.attachment'
                   size="mini"
                   @click="onPreview">查看大图</el-button>
      </p>
      <p>
        <el-image-viewer v-if="showViewer"
                         :on-close="closeViewer"
                         :url-list="[data.attachment]" />
        <el-image v-if='data.attachment'
                  style="width: 200px; "
                  :src="data.attachment"
                  fit="contain"></el-image>
      </p>
    </div>
  </div>
</template>

<script>
import ElImageViewer from 'cf-element-ui/packages/image/src/image-viewer'
const getByContractId = '/admin-center-server/contract/getByContractId'//详情
export default {
  components: { ElImageViewer },
  data () {
    return {
      showViewer: false, // 显示查看器
      url: 'https://goss.veer.com/creative/vcg/veer/800water/veer-*********.jpg',
      id: '',
      data: {}
    };
  },
  methods: {
    onPreview () {
      this.showViewer = true
    },
    // 关闭查看器
    closeViewer () {
      this.showViewer = false
    },
    rejectfn () {

    }
  },
  activated () {
    var that = this
    this.id = this.$route.query.id;
    this.$http.get(getByContractId + '?id=' + that.id).then(res => {
      console.log(res.data.data)
      that.data = res.data.data
    })
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.wrap {
  margin-left: 100px;
  font-size: 14px;
  .span-2 {
    color: #666;
  }
  .span-1 {
    width: 170px;
    display: inline-block;
    text-align: right;
    margin-right: 20px;
  }
}
#app {
  background: #fff;
}
.el-image {
  margin-left: 190px;
}
</style>
