<template>
    <el-dialog
        :close-on-click-modal="false"
        title="设置折扣"
        :visible.sync="open"
        destroy-on-close
        @close="cancel"
        width="600px"
    >
        <el-form
            ref="form"
            label-width="100px"
            :model="form"
            :rules="rules"
            v-loading="loading"
        >
            <el-row class="global-div-search">
                <el-col :span="20">
                    <el-form-item label="名称" prop="channelName">
                        <el-input
                            v-model="form.channelName"
                            clearable
                            placeholder="请输入渠道名称"
                            disabled
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="18">
                    <el-form-item label="折扣" prop="taxThreshold">
                        <el-input
                            v-model="form.taxThreshold"
                            clearable
                            placeholder="请输入1-100的数字，支持两位小数"
                            :max="100"
                            :min="0"
                        ></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="4">
                    <el-form-item label-width="10px">%</el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { onUnmounted } from "vue";
export default {
    name: "discountModel",
    props: {},
    data() {
        return {
            open: true,
            loading: false,
            form: {
                channelName: undefined,
                taxThreshold: undefined,
            },
            rules: {
                channelName: [
                    {
                        required: true,
                        validator: async (rule, value, callback) => {
                            if (!value) {
                                callback(new Error("请输入渠道名称"));
                            } else {
                                callback();
                            }
                        },
                        trigger: "blur",
                    },
                ],
                taxThreshold: [
                    {
                        required: true,
                        validator: async (rule, value, callback) => {
                            
                            if (!value) {
                                callback(new Error("请输入折扣比例"));
                            } else {
                                let num = Number(value)
                                let decimals = String(num).split('.')[1],
                                    decimalsLength = 0
                                if (decimals) {
                                    decimalsLength = String(decimals).length
                                }
                                console.log(decimals, decimalsLength)
                                if (Number.isNaN(num) || num > 100 || num < 1 || decimalsLength > 2) {
                                    callback('请输入1-100的数字，支持两位小数')
                                }
                            }
                            callback()
                        },
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    /** 事件监听 */
    watch: {},

    /** 计算属性 */
    computed: {},

    /** 生命周期 -- 实例创建后调用 */
    created() {},

    /** 生命周期 -- 实例挂载后调用 */
    mounted() {},

    /** 生命周期 -- 实例销毁后调用 */
    destroyed() {},

    methods: {
        edit(row) {
            this.reset(2);
            this.form = JSON.parse(JSON.stringify(row));
        },

        cancel() {
            this.reset(4);
            this.$emit("cancel");
        },

        // 表单重置
        reset(status) {
            this.form = {
                channelName: undefined,
                taxThreshold: undefined,
            };
            this.$refs.form.resetFields();
            this.status = status;
            switch (status) {
                case 1: // 新增
                    break;
                case 2: // 编辑
                    break;
                case 3: // 详情
                    break;
                case 4: // 取消
                    break;
            }
        },

        /** 提交按钮 */
        submitForm: function () {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    this.loading = true;
                    const { taxThresholdId, taxThreshold, taxThresholdIdStatus } = this.form;
                    this.$post(
                        "/admin-center-server/operations/updateUserTaxThreshold",
                        this.$qs.stringify({
                            id: taxThresholdId,
                            taxThreshold,
                            status: taxThresholdIdStatus,
                        })
                    )
                        .then((res) => {
                            this.$message.success("修改成功");
                            this.loading = false;
                            this.reset();
                            this.$emit("success");
                        })
                        .catch(() => {
                            this.loading = false;
                        });
                }
            });
        },
    },
};
</script>
<style scoped type="scss">
.warning {
    color: #ff0000;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
}
.global-div-search {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    /* font-size: 14px; */
    /* color: #555; */
    position: relative;
}

.el-select {
    width: 100% !important;
}

.el-date-editor.el-input,
.el-date-editor.el-input__inner {
    width: 100% !important;
}
.el-input-number--medium {
    width: 100%;
}
</style>
