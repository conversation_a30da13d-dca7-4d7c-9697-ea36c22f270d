<template>
  <div>
    <!-- <div class="type">货物运输服务</div> -->
    <div class="headline">
      <div class="headline-outer">
        <div class="headline-inner">
          电子发票（增值税专用发票）
        </div>
      </div>
    </div>
    <div class="main">
      <table class="table1">
        <tr>
          <td>
            <div class="title vertical-title">购买方信息</div>
          </td>
          <td>
            <div class="table1-row1">
              <div><span class="title">名称</span>： {{ info.buyName }}</div>
              <div><span class="title">统一社会信用代码 / 纳税人识别号</span>： {{ info.buyDutyNum }}</div>
            </div>
          </td>
          <td>
            <div class="title vertical-title">销售方信息</div>
          </td>
          <td>
            <div class="table1-row2">
              <div><span class="title">名称</span>： {{ info.sellName }}</div>
              <div><span class="title">统一社会信用代码 / 纳税人识别号</span>： {{ info.sellDutyNum }}</div>
            </div>
          </td>
        </tr>
      </table>
      <table class="table2">
        <tr class="list-row">
          <th>
            <div class="title">项目名称</div>
          </th>
          <th>
            <div class="title">单位</div>
          </th>
          <th>
            <div class="title">数量</div>
          </th>
          <th>
            <div class="title">单价</div>
          </th>
          <th>
            <div class="title">金额</div>
          </th>
          <th>
            <div class="title">税率/征收率</div>
          </th>
          <th>
            <div class="title">税额</div>
          </th>
        </tr>
        <tr
          v-for="(item, index) in data"
          :key="item.id" class="list-row"
          :class="{'list-row-last': index === 8}">
          <td>
            <div class="content">{{ item.hwysName }}</div>
          </td>
          <td>
            <div class="content">
              <template v-if="item.freightCalcType === '0'">吨</template>
              <template v-else-if="item.freightCalcType === '1'">车</template>
            </div>
          </td>
          <td>
            <div class="content">{{ item.ton }}</div>
          </td>
          <td>
            <div class="content">{{ item.freightkNotax }}</div>
          </td>
          <td>
            <div class="content">{{ item.amountNotax }}</div>
          </td>
          <td>
            <div class="content">{{ item.taxRate }}%</div>
          </td>
          <td>
            <div class="content">{{ item.taxMoney }}</div>
          </td>
        </tr>
        <tr
          v-for="item in 8 - data.length"
          :key="item"
          class="list-row"
          :class="{'list-row-last': item + data.length === 8}">
          <td>
            <div class="content"></div>
          </td>
          <td>
            <div class="content"></div>
          </td>
          <td>
            <div class="content"></div>
          </td>
          <td>
            <div class="content"></div>
          </td>
          <td>
            <div class="content"></div>
          </td>
          <td>
            <div class="content"></div>
          </td>
          <td>
            <div class="content"></div>
          </td>
          <td>
            <div class="content"></div>
          </td>
          <td>
            <div class="content"></div>
          </td>
        </tr>
        <tr class="sum">
          <td colspan="4">
            <div class="title">合计</div>
          </td>
          <td>
            <div class="content">{{ info.sumAmount }}</div>
          </td>
          <td></td>
          <td>
            <div class="content">{{ info.sumTaxMoney }}</div>
          </td>
        </tr>
      </table>
      <table class="table3">
        <colgroup>
          <col width="150px">
          <col width="150px">
          <col width="292px">
          <col width="292px">
          <col width="150px">
        </colgroup>
        <tr class="list-row">
          <th>
            <div class="title">运输工具种类</div>
          </th>
          <th>
            <div class="title">运输工具牌号</div>
          </th>
          <th>
            <div class="title">起运地</div>
          </th>
          <th>
            <div class="title">到达地</div>
          </th>
          <th>
            <div class="title">运输货物名称</div>
          </th>
        </tr>
      </table>
      <div class="table6-wrapper">
        <table class="table6">
          <colgroup>
            <col width="150px">
            <col width="150px">
            <col width="292px">
            <col width="292px">
            <col width="150px">
          </colgroup>
          <tr v-for="item in invoiceInfoList">
            <td>
              <div class="content">{{ item.typeOfTransport }}</div>
            </td>
            <td>
              <div class="content">{{ item.plateNumber }}</div>
            </td>
            <td>
              <div class="content">{{ item.pointOfOrigin }}</div>
            </td>
            <td>
              <div class="content">{{ item.arrivalPoint }}</div>
            </td>
            <td>
              <div class="content">{{ item.cargoType }}</div>
            </td>
          </tr>
        </table>
      </div>
      <table class="table4">
        <tr>
          <th>
            <div class="title">价税合计（大写）</div>
          </th>
          <td>
            <div class="content">
              <div class="capital"><i class="el-icon-circle-close"></i> {{ info.amountCN }}</div>
              <div class="lower">
                <div class="title">（小写）</div>￥{{ info.allAmount }}
              </div>
            </div>
          </td>
        </tr>
      </table>
      <table class="table5">
        <tr>
          <td class="vertical-td">
            <div class="title vertical-title">备注</div>
          </td>
          <td class="remark-td">
            <div class="remark-wrapper">
                <div class="remark-inner">
                  <template v-if="type === 'detail'">{{ remark }}</template>
                  <el-input
                    v-else
                    type="textarea"
                    class="remark-area"
                    resize="none"
                    :maxlength="92"
                    :show-word-limit="true"
                    v-model="remark"
                    placeholder="必填"></el-input>
                </div>
              </div>
          </td>
        </tr>
      </table>
      <div class="detail-btns">
        <el-button
          type="primary"
          size="mini"
          v-for="item in data"
          :key="item.id"
          @click="detail(item.id)">查看详情</el-button>
      </div>
      <div class="all_detail_button">
        <el-button
          type="primary"
          size="mini"
          @click="detailAll()">查看详情</el-button>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    props: ['info', 'data', 'type', 'invoiceInfoList', 'isSimplification', 'originData'],
    data() {
      return {
        remark: '',
      }
    },
    created() {
      this.remark = this.info.remark
    },
    methods: {
      getData() {
        return {
          remark: this.remark
        }
      },
      detail(id) {
        if (this.isSimplification) {
          this.$router.push({
            name: 'attachmentInventory',
            params: {
              data: this.originData
            }
          })
        } else {
          this.$router.push('/finance/invoice/waybill?id=' + id)
        }
      },
      detailAll() {
        // 全电票获取全部运单
        let ids = this.data.map( (item) => {
          return item.id
        })
        this.$router.push({
          path: '/finance/invoice/waybill',
          query: {
            id: 'all',
            invoiceId: this.info.id
          }
        })
      }
    }
  }
</script>
<style lang="scss" scoped>
.main,
.headline {
  // font-family: 'Kaiti';
}
.headline,
.title {
  color: #A06F11;
}
.headline {
  text-align: center;
}
.headline-outer {
  padding-bottom: 4px;
  display: inline-block;
  border-bottom: 2px solid #A06F11;
}
.headline-inner {
  padding: 0 40px 5px 40px;
  font-size: 26px;
  border-bottom: 2px solid #A06F11;
}
.title {
  text-align: center;
  font-size: 14px;
}
.vertical-title {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 41px;
  writing-mode: vertical-rl;
}
.vertical-td {
  width: 41px;
}
.main {
  box-sizing: content-box;
  position: relative;
  margin: 24px auto 0 auto;
  padding-right: 93px;
  padding-bottom: 20px;
  width: 1037px;
}
table {
  box-sizing: border-box;
  margin: 0 auto;
  margin-bottom: -2px;
  border-collapse: collapse;
  td, th {
    padding: 0;
    border: 2px solid #A06F11;
    vertical-align: middle;
    font-size: 14px;
  }
}
.table1-row1 {
  width: 471px;
}
.table1-row2 {
  width: 474px;
}
.table1-row1,
.table1-row2 {
  min-height: 98px;
  line-height: 22px;
  box-sizing: border-box;
  padding: 5px 10px;
  > div:nth-child(1) {
    margin-top: 15px;
  }
  > div:nth-child(2) {
    margin-top: 10px;
  }
}
.table2 {
  width: 1037px;
  border: 2px solid #A06F11;
}
.table2 .list-row th {
  padding: 5px 0;
}
.table2 .content {
  height: 20px;
  line-height: 20px;
  text-align: center;
}
.unit-select {
  width: 60px;
}
.table2 .list-row {
  td, th {
    border-top: 0;
    border-bottom: 0;
  }
}
.table2 .list-row-last {
   .content, .title {
    margin-bottom: 7px;
  }
}
.table2 .sum {
  height: 28px;
}
.table2 td,
.table2 th {
  border: 0;
}
.sum-content {
  margin-left: 60px;
}
.sum-item {
  display: inline-block;
  width: 50%;
}
.sum-content .title {
  display: inline-block;
}
table input,
table textarea {
  border: 1px solid #DCDFE6;
  outline: none;
}
.spec-input {
  box-sizing: border-box;
  width: 130px;
  height: 28px;
}
.remark-td {
  position: relative;
}
.remark-wrapper {
  width: 394px;
}
.remark-inner {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 5px;
}
.remark-area {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  resize: none;
  ::v-deep textarea {
    height: 100%;
  }
}
.detail-btns {
  position: absolute;
  top: 125px;
  right: 0;
  .el-button {
    display: block;
    margin-bottom: 6px;
    margin-left: 0;
  }
}
.all_detail_button {
  position: absolute;
  top: 325px;
  right: 0;
  .el-button {
    display: block;
    margin-bottom: 6px;
    margin-left: 0;
  }
}
.table3 {
  width: 1037px;
  border: 2px solid #A06F11;
  td, th {
    border: 0;
  }
}
.table3 .list-row th {
  padding: 5px 0;
  border-bottom: 2px solid #A06F11;
}
.table6-wrapper {
  overflow-y: auto;
  height: 87px;
  border-left: 2px solid #A06F11;
  border-right: 2px solid #A06F11;
}
.table6 {
  td, th {
    border: 0;
  }
}
.table6 .content {
  height: 30px;
  line-height: 30px;
  text-align: center;
}
.table4 {
  width: 1037px;
  th {
    border-right: 2px solid #A06F11;
    .title {
      width: 250px;
    }
  }
  .title,
  .content {
    height: 40px;
    line-height: 40px;
  }
  .content {
    width: 781px;
  }
  .capital {
    display: inline-block;
    width: 560px;
    margin-left: 20px;
  }
  .lower {
    display: inline-block;
    .title {
      display: inline-block;
    }
    span {
      color: #A06F11;
    }
  }
}
.table5 {
  width: 1037px;
  .title {
    height: 190px;
  }
}
</style>