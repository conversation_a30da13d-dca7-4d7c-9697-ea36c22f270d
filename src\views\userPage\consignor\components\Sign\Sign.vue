<template>
  <div>
    <el-form
      ref="form"
      :rules="rules"
      :model="form"
      label-width="160px">
      <el-form-item label="货主合作平台主体" prop="selectIds">
        <el-checkbox-group v-model="selectIds">
          <el-checkbox v-for="item in baseInfos" :key="item.id" :label="item.id">{{item.baseName}}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="司机签约方式">
        <el-radio-group v-model="form.driverSigningMethod">
          <el-radio label="0">线下签约</el-radio>
          <el-radio label="1">在线签约</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.driverSigningMethod === '1'" label="司机补签截止时机">
        <div class="text">如果司机接单时无法完成在线签约，则允许司机补签</div>
        <el-radio-group v-model="form.driverSigningCutoff" class="radio">
          <el-radio label="1">装货前完成补签
            <span class="text">未完成补签则司机无法装货</span>
          </el-radio>
          <el-radio label="2">收货前完成补签
            <span class="text">未完成补签则货主无法装货付、卸货付</span>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="设置收款方">
        <el-checkbox-group v-model="checkedPayees">
          <el-checkbox label="owner">货主模式下允许设置</el-checkbox>
          <el-checkbox label="operator">运营模式下允许设置</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="车辆限重规则" prop="weight">
        <el-switch @change="handleWeightLimitChange" v-model="isOpenWeightLimit"></el-switch>
        <span class="weight-warn">此设置不影响车辆载重本身，只影响系统中关于“是否超载”的校验，即按照下述设置认定车辆可装卸最大货物重量，此设置仅对承运车辆为“牵引车+挂车”的运单生效</span>
        <br>
        <template v-if="isOpenWeightLimit">
          <el-radio v-model="weightRuleType" label="0">按照“挂车行驶证核定载质量+上浮固定吨数”限重</el-radio>
          <div v-if="weightRuleType === '0'" class="weight-content">上浮
            <el-input-number v-model="weightNumberTon" class="weight-number"></el-input-number>
            吨
            <!-- <span class="weight-rule">允许输入(0,10]的整数</span> -->
          </div>
          <br>
          <el-radio v-model="weightRuleType" label="1">按照“车货总质量 + 上浮固定百分比”限重</el-radio>
          <div v-if="weightRuleType === '1'" class="weight-content">
            限重 = 49吨 + 49吨 × 
            <el-input-number v-model="weightNumberPercent" class="weight-number"></el-input-number>
            %  -  牵引车整备质量  -  挂车整备质量
            <!-- <span class="weight-rule">允许输入(0,10]的整数</span> -->
          </div>
        </template>
      </el-form-item>
      <el-form-item label="允许查看北斗在途轨迹">
        <el-switch v-model="form.beidouRealTimeTrackShow"></el-switch>
      </el-form-item>
      <el-form-item label="司机装卸凭证必填">
        <el-radio-group v-model="form.driverPhotoMust">
          <el-radio label="0">可以不上传装卸凭证</el-radio>
          <el-radio label="1">必须上传装卸凭证</el-radio>             
        </el-radio-group>
      </el-form-item>
      <el-form-item label="司机装卸凭证上传">
        <el-radio-group v-model="form.driverPhotoMustPhotograph">
          <el-radio label="0">允许拍照和从相册上传</el-radio>
          <el-radio label="1">只能拍照上传</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button @click="submit" type="primary">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      form: {
        driverSigningMethod: '',
        driverSigningCutoff: '',
        userId: '',
        operatorPayeeShow: '',
        bussinessPayeeShow: '',
        beidouRealTimeTrackShow: false,
        driverPhotoMust:'0',
        driverPhotoMustPhotograph:'0'
      },
      rules: {
        weight: [
          {
            validator: (rule, value, cb) => {
              if (!this.isOpenWeightLimit) cb()
              if (this.isOpenWeightLimit && !this.weightRuleType) {
                cb('请选择')
              }
              let num = this.weightRuleType === '0' ? Number(this.weightNumberTon) : Number(this.weightNumberPercent)
              if (num <= 0 || num > 10 || !Number.isInteger(num)) {
                cb('允许输入(0,10]的整数')
              }
              cb()
            },
            trigger: 'blur'
          }
        ],
        selectIds: [
          {
            validator: (rule, value, cb) => {
              if (this.selectIds.length == 0) {
                cb('请选择')
              }
              cb()
            }
          }
        ],

      },
      checkedPayees: [],
      isOpenWeightLimit: false,
      weightRuleType: '',
      weightNumberTon: '',
      weightNumberPercent: '',
      selectIds: [],
      baseInfos: [],
    }
  },
  methods: {
    init(data) {
      this.$get('/admin-center-server/commonUser/getUserSettingById', {
        userId: data.userId
      })
        .then(res => {
          this.form.userId = data.userId
          this.form.driverSigningMethod = res.driverSigningMethod
          this.form.driverSigningCutoff = res.driverSigningCutoff
          this.form.driverPhotoMust = res.driverPhotoMust
          this.form.driverPhotoMustPhotograph = res.driverPhotoMustPhotograph
          this.checkedPayees = []
          //货主 owner 运营 operator
          if (res.operatorPayeeShow === '1') this.checkedPayees.push('operator')
          if (res.bussinessPayeeShow === '1') this.checkedPayees.push('owner')

          this.isOpenWeightLimit = res.vehicleWeightLimit
          this.weightRuleType = res.vehicleWeightLimitWay
          if (this.weightRuleType === '0') {
            this.weightNumberTon = res.vehicleWeightLimitValue
            this.weightNumberPercent = '5'
          } else {
            this.weightNumberTon = '5'
            this.weightNumberPercent = res.vehicleWeightLimitValue
          }

          this.form.beidouRealTimeTrackShow = res.beidouRealTimeTrackShow === '1' ? true : false
          this.selectIds = res.baseIds.split(',')
        })
        this.baseInfos = this.$store.state.user.baseInfo
    },
    handleWeightLimitChange(v) {
      if (v)  {
        this.weightNumberTon = 5
        this.weightNumberPercent = 5
      }
    },
    async submit() {
      let validResult = await this.$refs.form.validate()
      console.log(validResult);
      if (!validResult) return
      if (this.form.driverSigningMethod === '1' && (this.form.driverSigningCutoff !== '1' && this.form.driverSigningCutoff !== '2')) {
        this.$message.error('签约方式为“在线签约”时，请选择“司机在线签约截止时机”')
        return
      }
      let params = { ...this.form }
      if (this.checkedPayees.includes('operator')) {
        params.operatorPayeeShow = '1'
      } else {
        params.operatorPayeeShow = '0'
      }
      if (this.checkedPayees.includes('owner')) {
        params.bussinessPayeeShow = '1'
      } else {
        params.bussinessPayeeShow = '0'
      }

      if (this.isOpenWeightLimit) {
        params.vehicleWeightLimit = true
        params.vehicleWeightLimitWay = this.weightRuleType
        params.vehicleWeightLimitValue = this.weightRuleType === '0' ? this.weightNumberTon : this.weightNumberPercent
      } else {
        params.vehicleWeightLimit = false
      }

      for (let i = 0; i < this.selectIds.length; i++) {
        if (i == 0) {
          params.baseIds = this.selectIds[i]
        } else {
          params.baseIds = params.baseIds + "," + this.selectIds[i]
        }
      }

      params.beidouRealTimeTrackShow = this.form.beidouRealTimeTrackShow === true ? '1' : '0'

      this.$post('/admin-center-server/commonUser/updateUserSetting', params)
        .then(() => {
          this.$emit('success')
          this.$message.success('操作成功')
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.text {
  color: rgb(170, 170, 170);
}
.radio {
  .el-radio {
    display: block;
    margin-top: 15px;
  }
}
.weight-warn {
  margin-left: 10px;
  font-size: 14px;
  color: #888;
}
.weight-radio-group {
  .el-radio {
    display: block;
    margin-top: 15px;
  }
}
.weight-content {
  margin-left: 22px;
}
.weight-number {
  margin: 0 10px;
  width: 150px;
}
.weight-rule {
  margin-left: 10px;
  color: rgb(221, 32, 66);
}
</style>