<template>
  <div id="app">
    <div class="content">
      <!-- <div class="warn">
          <span class="tips">
            提示：这个按钮用来更新后台数据
          </span>
          <el-button type="primary" @click="upDateData" :loading="laadingFlag" style="margin-left:10px">更新地区数据</el-button>
      </div> -->
      <el-row>
        <div class="regon-title">
          <el-col :span="6">
            <p>省/自治区/直辖市</p>
          </el-col>
          <el-col :span="6">
            <p>地级市</p>
          </el-col>
          <el-col :span="6">
            <p>市辖区/县/县级市</p>
          </el-col>
          <el-col :span="6">
            <p>乡/镇/街道</p>
          </el-col>
        </div>

        <!-- 省/自治区/直辖市  -->
        <el-col :span="6" class="region-item">
          <el-table
            size="mini"
            :data="province"
            border
            style="width: 80%;margin: 0 auto"
            highlight-current-row
            @row-click="openCity"
          >
            <el-table-column prop="name" label="地区和编码">
              <template slot-scope="scope">
                <span :class="{selected: scope.row.id === cityId}">{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope" class="miniBtn">
                <span
                  class="el-tag el-tag--primary el-tag--mini"
                  style="cursor: pointer;"
                  @click.stop="editRow(scope.row)"
                >编辑</span>
                <span
                  class="el-tag el-tag--danger el-tag--mini"
                  style="cursor: pointer;"
                  @click.stop="deleteProvinceRow(scope.row)"
                >删除</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="el-table-add-row">
            <el-button @click="add(1)">+ 添加</el-button>
          </div>
        </el-col>

        <!-- 地级市 -->
        <el-col :span="6" class="region-item">
          <el-table
            size="mini"
            :data="city"
            border
            style="width: 80%;margin: 0 auto"
            highlight-current-row
            @row-click="openDistrict"
          >
            <el-table-column prop="name" label="地区和编码">
              <template slot-scope="scope">
                <span :class="{selected: scope.row.id === districtId}">{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <span
                  class="el-tag el-tag--primary el-tag--mini"
                  style="cursor: pointer;"
                  @click.stop="editRow(scope.row)"
                >编辑</span>
                <span
                  class="el-tag el-tag--danger el-tag--mini"
                  style="cursor: pointer;"
                  @click.stop="deleteCityRow(scope.row)"
                >删除</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="el-table-add-row">
            <el-button @click="add(2)" v-if="cityId">+ 添加</el-button>
          </div>
        </el-col>

        <!-- 市辖区/县/县级市 -->
        <el-col :span="6" class="region-item">
          <el-table
            size="mini"
            :data="district"
            border
            style="width: 80%;margin: 0 auto"
            highlight-current-row
            @row-click="openStreet"
          >
            <el-table-column prop="name" label="地区和编码">
              <template slot-scope="scope">
                <span :class="{selected: scope.row.id === streetId}">{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <span
                  class="el-tag el-tag--primary el-tag--mini"
                  style="cursor: pointer;"
                  @click.stop="editRow(scope.row)"
                >编辑</span>
                <span
                  class="el-tag el-tag--danger el-tag--mini"
                  style="cursor: pointer;"
                  @click.stop="deleteDistrictRow(scope.row)"
                >删除</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="el-table-add-row">
            <el-button @click="add(3)" v-if="districtId">+ 添加</el-button>
          </div>
        </el-col>

        <!-- 乡/镇/街道 -->
        <el-col :span="6" class="region-item">
          <el-table
            size="mini"
            :data="street"
            border
            style="width: 80%;margin: 0 auto"
            highlight-current-row
            @row-click="openStreet"
          >
            <el-table-column prop="name" label="地区和编码"></el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope" class="miniBtn">
                <span
                  class="el-tag el-tag--primary el-tag--mini"
                  style="cursor: pointer;margin-right:5px"
                  @click.stop="editRow(scope.row)"
                >编辑</span>
                <span
                  class="el-tag el-tag--danger el-tag--mini"
                  style="cursor: pointer;"
                  @click.stop="deleteStreetRow(scope.row)"
                >删除</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="el-table-add-row">
            <el-button @click="add(4)" v-if="streetId">+ 添加</el-button>
          </div>
        </el-col>
      </el-row>
      <span>{{master_user.data}}</span>
    </div>

    <!-- 添加的弹窗 -->
    <el-dialog title="添加地址" :visible.sync="addDialog" @close="closeDialog">
      <el-form :model="form">
        <el-form-item label="地区名称" :label-width="formLabelWidth" required>
          <el-input v-model="form.name" autocomplete="off" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="地区编码" :label-width="formLabelWidth" required>
          <el-select v-model="form.code" placeholder="请选择地区编码">
            <el-option v-for="item in options" :key="item.id" :label="item.id" :value="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" :label-width="formLabelWidth">
          <el-input v-model="form.sort" autocomplete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="addSure" v-if="!isEditor">确定</el-button>

        <el-button type="primary" @click="editorSure" v-if="isEditor">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { constants } from "crypto";
export default {
  name: "",
  data() {
    return {

      laadingFlag:false,// 按钮的loading

      addDialog: false, //添加的弹窗
      options: [],
      parentId: 0,
      id: "", //当前行的 id
      cityId: "", //城市的id
      districtId: "", //市辖区id
      streetId: "", //乡
      form: {
        name: "",
        code: "",
        sort: "" //排序
      },
      isEditor: false, //是否是编辑
      province: [], //省
      city: [], //市
      district: [], //区、县级市
      street: [], // 乡、镇、街道
      level: "", //操作的级别数字
      formLabelWidth: "120px",
      master_user: {
        sel: null, //选中行
        columns: [
          {
            prop: "type",
            label: "地区编码"
          }
        ],

      }
    };
  },
  activated() {
    this.getAddressCode(); //获取地区编码
    this.getData(1); //初始化第一级列表数据
  },
  methods: {
    /* 获取地区编码 */
    getAddressCode() {
      this.$http
        .get("/admin-center-server/area_code/list", {
          params: {
            pageNumber: 1,
            pageSize: 1000
          }
        })
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            console.log(data);
            // debugger;
            this.options = data.data.list;
            console.log(this.options)
          }
        });
    },
    /* 获取省级数据列表 */
    getData(level) {
      this.$http
        .get("/admin-center-server/area/list", {
          params: {
            pageNumber: 1,
            pageSize: 1000,
            parentId: this.parentId
          }
        })
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            console.log(data);
            let list = data.data.list;
            if (level === 1) {
              list.forEach(item => {
                item.level = 1; //添加表示层级的标示
              });
              this.province = list;
              console.log(this.province, "--------this.province");
            } else if (level === 2) {
              let list = data.data.list;
              list.forEach(item => {
                item.level = 2; //添加表示层级的标示
              });
              this.city = list;
              console.log(this.city, "--------this.city");
            } else if (level === 3) {
              let list = data.data.list;
              list.forEach(item => {
                item.level = 3; //添加表示层级的标示
              });
              this.district = list;
              console.log(this.district, "--------this.district");
            } else if (level === 4) {
              let list = data.data.list;
              list.forEach(item => {
                item.level = 4; //添加表示层级的标示
              });
              this.street = list;
              console.log(this.street, "--------this.street");
            }
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 获取  地级市*/
    openCity(row) {
      console.log(row);
      this.cityId = row.id;
      this.parentId = row.id;
      this.getData(2); //后取
    },
    /* 地级市 */
    openDistrict(row) {
      console.log(row);
      this.districtId = row.id;
      this.parentId = row.id;
      this.getData(3); //后取
    },
    /* 市辖区/县/县级市 */
    openStreet(row) {
      console.log(row);
      this.streetId = row.id; //用来判断添加的按钮
      (this.parentId = row.id), //用来传值
        this.getData(4); //后取
    },

    /* 删除省的操作 */
    deleteProvinceRow(row) {
      console.log(row);
      let id = Number(row.id);
      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {

          this.$http
            .post("/admin-center-server/area/delete?id=" + id)
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
                this.getData(1); //刷新数据列表
                this.city = [];
                this.district = [];
                this.street = [];
              } else {
                this.$message.warning(data.message);
              }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    /* 删除市的操作 */
    deleteCityRow(row) {
      console.log(row);
      let id = Number(row.id);
      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {

          this.$http
            .post("/admin-center-server/area/delete?id=" + id)
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
                this.getData(2); //刷新数据列表
                this.district = [];
                this.street = [];
              } else {
                this.$message.warning(data.message);
              }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    /* 市辖区/县/县级市 */
    deleteDistrictRow(row) {
      console.log(row);
      let id = Number(row.id);
      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {

          this.$http
            .post("/admin-center-server/area/delete?id=" + id)
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
                this.getData(3); //刷新数据列表
                this.street = [];
              } else {
                this.$message.warning(data.message);
              }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    /* 乡/镇/街道 */
    deleteStreetRow(row) {
      console.log(row);
      let id = Number(row.id);
      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {

          this.$http
            .post("/admin-center-server/area/delete?id=" + id)
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
                this.getData(4); //刷新数据列表
              } else {
                this.$message.warning(data.message);
              }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    //添加
    add(level) {
      this.form = {};
      this.addDialog = true;
      this.level = level
    },
    /* 添加的确定 */
    addSure() {
      if (!this.form.name) {
        this.$message.error('请填写地区名称')
        return
      }
      if (!this.form.code) {
        this.$message.error('请选择地区编码')
        return
      }
      var postData = {
        name: this.form.name,
        code: this.form.code,
        parentId: this.parentId
      };
      console.log(postData, "-----postData");
      this.$http
        .post("/admin-center-server/area/save", this.$qs.stringify(postData))
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "添加成功!"
            });
            if(this.level ==1){
              this.getData(1)
            }else if(this.level ==2){
              this.getData(2)
            }else if(this.level == 3){
              this.getData(3)
            }else if(this.level == 4){
              this.getData(4)
            }

            this.addDialog = false;
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 编辑 */
    editRow(row) {
      console.log(row);
      this.level = row.level;
      console.log(this.level, "----level");
      this.id = row.id;
      this.addDialog = true;
      this.isEditor = true;
      this.form.name = row.name;
      this.form.code = row.code;
    },
    /*  取消按钮 */
    cancel() {
      this.isEditor = false;
      this.addDialog = false;
    },
    /* 关闭测差号 */
    closeDialog() {
      this.isEditor = false;
      this.addDialog = false;
    },
    /* 编辑的确定 */
    editorSure() {
      if (!this.form.name) {
        this.$message.error('请填写地区名称')
        return
      }
      if (!this.form.code) {
        this.$message.error('请选择地区编码')
        return
      }
      var postData = {
        name: this.form.name,
        code: this.form.code,
        id: this.id
      };
      console.log(postData, "-----postData");
      this.$http
        .post("/admin-center-server/area/update", this.$qs.stringify(postData))
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "更改成功!"
            });
            this.addDialog = false;
            if (this.level == 1) {
              this.getData(1);
            } else if (this.level == 2) {
              this.getData(2);
            } else if (this.level == 3) {
              this.getData(3);
            } else if (this.level == 4) {
              this.getData(4);
            }
          } else {
            this.$message.warning(data.message);
          }
        });
    },

    /* 更新后台数据 */
    upDateData(){
        this.laadingFlag = true;
        this.$http
        .post("/admin-center-server/area/updateAreaAll",{timeout:180000})
        .then(res => {
          let data= res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "更新成功!"
            });
            this.loadingFlag = false;
          } else {
            this.loadingFlag = false;
            // this.$message.warning(data.message);
          }
        });
    }
  }
};
</script>

<style lang="scss">
.content {
  width: 98%;
  margin: 0 auto;
}
.warn{
  background: #ffffff;
  margin: 10px;
  padding: 10px;
}
.tips{
  color:red
}
.el-table-add-row {
  margin-top: 10px;
  width: 100%;
  height: 34px;
  //   border: 1px dashed #c1c1cd;
  border-radius: 3px;
  cursor: pointer;
  justify-content: center;
  display: flex;
  line-height: 34px;
}
.regon-title {
  width: 100%;
  // background:  #ccc;
  p {
    text-align: center;
  }
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.selected {
  color: red;
}
</style>
