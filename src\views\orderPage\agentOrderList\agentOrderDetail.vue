<template>
  <div class="app-container carDetail">
    <div class="agent_order_status">调度单状态：{{baseInfo.statusString}}</div>
    <template>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="调度员单详情" name="first">
          <Dispatch :tableData="{...baseInfo, ...sendAndReceiveInfo, ...statisticsOrderItemStatus}">
          </Dispatch>
        </el-tab-pane>
        <el-tab-pane label="订单详情" name="second">
          <OrderDetail :tableData="orderDetailTable" :showCustomerName="true">
          </OrderDetail>
        </el-tab-pane>

        <el-tab-pane label="关联运单" name="third">
          <div class="main-box">
            <div class="list-main">
              <template>
                <el-table :data="wayBillTableData" border style="width: 100%"     
                cell-class-name="table_cell_gray"
                header-cell-class-name="table_header_cell_gray">
                  <el-table-column type="index" label="编号" width="50"></el-table-column>
                  <el-table-column prop="orderItemSn" label="运单单号"></el-table-column>
                  <el-table-column prop="orderItemStatusString" label="运单状态"></el-table-column>
                  <el-table-column prop="carNumber" label="抢单车辆"></el-table-column>
                  <el-table-column prop="driverName" label="司机名称"></el-table-column>
                  <el-table-column prop="driverMobile" label="电话"></el-table-column>
                  <el-table-column prop="ton" label="抢单吨数" width="120"></el-table-column>
                  <el-table-column prop="originalTon" label="原发吨数"></el-table-column>
                  <el-table-column prop="currentTon" label="实收吨数"></el-table-column>
                  <el-table-column prop="paymentTon" label="结算吨数"></el-table-column>
                  <el-table-column prop="deductPrice" label="扣款"></el-table-column>
                  <el-table-column prop="paymentAmount" label="结算金额"></el-table-column>
                  <el-table-column fixed="right" label="操作" width="100">
                    <template slot-scope="scope">
                      <el-button @click="goDetail(scope.row)" type="text" size="small">查看</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="调度单日志" name="fifth">
          <div class="main-box">
            
            <div class="list-main">
              <template>
                <el-table :data="logTableData" border style="width: 100%"          
                cell-class-name="table_cell_gray"
                header-cell-class-name="table_header_cell_gray">
                  <el-table-column type="index" label="编号" width="50"></el-table-column>
                  <el-table-column prop="operator" label="操作者"></el-table-column>
                  <el-table-column prop="createdDate" label="操作时间"></el-table-column>
                  <el-table-column prop="content" label="备注"></el-table-column>
                </el-table>
              </template>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </template>
  </div>
</template>


<script>
export default {
  name: "CarDetail",
  data() {
    return {
      isBrevityName: "", //短倒
      activeName: "first",
      dialogForCancel: false, // 取消订单
      textarea: "", //其他原因
      baseInfo: {},
      reason: "", //原因
      reasonId: "", //取消原因的id
      formLabelWidth: "120px",
      tableData: [{}],
      baseInfo: {}, // 基本信息
      sendAndReceiveInfo: {}, // 收发信息
      statisticsOrderItemStatus: {}, // 运单统计
      options: [], //原因下拉列表
      orderDetailTable: [{}], //订单详情
      wayBillTableData: [], //运单信息
      payinvoiceorderitems: [], //收款信息
      logTableData: [], //操作日志
      status: "", //用来判断订单的状态
      statusEnum: "", //订单状态
      orderBrokerId: ""
    };
  },
  activated() {
    this.getAgentOrderDetail(); //获取调度员单列表
    this.activeName = 'first'
  },
  methods: {
    goRevise() {
      this.$router.push("/carsList/carRevise");
    },

    /** 获取调度员单详情 **/
    getAgentOrderDetail() {
      let orderBrokerId = Number(this.$route.query.orderBrokerId);
      this.orderBrokerId = this.$route.query.orderBrokerId;
      // console.log(orderBrokerId, "--orderBrokerId-----");
      this.$http
        .get("/admin-center-server/orderDispatch/queryOrderDispatchInfo", {
          params: {
            orderDispatchId: orderBrokerId
          }
        })
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            //订单详情
            // console.log(data.data, "data");
            const responseData = data.data; //调度员单详情
            this.status = responseData.baseInfo.status;
            this.statusEnum = responseData.baseInfo.statusEnum;
            this.baseInfo = responseData.baseInfo
            this.sendAndReceiveInfo = responseData.sendAndReceiveInfo
            this.statisticsOrderItemStatus = responseData.statisticsOrderItemStatus
            this.isBrevityName = false;
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },

    /* 取消订单 */
    cancelOrder() {
      this.dialogForCancel = true;
      this.type = 1;
      this.getReasonList(); //获取取消原因列表
    },
    /* 操作选项卡 */
    handleClick(tab, event) {
      // console.log(tab.name);

      if (tab.name == "first") {
        //调度员单详情
        this.getAgentOrderDetail();
      }
      if (tab.name == "second") {
        //订单详情
        this.getOrderDetail();
      }
      if (tab.name == "third") {
        //运单信息
        this.wayBillDetai();
      }
      if (tab.name == "fourth") {
        //首款信息
        this.payDetail();
      }
      if (tab.name == "fifth") {
        //操作日志
        this.operateLog();
      }
    },
    frozenOrder() {},
    /* 获取原因 */
    getReasonList() {
      this.$http
        .get("/admin-center-server/order/reasonlist?reasonType=2")
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.options = data.data;
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },
    /* 取消订单的确认 */
    cancelSure() {
      if (this.reasonId == "") {
        this.$message.warning("请选择取消原因");
        return;
      }
      let orderId = this.$route.query.orderBrokerId;
      let postData = {
        orderId: this.$route.query.orderBrokerId,
        userType: 2,
        reasonMemo: this.reason,
        reasonId: Number(this.reasonId),
        type: 2
      };
      this.$http
        .post("/admin-center-server/order/cancelOrder", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "取消成功!"
            });
            this.dialogForCancel = false;
            this.$router.go(-1);
          } else {
            this.$qs.stringify(postData);
          }
        });
    },
    /* 订单详情 */
    getOrderDetail() {
      let id = this.baseInfo.orderBusinessId
      this.$http
        .get(
          "/admin-center-server/order/getOrderBusinessDetailOrder?orderBusinessId=" +
            id
        )
        .then(res => {
          let data = res.data;
          // console.log(res.data.data)
          if (data.code === "200") {
            this.orderDetailTable = res.data.data.orderBusinessDetail;
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },
    /* 运单信息 */
    wayBillDetai() {
      let orderBrokerId = Number(this.$route.query.orderBrokerId);
      this.$http
        .get(
          "/admin-center-server/orderDispatch/queryOrderDispatchSubOrderItem?orderDispatchId=" +
            orderBrokerId
        )
        .then(res => {
          let data = res.data;
          // console.log(res.data.data)
          if (data.code === "200") {
            this.wayBillTableData = res.data.data;
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },
    /*收款信息*/
    payDetail() {
      let orderBrokerId = Number(this.$route.query.orderBrokerId);
      this.$http
        .get(
          "/admin-center-server/order/getOrderBrokerDetailItemPay?orderBrokerId=" +
            orderBrokerId
        )
        .then(res => {
          let data = res.data;
          // console.log(res.data.data)
          if (data.code === "200") {
            this.payinvoiceorderitems = res.data.data; //收款信息
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },

    /* 操作日志 */
    operateLog() {
      let orderBrokerId = Number(this.$route.query.orderBrokerId);
      this.$http
        .get(
          "/admin-center-server/orderDispatch/queryOrderDispatchLogAdmin?orderDispatchId=" +
            orderBrokerId
        )
        .then(res => {
          let data = res.data;
          // console.log(res.data.data)
          if (data.code === "200") {
            this.logTableData = res.data.data; //收款信息
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },
    /* 查看详情页 */
    goDetail(row) {
      this.$router.push({
        path: "/transport/transportListDetail",
        query: {
          orderItemId: row.orderItemId,
          status: row.status,
          freezeStatus: row.freezeStatus
        }
      });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.agent_order_status {
  margin-bottom: -30px;
  text-align: right;
}
ul li {
  list-style: none;
}
.carDetail {
  .main-box {
    background-color: white;
    .title-box {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .title-box el-button {
      margin-right: 10px;
    }
    .list-box {
      margin-top: 20px;
      border: 1px solid #cccccc;
      border-left: none;
      .item-title {
        display: flex;
        flex-direction: row;
        div {
          width: 500px;
          height: 50px;
          font-weight: bold;
          font-size: 16px;
          line-height: 50px;
          border: 1px solid #cccccc;
          border-top: none;
          border-right: none;
          background-color: rgb(249, 252, 250);
          text-align: center;
        }
      }
      .item-info {
        display: flex;
        flex-direction: row;
        div {
          font-size: 14px;
          width: 500px;
          height: 50px;
          line-height: 50px;
          text-align: center;
          border: 1px solid #cccccc;
          border-top: none;
          border-right: none;
          border-bottom: none;
        }
      }
    }
    .base-info,
    .drive-info,
    .other-info,
    .driver-info {
      padding: 20px;
    }
  }
}
</style>
