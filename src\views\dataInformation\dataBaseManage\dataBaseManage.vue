<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">查询</div>
      <div class="select-info">
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="150px">
          <el-form-item label="车牌号:">
            <el-input v-model.trim="formInline.vehiclenumber" placeholder="请输入车牌号" maxlength="7"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="queryFun()">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
        <div>
          <el-button type="primary" @click="addCars">新增车辆</el-button>
        </div>
      </div>
      <div class="list-main">
        <template>
          <el-table :data="tableData" border style="width: 100%" :height="tableHeight" ref="table">
            <el-table-column type="index" label="序号" width="50"></el-table-column>
            <el-table-column prop="vehiclenumber" label="车牌号"></el-table-column>
            <el-table-column prop="licenseplatetypecode" label="类型代码"></el-table-column>
            <el-table-column prop="operateName" label="操作人"></el-table-column>
            <el-table-column prop="createddate" label="操作日期"></el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
              <template slot-scope="scope">
                <el-button @click="goDetail(scope.row)" type="text" size="small">编辑</el-button>
                <el-button type="text" size="small" @click="goAttestation(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="page">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInline.pageNumber"
              :page-sizes="[20, 40, 60, 80,100]"
              :page-size="formInline.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              style="margin: 10px auto"
            ></el-pagination>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CarsList",
  data() {
    return {
      formInline: {
        vehiclenumber: "", //车牌号
        pageNumber:1,
        pageSize:20
      },

      tableHeight: null, //表格的高度
      date: [], //创建日期
      tableData: [],
      total: null //总数
    };
  },
  activated() {
    this.getData();
    this.tableHeight =
      window.innerHeight - this.$refs.table.$el.offsetTop - 240;
  },
  methods: {
    /* 选取每页多少条数据 */
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.formInline.pageNumber = 1;
      this.formInline.pageSize = JSON.parse(`${val}`);
      // console.log(this.formInline.pageSize);
      this.getData();
    },
    /* 翻页 */
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.formInline.pageNumber = JSON.parse(`${val}`);
      // console.log(this.formInline.pageNumber);
      this.getData();
    },
    /** 获取数据列表 **/
    getData() {
      let postData = this.formInline;
      this.$http
        .get("/admin-center-server/admin/db_car/list", {
          params: postData
        })
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.tableData = data.data.list;
            this.total = JSON.parse(data.data.total);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    queryFun() {
      this.formInline.pageNumber =1;
      this.getData(); //获取查询数据
    },

    /*  编辑 */
    goDetail(row) {
      this.$router.push({
        path: "dataBaseManage/addCarInfo",
        query: {
          flag: 1, // 1--编辑 0 新增
          id: row.id
        }
      });
    },
    /* 删除的操作 */
    goAttestation(row) {
      let id = Number(row.id);
      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {

          this.$http
            .post("/admin-center-server/admin/db_car/delete?id=" + id)
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
                this.getData() //刷新数据列表
              } else {
                this.$message.warning(data.message);
              }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    /* 新增车辆 */
    addCars() {
      this.$router.push({
        path: "dataBaseManage/addCarInfo",
        query: {
          flag: 0 // 1--编辑 0 新增
        }
      });
    },
    /* 删除数据 */
    deleteData() {}
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.el-form-item {
  margin-bottom: 0;
}

.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }
    .page {
      text-align: right;
    }
    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }
  }
}
</style>
