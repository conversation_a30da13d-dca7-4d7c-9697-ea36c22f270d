<template>
  <div class="userDeal">
    <div class="select-box">
      <div class="select-info">
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="100px" size="mini">
          <el-form-item v-if="type!=5" label="订单号:">
            <el-input class="form-item-content-width" v-model="formInline.orderSn" placeholder="请输入订单号"></el-input>
          </el-form-item>
          <el-form-item label="运单号:">
            <el-input class="form-item-content-width" v-model="formInline.waybilSn" placeholder="请输入运单号"></el-input>
          </el-form-item>
          <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag&&type==5" label="收支类型:">
            <el-select class="form-item-content-width" v-model="formInline.flag" placeholder="不限" style="width: 180px">
              <el-option label="收入" value="1"></el-option>
              <el-option label="支出" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="交易类型:" v-if="type==5">
            <el-select class="form-item-content-width" v-model="formInline.type" @visible-change="handlePayTypeSelectorClick" placeholder="不限" style="width: 180px">
              <!-- <el-option v-for="item in payTypeList" :key="item.value" :label="item.comment" :value="item.value"></el-option> -->
              <el-option v-for="item in allTransactionType.manualTransferTypes" :key="item.transactionType" :label="item.transactionTypeName" :value="item.transactionType"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户类型:" v-if="type==5">
            <el-select class="form-item-content-width" v-model="formInline.userType" placeholder="不限" style="width: 180px">
              <el-option label="平台" value="0"></el-option>
              <el-option label="货主" value="1"></el-option>
              <el-option label="司机/车队长" value="3"></el-option>
              <el-option label="运力供应商" value="6"></el-option>
              <el-option label="渠道" value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户名称:">
            <el-input class="form-item-content-width" placeholder="请输入用户名称" v-model="formInline.userName"> </el-input>
          </el-form-item>
          <el-form-item label="用户手机号:">
            <el-input class="form-item-content-width" v-model="formInline.userMobile" placeholder="请输入用户手机号" maxlength="11"></el-input>
          </el-form-item>
          <el-form-item label="流水号:">
            <el-input class="form-item-content-width" v-model="formInline.transactionSn" placeholder="请输入流水号"></el-input>
          </el-form-item>
          <el-form-item label="交易时间:">
            <el-date-picker v-model="date" :clearable="false" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button style="margin-left: 20px" icon="el-icon-search" type="primary" @click="onSubmit" size="mini">查询</el-button>
            <el-button icon="el-icon-delete" type="danger" @click="clearForm" size="mini">清空筛选</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div class="list-title-left" v-if="type!=5">
          <!-- 收入 -->
          <div class="inList">
            <span class="checkText">收入</span>
            <el-tooltip class="symbol" effect="dark" placement="right" popper-class='prompt' v-if="type!='5'" >
              <div slot="content" class="tipContent" v-if="type=='1'">
                <div> <span> 充值：</span>货主充值至自己的平台货主账户，用于平台结算</div>
                <div> <span> 货主支付：</span>由于货主阶段回款过多，导致货主回尾款时需退还货主，通过【运营端 - 代货主支付】转至货主账户</div>
                <div> <span> 手动转账：</span>因业务中的特殊情况需要手动转账至货主账户</div>
                <div> <span> 手动转账 - 退还油费：</span>人工操作退还货主已支付的油费，通过【后台 - 财务管理 - 手动转账 - 转账类型“退还油费”】转账至货主账户</div>
                <div> <span> 手动转账 - 会员间交易：</span>技术运维转账至货主账户</div>
                <div> <span> 手动转账 - 其他：</span>因业务需要，人工操作【后台 - 财务管理 - 手动转账 - 转账类型“其他”】转账至货主账户</div>
              </div>
              <div slot="content" class="tipContent" v-if="type=='2'">
                <div> <span> 充值：</span>司机充值至自己的平台司机账户</div>
                <div> <span> 卸货付：</span>运单卸货后，司机收到的鸿飞达或平台货主的卸货付</div>
                <div> <span> 回单付：</span>回单收回后，司机收到的鸿飞达或平台货主的回单押金</div>
                <div> <span> 结算付：</span>运单通过平台审核后，司机收到的鸿飞达或平台货主的结算付</div>
                <div> <span> 下游补贴费用：</span>司机收到的鸿飞达或平台货主支付的补贴</div>
                <div> <span> 运费代收：</span>个人车队长作为代收人收到的运费，从司机账户转出至个人车队长账户</div>
                <div> <span> 手动转账：</span>因业务中的特殊情况需要手动转账至司机/个人车队长账户</div>
                <div> <span> 手动转账 - 油费转现金：</span>因业务需要，将司机油卡转为现金，通过【后台 - 财务管理 - 手动转账 - 转账类型“油费转现金”】转账至司机账户</div>
                <div> <span> 手动转账 - 会员间交易：</span>技术运维转账至司机/个人车队长账户</div>
                <div> <span> 手动转账 - 其他：</span>因业务需要，人工操作【后台 - 财务管理 - 手动转账 - 转账类型“其他”】转账至司机/个人车队长账户</div>
              </div>
              <div slot="content" class="tipContent" v-if="type=='3'">
                <div> <span> 回单付：</span>回单收回后，供应商收到的鸿飞达回单押金</div>
                <div> <span> 结算付：</span>对账完成后，供应商收到的鸿飞达结算付</div>
                <div> <span> 手动转账：</span>因业务中的特殊情况需要手动转账至供应商账户</div>
                <div> <span> 手动转账 - 会员间交易：</span>技术运维转账至供应商账户</div>
                <div> <span> 手动转账 - 其他：</span>因业务需要，人工操作【后台 - 财务管理 - 手动转账 - 转账类型“其他”】转账至供应商账户</div>
              </div>
              <div slot="content" class="tipContent" v-if="type=='4'">
                <div> <span> 充值(充油)：</span>渠道充值至自己的平台渠道账户，用于充油</div>
                <div> <span> 还款(还油)：</span>运单结算时，系统自动转账至渠道账户，用于还之前的渠道借款</div>
                <div> <span> 手动转账：</span>因业务中的特殊情况需要手动转账至渠道账户</div>
                <div> <span> 手动转账 - 会员间交易：</span>技术运维转账至渠道账户</div>
              </div>
              <i class="el-icon-warning"></i>
            </el-tooltip>
            <el-checkbox class="mr30" v-show="type==1||type==2||type==3||type==4" v-model="inAllSelect" @change="(val)=>handelAllChange(val,'in')">全部</el-checkbox>
            <el-checkbox-group v-model="formInline.incomeTypes" @change="(val)=>handSelectChange(val,'in')">
              <span class="mr30" v-for="(item, index) in inListSelect" :key="index">
                <el-checkbox v-if="item.transactionType!='28'" :label="item.transactionType" :name="item.transactionTypeName">
                  {{ item.transactionTypeName }}
                  <!-- <el-tooltip v-if="tips(item.transactionType)" effect="dark" :content="tips(item.transactionType)" placement="top">
                    <i class="el-icon-warning"></i>
                  </el-tooltip> -->
                </el-checkbox>
                <!-- 补贴相关 -->
                <el-checkbox v-if="item.transactionType=='28'" :indeterminate="isIndeterminate" :label="item.transactionType" :name="item.transactionTypeName" @change="(val)=>handelAllChange(val,'subsidy')">{{ item.transactionTypeName }}</el-checkbox>
                <el-popover popper-class='popBox manual' v-if="item.transactionType=='28'" trigger="click" placement="bottom" :append-to-body="false">
                  <i class="el-icon-arrow-down" slot="reference"></i>
                  <el-checkbox-group v-model="formInline.subTypeList" @change="(val)=>handSelectChange(val,'subsidy')">
                    <el-checkbox v-for="(item, index) in subsidyList" :key="index" :label="item.value" :name="item.label">
                      {{ item.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-popover>
              </span>
            </el-checkbox-group>
            <!-- 收入=手动转账 -->
            <el-checkbox v-if="type!=5" :indeterminate="manualInStatus" v-model="manualInAll" @change="(val)=>handelAllChange(val,'manualIn')">手动转账
              <!-- <el-tooltip effect="dark" :content="tips('991')" placement="top">
                <i class="el-icon-warning"></i>
              </el-tooltip> -->
            </el-checkbox>
            <el-popover v-if="type!=5" popper-class='popBox' trigger="click" placement="bottom" :append-to-body="false">
              <i class="el-icon-arrow-down" slot="reference"></i>
              <el-checkbox-group v-model="manualInList" @change="(val)=>handSelectChange(val,'manualIn')">
                <el-checkbox v-for="(item, index) in manualTransferInList" :key="index" :label="item.transactionType" :name="item.transactionTypeName">
                  {{ item.transactionTypeName }}
                </el-checkbox>
              </el-checkbox-group>
            </el-popover>

          </div>
          <!-- 支出 -->
          <div class="outList">
            <span class="checkText">支出</span>
              <el-tooltip class="symbol" effect="dark" placement="right" popper-class='prompt' v-if="type!='5'" >
              <div slot="content" class="tipContent" v-if="type=='1'">
                <div><span>提现：</span>货主从自己的平台货主账户提现</div>
                <div><span>货主支付：</span>货主回款至鸿飞达，通过【运营端 - 代货主支付】从货主账户转出</div>
                <div><span>油费付：</span>运单装货后，货主支付给下游的油费</div>
                <div><span>卸货付：</span>运单卸货后，货主支付给下游的卸货付</div>
                <div><span>回单付：</span>回单收回后，货主支付给下游的回单押金</div>
                <div><span>结算付：</span>运单通过平台审核后，货主支付给下游的结算付</div>
                <div><span>手动转账：</span>因业务中的特殊情况需要手动从货主账户转出</div>
                <div><span>手动转账 - 会员间交易：</span>技术运维从货主账户转出</div>
                <div><span>手动转账 - 其他：</span>因业务需要，人工操作【后台 - 财务管理 - 手动转账 - 转账类型“其他”】从货主账户转出</div>
              </div>
              <div slot="content" class="tipContent" v-if="type=='2'">
                <div><span>提现：</span>司机从自己的平台司机/个人车队长账户提现</div>
                <div><span>运费代收：</span>司机转给代收人运费，从司机账户转出至个人车队长账户</div>
                <div><span>手动转账：</span>因业务中的特殊情况需要手动从司机/个人车队长账户转出</div>
                <div><span>手动转账 - 会员间交易：</span>技术运维转账至司机/个人车队长账户</div>
                <div><span>手动转账 - 补充油费：</span>司机油费配少了，需要将司机现金收入的一部分从司机账户转出至平台油费账户，中骐专属</div>
                <div><span>手动转账 - 其他：</span>因业务需要，人工操作【后台 - 财务管理 - 手动转账 - 转账类型“其他”】从司机/个人车队长账户转出</div>
              </div>
              <div slot="content" class="tipContent" v-if="type=='3'">
                <div><span>提现：</span>供应商从自己的平台供应商账户提现</div>
                <div><span>手动转账：</span>因业务中的特殊情况需要手动从供应商账户转出</div>
                <div><span>手动转账 - 会员间交易：</span>技术运维转账至供应商账户</div>
                <div><span>手动转账 - 其他：</span>因业务需要，人工操作【后台 - 财务管理 - 手动转账 - 转账类型“其他”】从供应商账户转出</div>
              </div>
              <div slot="content" class="tipContent" v-if="type=='4'">
                <div><span>借款(充油)：</span>渠道充值至自己的账户后，系统自动将充值费用从渠道账户转出至平台油费账户，以便财务提现为渠道充油</div>
                <div><span>运费转出：</span>渠道收到还款后，系统自动从渠道账户转出</div>
                <div><span>运费代收：</span>渠道收到还款后，系统自动从渠道账户转出</div>
                <div><span>手动转账：</span>因业务中的特殊情况需要手动从渠道账户转出</div>
                <div><span>手动转账 - 会员间交易：</span>技术运维从渠道账户转出</div>
              </div>
              <i class="el-icon-warning"></i>
            </el-tooltip>
            <el-checkbox class="mr30" v-show="type==1||type==2||type==3||type==4" v-model="outAllSelect" @change="(val)=>handelAllChange(val,'out')">全部</el-checkbox>
            <el-checkbox-group v-model="formInline.expenseTypes" @change="(val)=>handSelectChange(val,'out')">
              <span class="mr30" v-for="(item, index) in outListSelect" :key="index">
                <el-checkbox v-if="item.transactionType!='28'" :label="item.transactionType" :name="item.transactionTypeName">
                  {{ item.transactionTypeName }}
                  <!-- <el-tooltip v-if="tips(item.transactionType)" effect="dark" :content="tips(item.transactionType)" placement="top">
                    <i class="el-icon-warning"></i>
                  </el-tooltip> -->
                </el-checkbox>
                <!-- 补贴相关 -->
                <el-checkbox v-if="item.transactionType=='28'" :indeterminate="isIndeterminate" :label="item.transactionType" :name="item.transactionTypeName" @change="(val)=>handelAllChange(val,'subsidy')">{{ item.transactionTypeName }}</el-checkbox>
                <el-popover popper-class='popBox manual' v-if="item.transactionType=='28'" trigger="click" placement="bottom" :append-to-body="false">
                  <i class="el-icon-arrow-down" slot="reference"></i>
                  <el-checkbox-group v-model="formInline.subTypeList" @change="(val)=>handSelectChange(val,'subsidy')">
                    <el-checkbox v-for="(item, index) in subsidyList" :key="index" :label="item.value" :name="item.label">
                      {{ item.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-popover>
              </span>
            </el-checkbox-group>
            <!-- 支出-手动转账 -->
            <el-checkbox v-if="type!=5" :indeterminate="manualOutStatus" v-model="manualOutAll" @change="(val)=>handelAllChange(val,'manualOut')">手动转账
              <!-- <el-tooltip effect="dark" :content="tips('992')" placement="top">
                <i class="el-icon-warning"></i>
              </el-tooltip> -->
            </el-checkbox>
            <el-popover v-if="type!=5" popper-class='popBox' trigger="click" placement="bottom" :append-to-body="false">
              <i class="el-icon-arrow-down" slot="reference"></i>
              <el-checkbox-group v-model="manualOutList" @change="(val)=>handSelectChange(val,'manualOut')">
                <el-checkbox v-for="(item, index) in manualTransferList" :key="index" :label="item.transactionType" :name="item.transactionTypeName">
                  {{ item.transactionTypeName }}
                </el-checkbox>
              </el-checkbox-group>
            </el-popover>
          </div>
        </div>
        <div class="list-title-right">
          <el-dropdown trigger="click">
            <el-button type="primary">导出</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <div @click="exportRow" type="text">导出Excel</div>
              </el-dropdown-item>
              <el-dropdown-item>
                <div @click="exportPdf" type="text">导出PDF电子回单</div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button type="primary" plain style="margin-left: 10px; margin-right: 10px" @click="getSum">计算合计交易金额</el-button>
          <div v-if="dataSum" style="font-size: 14px">
            当前列表中：收入合计：￥{{ incomeTotal }}， 支出合计：￥{{ expenseTotal }}， 净收入(收入-支出):￥{{ dataSum }} 
          </div>
          <div class="text" v-if="showTips">说明:当前列表数据同时包含承运型、结算型业务的收支明细</div>
        </div>
      </div>
      <div class="list-main">
        <template>
          <el-table :data="tableData" border style="width: 100%" class="table" cell-class-name="table_cell_gray" header-cell-class-name="table_header_cell_gray" @selection-change="handleSelectionChange">
            <el-table-column fixed type="index" label="序号" width="50"> </el-table-column>
            <el-table-column fixed prop="transactionSn" label="流水号" width="230"></el-table-column>

            <el-table-column show-overflow-tooltip prop="amount" label="交易金额(元)" width="120"></el-table-column>
            <el-table-column show-overflow-tooltip prop="mark" label="备注" width="220"> </el-table-column>
            <el-table-column show-overflow-tooltip prop="typeStr" label="交易类型" width="120"> </el-table-column>
            <el-table-column prop="flag" label="收支类型" :formatter="transactionType"> </el-table-column>
            <el-table-column prop="name" show-overflow-tooltip label="用户名称" width="180"> </el-table-column>
            <el-table-column show-overflow-tooltip prop="mobile" label="手机号" width="120"> </el-table-column>
            <el-table-column prop="userType" label="用户类型"  width="120"> </el-table-column>
            <el-table-column show-overflow-tooltip prop="payerName" label="付款人" width="120"> </el-table-column>
            <el-table-column show-overflow-tooltip prop="payerAccount" label="付款账号" width="120"></el-table-column>
            <el-table-column show-overflow-tooltip prop="payerBankName" label="付款人开户行" width="120"></el-table-column>
            <el-table-column show-overflow-tooltip prop="payeeName" label="收款人" width="120"> </el-table-column>
            <el-table-column show-overflow-tooltip prop="payeeAccount" label="收款账户" width="120"></el-table-column>
            <el-table-column show-overflow-tooltip prop="payeeBankName" label="收款人开户行" width="120"></el-table-column>
            <el-table-column show-overflow-tooltip prop="time" label="交易时间" width="180"></el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
              <template slot-scope="scope">
                <el-button @click="handleClick(scope.row)" type="text" size="small">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </div>
      <div class="paging">
        <div class="block">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pageNumber" :page-sizes="[10, 20, 30, 40, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { checkLess3Month } from "@/utils/date";
export default {
  data() {
    return {
      loading: false,
      dialogVisible: false,
      formInline: {
        userType: null,
        incomeTypes: [],
        expenseTypes: [],
        subTypeList: []
      },
      tableData: [],
      multipleSelection: [],
      total: 1,
      pageSize: 20,
      pageNumber: 1,
      date: [],
      payTypeList: [],
      exportData: {},
      dataSum: "",
      currentBaseId: '',
      // 收入/支出全选
      inAllSelect: false,
      outAllSelect: false,
      // 收入/支出手动转账全选
      manualInAll: false,
      manualOutAll: false,
      //收入/支出类型
      inListSelect: [],
      outListSelect: [],
      //补贴不确定类型
      isIndeterminate: false,
      // 手动转账不确定状态标志
      manualOutStatus: false,
      manualInStatus: false,
      // 手动转账类型列表-支出
      manualTransferList: [],
      // 手动转账类型列表-收入
      manualTransferInList: [],
      // 手动-支出列表
      manualOutList: [],
      //手动-收入列表
      manualInList: [],
      showTips: false,
      // 收入合计
      incomeTotal:'',
      // 支出合计
      expenseTotal:'',
    };
  },
  props: ['baseId', 'type', 'allTransactionType', 'subsidyList'],
  watch: {
    baseId(newVal, oldVal) {
      this.pageNumber = 1
      this.currentBaseId = newVal
      this.getDataList()
    },
    allTransactionType() {
      this.handleAccountTypeList()
    }
  },
  computed: {
    inStr() {
      switch (this.type) {
        case '1':
          return 'ownerIncomeTypes'
        case '2':
          return 'driverIncomeTypes'
        case '3':
          return 'supplierIncomeTypes'
        case '4':
          return 'channelIncomeTypes'
        case '5':
          return 'manualTransferTypes'
        default:
          return 'ownerIncomeTypes'
      }
    },
    outStr() {
      switch (this.type) {
        case '1':
          return 'ownerOutcomeTypes'
        case '2':
          return 'driverOutcomeTypes'
        case '3':
          return 'supplierOutcomeTypes'
        case '4':
          return 'channelOutcomeTypes'
        case '5':
          return 'manualTransferTypes'
        default:
          return 'ownerOutcomeTypes'
      }
    },
    userType() {
      switch (this.type) {
        case '1':
          return '1'
        case '2':
          return '3'
        case '3':
          return '6'
        case '4':
          return '4'
        default:
          return ''
      }
    },
    tabName() {
      switch (this.type) {
        case '1':
          return '货主账户'
        case '2':
          return '司机/车队长账户'
        case '3':
          return '供应商账户'
        case '4':
          return '渠道账户'
        case '5':
          return '手动转账'
        default:
          return ''
      }
    },
    accountType() {
      switch (this.type) {
        case '1':
          return 'third'
        case '2':
          return 'fourth'
        case '3':
          return 'fifth'
        case '4':
          return 'sixth'
        default:
          return ''
      }
    },
    // tips
    tips() {
      return (val) => {
        if (this.type == '1') {
          switch (val) {
            case '23':
              return '与货主对账完成后，货主支付运费至平台，即货主还运费'
            case '16':
              return '卸货后支付司机部分现金运费'
            case '991':
              return '通过【后台 - 财务管理 - 手动转账】转账至货主账户'
            case '992':
              return '通过【后台 - 财务管理 - 手动转账】从货主账户转出'
            default:
              return '';
          }
        } else if (this.type == '2') {
          switch (val) {
            case '12':
              return '因收款人不是司机本人，系统自动将运费从司机账户转至车队长账户'
            case '16':
              return '卸货后支付司机部分现金运费'
            case '991':
              return '通过【后台 - 财务管理 - 手动转账】转账至货主账户'
            case '992':
              return '通过【后台 - 财务管理 - 手动转账】从货主账户转出'
            default:
              return '';
          }
        } else if (this.type == '3') {
          switch (val) {
            case '991':
              return '通过【后台 - 财务管理 - 手动转账】转账至供应商账户'
            case '992':
              return '通过【后台 - 财务管理 - 手动转账】从供应商账户转出'
            default:
              return '';
          }
        } else if (this.type == '4') {
          switch (val) {
            case '18':
              return '运单结算时平台还渠道油'
            default:
              return '';
          }
        }
      }
    },

  },
  methods: {
    /** 多选 **/
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    exportCheck() {
      //日期校验
      if (
        !this.exportData.startTime ||
        !checkLess3Month(this.exportData.startTime, this.exportData.endTime)
      ) {
        this.$message.warning("请按照“交易时间”筛选数据，时间范围最长为3个月");
        return false;
      }
      return true;
    },
    exportRow() {
      if (this.total >= 3000) {
        return this.$message.warning('数据量超过3000条，请按交易时间选后再导出')
      }
      // if (!this.exportCheck()) {
      //   return;
      // }
      this.exportData.baseId = this.currentBaseId
      this.exportData.tabName = this.tabName=='司机/车队长账户'?'司机-个人车队长账户':this.tabName
      this.$post(
        "/admin-center-server/transaction/flow/exportTransaction",
        this.exportData
      ).then((res) => {
        this.$message.success(
          "正在导出，稍后您可在【导出任务】中查看导出进度及导出文件"
        );
      });
    },
    exportPdf() {
      if (this.total >= 3000) {
        return this.$message.warning('数据量超过3000条，请按交易时间选后再导出')
      }
      // if (!this.exportCheck()) {
      //   return;
      // }
      this.exportData.baseId = this.currentBaseId
      this.exportData.tabName = this.tabName
      this.$post(
        "/admin-center-server/transaction/flow/exportPdfReceipt",
        this.exportData
      ).then((res) => {
        this.$message.success(
          "正在导出，稍后您可在【导出任务】中查看导出进度及导出文件"
        );
      });
    },
    /** 费用类型 **/
    costType(row) {
      if (row.type === "0") {
        return "充值";
      } else if (row.type === "1") {
        return "提现";
      } else if (row.type === "2") {
        return "解冻";
      } else if (row.type === "3") {
        return "冻结";
      } else if (row.type === "4") {
        return "运单结算";
      } else if (row.type === "5") {
        return "平台手续费";
      } else if (row.type === "6") {
        return "平台服务费";
      } else if (row.type === "7") {
        return "会员间交易";
      } else if (row.type === "8") {
        return "运费代收";
      } else if (row.type === "9") {
        return "运费转出";
      } else if (row.type === "10") {
        return "销户转账";
      } else if (row.type === "11") {
        return "油气结算";
      }
    },
    /** 交易类型 **/
    transactionType(row) {
      if (row.flag === "1") {
        return "收入";
      } else if (row.flag === "2") {
        return "支出";
      }
    },
    /** 用户类型 **/
    dTypeType(row) {
      if (row.dtype === "1") {
        return "货主";
      } else if (row.dtype === "2") {
        return "调度员";
      } else if (row.dtype === "3") {
        return "司机";
      } else if (row.dtype === "4") {
        return "渠道";
      }
    },
    /** 清空筛选 **/
    clearForm() {
      this.formInline = this.$options.data().formInline
      this.resetData()
      this.date = [];
      this.getDataList();
    },
    /** 按条件查询 **/
    onSubmit() {
      this.pageNumber = 1;
      this.getDataList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNumber = 1;
      this.getDataList();
    },
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.getDataList();
    },
    getDataList() {
      // console.log(this.type);
      this.formInline.userType ? '' : this.formInline.userType = this.userType
      let params = {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        tabName: this.tabName,
        ...this.formInline,
      };
      params.incomeTypes = Array.from(new Set([...this.formInline.incomeTypes, ...this.manualInList]))
      params.expenseTypes = Array.from(new Set([...this.formInline.expenseTypes, ...this.manualOutList]))
      if (this.date && this.date.length !== 0) {
        params.startTime = this.date[0];
        params.endTime = this.date[1];
      }
      params.baseId = this.currentBaseId
      this.$post("/admin-center-server/transaction/flow/list", params).then(
        (res) => {
          this.getSum()
          this.exportData = {
            ...this.formInline,
            startTime:
              this.date && this.date.length !== 0 ? this.date[0] : "",
            endTime: this.date && this.date.length !== 0 ? this.date[1] : "",
          };
          this.exportData.incomeTypes = Array.from(new Set([...this.formInline.incomeTypes, ...this.manualInList]))
          this.exportData.expenseTypes = Array.from(new Set([...this.formInline.expenseTypes, ...this.manualOutList]))
          this.tableData = res.list;
          this.total = Number(res.total);
        }
      );

    },
    handlePayTypeSelectorClick(type) {
      if (!type) return;
      this.payTypeList = []
      this.$post(
        "/admin-center-server/transaction/flow/getTransactionDetailType"
      ).then((res) => {
        this.payTypeList = res;
        this.$get(
          "/order-center-server/order/dict/findDictByType?dictType=subsidyDown"
        ).then((res) => {
          if (res && res.length > 0) {
            res.forEach(element => {
              this.payTypeList.push({
                comment: element.label,
                value: element.value,
              });
            });
          }
        });
      });
    },
    getSum() {
      this.formInline.userType ? '' : this.formInline.userType = this.userType
      let params = {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        baseId: this.currentBaseId,
        ...this.formInline,
      };
      params.incomeTypes = Array.from(new Set([...this.formInline.incomeTypes, ...this.manualInList]))
      params.expenseTypes = Array.from(new Set([...this.formInline.expenseTypes, ...this.manualOutList]))
      if (this.date && this.date.length !== 0) {
        params.startTime = this.date[0];
        params.endTime = this.date[1];
      }
      this.$post(
        "/admin-center-server/transaction/flow/getTransactionDetailSum",
        params
      ).then((res) => {
        this.expenseTotal=res.expenseSum?Number(res.expenseSum).toFixed(2) : null;
        this.incomeTotal=res.incomeSum?Number(res.incomeSum).toFixed(2) : null;
        this.dataSum =res.netIncome?Number(res.netIncome).toFixed(2) : null;
      });
    },
    openBankUrl(url) {
      window.open(url);
    },
    handleClick(item) {
      this.$router.push({
        path: 'dealDetail',
        query: {
          ...item,
          accountType: 1
        }
      })
    },
    // 获取下游补贴类型
    getSubsidyDownList() {
      this.$get('/order-center-server/order/dict/findDictByType?dictType=subsidyDown').then(res => {
        this.subsidyList = res
      })
    },
    // 根据账户类型处理收入支出类型列表
    handleAccountTypeList() {
      // 1货主 2司机/车队长 3供应商  4渠道账户 5手动转账 
      //获取类型支出收入类型
      this.inListSelect = this.allTransactionType[this.inStr].filter(el => el.method == '0')
      this.outListSelect = this.allTransactionType[this.outStr].filter(el => el.method == '0')
      // 手动转账类型
      this.manualTransferInList = this.allTransactionType[this.inStr].filter(el => el.method == '1')
      this.manualTransferList = this.allTransactionType[this.outStr].filter(el => el.method == '1')
    },
    //全选
    handelAllChange(val, type) {
      console.log('全选');
      if (type == 'in') {
        this.formInline.incomeTypes = val ? this.inListSelect.map(item => item.transactionType) : []
        // 补贴
        if (this.type == 2) {
          this.formInline.subTypeList = val ? this.subsidyList.map(item => item.value) : []
          this.isIndeterminate = false
        }
        this.manualInList = val ? this.manualTransferInList.map(item => item.transactionType) : []
        this.manualInAll = val
        this.manualInStatus = false
      } else if (type == 'out') {
        this.formInline.expenseTypes = val ? this.outListSelect.map(item => item.transactionType) : []
        // 补贴
        if (this.type == 1) {
          this.formInline.subTypeList = val ? this.subsidyList.map(item => item.value) : []
          this.isIndeterminate = false
        }
        this.manualOutList = val ? this.manualTransferList.map(item => item.transactionType) : []
        this.manualOutAll = val
        this.manualOutStatus = false
      } else if (type == 'subsidy') { //补贴
        this.isIndeterminate = false
        this.formInline.subTypeList = val ? this.subsidyList.map(item => item.value) : []
      } else if (type == 'manualOut') { //手动转账-支出
        this.manualOutStatus = false
        this.manualOutList = val ? this.manualTransferList.map(item => item.transactionType) : []
        this.outAllSelect = val && this.formInline.expenseTypes.length === this.outListSelect.length
      } else if (type == 'manualIn') { //手动转账-收入
        this.manualInStatus = false
        this.manualInList = val ? this.manualTransferInList.map(item => item.transactionType) : []
        this.inAllSelect = val && this.formInline.incomeTypes.length === this.inListSelect.length
      }

    },
    // 关联全选
    handSelectChange(val, type) {
      console.log('单项');
      if (type == 'in') {
        this.inAllSelect = (val.length === this.inListSelect.length && this.manualInAll)
      } else if (type == 'out') {
        this.outAllSelect = (val.length === this.outListSelect.length && this.manualOutAll)
      } else if (type == 'subsidy') {
        this.isIndeterminate = val.length > 0 && val.length < this.subsidyList.length
        if (val.length === this.subsidyList.length) {
          this.formInline.expenseTypes.push('28')
        } else {
          this.formInline.expenseTypes = this.formInline.expenseTypes.filter(item => item != '28')
        }
        // 全选状态更新
        this.outAllSelect = this.formInline.expenseTypes.length === this.outListSelect.length
      } else if (type == 'manualOut') {
        this.manualOutStatus = val.length > 0 && val.length < this.manualTransferList.length
        this.outAllSelect = false
        if (val.length == this.manualTransferList.length) {
          this.manualOutAll = true
          this.outAllSelect = this.formInline.expenseTypes.length === this.outListSelect.length
        } else {
          this.manualOutAll = false
        }
      } else if (type == 'manualIn') {
        this.manualInStatus = val.length > 0 && val.length < this.manualTransferInList.length
        this.inAllSelect = false
        if (val.length == this.manualTransferInList.length) {
          this.manualInAll = true
          this.inAllSelect = this.formInline.incomeTypes.length === this.inListSelect.length
        } else {
          this.manualInAll = false
        }
      }
    },
    // 部分数据重置
    resetData() {
      if (this.type == 5) {
        this.formInline.transactionMethod = 1
      }
      this.inAllSelect = false
      this.outAllSelect = false
      this.manualInAll = false
      this.manualOutAll = false
      this.manualInList = []
      this.manualOutList = []
      // 不确定状态
      this.manualInStatus = false
      this.manualOutStatus = false
      this.isIndeterminate = false
    }
  },
  created() {
    if (this.type == 5) {
      this.formInline.transactionMethod = 1
    }
    // 获取交易开始时间 结束时间 
    let { startTime, endTime } = this.$route.query
    if (startTime && endTime) {
      this.date = [startTime, endTime]
    }
    if (this.accountType == this.$route.query.accountType) {
      if(this.$route.query.expenseTypes!=20&&this.$route.query.expenseTypes!=23){
        this.showTips = true
      }
      // 获取交易类型 incomeTypes expenseTypes
      this.formInline.incomeTypes = this.$route.query.incomeTypes ? this.$route.query.incomeTypes.split(',') : []
      this.formInline.expenseTypes = this.$route.query.expenseTypes ? this.$route.query.expenseTypes.split(',') : []
      this.formInline.subTypeList = this.$route.query.subTypeList ? this.$route.query.subTypeList.split(',') : []
    }

  },
  activated() {
    if (this.baseId) {
      this.getDataList();
    }
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.userDeal {
  .select-box {
    /*height: 260px;*/
    margin-left: -15px;
    background-color: #ffffff;
    .select-info {
      // padding-top: 30px;
      // padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    overflow: hidden;

    .list-title {
      // display: flex;
      // flex-direction: row;
      // justify-content: space-between;
      // align-items: flex-start;
      .list-title-left {
        .inList,
        .outList {
          margin-top: 15px;
          display: flex;
          .checkText {
            font-weight: 700;
            font-size: 14px;
            color: #606266;
            margin-right: 3px;
          }
          .symbol {
            margin-right: 10px;
          }
        }
      }

      .list-title-right {
        margin-top: 15px;
        align-items: center;
        display: flex;
        justify-content: flex-start;
      }
    }

    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }

    .paging {
      margin-top: 10px;
      float: right;
    }
  }
}
.quick-filter {
  font-size: 14px;
  margin: -20px 0 0 30px;
  padding-bottom: 20px;
}
.el-icon-warning {
  //橘色
  color: #f6a018;
}
.el-icon-arrow-down {
  font-size: 10px;
  cursor: pointer;
  margin-left: 3px;
}
.mr30 {
  margin-right: 30px;
}
.popBox {
  .el-checkbox-group {
    padding-top: 10px;
    display: flex;
    flex-wrap: wrap;
  }
  .el-checkbox {
    width: 140px;
    margin-right: 5px;
    margin-bottom: 10px;
  }
  &.manual {
    .el-checkbox {
      width: 19%;
      margin-right: 5px;
      margin-bottom: 10px;
    }
  }
}
.text {
  color: #e32c4d;
  font-size: 14px;
  text-align: right;
  flex: 1;
  margin-right: 10px;
}

</style>
<style lang="scss">
.prompt {
  max-width: 1000px !important;
  .tipContent{
    >div{
      margin-bottom: 5px;
      font-size: 14px;
      // css黑体
      font-family: "黑体", sans-serif;
      >span{
        font-weight: bold;
      }
    }
  }
}
</style>
