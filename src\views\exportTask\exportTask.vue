<template>
  <div class="app-container">
    <div class="list">
      <div class="line">
        <div>系统只保存近7天导出的文件，请尽快下载</div>
        <div>
          <el-button @click="clear">清除所有记录</el-button>
        </div>
      </div>
      <el-table
        border
        :data="data">
        <el-table-column prop="fileName" label="文件名"></el-table-column>
        <el-table-column prop="fileType" label="文件类型"></el-table-column>
        <el-table-column prop="createDate" label="导出时间"></el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <div v-if="scope.row.status === '0'">导出成功</div>
            <div v-if="scope.row.status === '1'">导出失败</div>
            <div v-if="scope.row.status === '2'">导出中</div>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="失败原因"></el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button :disabled="scope.row.status !== '0'" type="text" @click="download(scope.row.fileUrl)">下载文件</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagin">
        <el-pagination
          :total="total"
          :current-page.sync="page.pageNumber"
          :page-size="page.pageSize"
          @current-change="getList"
          ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      page: {
        pageSize: 10,
        pageNumber: 1
      },
      total: 0,
      data: []
    }
  },
  methods: {
    getList() {
      let params = {
        ...this.page
      }
      this.$post('/admin-center-server/export/task/exportList', params)
        .then(res => {
          this.data = res.list
          this.total = Number(res.total)
        })
    },
    download(url) {
      if (typeof url === 'string' && url !== '') {
        location.href = url
      }
    },
    clear() {
      this.$confirm('确定要清除所有记录吗')
        .then(() => {
          this.$post('/admin-center-server/export/task/exportDel')
            .then(() => {
              this.$message.success('操作成功')
              this.getList()
            })
        })
    }
  },
  activated() {
    this.getList()
  }
}
</script>

<style scoped lang="scss">
  .line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    color: #f6a018;
  }
  .list {
    padding: 30px 0;
    background: #fff;
  }
  .list {
    margin-top: 20px;
    padding: 20px 10px;
  }
  .pagin {
    margin-top: 10px;
    text-align: right;
  }
</style>