<template>
  <div>
    <div class='title'>司机用户认证</div>
    <el-form 
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="170px"
      size="medium"
      class="demo-ruleForm">
      <el-form-item
        label="认证类型"
        prop="authenticateType">
        <el-select
          v-model="ruleForm.authenticateType"
          placeholder="请选择"
          disabled>
          <el-option 
            v-for="item in authenticateTypeData"
            :key="item.value"
            :label="item.name"
            :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <!-- 姓名 -->
      <el-form-item
        :label="difference[0].label"
        prop="name">
        <el-input
          v-model="ruleForm.name"
          :placeholder="difference[0].placeholder"></el-input>
      </el-form-item>
      <el-form-item
        label="身份证号"
        prop="idCard">
        <el-input v-model="ruleForm.idCard"
          maxlength="18"
          placeholder="18位有效数字"></el-input>
      </el-form-item>
      <el-form-item label="身份证有效期" prop="idCardDate">
        <el-checkbox
          v-model="isIdLong">长期</el-checkbox>
        <el-date-picker v-model="idDate"
          type="date"
          :disabled="isIdLong"
          placeholder="请选择截止日期"
          style="width: 100%;"
          value-format="yyyy-MM-dd"> </el-date-picker>
        <div class="expire-text">
          <template v-if="idDateStatus === -1">（已过期）</template>
          <template v-else-if="idDateStatus !== -2">（{{ idDateStatus }}天后到期）</template>
        </div>
      </el-form-item>
      <el-form-item
        label="手机号"
        prop="tel">
        <el-input oninput="value=value.replace(/[^\d]/g,'')"
          maxlength="11"
          v-model="ruleForm.tel"
          placeholder="可作为登录名使用，支持全网号段"
          :disabled="true"></el-input>
      </el-form-item>
      <el-form-item
        :label="difference[1].label"
        prop="net">
        <imageUploader
          type="upload"
          :defaultUrl="data.idCardImage"
          @change="url => ruleForm.idCardImage = url"></imageUploader>
      </el-form-item>
      <el-form-item
        label="司机身份证照片国徽面"
        prop="net"
        v-if="ruleForm.authenticateType=='driver' || ruleForm.authenticateType === 'driverBoss'">
        <imageUploader
          type="upload"
          :defaultUrl="data.idCardBackImage"
          @change="url => ruleForm.idCardBackImage = url"></imageUploader>
      </el-form-item>
      <el-form-item
        label="从业资格证照片"
        v-if="ruleForm.authenticateType=='driver' || ruleForm.authenticateType === 'driverBoss'">
        <imageUploader
          type="upload"
          :defaultUrl="data.employmentCertImage"
          @change="url => {ruleForm.employmentCertImage = url; $forceUpdate()}"></imageUploader>
      </el-form-item>
      <!-- v-if="ruleForm.authenticateType=='driver' || ruleForm.authenticateType === 'driverBoss'">-->
      <el-form-item
        label="从业资格证号"
        :rules="[{ required: ruleForm.employmentCertImage, message: '请输入从业资格证号', trigger: 'blur' },]"
         prop="employmentCert">
        <el-input v-model="ruleForm.employmentCert"
                  placeholder="请输入从业资格证号"></el-input>
      </el-form-item>
      <!-- v-if="ruleForm.authenticateType=='driver' || ruleForm.authenticateType === 'driverBoss'" -->
      <el-form-item  label="从业资格证有效期" prop="employmentCertExpireDate"
        :rules="[{ required: ruleForm.employmentCertImage, message: '请输入从业资格证有效期', trigger: 'blur' },]"
      >
        <el-date-picker v-model="ruleForm.employmentCertExpireDate" value-format="yyyy-MM-dd" style="width: 100%;"></el-date-picker>
        <div class="expire-text">
          <template v-if="employmentCertExpireDateStatus === -1">（已过期）</template>
          <template v-else-if="employmentCertExpireDateStatus !== -2">（{{ employmentCertExpireDateStatus }}天后到期）</template>
        </div>
      </el-form-item>
      <el-form-item
        :label="difference[2].label"
        prop="net">
        <imageUploader
          type="upload"
          :defaultUrl="data.drivingLicencesImage"
          @change="url => ruleForm.drivingLicencesImage = url"></imageUploader>
      </el-form-item>
      <el-form-item label="驾驶证号" prop="drivingLicencesNumber">
        <el-input v-model="ruleForm.drivingLicencesNumber"
                  maxlength="18"
                  placeholder="18位有效数字"></el-input>
      </el-form-item>
      <el-form-item
                    :label="difference[3].label"
                    prop="date">
        <!-- <el-date-picker v-if="isDriverLisenceLong" type="date" v-model="drivingLicensesExpireStartDate" value-format="yyyy-MM-dd" placeholder="起始日期"></el-date-picker>
        <el-date-picker v-else v-model="drivingLicensesExpireDate" :disabled="isDriverLisenceLong" type="daterange" value-format="yyyy-MM-dd"></el-date-picker>
        <el-checkbox v-model="isDriverLisenceLong" class="long">长期</el-checkbox> -->
          <div v-if="isDriverLisenceLong">
            <el-date-picker  type="date" v-model="drivingLicensesExpireStartDate" value-format="yyyy-MM-dd" style="width:280px"></el-date-picker>
            <el-checkbox v-model="isDriverLisenceLong" class="long" @change="changeFn">长期</el-checkbox>
          </div>
          <!-- v-if="!isDriverLisenceLong && drivingLicensesExpireDate" -->
          <div class="driving_licenses_expireDate" v-else>
            <el-date-picker style="width:137px" v-model="drivingLicensesExpireDate[0]" :disabled="isDriverLisenceLong" type="date" value-format="yyyy-MM-dd" :picker-options="pickerOptionsStart"></el-date-picker>
            <span style="color:#888;width:6px">-</span>
            <el-date-picker style="width:137px" v-model="drivingLicensesExpireDate[1]" :disabled="isDriverLisenceLong" type="date" value-format="yyyy-MM-dd" :picker-options="pickerOptionsEnd"></el-date-picker>
            <el-checkbox v-model="isDriverLisenceLong" class="long" @change="changeFn">长期</el-checkbox>
          </div>
        <div class="expire-text" v-if="!isDriverLisenceLong">
          <template v-if="drivingLicensesExpireDateStatus === -1">（已过期）</template>
          <template v-else-if="drivingLicensesExpireDateStatus !== -2">（{{ drivingLicensesExpireDateStatus }}天后到期）</template>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button @click="submitForm('ruleForm')" :loading="!addFlag" type="primary">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { checkExpire } from '@/utils/date'
const forUpdateInfo = '/admin-center-server/commonUser/forUpdateInfo'//回显
const updateUser = '/admin-center-server/commonUser/updateUser'//编辑-添加
export default {
  data () {
    return {
      isDriverLisenceLong: false,
      id: '',
      difference: [],
      urlType: '',
      authenticateTypeData: [
        {
          name: '司机',
          value: 'driver'
        },
        {
          name: '司机（个人车队长）',
          value: 'driverBoss'
        },
        {
          name: '个人车队长',
          value: 'singleBoss'
        }
      ],
      addFlag: true,
      ruleForm: {
        authenticateType: '',
        name: '',
        tel: '',
        code: '',
        idCard: '',
        quasidriving: '',
        legalPersonName: '',
        legalPersonCode: '',
        drivingLicencesNumber: '',
        employmentCert: '',
        employmentCertWarnMemo: '',
        employmentCertExpireDate: '',
        companyAddress: ''
      },
      drivingLicensesExpireStartDate: null,
      drivingLicensesExpireDate: [],
      rules: {
        authenticateType: [{ required: true, message: '请选择认证状态', trigger: 'change' }],
        quasidriving: [{ required: true, message: '请选择准驾车型', trigger: 'change' }],
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { min: 1, max: 34, message: '长度在 1 到 34 个字符', trigger: 'change' }
        ],
        idCard: [{ required: true, message: '请输入身份证号', trigger: 'blur' },
        { required: true, trigger: 'blur', validator: this.isCardNo }],
        idCardDate: [
          { required: true,
            validator: (rule, value, cb) => {
              if (this.isIdLong || this.idDate) {
                return cb()
              } else {
                return cb('请选择身份证有效期')
              }
            }
          }
        ],
        tel: [
          { required: true, message: '请输入客户电话', trigger: 'blur' },
          { min: 8, max: 11, message: '手机号格式不正确', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: this.telReg }
        ],
        code: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: this.socialCreditCode },
        ],
        drivingLicencesNumber: [{ required: true, message: '请输入驾驶证号', trigger: 'blur' },],
        date: [
          { required: true,
            validator: (rule, value, cb) => {
              if (this.isDriverLisenceLong && !this.drivingLicensesExpireStartDate) {
                return cb('驾驶证有效期不能为空')
              } else if (!this.isDriverLisenceLong && (!this.drivingLicensesExpireDate || this.drivingLicensesExpireDate.length<2 || (this.drivingLicensesExpireDate.length == 2 && (!this.drivingLicensesExpireDate[1] || !this.drivingLicensesExpireDate[0])))) {
                return cb('驾驶证有效期不能为空')
              } else {
                return cb()
              }
            }
          }
        ],
        legalPersonName: [
          { required: true, message: '请输入法人名称', trigger: 'blur' },
          { min: 1, max: 34, message: '长度在 1 到 34 个字符', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: this.chineseReg }],
        legalPersonCode: [{ required: true, message: '请输入法人身份证号', trigger: 'blur' },
        { required: true, trigger: 'blur', validator: this.isCardNo }]
      },
      isIdLong: false,
      idDate: '',
      data: {},
      pickerOptions: {
        disabledDate(now) {
          return now.getTime() <= Date.now()
        }
      },
      //从业资格证异常
      isCertAbnormal: false,
      idDateStatus: -2,
      employmentCertExpireDateStatus: -2,
      drivingLicensesExpireDateStatus: -2,
      // 开始结束日期限制
      pickerOptionsStart: {
        disabledDate: time => {
          if (this.drivingLicensesExpireDate[1]) {
            return (
              time.getTime() >= new Date(this.drivingLicensesExpireDate[1]).getTime()
            );
          }
        }
      },
      // 结束日期限制
      pickerOptionsEnd: {
        disabledDate: time => {
          if (this.drivingLicensesExpireDate[0]) {
            return (
              time.getTime() <= new Date(this.drivingLicensesExpireDate[0]).getTime()-8.64e7
            );
          }
        }
      },
    }
  },
  methods: {
    changeFn(){
      this.$refs.ruleForm.clearValidate('date');
    },
    authenticateTypefn (value) {
      if (value == 'driver') {
        this.difference = [
          {
            label: '司机名称',
            placeholder: '司机名称（如含有特殊字符等不符合规范）'
          },
          {
            label: '司机身份证照片人像面'
          },
          {
            label: '司机驾驶证照片'
          },
          {
            label: '驾驶证有效期'
          }
        ]
      } else if (value == 'driverBoss') {
        this.difference = [
          {
            label: '司机名称/车队长',
            placeholder: '司机名称（如含有特殊字符等不符合规范）'
          },
          {
            label: '司机身份证照片人像面'
          },
          {
            label: '驾驶证照片'
          },
          {
            label: '驾驶证有效期'
          }
        ]
      } else if (value == 'singleBoss') {
        this.difference = [
          {
            label: '个人车队长名称',
            placeholder: '个人车队长姓名全称（如含有特殊字符等不符合规范）'
          },
          {
            label: '身份证照片'
          },
          {
            label: ''
          },
          {
            label: ''
          }
        ]
      }
    },
    submitForm (formName) {
      var that = this
      if (that.difference[0].label == '司机名称') {
        var reg = /^[\u4E00-\u9FA5\uf900-\ufa2d·s]{2,34}$/
        if (!reg.test(that.ruleForm.name)) {
          that.$message.error('司机名称最多可输入34个中文和.')
          return false
        }
      }
      this.$refs[formName].validate(valid => {
        var v = that.ruleForm,
          data = {}
        if (!valid) return
        if (!this.addFlag) return
        this.addFlag = false
        var authenticateTypeNum = ''
        data = {
          userType: that.urlType,
          companyName: v.name,
          mobile: v.tel,
          organizationCode: v.code,
          id: that.id
        }
        if (v.idCard) {
          data.idCard = v.idCard
        }

        //个人车队长singleBoss、司机个人车队长driverBoss
        if (v.authenticateType == 'singleBoss') {
          if (!v.idCardImage) {
            this.addFlag = true
            this.$message.error('请上传身份证照片');
            return false
          }
          data.idCardImage = v.idCardImage
          authenticateTypeNum = 3
        } else if (v.authenticateType == 'driverBoss') {
          data.drivenVehicleModel = v.quasidriving
          if (!v.idCardImage) {
            this.addFlag = true
            this.$message.error('请上传身份证照片');
            return false
          }
          if (!v.drivingLicencesImage) {
            this.addFlag = true
            this.$message.error('请上传驾驶证照片');
            return false
          }
          data.idCardImage = v.idCardImage
          data.drivingLicencesImage = v.drivingLicencesImage
          authenticateTypeNum = 2
          data.idCardBackImage = this.ruleForm.idCardBackImage
          if (this.isIdLong) {
            data.idCardExpireDate = '9999-12-31'
          } else {
            data.idCardExpireDate = this.idDate
          }
          data.employmentCert = this.ruleForm.employmentCert
          data.employmentCertImage = this.ruleForm.employmentCertImage
          data.drivingLicencesNumber = this.ruleForm.drivingLicencesNumber
        } else if (v.authenticateType == 'driver') {
          data.drivenVehicleModel = v.quasidriving
          if (!v.idCardImage) {
            this.addFlag = true
            this.$message.error('请上传司机身份证照片');
            return false
          }
          if (!v.drivingLicencesImage) {
            this.addFlag = true
            this.$message.error('请上传司机驾驶证照片');
            return false
          }
          authenticateTypeNum = 1
          data.idCardImage = v.idCardImage
          data.idCardBackImage = this.ruleForm.idCardBackImage
          if (this.isIdLong) {
            data.idCardExpireDate = '9999-12-31'
          } else {
            data.idCardExpireDate = this.idDate
          }
          data.employmentCert = this.ruleForm.employmentCert
          data.employmentCertImage = this.ruleForm.employmentCertImage
          
          data.drivingLicencesNumber = this.ruleForm.drivingLicencesNumber
          data.drivingLicencesImage = v.drivingLicencesImage
        }

        //从业资格证
        if (v.authenticateType === 'driver' || v.authenticateType === 'driverBoss') {
          if (this.isCertAbnormal) {
            data.employmentCertWarnFlag = 1
            data.employmentCertWarnMemo = v.employmentCertWarnMemo
          } else {
            data.employmentCertWarnFlag = 0
          }
          data.employmentCertExpireDate = v.employmentCertExpireDate
        }

        if (that.id) {
          data.driverType = authenticateTypeNum
        }

        {
          if (this.isDriverLisenceLong) {
            data.drivingLicensesStartDate = this.drivingLicensesExpireStartDate
            data.drivingLicensesExpireDate = '9999-12-31 00:00:00'
          } else {
            data.drivingLicensesStartDate = this.drivingLicensesExpireDate && this.drivingLicensesExpireDate[0]
            data.drivingLicensesExpireDate = this.drivingLicensesExpireDate && this.drivingLicensesExpireDate[1]
          }
        }

        if (this.id) {

          this.$http.post(updateUser, data).then(res => {
            var that = this
            if (res.data.code == '200') {
              this.$message({
                message: res.data.message,
                type: 'success'
              });

              setTimeout(function () {
                that.$router.go(-1);
              }, 3000);


            } else {
              this.addFlag = true
              this.$message.error(res.data.message);
            }
          })
        }
      })
    },
  },
  activated () {
    this.addFlag = true
    this.urlType = this.$route.query.urlType
    this.id = this.$route.query.id
    this.driverType = this.$route.query.driverType
    if (this.driverType) {
      this.authenticateTypefn(this.driverType)
      // this.ruleForm.authenticateType = this.driverType
    }

    //密码为6个* ,修改时候不传
    let url = this.urlType == '3' ? (this.driverType == 'driver' ? forUpdateInfo + `?id=${this.id}` + '&type=3' : forUpdateInfo + `?id=${this.id}` + '&type=4') : forUpdateInfo + `?id=${this.id}`
    this.$http.get(url).then(res => {
      var data = this.data = res.data.data
      // this.ruleForm = data
      this.ruleForm.authenticateType = this.driverType
      var v = this.ruleForm
      //v.name = data.companyName
      v.name = data.name
      v.tel = data.mobile
      v.pwd = '******'
      v.code = data.organizationCode
      v.idCard = data.idCard
      v.legalPersonName = data.corporationName
      v.legalPersonCode = data.corporationIdCard
      v.quasidriving = data.drivenVehicleModel

      v.employmentCert = data.employmentCert
      v.employmentCertImage = data.employmentCertImage
      v.employmentCertExpireDate = data.employmentCertExpireDate
      //从业资格证异常相关
      this.isCertAbnormal = data.employmentCertWarnFlag === '1' ? true : false

      v.drivingLicencesNumber = data.drivingLicencesNumber
      if (data.idCardExpireDate === '9999-12-31') {
        this.isIdLong = true
      } else {
        this.isIdLong = false
        this.idDate = data.idCardExpireDate
      }

      this.ruleForm.idCardImage = data.idCardImage
      if (data.idCardBackImage != null && data.idCardBackImage != '') {
        this.ruleForm.idCardBackImage = data.idCardBackImage
      }

      this.ruleForm.drivingLicencesImage = data.drivingLicencesImage
      {
        if (data.drivingLicensesExpireDate === '9999-12-31') {
          this.isDriverLisenceLong = true
          this.drivingLicensesExpireStartDate = data.drivingLicensesStartDate
        } else {
          this.isDriverLisenceLong = false
          if(data.drivingLicensesStartDate && data.drivingLicensesExpireDate) {
            this.drivingLicensesExpireDate = [data.drivingLicensesStartDate, data.drivingLicensesExpireDate]
          } else {
            this.drivingLicensesExpireDate = ['', '']
          }
        }
      }
    })

    this.$watch('idDate', {
      deep: true,
      handler(v) {
        checkExpire(v)
          .then(res => this.idDateStatus = res)
      }
    })
    this.$watch('ruleForm.employmentCertExpireDate', {
      deep: true,
      handler(v) {
        checkExpire(v)
          .then(res => this.employmentCertExpireDateStatus = res)
      }
    })
    this.$watch('drivingLicensesExpireDate', {
      deep: true,
      handler(v) {
        checkExpire(v[1])
          .then(res => this.drivingLicensesExpireDateStatus = res)
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.demo-ruleForm {
  width: 560px;
  margin-top: 20px;
}
.upload-box {
  width: 100%;
  height: 100%;
  /*border: 1px solid #00cb8a;*/
  position: relative;

  .icon-XZ {
    width: 92px;
    height: 92px;
    margin: 0 auto;
    background: url("./images/xiazai.png") no-repeat;
    background-size: 100% 100%;
  }

  .icon-word {
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 10px;
    position: absolute;
    bottom: 25px;
    left: 0px;
    color: #cccccc;
  }
}
.title {
  font-size: 16px;
  margin: 20px 0 0 20px;
  color: #1898ff;
}
.big-img {
  float: right;
  margin-right: -100px;
  margin-top: -30px;
}
.abnormal {
  margin-top: 15px;
  .el-checkbox {
    margin-right: 10px;
  }
}
.expire-text {
  color: #D92929;
}
.long {
  // display: block;
  width: 100px;
  margin-left: 10px;
}
.driving_licenses_expireDate {
  display: flex;
  .el-date-picker {
    width: 140px;
  }
}
</style>

