<template>
  <div>
    <div class="list-box">
      <div class="main-box">
        <div class="list-info">
          <el-form ref="form" label-width="140px" :inline="true" size="mini">
            <el-form-item label="运单号：">
              <el-input v-model="search.orderItemSn"></el-input>
            </el-form-item>
            <el-form-item label="收款人姓名：">
              <el-input v-model="search.payeeName"></el-input>
            </el-form-item>
            <el-form-item label="收款人手机号：">
              <el-input v-model="search.payeeMobile"></el-input>
            </el-form-item>
            <el-form-item label="车牌号：">
              <el-input v-model="search.carNumber"></el-input>
            </el-form-item>
            <el-form-item label="油卡卡号：">
              <el-input v-model="search.oilAccount"></el-input>
            </el-form-item>
            <el-form-item label="货主名称：">
              <el-input v-model="search.businessName"></el-input>
            </el-form-item>
            <el-form-item label="油分配状态:">
              <el-select
                v-model="search.status"
                placeholder="未分配"
                style="width: 178px"
                @change="doSearch"
              >
                <el-option label="未分配" value="0"></el-option>
                <el-option label="已分配" value="1"></el-option>
                <el-option label="全部" value=""></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="是否作废:">
              <el-select
                v-model="search.cancelFlag"
                placeholder="请选择"
                style="width: 178px"
              >
                <el-option label="否" value="0"></el-option>
                <el-option label="是" value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="doSearch">查询</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="list-box">
      <el-table
        v-loading="loading"
        :data="data"
        style="width: 100%"
        :max-height="tableHeight"
        ref="table"
        :cell-style="{ 'text-align': 'center', border: '0.5px solid #EAF0FB' }"
        :header-cell-style="{
          'text-align': 'center',
          border: '0.3px solid #EAF0FB',
          'background-color': '#F5F6F9',
          height: '60px',
        }"
      >
        <el-table-column label="支付时间" prop="payDate"></el-table-column>
        <el-table-column label="货主名称" prop="businessName"></el-table-column>
        <el-table-column label="合作平台主体" prop="baseName"></el-table-column>
        <el-table-column label="货主所在地" prop="areaName"></el-table-column>
        <el-table-column label="运单号" prop="orderItemSn" width="180">
          <template slot-scope="scope">
            <el-button @click="toDetail(scope.row.orderItemId)" type="text">{{ scope.row.orderItemSn }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="司机运费" prop="driverAmount"></el-table-column>
        <el-table-column label="其中油气" prop="money"></el-table-column>
        <el-table-column label="收款人姓名" prop="name"></el-table-column>
        <el-table-column label="收款人手机号" prop="mobile"></el-table-column>
        <el-table-column label="收款人身份证号" prop="idCard"></el-table-column>
        <el-table-column label="车牌号" prop="carNumber"></el-table-column>
        <el-table-column label="能源类型" prop="energyType">
          <template scope="scope">
            <div style="color: red">{{scope.row.energyType}}</div>
          </template>
        </el-table-column>
        <el-table-column label="司机油气卡" prop="oilAccount"></el-table-column>
        <el-table-column label="油气分配金额" prop="allocationOilMoney"></el-table-column>
        <el-table-column label="其中积分分配金额" prop="integralMoney"></el-table-column>
        <el-table-column label="油气分配外部单号" prop="oilSequenceCode"></el-table-column>
        <el-table-column label="油气分配状态" prop="status"
          ><template slot-scope="scope"
            ><div :class="getAllocationTextClass(scope.row.status)">
              {{ scope.row.status == 1 ? "已分配" : "未分配" }}
            </div></template
          >
        </el-table-column>
        <el-table-column label="是否作废" prop="cancelFlag">
          <template slot-scope="scope">
            <span>{{scope.row.cancelFlag == 1 ? '是' : '否'}}</span>
          </template>
        </el-table-column>
        <el-table-column label="作废原因" prop="cancelReason"></el-table-column>

        <el-table-column label="操作" fixed="right" width="180px">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status == 0 && scope.row.cancelFlag != 1"
              type="text"
              @click="sign(scope.row)"
              >标记分配
            </el-button>
            <el-button  type="text" @click="unSignFn(scope.row)" v-if="scope.row.status == '1'">取消标记分配</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 10px; text-align: right">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="getList"
          :current-page.sync="page.pageNumber"
          :page-sizes="[10, 20, 40, 60, 80, 100]"
          :page-size="page.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
        ></el-pagination>
      </div>
    </div>

    <el-dialog
      :close-on-click-modal="false"
      title="标记分配"
      :visible.sync="isAllocationShow"
      :before-close="handleClose"
      destroy-on-close
      width="600px"
    >
      <div style="color: red; margin: 0 0 20px 20px">
        请认真核实以下信息，一旦确定则无法修改
      </div>
      <el-form
        ref="modelForm"
        label-width="130px"
        :model="form"
        :rules="rules"
        label-position="left"
        style="margin-left: 20px"
      >
        <el-row class="global-div-search">
          <el-col :span="24">
            <el-form-item label="收款人姓名" prop="name">
              {{ form.name || "-" }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="收款人手机号" prop="name">
              {{ form.mobile || "-" }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="收款人身份证号" prop="name">
              {{ form.idCard || "-" }}
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="卡号" prop="oilAccount">
              <el-input
                @input="
                  () => {
                    form.oilAccount = form.oilAccount.replace(
                      /^0{1}|[^\d]/g,
                      ''
                    );
                  }
                "
                v-model="form.oilAccount"
                clearable
                placeholder="请输入卡号"
                :disabled="form.isOliAccount"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="油气分配金额" prop="money">
              {{
                form.allocationOilMoney
              }}
              元
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="其中积分分配金额" prop="integralMoney">
              <el-input
                v-model="form.integralMoney"
                clearable
                placeholder="请输入金额"
                @input="
                  (value) => {
                    form.integralMoney = (
                      value.match(/\d+\.?\d{0,2}/) || []
                    ).join('');
                  }
                "
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label-width="10px">元</el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="油气分配外部单号" prop="oilSequenceCode">
              <el-input
                v-model="form.oilSequenceCode"
                clearable
                placeholder="可填写中石化或中石油的油气分配订单号"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label-width="10px"></el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label-width="140px"
              style="font-weight: 800"
              required
              label="上传分配油气凭证"
            ></el-form-item>
          </el-col>
          <el-col :span="16">
            <el-upload
              class="upload-demo"
              action
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :file-list="fileList"
              list-type="picture"
              :http-request="ossUpload"
              drag
              multiple
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <div class="el-upload__tip" slot="tip">只能上传图片</div>
            </el-upload>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isAllocationShow = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </el-dialog>
    <!-- <el-image-viewer v-if="showImageUrl" :on-close="closeViewer" :url-list="[showImageUrl]"></el-image-viewer> -->
  </div>
</template>

<script>
import { upload } from "@/utils/file";
import ElImageViewer from "element-ui/packages/image/src/image-viewer.vue";

export default {
  components: {
    ElImageViewer,
  },
  data() {
    return {
      activeName: "first",
      fileList: [],
      showImageUrl: "",
      search: {
        status: '0',
        cancelFlag: '0'
      },
      isSearch: false,
      date: null,
      page: {
        pageNumber: 1,
        pageSize: 10,
      },
      data: [],
      total: 0,
      isAllocationShow: false,
      cardNumber: "",
      loading: true,
      tableHeight: null,
      form: {},
      rules: {
        name: [
          {
            required: false,
            message: "",
            trigger: "blur",
          },
        ],
        oilAccount: [
          {
            required: true,
            message: "请输入油卡卡号",
            trigger: "blur",
          },
        ],
        money: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
        ],
        integralMoney: [
          {
            required: false,
            validator: (rule, value, callback) => {
              if (!value) {
                callback();
              } else if (
                +value >=
                (+this.form.ifChannelRecharge == 1
                  ? +this.form.allocationOilMoney
                  : +this.form.money)
              ) {
                callback(
                  new Error(
                    `积分分配金额不能大于${
                      this.form.ifChannelRecharge == 1
                        ? this.form.allocationOilMoney
                        : this.form.money
                    }`
                  )
                );
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
    };
  },
  activated() {
    this.getList();
    this.tableHeight =
      window.innerHeight - this.$refs.table.$el.offsetTop - 170;
  },
  methods: {
    handleClick() {},
    getList() {
      this.loading = true;
      let params = { ...this.page };
      if (this.isSearch) {
        Object.assign(params, this.search);
        if (this.date !== null) {
          params.startDate = this.date[0];
          params.endDate = this.date[1];
        }
      } else {
        params.status = this.search.status
        params.cancelFlag = this.search.cancelFlag
      }
      this.$post(
        "/admin-center-server/oilRecharge/oilDriverRechargeList",
        params
      ).then((res) => {
        this.data = res.list;
        this.total = Number(res.total);
        this.loading = false;
      });
    },
    doSearch() {
      this.page.pageNumber = 1;
      this.isSearch = true;
      this.getList();
    },
    reset() {
      this.search = {status: '0', cancelFlag: '0'};
      this.date = null;
      this.page.pageNumber = 1;
      this.isSearch = false;
      this.getList();
    },
    handleSizeChange(v) {
      this.page.pageSize = v;
      this.getList();
    },
    sign(row) {
      this.form = JSON.parse(JSON.stringify(row));
      if (row.oilAccount) this.form.isOliAccount = true;
      this.isAllocationShow = true;
    },
    unSignFn(row){
      const h = this.$createElement
      this.$confirm('提示',{
        title:'提示',
        message:h('div',[h('p', '请确保分油系统中已将油收回，取消标记分配后，请务必将运单中的油费退回，退回方式为：'), h('p', '1、【后台 - 运单详情】操作“退还货主油费”，油费退还后，运单会自动取消'),h('p', '2、【运营端 - 运单详情】操作“取消运单”，如果运单存在取消标记分配的油费记录，系统会自动将油费退还货主并取消运单”')])
      })
      .then(() => {
        this.$post('/admin-center-server/oilRecharge/allocationCancel', {
          id:row.id
        })
          .then(() => {
            // this.$message.success(`操作成功`)
            this.getList()
          })
      })
    },
    submit() {
      const { id, orderItemId, integralMoney, oilAccount, orderItemSn, oilSequenceCode } = this.form;
      if (oilAccount == null || oilAccount.trim() == "") {
        this.$message({
          type: "warning",
          message: "必填选项不能为空，请填写完整的信息"
        });
        return;
      }
      if (this.fileList.length > 0) {
        let allocationOilProofList = this.fileList[0].url;
        for (let i = 1; i < this.fileList.length; i++) {
          allocationOilProofList += "," + this.fileList[i].url;
        }
        this.$http
          .post("/admin-center-server/oilRecharge/uploadAllocationOilProof", {
            orderItemid: Number(orderItemId),
            allocationOilProofList: allocationOilProofList,
          })
          .then((res) => {
            if (res.data.code == 200) {
              this.$refs.modelForm.validate((valid) => {
                if (valid) {
                  this.$http
                    .post("/admin-center-server/oilRecharge/allocation", {
                      id,
                      integralMoney,
                      oilAccount,
                      orderItemSn,
                      oilSequenceCode
                    })
                    .then((res) => {
                      if (res.data.code == 200) {
                        this.$message.success(res.data.message);
                      } else {
                        this.$message.error(res.data.message);
                      }
                      this.isAllocationShow = false;
                      this.fileList = []
                      this.getList();
                    });
                }
              });
            } else {
              this.$message.error(res.data.message);
            }
          });
      } else {
        this.$message.error("请上传分油凭证");
      }
    },
    handleClose(done) {
      this.fileList = []
      done();
    },
    handlePreview(file) {
      let selectFile = this.fileList.filter((item) => {
        return item.name == file.name; 
      });
      if (selectFile.length > 0) {
        this.showImageUrl = selectFile[0].url;
      }
    },
    closeViewer() {
      this.showImageUrl = "";
    },
    handleRemove(file, list) {
      this.fileList = this.fileList.filter((item) => {
        return item.name != file.name;
      });
    },
    ossUpload(param, type) {
      upload(param.file).then((res) => {
        this.fileList.push({ name: param.file.name, url: res.url });
        this.$message.success("上传成功");
      });
    },
    toDetail(orderItemId) {
      this.$router.push('/transport/transportListDetail?orderItemId=' + orderItemId)
    },

    getAllocationTextClass(val) {
      if (val == "1") {
        return "allocationedText"
      } 
      return "unAllocationText"
    }
  },
};
</script>

<style lang="scss" scoped>
.list-box {
  background-color: #ffffff;
  margin: 0px;
  padding: 10px;

  .list-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    div {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
    }
  }

  .list-main {
    width: 100%;
    //   border: 1px solid #cccccc;
    margin-top: 10px;
  }

  .releaseMessage {
    margin-right: 20px;
  }

  .pagination {
    text-align: right;
    margin-top: 10px;
  }
}
.global-div-search {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  /* font-size: 14px; */
  /* color: #555; */
  position: relative;
}
.upload-demo {
  float: left;
  width: 100%;
  margin-left: -5px;
}
.upload-demo ::v-deep .el-upload-list--picture .el-upload-list__item {
  height: 41px;
}
.upload-demo
  ::v-deep
  .el-upload-list--picture
  .el-upload-list__item.is-success
  .el-upload-list__item-name {
  line-height: 25px;
}
.upload-demo ::v-deep .el-upload-list--picture .el-upload-list__item-name {
  margin-top: 0px;
}
.upload-demo ::v-deep .el-upload-list--picture .el-upload-list__item-name i {
  font-size: 21px;
}
.upload-demo ::v-deep .el-upload-list--picture .el-upload-list__item-thumbnail {
  height: 21px;
}

.unAllocationText {
  color: red;
}
.allocationedText {
  color: black;
}
</style>