<template>
  <div class="app-container carsList">
    <el-tabs v-model="tab" @tab-click="handleTabClick" type="card">
      <el-tab-pane label="合同归档" name="1"></el-tab-pane>
      <el-tab-pane label="合同协议设置" name="2"></el-tab-pane>
    </el-tabs>
    <agreementSetup v-if="tab === '1'" ref="agreementSetup"></agreementSetup>
    <template v-else-if="tab === '2'">
      <div class="select-box">
        <div class="top-title">合同协议查询</div>
        <div class="select-info">
          <el-form
            :inline="true"
            :model="formInline"
            ref="formInline"
            class="demo-form-inline"
            size="mini"
            label-width="150px"
          >
            <el-form-item label="合同协议名称：">
              <el-input
                type="text"
                maxlength="50"
                v-model="formInline.protocolName"
                placeholder="请输入合同协议名称"
              />
            </el-form-item>
            <el-form-item label="合同协议所在位置：">
              <el-select v-model="formInline.protocolPosition" placeholder="请选择" clearable>
                <el-option v-for="item in options" :key="item.id" :label="item.text" :value="item.id"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button class="left" icon="el-icon-search" @click="onSubmit">查询</el-button>
              <el-button class="left" @click="resetForm" icon="el-icon-refresh-right">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="list-box">
        <div class="list-title">
          <div>合同协议列表</div>
        </div>
        <div class="list-main">
          <template>
            <el-table :data="tableData" border style="width: 100%">
              <el-table-column prop="protocolName" label="合同协议名称" width="300"></el-table-column>
              <el-table-column prop="protocolPosition" label="合同协议所在位置" width="300"></el-table-column>
              <el-table-column prop="operatorName" label="操作人名称"></el-table-column>
              <el-table-column prop="operatorAccount" label="操作人账号"></el-table-column>
              <el-table-column prop="operatorTime" label="操作日期"></el-table-column>
              <el-table-column fixed="right" label="操作" width="100">
                <template slot-scope="scope">
                  <el-button @click="handleClick(scope.row)" type="text" size="small">查看</el-button>
                  <el-button type="text" size="small" @click="editClick(scope.row)">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
          <!-- <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formInline.pageNumber"
            :page-sizes="[10,20, 40, 60, 80,100]"
            :page-size="formInline.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            class="pagination"
          ></el-pagination> -->
        </div>
      </div>

      <el-dialog title="预览" :visible.sync="dialogTableVisible">
        <div class="agreementContent">
          <p class="title">{{agreementTitle}}</p>
          <div class="article" v-html="article">{{article}}</div>
        </div>
      </el-dialog>
    </template>
  </div>
</template>

<script>
import agreementSetup from './agreementSetup'
export default {
  components: {
    agreementSetup
  },
  name: "contractManage",
  data() {
    return {
      tab: '1',
      tableHeight: null, //表格的高度
      dialogTableVisible: false,
      options: [
        // {
        //   value: "DRIVER_REGISTER",
        //   label: "司机端-注册协议"
        // },
        // {
        //   value: "CONSIGNOR_REGISTER",
        //   label: "客户端-注册协议"
        // },
        // {
        //   value: "BROKER_REGISTER",
        //   label: "调度员端-注册协议"
        // },
        // {
        //   value: "TRADING_RULES",
        //   label: "平台交易规则"
        // },
        // {
        //   value: "PRIVACY_POLICY",
        //   label: "隐私政策"
        // }
      ],
      total: 0,
      formInline: {
        protocolPosition: "", //合同位置
        protocolName: "" //合同名称
        // pageSize:20,
        // pageNumber:1
      },
      tableData: [],
      agreementTitle: "", //协议的名称
      article: "<h1>aaaaaaa</h1>"
    };
  },
  methods: {
    //重置
    resetForm(formName) {
      this.formInline = {
        protocolPosition: "", //合同位置
        protocolName: "" //合同名称
        // pageSize:20,
        // pageNumber:1
      };
      this.getData();
    },
    // 刷新当前页
    refreshfn() {
      this.$router.go(0);
    },

    onSubmit() {
      this.formInline.pageNumber = 1
      this.getQuery();
    },

    handleSizeChange(val) {
      this.formInline.pageNumber = 1;
      this.formInline.pageSize = val;
      this.getData();
    },
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.getData();
    },
    /* 获取类型列表 */
    getType() {
      this.$http
        .get("/base-center-server/protocol/getAgreementType")
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.options = res.data.data;
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 获取列表数据 */
    getData() {
      let postData = this.formInline;
      this.$http
        .get("/base-center-server/protocol/getProtocolList")
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.tableData = data.data;
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 查询 */
    getQuery() {
      let postData = this.formInline;
      this.$http
        .post("/base-center-server/protocol/searchAgreementList", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.tableData = data.data;
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 点击查看 */
    handleClick(row) {
      this.dialogTableVisible = true;
      this.agreementTitle = row.protocolName;
      this.article = row.protocolCopywriting;
    },
    /* 编辑 */
    editClick(row) {
      this.$router.push({
        path: "editContract",
        query: {
          protocolName: row.protocolName,
          protocolPosition: row.protocolPositionCode
        }
      });
    },
    handleTabClick() {
      if (this.tab === '1') {
        this.$nextTick(() => {
          this.$refs.agreementSetup.init()
        })
      }
    }
  },
  activated() {
    this.getData();
    this.getType(); //获取协议类型

    this.$refs.agreementSetup.init()
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  margin: 20px;
  padding: 0;
  background: #fff;
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
