<template>
    <div class="app-container addFramework">
        <div class="tip">
            <div>新增管理员</div>
            <div><em style="color: red">*</em>为必填项</div>
        </div>
        <div class="inner-box">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="200px" class="demo-ruleForm">
                <el-form-item label='部门代码:' required>
                    <el-input v-model="ruleForm.depNo" style="width: 220px" placeholder="请输入部门代码"></el-input>
                </el-form-item>
                <el-form-item label="部门名称:" required>
                    <el-input v-model="ruleForm.depName" style="width: 220px" placeholder="请输入部门名称"></el-input>
                </el-form-item>
                <el-form-item label="负责人:" required>
                    <el-input v-model="ruleForm.chargeName" style="width: 220px" placeholder="请输入负责人"></el-input>
                </el-form-item>
                <el-form-item label="负责人电话:" required>
                    <el-input v-model="ruleForm.chargePhone" style="width: 220px" placeholder="请输入负责人电话" maxlength="11" :οnkeyup="ruleForm.chargePhone=ruleForm.chargePhone.replace(/[^\d\.]/g,'')"></el-input>
                </el-form-item>
                <el-form-item label="负责人职位:" required>
                    <el-input v-model="ruleForm.chargePost" style="width: 220px" placeholder="请输入负责人职位"></el-input>
                </el-form-item>
                <el-form-item label="部门创建时间:">
                    <el-date-picker
                     type="date"
                      placeholder="选择日期"
                      v-model="ruleForm.depCreateTime"
                      style="width: 220px"
                      format="yyyy 年 MM 月 dd 日"
                      value-format="yyyy-MM-dd"
                      ></el-date-picker>
                </el-form-item>
                <el-form-item label="备注:">
                    <el-input v-model="ruleForm.remarks" style="width: 220px"></el-input>
                </el-form-item>
                <el-form-item label="是否可进行开票:">

                    <el-switch
                       v-model="ruleForm.billModels"
                       active-color="#13ce66"
                       inactive-color="#ff4949"
                       active-text="是"
                       inactive-text="否">
                    </el-switch>
                </el-form-item>
                <el-form-item>
                    <el-button :loading="loading" type="primary" @click="submitForm" style="width: 120px">保存</el-button>
                    <el-button type="primary" @click="gopPrev" style="width: 120px">返回</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                loading:false,
                ruleForm: {
                    depNo:'',
                    depName:'',
                    chargeName:'',
                    chargePhone:'',
                    chargePost:'',
                    depCreateTime:'',
                    remarks:'',
                    billModels:0
                },
                pid:'',
                rules: {
                    name: [
                        {required: true, message: '请输入活动名称', trigger: 'blur'},
                        {message: '长度在 3 到 5 个字符', trigger: 'blur'}
                    ],
                    region: [
                        {required: true, message: '请选择车型', trigger: 'change'}
                    ],
                    zongzhong: [
                        {required: true, message: '填写总重', trigger: 'blur'},
                    ],
                    zaizhong: [
                        {required: true, message: '填写载重', trigger: 'blur'},
                    ]
                }
            };
        },
        methods: {
            submitForm() {
                this.loading=true;
                let form = this.ruleForm;
                if(this.ruleForm.billModels===false){
                    form.billModels=0
                }else if(this.ruleForm.billModels===true){
                    form.billModels=1
                }
                form.pid = this.pid;
                // this.$message.warning('保存时需验证部门代码唯一性')
                this.$http.post('/admin-center-server/sys/addDepartment',form).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                        this.$message.success('添加成功');
                        setTimeout(()=>{
                           this.$router.push('framework');
                            this.loading=false;
                           sessionStorage.removeItem('curPid');
                        },1000)
                    }else {
                        this.loading=false;
                        this.$message.warning(data.message)
                    }

                })
            },
            gopPrev(){
                this.$router.push('framework')
                sessionStorage.removeItem('curPid');
            },
            getPid(){
               let pid =  sessionStorage.getItem('curPid')
               this.pid = pid
            }
        },
        activated(){
            this.getPid()
        }
    }
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .addFramework {
        /*background-color: #ffffff;*/
        .tip{
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            padding: 0 40px 40px 0;
            font-size: 12px;
            em{
                margin-right: 5px;
            }
        }
        .inner-box {
            margin-left: 10%;
            width: 70%;
            .upload-box {
                width: 100%;
                height: 100%;
                position: relative;
                .icon-XZ {
                    width: 92px;
                    height: 92px;
                    margin: 0 auto;
                    background-size: 100% 100%;
                }
                .icon-word {
                    width: 100%;
                    height: 20px;
                    line-height: 20px;
                    font-size: 10px;
                    position: absolute;
                    bottom: 25px;
                    left: 0px;
                    color: #cccccc;
                }
            }
        }

    }
</style>
