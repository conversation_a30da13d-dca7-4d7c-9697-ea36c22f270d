:export {
  menuText: #bfcbd9;
  menuActiveText: #409EFF;
  subMenuActiveText: #f4f4f5;
  menuBg: #304156;
  menuHover: #263445;
  subMenuBg: #1f2d3d;
  subMenuHover: #001528;
  sideBarWidth: 180px;
}

#app .main-container {
  min-height: 100%;
  -webkit-transition: margin-left .28s;
  transition: margin-left .28s;
  margin-left: 180px;
  position: relative;
}

#app .sidebar-container {
  -webkit-transition: width 0.28s;
  transition: width 0.28s;
  width: 180px !important;
  height: 100%;
  position: fixed;
  font-size: 0px;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
}

#app .sidebar-container .horizontal-collapse-transition {
  -webkit-transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
  transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
}

#app .sidebar-container .scrollbar-wrapper {
  overflow-x: hidden !important;
}

#app .sidebar-container .scrollbar-wrapper .el-scrollbar__view {
  height: 100%;
}

#app .sidebar-container .el-scrollbar__bar.is-vertical {
  right: 0px;
}

#app .sidebar-container .is-horizontal {
  display: none;
}

#app .sidebar-container a {
  display: inline-block;
  width: 100%;
  overflow: hidden;
}

#app .sidebar-container .svg-icon {
  margin-right: 16px;
}

#app .sidebar-container .el-menu {
  border: none;
  height: 100%;
  width: 100% !important;
}

#app .sidebar-container .submenu-title-noDropdown:hover,
#app .sidebar-container .el-submenu__title:hover {
  background-color: #263445 !important;
}

#app .sidebar-container .is-active > .el-submenu__title {
  color: #f4f4f5 !important;
}

#app .sidebar-container .nest-menu .el-submenu > .el-submenu__title,
#app .sidebar-container .el-submenu .el-menu-item {
  min-width: 180px !important;
  background-color: #1f2d3d !important;
}

#app .sidebar-container .nest-menu .el-submenu > .el-submenu__title:hover,
#app .sidebar-container .el-submenu .el-menu-item:hover {
  background-color: #001528 !important;
}

#app .hideSidebar .sidebar-container {
  width: 36px !important;
}

#app .hideSidebar .main-container {
  margin-left: 36px;
}

#app .hideSidebar .submenu-title-noDropdown {
  padding-left: 10px !important;
  position: relative;
}

#app .hideSidebar .submenu-title-noDropdown .el-tooltip {
  padding: 0 10px !important;
}

#app .hideSidebar .el-submenu {
  overflow: hidden;
}

#app .hideSidebar .el-submenu > .el-submenu__title {
  padding-left: 10px !important;
}

#app .hideSidebar .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

#app .hideSidebar .el-menu--collapse .el-submenu > .el-submenu__title > span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}

#app .el-menu--collapse .el-menu .el-submenu {
  min-width: 180px !important;
}

#app .mobile .main-container {
  margin-left: 0px;
}

#app .mobile .sidebar-container {
  -webkit-transition: -webkit-transform .28s;
  transition: -webkit-transform .28s;
  transition: transform .28s;
  transition: transform .28s, -webkit-transform .28s;
  width: 180px !important;
}

#app .mobile.hideSidebar .sidebar-container {
  pointer-events: none;
  -webkit-transition-duration: 0.3s;
          transition-duration: 0.3s;
  -webkit-transform: translate3d(-180px, 0, 0);
          transform: translate3d(-180px, 0, 0);
}

#app .withoutAnimation .main-container,
#app .withoutAnimation .sidebar-container {
  -webkit-transition: none;
  transition: none;
}

.el-menu--vertical > .el-menu .svg-icon {
  margin-right: 16px;
}

.el-menu--vertical .nest-menu .el-submenu > .el-submenu__title:hover,
.el-menu--vertical .el-menu-item:hover {
  background-color: #263445 !important;
}

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  -webkit-box-sizing: inherit;
          box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0;
}

code {
  background: #eef1f6;
  padding: 15px 16px;
  margin-bottom: 20px;
  display: block;
  line-height: 36px;
  font-size: 15px;
  font-family: "Source Sans Pro", "Helvetica Neue", Arial, sans-serif;
}

code a {
  color: #337ab7;
  cursor: pointer;
}

code a:hover {
  color: #20a0ff;
}

.warn-content {
  background: rgba(66, 185, 131, 0.1);
  border-radius: 2px;
  padding: 16px;
  padding: 1rem;
  line-height: 1.6rem;
  word-spacing: .05rem;
}

.warn-content a {
  color: #42b983;
  font-weight: 600;
}

.app-container {
  padding:16px 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  -webkit-transition: 600ms ease position;
  transition: 600ms ease position;
  background: -webkit-gradient(linear, left top, right top, from(#20b6f9), color-stop(0%, #20b6f9), color-stop(100%, #2178f1), to(#2178f1));
  background: linear-gradient(90deg, #20b6f9 0%, #20b6f9 0%, #2178f1 100%, #2178f1 100%);
}

.sub-navbar .subtitle {
  font-size: 20px;
  color: #fff;
}

.sub-navbar.draft {
  background: #d0d0d0;
}

.sub-navbar.deleted {
  background: #d0d0d0;
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;
}

.link-type:hover,
.link-type:focus:hover {
  color: #20a0ff;
}

.filter-container {
  padding-bottom: 10px;
}

.filter-container .filter-item {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 10px;
}

.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}
/*# sourceMappingURL=index.css.map */