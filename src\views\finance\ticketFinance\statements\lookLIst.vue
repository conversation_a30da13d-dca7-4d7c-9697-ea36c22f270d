/**
结算单下的 查看按钮 列表
**/
<template>
    <div class="app-container detailedList">
        <div class="select-box">
            <div class="top-title">结算单列表-查看</div>
        </div>
        <div class="codeInfo">
            客户名称：{{businessName}}  结算单号：{{sn}}
        </div>
        <div class="list-box" style="overflow: hidden">
            <div class="right-title" style="display: flex;justify-content: space-between"><span>数据列表</span>
                <span style="margin-right: 10px"> 当前搜索结果总计：开票吨数 <span style="color: red">{{ton}}</span> 吨；开票金额 <span style="color: red">{{amount}}</span> 元</span>
            </div>
            <template>
                <el-table
                        :data="tableData"
                        border
                        style="width: 100%">
                    <el-table-column
                            type="index"
                            label="序号"
                            width="55">
                    </el-table-column>
                    <el-table-column
                            prop="sn"
                            label="运单编号"
                            show-overflow-tooltip
                            width="180">
                        <template slot-scope="scope">
                            <span @click="goYdDetail(scope.row)" style="color: blue;cursor: pointer">{{scope.row.sn}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="lastModifiedDate"
                            show-overflow-tooltip
                            label="结算时间"
                            width="180">
                    </el-table-column>
                    <el-table-column
                            prop="paymentTon"
                            label="结算吨数(吨)"
                            show-overflow-tooltip
                            width="180">
                    </el-table-column>
                    <el-table-column
                            prop="deductPrice"
                            show-overflow-tooltip
                            label="扣款金额(元)"
                            width="180">
                    </el-table-column>
                    <el-table-column
                            prop="amount"
                            show-overflow-tooltip
                            label="运单金额(元)"
                            width="120">
                    </el-table-column>
                    <el-table-column show-overflow-tooltip prop="transportAmount" label="结算运费(元)" width="120"></el-table-column>
                    <el-table-column show-overflow-tooltip prop="gasAmount" label="结算油气(元)" width="120"></el-table-column>
                    <el-table-column
                            prop="driverName"
                            show-overflow-tooltip
                            label="司机">
                    </el-table-column>
                    <el-table-column
                            prop="plateNumber"
                            show-overflow-tooltip
                            label="车牌号">
                    </el-table-column>
                    <el-table-column
                            prop="originalTon"
                            show-overflow-tooltip
                            label="原发吨数">
                    </el-table-column>
                    <el-table-column
                            prop="paymentTon"
                            show-overflow-tooltip
                            label="实收吨数">
                    </el-table-column>
                    <el-table-column
                            prop="shortTon"
                            show-overflow-tooltip
                            label="亏吨数">
                    </el-table-column>
                </el-table>
            </template>
            <div class="pagination" style="float: right;margin-top: 20px">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 40,50,100]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                amount:'',
                ton:'',
                sn:'',
                currentPage:1,
                pageSize:10,
                total:1,
                businessName:'',
                tableData: []
            }
        },
        methods: {
            /** 点击运单号 跳转至运单详情 **/
            goYdDetail(row) {
                let id = row.orderItemId;
                this.$router.push({
                    name: "TransportListDetail",
                    query: {
                        orderItemId: id,
                    }
                });
            },
            /** 分页方法 **/
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1;
                this.getDataList();
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getDataList();
            },
            getDataList() {
                let id = this.$route.query.id;
                this.amount = this.$route.query.amount;
                this.ton = this.$route.query.ton;
                this.businessName = this.$route.query.businessName;
                this.sn = this.$route.query.sn;
                this.$http.get('/admin-center-server/app/invoice_order_item/allItemlist', {
                    params: {
                        finalStatementId: id,
                        pageNumber: this.currentPage,
                        pageSize: this.pageSize,
                    }
                }).then(res => {
                    let data = res.data;
                    if(data.code==='200'){
                        this.tableData=data.data.list;
                        this.total=Number(data.data.total)
                    }
                })
            }
        },
        activated() {
            this.getDataList()
        }
    }
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .tip {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 40px 40px 0;
        font-size: 12px;

        em {
            margin-right: 5px;
        }
    }
.codeInfo{
    margin-top: 10px;
    background-color: #ffffff;
    padding: 10px;
    font-size: 14px;
}
    .right-title {
        font-size: 14px;
        font-weight: 700;
        height: 30px;
    }
    .inner-box {
        margin-left: 10%;
        width: 70%;

        .upload-box {
            width: 100%;
            height: 100%;
            position: relative;

            .icon-XZ {
                width: 92px;
                height: 92px;
                margin: 0 auto;
                background-size: 100% 100%;
            }

            .icon-word {
                width: 100%;
                height: 20px;
                line-height: 20px;
                font-size: 10px;
                position: absolute;
                bottom: 25px;
                left: 0px;
                color: #cccccc;
            }
        }
    }

    .detailedList {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
            }

            .select-info {
                padding-top: 20px;
            }

            .curTotalInfo {
                padding-left: 20px;
                height: 30px;
                line-height: 30px;
                font-size: 12px;
                color: #999999;
            }
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;
            overflow: hidden;

            .list-right {
                padding-left: 10px;

                .right-title {
                    font-size: 14;
                    font-weight: 700;
                }

                .list-main {
                    width: 100%;
                    border: 1px solid #cccccc;
                    margin-top: 10px;
                }

                .paging {
                    margin-top: 10px;
                    float: right;
                }
            }
        }
    }
</style>
