<template>
  <div class="app-container operationLog">
    <div class="select-box">
      <div class="top-title">
        <div>销户</div>
      </div>
      <div class="select-info">
        <el-form :model="ruleForm" ref="ruleForm" label-width="100px" class="demo-ruleForm">
          <el-form-item label="司机名称" required>
            <el-input v-model="ruleForm.sysTitle" style="width: 200px"></el-input>
          </el-form-item>
          <el-form-item label="消息图片:" required>
            <el-upload
              action
              list-type="picture-card"
              :on-preview="handlePictureCardPreview"
              :on-remove="handleRemove"
              :http-request="ossUpload"
              :file-list="fileList"
            >
              <div class="upload-box">
                <div class="icon-XZ"></div>
                <div class="icon-word">支持jpg、jpeg、png 格式不超过10M</div>
              </div>
            </el-upload>
            <el-dialog :visible.sync="dialogVisible" size="tiny">
            <img width="100%" :src="dialogImageUrl" alt />
          </el-dialog>
          </el-form-item>



          <el-form-item>
            <!-- <el-button type="primary" @click="submitInfo">提交</el-button>
            <el-button @click="resetForm('ruleForm')">重置</el-button> -->
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import { client, OSS_REGION } from "@/utils/alioss";
export default {
  data() {
    return {
      dialogVisible: false,
      Aliyun: {}, //阿里云相关
      fileList: [], //图片回显
      ruleForm: {

      },
    };
  },
  activated() {

  },
  methods: {
    /* 阿里云相关 */
    getAliyunData() {
      const ossApiUrl = "/base-center-server/sts/oss_signature"; // 获取oss签名的地址
      // 获取oss签名
      this.$http.get(ossApiUrl).then(res => {
        if (res.data.code == "200") {
          let data = res.data;
          this.Aliyun = data.data;
          this.Aliyun.region = OSS_REGION;
          // return data
        }
      });
    },
    /** 上传预览 **/
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList);
    },
    /* 上传图片 */
    ossUpload(param) {
      let file = param.file; // 文件的
      const tmpcnt = file.name.lastIndexOf(".");
      const exname = file.name.substring(tmpcnt + 1);
      const fileName =
        "/" +
        this.Aliyun.bucket +
        "/" +
        this.Aliyun.dir +
        this.$md5(name) +
        "." +
        exname;
      client(this.Aliyun)
        .put(fileName, file)
        .then(res => {
          if (res.res.status === 200) {
            // 上传
            let imgUrl = res.res.requestUrls[0];
            this.ruleForm.sysMessageUrl = imgUrl;
            console.log(this.ruleForm.sysMessageUrl)
            this.$message.success("上传成功");
          } else {
            this.$message.error(res.res.message);
          }
        });
    },


    /* 提交 */
    submitInfo() {

    },
  },
  created() {
    this.getAliyunData();
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.wrapper {
  ::v-deep .el-select-dropdown {
    z-index: 9999999 !important;
    background: red !important;
  }
}
.upload-box {
  width: 100%;
  height: 100%;
  position: relative;
  .icon-XZ {
    width: 92px;
    height: 92px;
    margin: 0 auto;
    background: url("../../../assets/xiazai.png") no-repeat;
    background-size: 100% 100%;
  }
  .icon-word {
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 10px;
    position: absolute;
    bottom: 25px;
    left: 0px;
    color: #cccccc;
  }
}
.operationLog {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
      display: flex;
      justify-content: space-between;

      .button {
        margin-right: 20px;
      }
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;
    overflow: hidden;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }

    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }

    .paging {
      margin-top: 10px;
      float: right;
    }
  }
}
</style>
