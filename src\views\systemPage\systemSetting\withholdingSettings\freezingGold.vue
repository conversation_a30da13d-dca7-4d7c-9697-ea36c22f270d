<template>
  <div class="app-container addSystemUser">
    <div class="inner-box">
      <el-form :model="ruleForm"
               :rules="rules"
               ref="ruleForm"
               label-width="180px"
               size="mini"
               class="demo-ruleForm">

        <el-form-item label="调度员季度结算金额上限"
                      prop="brokerSettlementCap">
          <el-input v-model="ruleForm.brokerSettlementCap"><template slot="append">￥</template></el-input>
        </el-form-item>
        <el-form-item label="调度员平台服务费比例"
                      prop="brokerServiceFeeRatio">
          <el-input v-model="ruleForm.brokerServiceFeeRatio"></el-input>
        </el-form-item>
        <el-form-item label="车主季度结算金额上限"
                      prop="carOwnerSettlementCap">
          <el-input v-model="ruleForm.carOwnerSettlementCap"><template slot="append">￥</template></el-input>
        </el-form-item>
        <el-form-item label="车主平台服务费比例"
                      prop="carOwnerServiceFeeRatio">
          <el-input v-model="ruleForm.carOwnerServiceFeeRatio"></el-input>
        </el-form-item>
        <el-form-item label="司机季度结算金额上限"
                      prop="driverSettlementAmountCap">
          <el-input v-model="ruleForm.driverSettlementAmountCap"><template slot="append">￥</template></el-input>
        </el-form-item>
        <el-form-item label="司机平台服务费比例"
                      prop="driverServiceFeeRatio">
          <el-input v-model="ruleForm.driverServiceFeeRatio"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="resetForm('ruleForm')">重置</el-button>
          <el-button type="primary"
                     @click="submitForm('ruleForm')">提交</el-button>

        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
const update = '/admin-center-server/sys/funds/frozen/update'//设置
const modification = '/admin-center-server/sys/funds/frozen/latest/modification'//数据回显
export default {
  data () {
    return {
      ruleForm: {
        brokerServiceFeeRatio: '',
        brokerSettlementCap: '',
        carOwnerServiceFeeRatio: '',
        carOwnerSettlementCap: '',
        driverServiceFeeRatio: '',
        driverSettlementAmountCap: ''
      },
      rules: {
        brokerServiceFeeRatio: [
          { required: true, message: '请输入调度员平台服务费比例', trigger: 'blur' },
          { required: true, trigger: 'change', validator: this.fuwuReg }
        ],
        brokerSettlementCap: [
          { required: true, message: '请输入调度员季度结算金额上限', trigger: 'blur' },
          { required: true, trigger: 'change', validator: this.Price500 }
        ],
        carOwnerServiceFeeRatio: [
          { required: true, message: '请输入车主平台服务费比例', trigger: 'blur' },
          { required: true, trigger: 'change', validator: this.fuwuReg }
        ],
        carOwnerSettlementCap: [
          { required: true, message: '请输入车主季度结算金额上限', trigger: 'blur' },
          { required: true, trigger: 'change', validator: this.Price500 }
        ],
        driverServiceFeeRatio: [
          { required: true, message: '请输入司机平台服务费比例', trigger: 'blur' },
          { required: true, trigger: 'change', validator: this.fuwuReg }
        ],
        driverSettlementAmountCap: [
          { required: true, message: '请输入司机季度结算金额上限', trigger: 'blur' },
          { required: true, trigger: 'change', validator: this.Price500 }
        ]
      }
    };
  },
  methods: {
    submitForm (formName) {
      var that = this
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var v = that.ruleForm
          var data = {
            "brokerServiceFeeRatio": v.brokerServiceFeeRatio,
            "brokerSettlementCap": v.brokerSettlementCap,
            "carOwnerServiceFeeRatio": v.carOwnerServiceFeeRatio,
            "carOwnerSettlementCap": v.carOwnerSettlementCap,
            "driverServiceFeeRatio": v.driverServiceFeeRatio,
            "driverSettlementAmountCap": v.driverSettlementAmountCap
          }
          console.log(data)
          this.$http.post(update, data).then(res => {
            if (res.data.code == '200') {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
            } else {
              this.$message.error(res.data.message);
            }

          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetForm (formName) {
      this.$refs[formName].resetFields();
    },
    getData () {
      var v = this.ruleForm;
      this.$http.get(modification).then(res => {
        if (res.data.code == '200') {
          console.log(res.data)
          var data = res.data.data
          v.brokerServiceFeeRatio = data.brokerServiceFeeRatio
          v.brokerSettlementCap = data.brokerSettlementCap
          v.carOwnerServiceFeeRatio = data.carOwnerServiceFeeRatio
          v.carOwnerSettlementCap = data.carOwnerSettlementCap
          v.driverServiceFeeRatio = data.driverServiceFeeRatio
          v.driverSettlementAmountCap = data.driverSettlementAmountCap
        } else {
          this.$message.error(res.data.message);
        }

      })
    }
  },
  activated () {
    this.getData()
    // var v = this.ruleForm;
    // var modifyData = JSON.parse(localStorage.getItem('modifyData'));
    // console.log(modifyData)
    // v.calculateTaxPointBase = modifyData.calculateTaxPointBase
    // v.valueAddedTax = modifyData.valueAddedTax
    // v.buildingTax = modifyData.buildingTax
    // v.educationSurcharge = modifyData.educationSurcharge
    // v.localEducationSurcharge = modifyData.localEducationSurcharge
    // v.personalIncomeTax = modifyData.personalIncomeTax
    // v.comparisonSymbol = modifyData.comparisonSymbol == 0 ? '' : modifyData.comparisonSymbol
    // v.type = modifyData.rules
    // this.id = modifyData.id
  }
}
</script>
<style>
.radioBox {
  width: 410px;
  flex-wrap: wrap;
}
.radioBox .el-checkbox {
  width: 100px;
}
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
.addSystemUser {
  .tip {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 40px 40px 0;
    font-size: 12px;
    em {
      margin-right: 5px;
    }
  }
  .inner-box {
    margin-left: 10%;
    width: 70%;
    .upload-box {
      width: 100%;
      height: 100%;
      position: relative;
      .icon-XZ {
        width: 92px;
        height: 92px;
        margin: 0 auto;
        background-size: 100% 100%;
      }
      .icon-word {
        width: 100%;
        height: 20px;
        line-height: 20px;
        font-size: 10px;
        position: absolute;
        bottom: 25px;
        left: 0px;
        color: #cccccc;
      }
    }
  }
}
</style>
