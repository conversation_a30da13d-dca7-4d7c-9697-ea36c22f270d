<template>
  <div id="app">
    <router-view />
    <audio id="eventAudio"></audio>
    <div ref="watermarkContainer" class="watermark-container" v-show="showWatermark"></div>
  </div>
</template>

<script>
import { grayFn } from '@/utils/index.js'
export default {
  name: 'app',
  data () {
    return {
      showWatermark: false
    };
  },
  mounted() {
    this.addWatermark();
    this.changehost();
  },
  updated() {
    this.changehost();
  },
  components: {},
  methods:{
     // 切换环境判断显示水印
     changehost(){
      let showWatermark = false;
      if(grayFn()){
        showWatermark = true;
      }
      this.showWatermark = showWatermark;
    },
    // 添加灰度测试水印
    addWatermark() {
      const canvas = document.createElement('canvas');
      canvas.width = 300;
      canvas.height = 200;
      const ctx = canvas.getContext('2d');
      ctx.font = '30px Arial';
      ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.translate(canvas.width / 2, canvas.height / 2);
      ctx.rotate(-Math.PI / 4);
      ctx.fillText('灰度测试', 0, 0);
      this.$refs.watermarkContainer.style.backgroundImage = `url(${canvas.toDataURL()})`;
    }
  }
}
</script>

<style>
body {
  background-color: #f2f2f2;
}

/*.is-current{*/
/*  background-color: #42b983 !important;*/
/*}*/
.el-tree-node {
}

.is-current {
  /*background-color: #42b983;*/
}

.is-focusable {
  /*background-color: #42b983;*/
}
.el-table .warning-row {
  background: #f0f9eb;
}

.el-table .success-row {
  background: #f9f9f9;
}
.el-table .inAudit-row {
  background: #fbdfdf;
}
.watermark-container{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* 确保水印不影响用户交互 */
}
</style>
