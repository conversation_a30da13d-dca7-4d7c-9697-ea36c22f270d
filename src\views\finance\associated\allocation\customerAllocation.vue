<template>
  <div>
    <div class="list-box">
      <div class="main-box">
        <div class="list-info">
          <el-form ref="form" label-width="140px" :inline="true" size="mini">
            <el-form-item label="客户名称：">
              <el-input v-model="search.name"></el-input>
            </el-form-item>
            <el-form-item label="客户手机号：">
              <el-input v-model="search.mobile"></el-input>
            </el-form-item>
            <el-form-item label="油卡卡号：">
              <el-input v-model="search.oilAccount"></el-input>
            </el-form-item>
            <el-form-item label="充值时间：">
              <el-date-picker
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                v-model="date"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="油分配状态:">
              <el-select
                v-model="search.status"
                placeholder="未分配"
                style="width: 178px"
                @change="doSearch"
              >
                <el-option label="未分配" value="0"></el-option>
                <el-option label="已分配" value="1"></el-option>
                <el-option label="全部" value=""></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="doSearch">查询</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="list-box">
      <el-table
        v-loading="loading"
        :data="data"
        style="width: 100%"
        :max-height="tableHeight"
        ref="table"
        :cell-style="{'text-align': 'center', 'border': '0.5px solid #EAF0FB'}"
        :header-cell-style="{'text-align': 'center', 'border': '0.3px solid #EAF0FB', 'background-color': '#F5F6F9', 'height': '60px'}"
      >
        <el-table-column label="充值时间" prop="createTime"></el-table-column>
        <el-table-column label="合作平台主体" prop="baseName"></el-table-column>
        <el-table-column label="油卡卡号" prop="oilAccount"></el-table-column>
        <el-table-column label="客户名称" prop="name"></el-table-column>
        <el-table-column label="客户手机号" prop="mobile"></el-table-column>
        <el-table-column label="充值金额" prop="money"></el-table-column>
        <el-table-column label="油分配金额" prop="allocationOilMoney"></el-table-column>
        <el-table-column label="油折扣" prop="oilDiscount">
          <template slot-scope="scope">
            <span>{{scope.row.oilDiscount ? scope.row.oilDiscount + '%' : ''}}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180px">
          <template slot-scope="scope">
            <el-button
              type="text"
              v-if="
                scope.row.oilAccount === null &&
                scope.row.ifChannelRecharge != 1
              "
              @click="
                $router.push(
                  `/consignorExamine/auditDetails?type=1&id=${scope.row.userId}&operation=1`
                )
              "
              >查看</el-button
            >
            <el-button :type="scope.row.status == '0' ? 'text' : 'text gray-status'" @click="sign(scope.row)">{{scope.row.status == '0' ? '标记分配' : '-'}}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 10px; text-align: right">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="getList"
          :current-page.sync="page.pageNumber"
          :page-sizes="[10, 20, 40, 60, 80, 100]"
          :page-size="page.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
        ></el-pagination>
      </div>
    </div>

    <el-dialog
      :close-on-click-modal="false"
      title="标记分配"
      :visible.sync="isAllocationShow"
      destroy-on-close
      width="600px"
    >
      <div style="color: red; margin: 0 0 20px 20px">
        请认真核实以下信息，一旦确定则无法修改
      </div>
      <el-form
        ref="modelForm"
        label-width="130px"
        :model="form"
        :rules="rules"
        label-position="left"
        style="margin-left: 20px"
      >
        <el-row class="global-div-search">
          <el-col :span="24">
            <el-form-item label="客户名称" prop="name">
              {{ form.name || "-" }}
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="卡号" prop="oilAccount">
              <el-input
                @input="
                  () => {
                    form.oilAccount = form.oilAccount.replace(
                      /^0{1}|[^\d]/g,
                      ''
                    );
                  }
                "
                v-model="form.oilAccount"
                clearable
                placeholder="请输入卡号"
                :disabled="form.isOliAccount"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
              <el-form-item label="油折扣" prop="oilDiscount">
                  <el-input
                      v-model="form.oilDiscount"
                      clearable
                      placeholder="请输入1-100的数字，支持两位小数"
                      :max="100"
                      :min="0"
                  >
                   <template slot="append">%</template>
                  </el-input>
              </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="油分配总金额" prop="money">
              {{
                (form.ifChannelRecharge == 1
                  ? form.allocationOilMoney
                  : form.money) || "-"
              }}
              元
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="其中积分分配金额" prop="integralMoney">
              <el-input
                v-model="form.integralMoney"
                clearable
                placeholder="请输入金额"
                @input="
                  (value) => {
                    form.integralMoney = (
                      value.match(/\d+\.?\d{0,2}/) || []
                    ).join('');
                  }
                "
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label-width="10px">元</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isAllocationShow = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Big } from 'big.js'
export default {
  data() {
    return {
      activeName: "first",
      search: {
        status: '0'
      },
      isSearch: false,
      date: null,
      page: {
        pageNumber: 1,
        pageSize: 10,
      },
      data: [],
      total: 0,
      isAllocationShow: false,
      cardNumber: "",
      loading: true,
      tableHeight: null,
      form: {},
      rules: {
        name: [
          {
            required: false,
            message: "",
            trigger: "blur",
          },
        ],
        oilAccount: [
          {
            required: true,
            message: "请输入油卡卡号",
            trigger: "blur",
          },
        ],
        oilDiscount: [
          {
            required: true,
            validator: async (rule, value, callback) => {
                
                if (!value) {
                    callback(new Error("请输入折扣比例"));
                } else {
                    let num = Number(value)
                    let decimals = String(num).split('.')[1],
                        decimalsLength = 0
                    if (decimals) {
                        decimalsLength = String(decimals).length
                    }
                    console.log(decimals, decimalsLength)
                    if (Number.isNaN(num) || num > 100 || num < 1 || decimalsLength > 2) {
                        callback('请输入1-100的数字，支持两位小数')
                    } else { 
                      // 计算分油                   
                      this.form.allocationOilMoney = Big(this.form.money).div(value).times(100).toFixed(2)
                    }
                }
                callback()
            },
            trigger: "blur",
          },
        ],
        money: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
        ],
        integralMoney: [
          {
            required: false,
            validator: (rule, value, callback) => {
              if (!value) {
                callback();
              } else if (
                +value >=
                (+this.form.ifChannelRecharge == 1
                  ? +this.form.allocationOilMoney
                  : +this.form.money)
              ) {
                callback(
                  new Error(
                    `积分分配金额不能大于${
                      this.form.ifChannelRecharge == 1
                        ? this.form.allocationOilMoney
                        : this.form.money
                    }`
                  )
                );
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
    };
  },
  activated() {
    this.getList();
    this.tableHeight =
      window.innerHeight - this.$refs.table.$el.offsetTop - 170;
  },
  methods: {
    getList() {
      this.loading = true;
      let params = { ...this.page };
      if (this.isSearch) {
        Object.assign(params, this.search);
        if (this.date !== null) {
          params.startDate = this.date[0];
          params.endDate = this.date[1];
        }
      } else {
        params.status = this.search.status
      }
      this.$post(
        "/admin-center-server/oilRecharge/oilRechargeList",
        params
      ).then((res) => {
        this.data = res.list;
        this.total = Number(res.total);
        this.loading = false;
      });
    },
    doSearch() {
      this.page.pageNumber = 1;
      this.isSearch = true;
      this.getList();
    },
    reset() {
      this.search = {status: '0'};
      this.date = null;
      this.page.pageNumber = 1;
      this.isSearch = false;
      this.getList();
    },
    handleSizeChange(v) {
      this.page.pageSize = v;
      this.getList();
    },
    sign(row) {
      if (row.status == '1') {
        return
      }
      this.form = JSON.parse(JSON.stringify(row));
      if (row.oilAccount) this.form.isOliAccount = true;
      this.isAllocationShow = true;
    },
    submit() {
      const { id, integralMoney, oilAccount, allocationOilMoney , oilDiscount} = this.form;
      this.$refs.modelForm.validate((valid) => {
        if (valid) {
          this.$http
            .post("/admin-center-server/oilRecharge/allocation", {
              id,
              integralMoney,
              oilAccount,
              allocationOilMoney,
              oilDiscount
            })
            .then((res) => {
              if (res.data.code == 200) {
                this.$message.success(res.data.message);
              } else {
                this.$message.error(res.data.message);
              }
              this.isAllocationShow = false;
              this.getList();
            });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.list-box {
  background-color: #ffffff;
  margin: 20px;
  padding: 10px;

  .list-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    div {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
    }
  }

  .list-main {
    width: 100%;
    //   border: 1px solid #cccccc;
    margin-top: 10px;
  }

  .releaseMessage {
    margin-right: 20px;
  }

  .pagination {
    text-align: right;
    margin-top: 10px;
  }
}
.global-div-search {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  /* font-size: 14px; */
  /* color: #555; */
  position: relative;
}
.gray-status {
  color: #888;
}
</style>