<template>
  <div class="app-container operationLog">
    <div class="select-box">
      <div class="top-title">
        <div>筛选查询</div>
        <div class="button">
          <el-button
            class="left"
            icon="el-icon-search"
            size="mini"
            type="primary"
            @click="onSubmit"
          >查询</el-button>
          <el-button
            class="left"
            icon="el-icon-delete"
            size="mini"
            type="danger"
            @click="resetSubmit"
          >清空筛选</el-button>
        </div>
      </div>
      <div class="select-info">
        <el-form
          :inline="true"
          size="small"
          :model="formInline"
          class="demo-form-inline"
          label-width="100px"
        >
          <el-form-item label="操作项:">
            <el-input v-model="formInline.action" placeholder="请输入操作项"></el-input>
          </el-form-item>
          <el-form-item label="操作人姓名:">
            <el-input v-model="formInline.operateName" placeholder="请输入操作人姓名"></el-input>
          </el-form-item>
          <el-form-item label="操作人账号:">
            <el-input v-model="formInline.operateAccount" placeholder="请输入操作人账号"></el-input>
          </el-form-item>
          <el-form-item label="创建日期:">
            <el-date-picker
              @blur="selectTime"
              v-model="formInline.date"
              type="datetimerange"
              start-placeholder="开始日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
              range-separator="至"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table 
          :data="tableData" 
          border 
          style="width: 100%" 
          cell-class-name="table_cell_gray"
          header-cell-class-name="table_header_cell_gray">
            <el-table-column prop="num" label="序号" type="index" width="50"></el-table-column>
            <el-table-column prop="action" label="操作项"></el-table-column>
            <el-table-column prop="detail" label="操作内容"></el-table-column>
            <el-table-column prop="operateName" label="操作人姓名"></el-table-column>
            <el-table-column prop="operateAccount" label="操作人账号"></el-table-column>
            <el-table-column prop="operateTime" label="操作时间"></el-table-column>
          </el-table>
        </template>
      </div>
      <div class="paging">
        <div class="block">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      currentPage: 1,
      pageSize: 20,
      formInline: {
        action: "",
        operateName: "",
        operateAccount: "",
        date: ""
      },
      tableData: [],
      startTime: "",
      endTime: "",
      total: 1
    };
  },
  methods: {
    /** 分页方法 **/
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.pageSize = val;
      this.currentPage = 1;
      this.getDataList();
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.currentPage = val;
      this.getDataList();
    },

    /** 根据时间态搜索 **/
    selectTime() {
      let startTime = this.formInline.date[0];
      let endTime = this.formInline.date[1];
      this.startTime = startTime;
      this.endTime = endTime;
    },
    /** 搜索提交 车牌号 车队长名字 **/
    onSubmit() {
      this.currentPage = 1;
      this.getDataList();
    },
    /** 清空搜索选项 **/
    resetSubmit() {
      this.formInline = {
        action: "",
        operateName: "",
        operateAccount: "",
        date: ""
      };
      this.currentPage = 1;
      this.pageSize = 10;
      this.total = 1;
      this.startTime = "";
      this.endTime = "";
      this.getDataList();
    },

    /** 拉取列表数据 **/
    getDataList() {
      this.$http
        .get("/admin-center-server/sys/getAuditLogs", {
          params: {
            action: this.formInline.action,
            maxCreateTime: this.endTime,
            minCreateTime: this.startTime,
            operateAccount: this.formInline.operateAccount,
            operateName: this.formInline.operateName,
            order: "",
            pageNumber: this.currentPage,
            pageSize: this.pageSize
          }
        })
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.tableData = data.data.list;
            this.total = Number(data.data.total);
          }
        });
    }
  },
  activated() {
    this.getDataList();
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.operationLog {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
      display: flex;
      justify-content: space-between;

      .button {
        margin-right: 20px;
      }
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;
    overflow: hidden;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }

    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }

    .paging {
      margin-top: 10px;
      float: right;
    }
  }
}
</style>
