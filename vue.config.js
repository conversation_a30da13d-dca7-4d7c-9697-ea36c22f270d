module.exports = {
  publicPath: '/sysAdmin',
  chainWebpack: config => {
    // 一个规则里的 基础Loader
    // svg是个基础loader
    const svgRule = config.module.rule('svg');

    // 清除已有的所有 loader。
    // 如果你不这样做，接下来的 loader 会附加在该规则现有的 loader 之后。
    svgRule.uses.clear();

    // 添加要替换的 loader
    svgRule
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
  },
  devServer: {
    port: 8100,
    open: true,
    // proxy: {
    //   [process.env.VUE_APP_BASE_API]: {
    //     target: `http://gateway.5hefeng.com:8084`,
    //     changeOrigin: true,
    //     pathRewrite: {
    //       ['^' + process.env.VUE_APP_BASE_API]: ''
    //     }
    //   }
    // },
    // proxy: {
    //   '/api': {
    //     target: 'https://gateway.5hefeng.com',
    //     changeOrigin: true,
    //     pathRewrite: {
    //       '^/api': ''
    //     }
    //   }
    // }

    proxy: {
      '/api': {
        target: process.env.VUE_APP_BASE_API,
        // target: 'http://127.0.0.1:7015',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        },
      },
      '/map': {
        target: 'https://restapi.amap.com', // 需要请求的地址
        changeOrigin: true, // 是否跨域
        pathRewrite: {
          '^/map': '' //需要rewrite的,
        }
      },
      '/pro': {
        target: 'https://pro.hongfeida.cn',
        // target: 'http://127.0.0.1:7015',
        // target: 'https://api.hongfeida.com.cn',
        changeOrigin: true,
        pathRewrite: {
          '^/pro': ''
        }
      }
    }

  },

}
