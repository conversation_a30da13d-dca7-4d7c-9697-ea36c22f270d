<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">
        <div>筛选查询</div>
        <div class="button">
          <el-button
            class="left"
            icon="el-icon-search"
            size="mini"
            type="primary"
            @click="queryFun"
          >查询</el-button>
          <el-button
            class="left"
            icon="el-icon-delete"
            size="mini"
            type="danger"
            @click="resetSubmit"
          >清空筛选</el-button>
        </div>
      </div>

      <div class="select-info">
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="100px">
          <el-form-item label="消息标题:">
            <el-input v-model.trim="formInline.title" placeholder="请输入消息标题" maxlength="50" size="small"></el-input>
          </el-form-item>

          <el-form-item label="发布人员:">
            <el-input
              v-model.trim="formInline.releaseUserName"
              placeholder="请输入发布人员"
              maxlength="50"
              size="small"
            ></el-input>
          </el-form-item>

          <el-form-item label="发布日期:" size="small">
            <el-col :span="11">
              <el-date-picker
                type="date"
                placeholder="请选择发布时间"
                v-model="formInline.releaseDate"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </el-col>
          </el-form-item>
        </el-form>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          label-width="150px"
          style="margin-top: 30px"
        ></el-form>
      </div>
    </div>

    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
        <div>
          <el-button class="releaseMessage" @click="releaseMessage">发布消息</el-button>
          <el-select v-model="formInline.order" placeholder="请选择" style="width: 130px">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </div>

      <div class="list-main">
        <el-table
          ref="multipleTable"
          :data="tableData"
          tooltip-effect="dark"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          border
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column type="index" label="编号" width="55"></el-table-column>
          <el-table-column prop="title" label="消息标题"></el-table-column>
          <el-table-column prop="releaseDate" label="发布时间"></el-table-column>
          <el-table-column prop="releaseUserName" label="发布人员"></el-table-column>
          <el-table-column fixed="right" label="操作" width="240">
            <template slot-scope="scope">
              <!-- pushFlag 为 '0' 才显示 '立即发布'， '修改信息' -->
              <el-button
                @click="immediateRelease(scope.row)"
                type="text"
                size="small"
                v-if="scope.row.pushFlag == '0' "
              >立即发布</el-button>
              <el-button
                @click="editorInfo(scope.row)"
                type="text"
                size="small"
                v-if="scope.row.pushFlag == '0' "
              >修改信息</el-button>
              <el-button @click="goDetail(scope.row)" type="text" size="small">查看</el-button>
              <el-button type="text" size="small" @click="deleteItem(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="page" >
          <el-button @click="deleteAll">批量删除</el-button>
          <div>
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInline.pageNumber"
              :page-sizes="[20, 40, 60, 80,100]"
              :page-size="formInline.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              style="margin: 10px auto"
            ></el-pagination>
          </div>
        </div>
        <!-- 查看的弹窗 -->
        <el-dialog title="查看消息" :visible.sync="dialogTableVisible" class="messageDetail">
          <p>{{title}}</p>
          <h4>{{time}}</h4>
          <h5 class="content" v-html="content"></h5>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dialogTableVisible: false,

      //查看展示的值
      title: "",
      time: "",
      content: "",
      /* ******* */
      value: "",
      tableHeight: null, //表格的高度
      tableData: [],
      total: null, //总数
      options: [
        {
          value: "asc",
          label: "按照时间升序"
        },
        {
          value: "desc",
          label: "按照时间降序"
        }
      ],
      formInline: {
        title: "", //消息标题
        releaseUserName: "", //发布人员
        releaseDate: "", //发布日期
        pageNumber: 1,
        pageSize: 20,
        order: "desc"
      },
      multipleSelection: [] //多选的值
    };
  },
  activated() {
    this.getDataList();
  },
  methods: {
    onSubmit() {
      console.log("submit!");
    },
    /* 查看 */
    goDetail(row) {
      console.log(row);
      this.dialogTableVisible = true;
      this.title = row.title;
      this.content = row.content;
      this.time = row.releaseDate;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      console.log(this.multipleSelection, "-----");
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then(_ => {
          done();
        })
        .catch(_ => {});
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.formInline.pageNumber=1
      this.formInline.pageSize = `${val}`;
      this.getDataList();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.formInline.pageNumber = `${val}`;
      this.getDataList();
    },
    /* 获取数据列表 */
    getDataList() {
      var postData = this.formInline;
      this.$http
        .post("/admin-center-server/message/queryMessageList", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            console.log(data);
            this.tableData = data.data.list;
            this.total = JSON.parse(data.data.total);
          }
        });
    },

    /* 删除的操作 */
    deleteItem(row) {
      console.log(row.id);
      let ids = [];
      ids[0] = Number(row.id);
      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http
            .post("/admin-center-server/message/deleteMessage", ids)
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
              }
              this.getDataList();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },

    /* 查询 */
    queryFun() {
      this.formInline.pageNumber =1;
      this.getDataList();
    },
    /*  清空 */
    resetSubmit() {
      (this.formInline.title = ""),
        (this.formInline.releaseUserName = ""),
        (this.formInline.releaseDate = "");
    },
    /* 发布消息的弹窗 */
    releaseMessage() {
      this.$router.push({
        name: "sendMessage",
        params: {
          flag: 1 //新增
        }
      });
    },
    /* 批量删除 */
    deleteAll() {
      var deleteAllData = this.multipleSelection; //批量删除的数据
      let ids = deleteAllData.map(obj => {
        return obj.id;
      });
      console.log(ids, "----");
      this.dialogVisible = true;
      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http
            .post("/admin-center-server/message/deleteMessage", ids)
            .then(res => {
              let data = res.data;
              if (data.code == "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
              }
              this.getDataList();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    /* 修改信息 */
    editorInfo(row) {
      // console.log(row.content);
      this.$router.push({
        name: "sendMessage",
        params: {
          content: row.content,
          title: row.title,
          flag: 2, //修改
          id: row.id,
          push: row.push,
          releaseDate: row.releaseDate
        }
      });
    },
    /* 立即发布 */
    immediateRelease(row) {
      let postData = {
        id: row.id
      };
      this.$http
        .post(
          "/admin-center-server/message/sendMessage",
          this.$qs.stringify(postData)
        )
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.$message({
              type: "success",
              message: "发布成功!"
            });
          }
          this.getDataList();
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.el-form-item {
  margin-bottom: 0;
}
.top-title {
  font-size: 16px;
  font-weight: 700;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  border-bottom: 1px solid #cccccc;
  display: flex;
  justify-content: space-between;

  .button {
    margin-right: 20px;
  }
}
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }
    .page {
      text-left: left;
      margin: 10px ;
    }
    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
    .messageDetail p {
      text-align: center;
      font-weight: 600;
      font-size: 20px;
    }
    .messageDetail h4 {
      font-size: 16px;
      text-align: center;
      font-weight: 200;
    }
    .messageDetail h5 {
      font-size: 16px;
      line-height: 30px;
    }
  }
}

.editor {
  margin-top: 30px;
}

.myQuillEditor {
  height: 400px;
}

.editCommit {
  margin: 100px;
}
</style>
