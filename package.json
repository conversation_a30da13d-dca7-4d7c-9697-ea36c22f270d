{"name": "ljb_admin", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "dat": "vue-cli-service serve --mode dat", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "pro": "vue-cli-service serve --mode production", "build:dat": "vue-cli-service build --mode dat", "build:production": "vue-cli-service build --mode production", "build:dev": "vue-cli-service build --mode dev"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "Base64": "^1.2.0", "ali-oss": "^6.4.0", "axios": "^0.18.0", "big.js": "^6.2.1", "cf-element-ui": "^1.0.0", "dayjs": "^1.11.8", "driver.js": "^0.9.3", "element-ui": "^2.5.4", "fingerprintjs2": "^2.1.0", "fuse.js": "^3.4.2", "goeasy": "^1.0.5", "html2canvas": "^1.0.0-rc.5", "jquery": "^3.5.1", "js-cookie": "^2.2.0", "mockjs": "^1.0.1-beta3", "node-sass": "^7.0.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "particles.js": "^2.0.0", "path-to-regexp": "^3.0.0", "qrcodejs2": "0.0.2", "screenfull": "^4.0.1", "sm-crypto": "^0.3.13", "vue": "^2.5.22", "vue-amap": "^0.5.10", "vue-i18n": "^8.8.2", "vue-router": "^3.0.2", "vuex": "^3.1.0", "wangeditor": "^3.1.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.4.0", "@vue/cli-plugin-eslint": "^3.4.0", "@vue/cli-service": "^3.4.0", "babel-eslint": "^10.0.1", "eslint": "^5.8.0", "eslint-plugin-vue": "^5.0.0", "js-md5": "^0.7.3", "node-sass": "^4.11.0", "sass": "^1.49.0", "sass-loader": "^7.1.0", "svg-sprite-loader": "^4.1.3", "vue-template-compiler": "^2.5.21"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {"no-console": [0, false], "no-unused-vars": 0}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}