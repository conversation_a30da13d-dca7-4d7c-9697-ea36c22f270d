/**
结算单-编辑-查看
**/
<template>
    <div class="app-container detailedList">
        <div class="select-box">
            <div class="top-title">结算单-编辑-查看</div>
            <div class="curTotalInfo"><span>结算单号：{{businessOrderSn}}  客户名称：{{editRowUserName}} </span></div>
        </div>
        <div class="select-time">
            <el-row>
                <el-date-picker
                        :clearable="false"
                        @blur="selectTime"
                        v-model="date"
                        format="yyyy 年 MM 月 dd 日"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                ></el-date-picker>
                <el-button style="margin-left: 20px" size="mini" icon="el-icon-search" type="primary" @click="goSearch">查 询</el-button>
                <el-button
                        class="left"
                        icon="el-icon-delete"
                        size="mini"
                        type="danger"
                        @click="resetSubmit"
                >清空筛选
                </el-button>
            </el-row>
        </div>
        <div class="list-box">
            <el-row>
                <div class="list-right" style="overflow: hidden">
                    <div class="right-title" style="display: flex;justify-content: space-between"><span>数据列表</span>
                        <span style="margin-right: 10px"> 当前搜索结果总计：开票吨数 <span style="color: red">{{sumTon}}</span> 吨；开票金额 <span style="color: red">{{sumAmount}}</span> 元</span>
                    </div>
                    <div class="list-main">
                        <template>
                            <el-table :data="tableData" border style="width: 100%">
                                <el-table-column prop="num" label="序号" type="index" width="50"></el-table-column>
                                <el-table-column prop="ydsn" label="订单号" width="200px"></el-table-column>
                                <el-table-column prop="sn" label="运单号" width="200px"></el-table-column>
                                <el-table-column prop="freight" label="运费单价(元)" width="120px"></el-table-column>
                                <el-table-column prop="originalTon" label="原发吨数(吨)" width="120px"></el-table-column>
                                <el-table-column prop="paymentTon" label="结算吨数(吨)" width="120px"></el-table-column>
                                <el-table-column prop="shortTon" label="亏吨数(吨)" width="120px"></el-table-column>
                                <el-table-column prop="deductPrice" label="扣款金额(元)" width="120px"></el-table-column>
                                <el-table-column prop="amount" label="结算金额(元)" width="120px"></el-table-column>
                                <el-table-column prop="driverName" label="司机" width="120px"></el-table-column>
                                <el-table-column prop="driverMobile" label="电话" width="150px"></el-table-column>
                                <el-table-column prop="plateNumber" label="车牌号" width="120px"></el-table-column>
                                <el-table-column prop="lastModifiedDate" label="结算时间" width="200px"></el-table-column>
                            </el-table>
                        </template>
                    </div>
                    <div style="float: right;margin-top: 10px" >
                        <el-pagination
                                @size-change="handleSizeChange"
                                @current-change="handleCurrentChange"
                                :current-page="currentPage"
                                :page-sizes="[10, 20, 30, 40,50,100]"
                                :page-size="pageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="innerTotal">
                        </el-pagination>
                    </div>
                </div>
            </el-row>
        </div>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                editRowUserName:'',
                date:'',
                invoiceBusinessOrderId:'',
                finalStatementId:'',
                businessOrderSn:'',
                currentPage:1,
                innerTotal:1,
                pageSize:10,
                ydsn:'',
                sumAmount:'',
                sumTon:'',
                startDate:'',
                endDate:'',
                tableData: []
            };
        },
        methods: {
            /** 根据时间态搜索 **/
            selectTime() {
                let startTime = this.date[0];
                let endTime = this.date[1];
                this.startDate = startTime;
                this.endDate = endTime;
            },
            goSearch(){
                this.getDataList();
            },
            resetSubmit(){
                this.date='';
                this.startDate = '';
                this.endDate = '';
                this.getDataList();
            },
            handleSizeChange(val) {
                this.pageSize =val;
                this.currentPage=1;
                this.getDataList();
            },
            handleCurrentChange(val) {
                this.currentPage=val;
                this.getDataList();
            },
            getDataList(){
                 let finalStatementId = this.finalStatementId;
                 let invoiceBusinessOrderId = this.invoiceBusinessOrderId;
                 this.$http.get('/admin-center-server/app/invoice_order_item/editDoneList',{
                     params:{
                         finalStatementId:finalStatementId,
                         invoiceBusinessOrderId:invoiceBusinessOrderId,
                         pageNumber:this.currentPage,
                         pageSize:this.pageSize,
                         startDate:this.startDate,
                         endDate:this.endDate,
                     }
                 }).then(res=>{
                     let data = res.data;
                     if(data.code==='200'){
                         this.innerTotal = Number(data.data.pageInfo.total);
                         this.sumAmount = data.data.sumAmount;
                         this.sumTon = data.data.sumTon;
                         this.businessOrderSn = data.data.businessOrderSn;
                          data.data.pageInfo.list.map((item,index)=>{
                             item.ydsn=this.ydsn
                         });
                         this.tableData=data.data.pageInfo.list
                     }
                 })
            },

        },
        activated() {
            this.finalStatementId = this.$route.query.finalStatementId;
            this.invoiceBusinessOrderId = this.$route.query.invoiceBusinessOrderId;
            this.ydsn = this.$route.query.ydsn;
            this.editRowUserName = this.$route.query.editRowUserName
            this.getDataList()
        }
    };
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .tip {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 40px 40px 0;
        font-size: 12px;
        em {
            margin-right: 5px;
        }
    }
    .inner-box {
        margin-left: 10%;
        width: 70%;
        .upload-box {
            width: 100%;
            height: 100%;
            position: relative;
            .icon-XZ {
                width: 92px;
                height: 92px;
                margin: 0 auto;
                background-size: 100% 100%;
            }
            .icon-word {
                width: 100%;
                height: 20px;
                line-height: 20px;
                font-size: 10px;
                position: absolute;
                bottom: 25px;
                left: 0px;
                color: #cccccc;
            }
        }
    }

    .detailedList {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
            }

            .select-info {
                padding-top: 20px;
            }
            .curTotalInfo {
                padding-left: 20px;
                height: 30px;
                line-height: 30px;
                font-size: 12px;
                color: #999999;
            }
        }
        .select-time{
            background: #ffffff;
            height: 50px;
            padding-left: 10px;
            padding-top: 10px;
            margin-top: 10px;
            text-align: right;
            padding-right: 20px;
        }
        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;
            overflow: hidden;

            .list-right {
                padding-left: 10px;

                .right-title {
                    font-size: 14;
                    font-weight: 700;
                }

                .list-main {
                    width: 100%;
                    border: 1px solid #cccccc;
                    margin-top: 10px;
                }

                .paging {
                    margin-top: 10px;
                    float: right;
                }
            }
        }
    }
</style>
