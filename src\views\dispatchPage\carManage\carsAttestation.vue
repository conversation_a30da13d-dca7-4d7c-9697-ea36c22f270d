<template>
    <div class="app-container carsAttestation">
        <div class="select-box">
            <div class="top-title">
                <div>筛选查询</div>
                <div class="button">
                    <el-button
                            class="left"
                            icon="el-icon-refresh-right"
                            size="mini"
                            type="success"
                            @click="()=>{this.$router.go(0)}"
                    >刷新
                    </el-button>
                    <el-button class="left"
                               icon="el-icon-search"
                               size="mini"
                               type="primary"
                               @click="onSubmit">查询
                    </el-button>
                    <el-button class="left"
                               icon="el-icon-delete"
                               size="mini"
                               type="danger"
                               @click="resetSubmit">清空筛选
                    </el-button>
                </div>
            </div>
            <div class="select-info">
                <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="150px" size="mini">
                    <el-form-item label="车牌号:">
                        <el-input v-model="formInline.plateNumber" placeholder="请输入车牌号"></el-input>
                    </el-form-item>
                    <el-form-item label="车辆认证状态:">
                        <el-select v-model="formInline.authStatus"  @change="authStatusChange">
                            <el-option label="不限" value=""></el-option>
                            <el-option label="未认证" value="0"></el-option>
                            <el-option label="认证成功" value="1"></el-option>
                            <el-option label="认证失败" value="2"></el-option>
                            <el-option label="认证中" value="3"></el-option>
                            <el-option label="证件过期" value="4"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="所属车队长:">
                        <el-input :disabled="isDisabled"
                                  v-model="boss" placeholder="请输入车队长名称"></el-input>
                    </el-form-item>
                    <el-form-item label="">
                        <el-radio :disabled="isRadioDisabled" v-model="resource" label="100">无车队长</el-radio>
                    </el-form-item>
                </el-form>
                <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="150px"
                         style="margin-top: 30px" size="mini">
                    <el-form-item label="创建日期:">
                        <el-date-picker
                                :clearable="false"
                                @blur="selectTime"
                                v-model="formInline.date"
                                format="yyyy 年 MM 月 dd 日"
                                value-format="yyyy-MM-dd"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="list-box">
            <div class="list-title">
                <div>数据列表</div>
                <div>
                    <el-button size="small" type="primary" @click="addCar">新增车辆</el-button>
                </div>
            </div>
            <div class="list-main">
                <template>
                    <el-table
                            :data="tableData"
                            border
                            :row-class-name="tableRowClassName"
                            style="width: 100%">
                        <el-table-column
                                prop="num"
                                label="序号"
                                type="index"
                                width="50">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="plateNumber"
                                label="车牌号"
                                width="220"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="carModelName"
                                label="车型"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="sumCapacityTonnage"
                                label="总重量"
                                width="150"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="capacityTonnage"
                                label="载重"
                                width="150"
                        >
                        </el-table-column>
                        <el-table-column
                                prop="axleNumber"
                                label="轴数"
                                width="50"
                        >
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="ownerName"
                                label="所属车队长"
                                width="120">
                        </el-table-column>
                        <el-table-column
                                prop="authStatus"
                                :formatter="authStatus"
                                label="车辆认证状态"
                                width="120">
                        </el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="createdDate"
                                label="创建日期"
                                width="220"
                        >
                        </el-table-column>
                        <el-table-column
                                fixed="right"
                                label="操作"
                                width="100">
                            <template slot-scope="scope">
                                <el-button type="text" size="small" @click="goAttestation(scope.row)">审核</el-button>
                                <el-button type="text" size="small" @click="goEditCar(scope.row)">修改</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </div>
            <div class="pagination" style="float: right;margin-top: 20px">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 40,50,100,200]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total=total>
                </el-pagination>
            </div>
        </div>
    </div>
</template>
<script>
    export default {
        name: 'CarsAttestation',
        data() {
            return {
                isDisabled:false,
                isRadioDisabled:false,
                boss: '',
                resource:'',
                formInline: {
                    plateNumber: '',
                    authStatus: '3',
                    date: '',
                },
                tableData: [],
                currentPage: 1,
                pageSize: 20,
                total: 1,
                startTime: '',
                endTime: '',
            }
        },
        methods: {
            addCar() {
                this.$router.push('/carsList/addCar')
            },
            goEditCar(row) {
                // let editCarId = row.id;
                // sessionStorage.setItem('detailCarId',editCarId);
                this.$router.push({
                    path:'/carsList/carRevise',
                    query:{
                        carId:row.id
                    }
                })
            },
            goAttestation(row) {
                // let carsAttestationId = row.id;
                // sessionStorage.setItem('carsAttestationId',carsAttestationId);
                this.$router.push({
                    path:'/carsAttestation/attestationExamine',
                    query:{
                        carId:row.id
                    }
                })
            },
            onSubmit() {
                this.currentPage = 1;
                this.getDataList()
            },
            /** 车辆认证状态 **/
            authStatus(row) {
                if (row.authStatus === '0') {
                    return '未认证'
                } else if (row.authStatus === '1') {
                    return '认证成功'
                } else if (row.authStatus === '2') {
                    return '认证失败'
                } else if (row.authStatus === '3') {
                    return '认证中'
                }
            },
            tableRowClassName ({ row, rowIndex }) {
                if (row.authStatus === '3'||row.authStatus === '0') {
                    return 'inAudit-row '; //认证中 未认证
                } else if (row.authStatus === '2') {
                    return 'success-row'; // 认证失败
                } else if (row.authStatus === '1') {
                    return 'warning-row'; //认证成功
                }
                return '';
            },
            /** 根据车辆认证状态搜索 **/
            authStatusChange(value) {
                this.formInline.authStatus = value
            },
            /** 根据时间态搜索 **/
            selectTime() {
                let startTime = this.formInline.date[0]
                let endTime = this.formInline.date[1]
                this.startTime = startTime
                this.endTime = endTime
            },
            /** 分页方法 **/
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1;
                this.getDataList()
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getDataList()
            },
            /** 清空搜索选项 **/
            resetSubmit() {
                this.formInline = {
                    plateNumber: '',
                    authStatus: '',
                    date: '',
                };
                this.currentPage = 1;
                this.pageSize = 10;
                this.total = 1;
                this.startTime = '';
                this.endTime = '';
                this.boss = '';
                this.resource = '';
                this.getDataList()
            },

            /** 拉取列表数据 **/
            getDataList() {
                this.$http.get('/admin-center-server/car/auth/manager/list', {
                    params: {
                        pageNumber: this.currentPage,
                        pageSize: this.pageSize,
                        authStatus: this.formInline.authStatus,
                        boss: this.boss,
                        startTime: this.startTime,
                        endTime: this.endTime,
                        isFree: '',
                        mobile: '',
                        order: '',
                        plateNumber: this.formInline.plateNumber,
                        existCarBoss:this.resource,
                    }
                }).then(res => {
                    let data = res.data
                    if (data.code === '200') {
                        this.tableData = data.data.list
                        this.total = Number(data.data.total)
                    }
                })
            },
        },
        activated() {
            this.getDataList()
        },
        watch: {
            boss(val) {
                if (val === '') {
                    this.isDisabled = false;
                    this.isRadioDisabled = false;
                    this.resource=''
                }else if(val!==''){
                    this.isRadioDisabled = true;
                }
            },
        }
    }
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
    .el-form-item {
        margin-bottom: 0;
    }

    .select-box {
        /*height: 260px;*/
        background-color: #ffffff;

        .top-title {
            font-size: 16px;
            font-weight: 700;
            height: 40px;
            line-height: 40px;
            padding-left: 10px;
            border-bottom: 1px solid #cccccc;
            display: flex;
            justify-content: space-between;

            .button {
                margin-right: 20px;
            }
        }

        .select-info {
            padding-top: 30px;
            padding-bottom: 30px;
        }
    }

    .carsAttestation {


        .list-box {

            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;
            overflow: hidden;

            .list-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                div {
                    height: 38px;
                    line-height: 38px;
                }
            }

            .list-main {
                width: 100%;
                border: 1px solid #cccccc;
                margin-top: 10px;
            }
        }
    }
</style>
