import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import MessageBox from 'element-ui/packages/message-box/src/main'
import { getToken } from '@/utils/auth'
import { grayFn } from './index.js'
let base_url = process.env.VUE_APP_BASE_API

// if (process.env.NODE_ENV === 'production') {

//   base_url = 'https://dev.hongfeida.cn'
//   // base_url = 'http://127.0.0.1:7015'

// } else if (process.env.NODE_ENV === 'development') {
//   base_url = '/api'
// }
// create an axios instance
const service = axios.create({
  baseURL: base_url, // api 的 base_url
  timeout: 1000*30 // request timeout
});

// request interceptor
service.interceptors.request.use(
  config => {
    // 登录页也加上灰度标识
    if(grayFn()){
      config.headers['Env'] = 'gray'
    }
    // Do something before request is sent
    if (store.getters.token) {
      // 让每个请求携带token-- ['X-Token']为自定义key 请根据实际情况自行修改
      config.headers['X-Token'] = getToken()
    }
    return config
  },
  error => {
    // Do something with request error
    console.log(error) // for debug
    Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  response => response,

  response => {
    const res = response.data
    if (res.code !== 200) {
      Message({
        message: res.message,
        type: 'error',
        duration: 5 * 1000
      });
      return Promise.reject('error')
    } else {
      return response.data
    }
  },
  error => {
    if(typeof (error.response) !=='undefined'){
      if (error.response.code === 403) {
        Message({
          message: '请重新登录',
          type: 'info',
          duration: 5 * 1000
        });
        setTimeout(function () {
          store.dispatch('LogOut').then(()=>{
            location.reload()
          })
        },1000)
      }else {
        Message({
          message:'服务器错误',
          type: 'error',
          duration: 5 * 1000
        })
      }
    }
    return Promise.reject(error)
  }
)

export default service
