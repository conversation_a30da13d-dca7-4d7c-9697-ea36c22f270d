<template>
  <div class="app-container">
    <el-form
      ref="searchForm"
      :model="search"
      :inline="true"
      class="search"
      size="mini"
    >
      <el-form-item label="车牌号：" prop="orderCreateUserName">
        <el-input
          placeholder="请输入车牌号"
          v-model="search.plateNumber"
        ></el-input>
      </el-form-item>
      <el-form-item label="到期证件类型：" prop="type">
        <el-select v-model="search.type" placeholder="请选择">
          <el-option label="行驶证" value="1"></el-option>
          <el-option label="道路运输证" value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="mini" @click="doSearch" type="primary">查询</el-button>
        <el-button size="mini" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="wrapper">
      <el-table
        :data="data"
        class="table"
        style="width: 100%"
        cell-class-name="table_cell_gray"
        header-cell-class-name="table_header_cell_gray"
      >
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column label="车牌号" prop="carNumber"></el-table-column>
        <el-table-column label="车辆类型" prop="carModel"></el-table-column>
        <el-table-column label="行驶证档案编号" prop="licenseNumber"></el-table-column>
        <el-table-column label="行驶证到期时间" prop="licenseExpire">
          <template slot-scope="scope">
           <span>{{scope.row.licenseExpire}}</span><span class="warning">{{getWaringDays(scope.row.licenseExpire)}}</span>
          </template>
        </el-table-column>
        <el-table-column label="道路运输证号" prop="shippingCert"></el-table-column>
        <el-table-column label="道路运输证到期时间" prop="shippingCertExpire">
          <template slot-scope="scope">
           <span>{{scope.row.shippingCertExpire}}</span><span class="warning">{{getWaringDays(scope.row.shippingCertExpire)}}</span>
          </template>
        </el-table-column>
        <el-table-column label="注册时间" prop="registrationDate"></el-table-column>

      </el-table>
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :total="total"
        :current-page.sync="page.pageNumber"
        @current-change="getList"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';

export default {
  data() {
    return {
      page: {
        pageSize: 10,
        pageNumber: 1,
      },
      isSearching: false,
      search: {
        type: "",
        plateNumber: "",
      },
      data: [],
      total: 0
    };
  },
  activated() {
    this.getList();
  },
  methods: {
    dayjs,
    getList() {
      let params = {
        ...this.page,
      };
      if (this.isSearching) {
        Object.assign(params, this.search);
      }
      this.$post("/admin-center-server/certificateWarning/queryCarCertificateWarningPage", params)
        .then((res) => {
          this.data = res.list;
          this.total = Number(res.total);
        })
        .catch(() => {
          this.data = [];
          this.total = 0;
        });
    },
    handleSizeChange(v) {
      this.page.pageSize = v;
      this.getList();
    },
    doSearch() {
      this.isSearching = true;
      this.page.pageNumber = 1;
      this.getList();
    },
    reset() {
      this.isSearching = false;
      this.$refs.searchForm.resetFields();
      this.search = {
        type: "",
        plateNumber: "",
      }
      this.page.pageNumber = 1;
      this.getList();
    },
    getWaringDays(date) {
      if(!date) {
          return
      }
      const currentDate = dayjs().format('YYYY-MM-DD')
      const cDate = dayjs(date)
      const days = cDate.diff(currentDate, 'day')
      if (days < 0) {
          return '(已到期)'
      } 
      if (days <= this.warningDays) {
          return `(${days}天后到期）`
      }
      return ''
    }
  },
};
</script>

<style scoped lang="scss">
.search {
  padding: 20px;
  background: #fff;
}
.wrapper {
  margin-top: 10px;
  background: #fff;
}
.el-pagination {
  margin: 10px 0;
  text-align: right;
}
.warning {
  color: red;
}
</style>