<template>
  <div class="userDeal">
    <div class="select-box">
      <div class="select-info">
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          label-width="100px"
          size="mini"
        >
          <el-form-item label="订单号:">
            <el-input
              class="form-item-content-width"
              v-model="formInline.orderSn"
              placeholder="请输入订单号"
            ></el-input>
          </el-form-item>
          <el-form-item label="运单号:">
            <el-input
              class="form-item-content-width"
              v-model="formInline.waybilSn"
              placeholder="请输入运单号"
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="!$store.state.user.userInfo2.hasStandardModeFlag"
            label="收支类型:"
          >
            <el-select
              class="form-item-content-width"
              v-model="formInline.flag"
              placeholder="不限"
              style="width: 180px"
            >
              <el-option label="收入" value="1"></el-option>
              <el-option label="支出" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="交易类型:">
            <el-select
              class="form-item-content-width"
              v-model="formInline.type"
              @visible-change="handlePayTypeSelectorClick"
              placeholder="不限"
              style="width: 180px"
            >
              <el-option
                v-for="item in payTypeList"
                :key="item.value"
                :label="item.comment"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户类型:">
            <el-select
              class="form-item-content-width"
              v-model="formInline.userType"
              placeholder="不限"
              style="width: 180px"
            >
              <el-option label="货主" value="1"></el-option>
              <el-option
                v-if="!$store.state.user.userInfo2.hasStandardModeFlag"
                label="调度员"
                value="2"
              ></el-option>
              <el-option label="司机" value="3"></el-option>
              <el-option
                v-if="!$store.state.user.userInfo2.hasStandardModeFlag"
                label="渠道"
                value="4"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户名称:">
            <el-input class="form-item-content-width" placeholder="请输入用户名称" v-model="formInline.userName"> </el-input>
          </el-form-item>
          <el-form-item label="用户手机号:">
            <el-input
              class="form-item-content-width"
              v-model="formInline.userMobile"
              placeholder="请输入用户手机号"
              maxlength="11"
            ></el-input>
          </el-form-item>
          <el-form-item label="流水号:">
            <el-input
              class="form-item-content-width"
              v-model="formInline.transactionSn"
              placeholder="请输入流水号"
            ></el-input>
          </el-form-item>
          <el-form-item label="交易时间:">
            <el-date-picker
              v-model="date"
              :clearable="false"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
              style="margin-left: 20px"
              icon="el-icon-search"
              type="primary"
              @click="onSubmit"
              size="mini"
              >查询</el-button
            >
            <el-button
              icon="el-icon-delete"
              type="danger"
              @click="clearForm"
              size="mini"
              >清空筛选</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
        <div class="list-title-right">
          <div v-if="dataSum" style="font-size: 14px">
            当前列表中'交易金额'合计：￥{{ dataSum }}
          </div>
          <el-button
            type="primary"
            plain
            style="margin-left: 10px; margin-right: 10px"
            @click="getSum"
            >计算合计交易金额</el-button
          >
          <el-dropdown trigger="click">
            <el-button type="primary">导出</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <div @click="exportRow" type="text">导出Excel</div>
              </el-dropdown-item>
              <el-dropdown-item>
                <div @click="exportPdf" type="text">导出PDF电子回单</div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      <div class="list-main">
        <template>
          <el-table
            :data="tableData"
            border
            style="width: 100%"
            class="table"
            cell-class-name="table_cell_gray"
            header-cell-class-name="table_header_cell_gray"
            @selection-change="handleSelectionChange"
          >
            <el-table-column fixed type="index" label="序号" width="50"> </el-table-column>
            <el-table-column fixed prop="transactionSn" label="流水号" width="230"></el-table-column>

            <el-table-column show-overflow-tooltip prop="amount" label="交易金额(元)" width="120" ></el-table-column>
            <el-table-column show-overflow-tooltip prop="mark" label="备注" width="220">  </el-table-column>
            <el-table-column show-overflow-tooltip prop="typeStr"  label="交易类型" width="120" > </el-table-column>
            <el-table-column  prop="flag" label="收支类型" :formatter="transactionType"  >  </el-table-column>
            <el-table-column  prop="name"  show-overflow-tooltip label="用户名称"  width="180"  >  </el-table-column>
            <el-table-column show-overflow-tooltip  prop="mobile" label="手机号"  width="120"  >  </el-table-column>
            <el-table-column  prop="dType" label="用户类型"  :formatter="dTypeType"  width="120" >  </el-table-column>
            <el-table-column show-overflow-tooltip prop="payerName" label="付款人" width="120" > </el-table-column>
            <el-table-column show-overflow-tooltip prop="payerAccount" label="付款账号" width="120" ></el-table-column>
            <el-table-column show-overflow-tooltip prop="payerBankName" label="付款人开户行" width="120" ></el-table-column>
            <el-table-column show-overflow-tooltip prop="payeeName" label="收款人" width="120" > </el-table-column>
            <el-table-column show-overflow-tooltip prop="payeeAccount" label="收款账户" width="120" ></el-table-column>
            <el-table-column show-overflow-tooltip prop="payeeBankName" label="收款人开户行" width="120" ></el-table-column>
            <el-table-column show-overflow-tooltip prop="time" label="交易时间" width="180"></el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
            <template slot-scope="scope">
                <el-button @click="handleClick(scope.row)" type="text" size="small">详情</el-button>
            </template>
            </el-table-column>
          </el-table>
        </template>
      </div>
      <div class="paging">
        <div class="block">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pageNumber"
            :page-sizes="[10, 20, 30, 40, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { checkLess3Month } from "@/utils/date";
export default {
  data() {
    return {
      loading: false,
      dialogVisible: false,
      formInline: {},
      tableData: [],
      multipleSelection: [],
      total: 1,
      pageSize: 20,
      pageNumber: 1,
      date: [],
      payTypeList: [],
      exportData: {},
      dataSum: "",
      currentBaseId: ''
    };
  },
  props: ['baseId'],
  watch: {
    baseId(newVal, oldVal) {
      this.formInline = {};
      this.pageNumber = 1
      this.currentBaseId = newVal
      this.getDataList()
    }
  },
  methods: {
    /** 多选 **/
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    exportCheck() {
      //日期校验
      if (
        !this.exportData.startTime ||
        !checkLess3Month(this.exportData.startTime, this.exportData.endTime)
      ) {
        this.$message.warning("请按照“交易时间”筛选数据，时间范围最长为3个月");
        return false;
      }
      // if (this.multipleSelection.length === 0) {
      //     this.$message.warning('至少选择一条')
      //     return false
      // }
      return true;
    },
    exportRow() {
      if (!this.exportCheck()) {
        return;
      }

      this.$post(
        "/admin-center-server/transaction/flow/exportTransaction",
        this.exportData
      ).then((res) => {
        this.$message.success(
          "正在导出，稍后您可在【导出任务】中查看导出进度及导出文件"
        );
      });
    },
    exportPdf() {
      if (!this.exportCheck()) {
        return;
      }

      this.$post(
        "/admin-center-server/transaction/flow/exportPdfReceipt",
        this.exportData
      ).then((res) => {
        this.$message.success(
          "正在导出，稍后您可在【导出任务】中查看导出进度及导出文件"
        );
      });
    },
    /** 费用类型 **/
    costType(row) {
      if (row.type === "0") {
        return "充值";
      } else if (row.type === "1") {
        return "提现";
      } else if (row.type === "2") {
        return "解冻";
      } else if (row.type === "3") {
        return "冻结";
      } else if (row.type === "4") {
        return "运单结算";
      } else if (row.type === "5") {
        return "平台手续费";
      } else if (row.type === "6") {
        return "平台服务费";
      } else if (row.type === "7") {
        return "会员间交易";
      } else if (row.type === "8") {
        return "运费代收";
      } else if (row.type === "9") {
        return "运费转出";
      } else if (row.type === "10") {
        return "销户转账";
      } else if (row.type === "11") {
        return "油气结算";
      }
    },
    /** 交易类型 **/
    transactionType(row) {
      if (row.flag === "1") {
        return "收入";
      } else if (row.flag === "2") {
        return "支出";
      }
    },
    /** 用户类型 **/
    dTypeType(row) {
      if (row.dtype === "1") {
        return "货主";
      } else if (row.dtype === "2") {
        return "调度员";
      } else if (row.dtype === "3") {
        return "司机";
      } else if (row.dtype === "4") {
        return "渠道";
      }
    },
    /** 清空筛选 **/
    clearForm() {
      this.formInline = {};
      this.date = [];
      this.getDataList();
    },
    /** 按条件查询 **/
    onSubmit() {
      this.pageNumber = 1;
      this.getDataList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNumber = 1;
      this.getDataList();
    },
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.getDataList();
    },
    getDataList() {
      let params = {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        ...this.formInline,
      };
      if (this.date && this.date.length !== 0) {
        params.startTime = this.date[0];
        params.endTime = this.date[1];
      }
      params.baseId = this.currentBaseId
      if (this.$store.state.user.userInfo2.hasStandardModeFlag) {
        this.$post(
          "/admin-center-server/transaction/flow/list/temp",
          params
        ).then((res) => {
          this.exportData = {
            ...this.formInline,
            startTime: this.date && this.date.length !== 0 ? this.date[0] : "",
            endTime: this.date && this.date.length !== 0 ? this.date[1] : "",
            userType: "3",
            type: "4",
          };
          this.tableData = res.list;
          this.total = Number(res.total);
        });
      } else {
        this.$post("/admin-center-server/transaction/flow/list", params).then(
          (res) => {
            this.exportData = {
              ...this.formInline,
              startTime:
                this.date && this.date.length !== 0 ? this.date[0] : "",
              endTime: this.date && this.date.length !== 0 ? this.date[1] : "",
            };
            this.tableData = res.list;
            this.total = Number(res.total);
          }
        );
      }
    },
    handlePayTypeSelectorClick(type) {
      if (!type) return;
      this.payTypeList = []
      this.$post(
        "/admin-center-server/transaction/flow/getTransactionDetailType"
      ).then((res) => {
        this.payTypeList = res;
        if (this.$store.state.user.userInfo2.hasStandardModeFlag) {
          this.payTypeList = res.filter((item) =>
            ["0", "1", "4"].includes(item.value)
          );
        } else {
          this.payTypeList = res;
        }
        this.$get(
          "/order-center-server/order/dict/findDictByType?dictType=subsidyDown"
        ).then((res) => {
          if (res && res.length > 0) {
            res.forEach(element => {
              this.payTypeList.push({
                comment: element.label,
                value: element.value,
              });
            });
          }
        });
      });
    },
    getSum() {
      let params = {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        baseId: this.currentBaseId,
        ...this.formInline,
      };
      if (this.date && this.date.length !== 0) {
        params.startTime = this.date[0];
        params.endTime = this.date[1];
      }
      this.$post(
        "/admin-center-server/transaction/flow/getTransactionDetailSum",
        params
      ).then((res) => {
        this.dataSum = res;
      });
    },
    openBankUrl(url) {
      window.open(url);
    },
    handleClick(item) {
      this.$router.push({
        path: 'dealDetail',
        query: {
          ...item,
          accountType: 1
        }
      })
    }
  },
  activated() {
    this.getDataList();
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.userDeal {
  .select-box {
    /*height: 260px;*/
    margin-left: -15px;
    background-color: #ffffff;
    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    overflow: hidden;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .list-title-right {
        display: flex;
      }
      div {
        height: 38px;
        line-height: 38px;
      }
    }

    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }

    .paging {
      margin-top: 10px;
      float: right;
    }
  }
}
.quick-filter {
  font-size: 14px;
  margin: -20px 0 0 30px;
  padding-bottom: 20px;
}
</style>
