<template>
  <div class="earlyWarningEmail">
    <div class="item" v-for="(item, index) in data" :key="index">
      <div class="title">
        {{ item.title }}
        <div class="title-right">
          <template v-if="isShowEdit(item)">
            <el-button v-if="!isOriginEmpty(item)" size="mini" @click="cancel(item)">取消</el-button>
            <el-button type="primary" size="mini" @click="save(item, index)">保存</el-button>
          </template>
          <template v-else>
            <i class="icon-xiugai" @click="edit(item)"></i>
          </template>
        </div>
      </div>
      <template v-if="isShowEdit(item)">
        <div class="input-item">
          <div class="input-title"><span>收件人</span>：</div>
          <div class="input">
            <el-input v-model="item.data[0]" resize="none" :rows="3" :placeholder="placeholder"></el-input>
          </div>
        </div>
        <div class="input-item">
          <div class="input-title"><span>抄送</span>：</div>
          <div class="input">
            <el-input v-model="item.data[1]" resize="none" :rows="3" :placeholder="placeholder"></el-input>
          </div>
        </div>
      </template>
      <div v-else class="content">
        <div class="input-item">
          <div class="input-title"><span>收件人</span>：</div>
          <div class="input">
            {{ item.data[0] }}
          </div>
        </div>
        <div class="input-item">
          <div class="input-title"><span>抄送</span>：</div>
          <div class="input">
            {{ item.data[1] }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      placeholder: "请使用英文分号连接邮箱，示例：<EMAIL>;<EMAIL>;<EMAIL>",
      data: [
        {
          title: '年累计收入超60W预警',
          code: 'MONITOR_YEAR_INCOME'
        },
        {
          title: '司机/车队长身份证有效期预警',
          code: 'MONITOR_EXPIRE_ID_CARD'
        },
        {
          title: '货主营业执照有效期预警',
          code: 'MONITOR_EXPIRE_COMPANY_LICENSE'
        },
        {
          title: '驾驶证有效期预警',
          code: 'MONITOR_EXPIRE_DRIVER_LICENSE'
        },
        {
          title: '行驶证有效期预警',
          code: 'MONITOR_EXPIRE_CAR_LICENSE'
        }
      ]
    }
  },
  created() {
    this.data.forEach((v, i) => {
      this.$set(v, 'data', ['', ''])
      this.$set(v, 'originData', ['', ''])
      this.$set(v, 'isEdit', false)
    })
  },
  activated() {
    this.data.forEach(v => {
      this.$http.get('/admin-center-server/base/getSetting?group=' + v.code)
        .then(res => {
          let data = res.data
          if (data.code === "200") {
            let arr = data.data
            let receiver = arr.find(item => item.code === 'receiver')
            let copy = arr.find(item => item.code === 'copyCarryer')
            this.$set(v, 'data', [receiver.value, copy.value])
            this.$set(v, 'originData', [receiver.value, copy.value])
          }
        })
    })
  },
  methods: {
    save(item, index) {
      let mailRegExp = /^[a-zA-Z0-9_\-.]+@[a-zA-Z0-9\-]+(\.[a-zA-Z0-9\-]+)+$/,
        receiver = item.data[0] || '',
        copy = item.data[1] || ''
      receiver = receiver.replace(/，/g, ',')
      copy = copy.replace(/，/g, ',')
      let receiverArr = receiver !== '' ? receiver.split(',') : [],
        copyArr = copy !== '' ? copy.split(',') : []

      //以下为填写内容合法规则，对规则取反表示不合法弹出提示：
      //收件人和抄送都符合以下两种情况之一：
      //1.规则项目数量为0
      //2.规则中的每一项都可以通过邮箱验证
      if (
        !(
          (receiverArr.every(item => item.match(mailRegExp)) || receiverArr.length === 0)
          &&
          (copyArr.every(item => item.match(mailRegExp)) || copyArr.length === 0)
        )
      ) {
        this.$message.error('请输入逗号分隔的合法邮箱')
        return
      }
      let params = [
        {
          group: item.code,
          code: 'receiver',
          value: receiver
        },
        {
          group: item.code,
          code: 'copyCarryer',
          value: copy
        }
      ]
      this.$http.post('/admin-center-server/base/updateSetting', params)
        .then(res => {
          let data = res.data
          if (data.code === "200") {
            item.originData = [receiver, copy]
            item.data = [receiver, copy]
            item.isEdit = false
            this.$message.success('修改成功')
          } else {
            this.$message.warning(data.message)
          }
        })
    },
    isShowEdit(item) {
      return item.isEdit === true || this.isOriginEmpty(item)
    },
    isOriginEmpty(item) {
      return !item.originData[0] && !item.originData[1]
    },
    edit(item) {
      item.isEdit = true
    },
    cancel(item) {
      item.data = [...item.originData]
      item.isEdit = false
    }
  }
}
</script>
<style scoped lang="scss">
.earlyWarningEmail {
  margin: 10px;
  padding: 10px;
  background: #fff;
}
.item {
  margin-bottom: 30px;
}
.title {
  position: relative;
  padding-bottom: 9px;
  padding-left: 12px;
  color: #333333;
  font-size: 14px;
  line-height: 30px;
  background: #fff;
  &::after {
    content: '';
    position: absolute;
    top: 7px;
    left: 0;
    width: 4px;
    height: 14px;
    background-color: #F6A018;
    border-radius: 2px;
  }
}
.title-right {
  float: right;
}
.input-item {
  margin-bottom: 10px;
}
.input-title {
  float: left;
  margin-left: 12px;
  height: 36px;
  line-height: 36px;
  font-size: 14px;
  span {
    display: inline-block;
    text-align-last: justify;
    width: 50px;
  }
}
.input {
  margin-left: 82px;
  height: 36px;
  line-height: 36px;
}
.content {
  min-height: 75px;;
  color: #333;
  font-size: 14px;
}
.icon-xiugai {
  font-size: 16px;
  color: #F6A018;
  cursor: pointer;
}
</style>