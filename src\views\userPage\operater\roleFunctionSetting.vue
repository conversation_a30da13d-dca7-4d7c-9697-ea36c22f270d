<template>
  <div class="limit-config">
    <div class="limit-title">
      角色名称 ：{{ roleName }} 当前角色id: {{ roleId }}
    </div>
    <div class="limit-main">
      <el-tree
        :data="treeData"
        :props="defaultProps"
        ref="tree"
        node-key="id"
        default-expand-all
        show-checkbox
        :check="true"

        :default-checked-keys="defaultSelected"
        @check="handleNodeClick"
        :check-strictly="isCheckStrictly"
      >
        <div class="custom-tree-node" slot-scope="{ node, data }">
          <i v-if="data.type === '0'" class="el-icon-folder"></i>
          <i v-else-if="data.type === '1' && node.level<3" class="el-icon-document"></i>
          <i v-else-if="data.type === '2' || node.level==3" class="el-icon-thumb"></i>
          <div class="custom-label">{{ node.label }}</div>
        </div>
      </el-tree>
    </div>
    <div class="keep-box">
      <el-button class="button" type="primary" plain @click="goBack">返回</el-button>
      <el-button class="button" type="primary" @click="saveInfo">保存</el-button>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
export default {
  data() {
    return {
      canSave: true,
      roleName: "",
      roleId: "",
      data: [],
      treeData: [],
      defaultSelected: [],
      selectedIdList: [],
      sonAccount: "",
      defaultProps: {
        children: "children",
        label: "label",
      },
      isCheckStrictly: true,
    };
  },
  methods: {
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.checkedLimits.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.checkedLimits.length;
    },
    handleNodeClick(data, node, checked) {
      this.canSave = false;
      let res = this.$refs.tree
        .getCheckedKeys()
        .concat(this.$refs.tree.getHalfCheckedKeys());
        //  运单列表 下导出运单、批量操作、批量编辑按钮同步操作
        let arr = ['181','327','328']
        arr.forEach((ele)=>{
          if(data.id == ele){
            let list = arr.filter((item)=>{
              return item != ele
            })
            if(res.includes(ele)){
              this.$refs.tree.setChecked(list[0], true);
              this.$refs.tree.setChecked(list[1], true);
            }else{
              this.$refs.tree.setChecked(list[0], false);
              this.$refs.tree.setChecked(list[1], false); 
            }
          }
        })
     
      let res2 = this.$refs.tree
        .getCheckedKeys()
        .concat(this.$refs.tree.getHalfCheckedKeys());
      this.selectedIdList = res2;
    },
    getSelectedIdsFromList(arr) {
      let ids = [];
      arr.forEach((v) => {
        if (v.check === true) {
          ids.push(v.id);
        }
        if (v.children) {
          ids.push(...this.getSelectedIdsFromList(v.children));
        }
      });
      return ids;
    },
    //去掉财务管理、货主结算、运力管理，基础信息管理 二级以后的按钮
    filterButton(arr, level = 1) {
      let result = [];
      arr.forEach((v) => {
        if (v.type === "2") return;
        if (v.children) {
          if (level <= 1) {
            v.children = this.filterButton(v.children, level + 1);
          } else {
            if(v.parentId==='141'|| v.parentId==='258' ||v.parentId==='254' || v.parentId==='163'){
              delete v.children;
            }
          }
        }
        result.push(v);
      });
      return result;
    },
    /** 角色权限 **/
    getTree() {
      // this.isCheckStrictly = true;
      this.$get(
        "/admin-center-server/user/resources/getRoleResourceList?roleId=" + this.roleId
      ).then(
        (list) => {
          this.data = list;
          this.treeData = this.filterButton(cloneDeep(list));
          // this.treeData = cloneDeep(list);
          let defaultSelected = this.getSelectedIdsFromList(list);
          this.defaultSelected = defaultSelected;
          // this.isCheckStrictly = false;
        }
      );
    },
    getIdsFromList(arr) {
      let ids = [];
      arr.forEach((v) => {
        ids.push(v.id);
        if (v.children) {
          ids.push(...this.getIdsFromList(v.children));
        }
      });
      return ids;
    },
    findIdLevelLessThan2(arr, level = 1) {
      let idList = [];
      arr.forEach((v) => {
        if (v.children && level < 2) {
          idList.push(...this.findIdLevelLessThan2(v.children, level + 1));
        }
        if (v.children && level === 2 && this.selectedIdList.includes(v.id) && (v.parentId==='141'|| v.parentId==='258' ||v.parentId==='254' || v.parentId==='163')) {
          idList.push(...this.getIdsFromList(v.children));
        }
      });
      return idList;
    },
    saveInfo() {
      if (!this.selectedIdList.length) {
        this.$message.warning('请选择或取消选择您当前的功能权限')
        return
      }
      let postData = {};
      let postUrl = "";
      postUrl = "/admin-center-server/user/role/saveRoleResource"; //按角色的去权限
      postData = {
        roleId: this.roleId,
        userType: 5,
        roleName: this.roleName,
        resourcesIds: [
          ...this.selectedIdList,
          ...this.findIdLevelLessThan2(this.data),
        ],
      };
      this.$post(postUrl, postData).then((res) => {
        this.$message.success("设置成功");
        this.$router.go(-1);
      });
    },

    goBack() {
      this.$router.go(-1);
    },
  },
  mounted() {
    this.roleName = this.$route.query.name;
    this.roleId = this.$route.query.id;
    this.getTree();
  },
};
</script>
<style lang="scss" scoped>
.tree {
  margin-top: 10px;
  border-bottom: 1px solid #e9e9e9;
}

.tree1 {
  height: 40px;
  line-height: 40px;
  overflow: hidden;
  border: 1px solid #e9e9e9;
  border-bottom: none;
}

.tree1 .tree1_1 {
  float: left;
  width: 150px;
  padding-left: 10px;
}

.tree1 .tree1_2 li {
  width: 230px;
  border-left: 1px solid #e9e9e9;
  padding-left: 5px;
}

.tree1 ul {
  float: left;
  margin-left: 20px;
  height: 40px;
  line-height: 40px;
}

.tree1 ul li {
  float: left;
}
</style>
<style lang="scss" scoped>
.limit-config {
  width: 100%;
  margin: 10px;

  .limit-title {
    height: 60px;
    width: 100%;
    line-height: 60px;
    padding-left: 10px;
    font-size: 14px;
    background-color: white;
    // border-bottom: 1px solid #cccccc;
  }
  .limit-all {
    height: 60px;
    width: 100%;
    line-height: 60px;
    padding-left: 10px;
    font-size: 14px;
    background-color: #e9e9e9;
    border-bottom: 1px solid #cccccc;
    margin-top: 20px;
  }
  .limit-main {
    margin-bottom: 60px;
  }
  .keep-box {
    position: fixed;
    bottom: 0;
    right: 0;
    left: 200px;
    height: 70px;
    background-color: white;
    z-index: 99;
    .button {
      margin-left: 20px;
      margin-top: 10px;
      height: 40px;
      width: 90px;
    }
  }
}
.custom-label {
  display: inline-block;
  margin-left: 5px;
  font-size: 14px;
}
</style>
