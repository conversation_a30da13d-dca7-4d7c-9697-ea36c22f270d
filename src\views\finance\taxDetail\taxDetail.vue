<template>
  <div class="app-container">
    <div class="main-box">
      <div class="base-info">
        <div class="title-box">
          <div>费用信息</div>
          <slot></slot>
        </div>
        <div class="list-box">
          <detail-row>
            <detail-col :span="6" label="客户名称" :value="data.fbfnsrmc"></detail-col>
            <detail-col :span="6" label="货主运费" :value="data.fbfddje?data.fbfddje+'元':''"></detail-col>
          </detail-row>
          <detail-row>
            <detail-col :span="6" label="司机名称" :value="data.fwfxm"></detail-col>
            <detail-col :span="6" label="司机运费" :value="data.fwfddje?data.fwfddje+'元':''"></detail-col>
          </detail-row>
          <detail-row>
            <detail-col :span="6" label="支付方式" :value="data.zffs"></detail-col>
            <detail-col :span="6" label="支付账号" :value="data.yhkh"></detail-col>
            <detail-col :span="6" label="资金流水号" :value="data.yhls"></detail-col>
          </detail-row>
        </div>
      </div>
      <div class="base-info">
        <div class="title-box">
          <div>基本信息</div>
          <slot></slot>
        </div>
        <div class="list-box">
          <detail-row>
            <detail-col :span="6" label="订单号" :value="data.ddbh"></detail-col>
            <detail-col :span="6" label="运单号" :value="$route.query.sn"></detail-col>
            <detail-col :span="6" label="订单创建时间" :value="data.fdsj"></detail-col>
            <detail-col :span="6" label="订单结束时间" :value="data.jssj"></detail-col>
          </detail-row>
          <detail-row>
            <detail-col :span="6" label="货物名称" :value="data.hwmc"></detail-col>
            <detail-col :span="6" label="货物重量" :value="data.hwzl + '吨'"></detail-col>
          </detail-row>
        </div>
      </div>
      <div class="base-info">
        <div class="title-box">
          <div>收发信息</div>
          <slot></slot>
        </div>
        <div class="list-box">
          <detail-row>
            <detail-col :span="6" label="装车点" :value="data.sfdxxdz"></detail-col>
            <detail-col :span="6" label="装货时间" :value="data.qsfwsj"></detail-col>
          </detail-row>
          <detail-row>
            <detail-col :span="6" label="卸货点" :value="data.mddxxdz"></detail-col>
            <detail-col :span="6" label="卸货时间" :value="data.jsfwsj"></detail-col>
          </detail-row>
          <!-- <detail-row>
            <detail-col :span="6" label="装货凭证">
              <ImageUploader v-for="item in data.rkdfj" type="viewer" :defaultUrl="item" :key="item"></ImageUploader>
            </detail-col>
            <detail-col :span="6" label="卸货凭证">
              <ImageUploader v-for="item in data.thdfj" type="viewer" :defaultUrl="item" :key="item"></ImageUploader></detail-col>
          </detail-row> -->
          <detail-row>
            <detail-col :span="12" label="装货凭证" class="img-view">
              <img v-for="item in data.rkdfj" :src="item" @click="() => showedPic = item" class="thumbnail">
            </detail-col>
            <detail-col :span="12" label="卸货凭证" class="img-view">
              <img v-for="item in data.thdfj" :src="item" @click="() => showedPic = item" class="thumbnail">
            </detail-col>
          </detail-row>
        </div>
      </div>
    </div>
    <el-image-viewer v-if="showedPic" :url-list="[showedPic]" :on-close="() => showedPic = null"/>
  </div>
</template>

<script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer"
  export default {
    components: { ElImageViewer },
    activated() {
      this.$post('/admin-center-server/finance/order/getTaxSendInfo?orderItemSn=' + this.$route.query.sn)
        .then(res => {
          this.data = res
        })
    },
    data() {
      return {
        data: {},
        showedPic: null,
      }
    }
  }
</script>
<style scoped lang="scss">
.main-box {
  background-color: white;
  .title-box {
    font-size: 16px;
    font-weight: 700;
    height: 40px;
    line-height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  .list-box {
    margin-top: 20px;
    border: 1px solid #cccccc;
    border-left: none;
    .item-title {
      display: flex;
      flex-direction: row;
      div {
        width: 500px;
        height: 50px;
        font-weight: bold;
        font-size: 16px;
        line-height: 50px;
        border: 1px solid #cccccc;
        border-top: none;
        border-right: none;
        background-color: rgb(249, 252, 250);
        text-align: center;
      }
    }
    .item-info {
      display: flex;
      flex-direction: row;
      div {
        font-size: 14px;
        width: 500px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        border: 1px solid #cccccc;
        border-top: none;
        border-right: none;
        border-bottom: none;
      }
    }
  }
  .base-info {
    padding: 20px;
  }
}
.upload-wrapper {
  width: 100% !important;
}
</style>