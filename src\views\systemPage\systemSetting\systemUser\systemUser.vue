<template>
    <div class="app-container systemUser">
        <div class="select-box">
            <div class="top-title">
                <div>筛选查询</div>
                <div class="button">
                    <el-button
                            class="left"
                            icon="el-icon-search"
                            size="mini"
                            type="primary"
                            @click="onSubmit"
                    >查询
                    </el-button>
                    <el-button
                            class="left"
                            icon="el-icon-delete"
                            size="mini"
                            type="danger"
                            @click="resetSubmit"
                    >清空筛选
                    </el-button>
                </div>
            </div>

            <div class="select-info">
                <el-form :inline="true" size="mini" class="demo-form-inline" label-width="120px">
                    <el-form-item label="系统用户名称:">
                        <el-input v-model="realName" placeholder="请输入系统用户名称"></el-input>
                    </el-form-item>
                    <el-form-item label="系统用户账号:">
                        <el-input v-model="nickName" placeholder="请输入系统用户id"></el-input>
                    </el-form-item>
                    <el-form-item label="角色名称:">
                        <el-input v-model="roleName" placeholder="请输入角色名称"></el-input>
                    </el-form-item>
                    <!-- <el-form-item>
                      <el-button type="primary" @click="onSubmit" size="mini" icon="el-icon-search">查询</el-button>
                    </el-form-item>-->
                </el-form>
            </div>
        </div>
        <div class="list-box">
            <div class="list-title">
                <div></div>
                <div>
                    <el-button type="primary" icon="el-icon-plus" @click="goAdd" size="mini">新增系统用户</el-button>
                </div>
            </div>
            <el-row>
                <el-col :span="4">
                    <div class="list-left">
                        <div class="left-title">组织架构树</div>
                        <div class="tree-box">
                            <el-tree
                                    :data="treeData"
                                    highlight-curren
                                    check-strictly
                                    ref="tree"
                                    :render-content="renderContent"
                                    @node-click="handleNodeClick"
                            ></el-tree>
                        </div>
                    </div>
                </el-col>
                <el-col :span="20">
                    <div class="list-right">
                        <div class="right-title">系统用户信息表</div>
                        <div class="list-main">
                            <template>
                                <el-table :data="tableData" border style="width: 100%" height="600">
                                    <el-table-column prop="num" label="序号" type="index" width="50"></el-table-column>
                                    <el-table-column prop="realName" label="系统用户名称" width="120"></el-table-column>
                                    <el-table-column prop="nickName" label="系统用户账号" width="150"></el-table-column>
                                    <el-table-column prop="departmentName" label="部门" width="150"></el-table-column>
                                    <el-table-column prop="postName" label="职务" width="150"></el-table-column>
                                    <el-table-column prop="roleName" label="角色名称" width="150"></el-table-column>
                                    <el-table-column prop="genderName" label="性别" width="150"></el-table-column>
                                    <el-table-column prop="mobile" label="手机" width="150"></el-table-column>
                                    <el-table-column prop="isEnabledName" label="启用状态" width="150"></el-table-column>
                                    <el-table-column prop="remarks" label="备注" width="150"></el-table-column>
                                    <!-- <el-table-column
                                                              prop="date"
                                                              label="登记人"
                                                              width="150"
                                                      >
                                    </el-table-column>-->
                                    <!-- <el-table-column
                                                              prop="date"
                                                              label="登记时间"
                                                              width="150"
                                                      >
                                    </el-table-column>-->
                                    <el-table-column prop="lastLoginDate" label="最后登录时间" width="160"></el-table-column>
                                    <el-table-column fixed="right" label="操作" width="100">
                                        <template slot-scope="scope">
                                            <el-button @click="handleClick(scope.row)" type="text" size="small">编辑
                                            </el-button>
                                            <el-button type="text" size="small" @click="deleteRow(scope.row)">删除
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </template>
                        </div>
                        <div class="paging">
                            <div class="block">
                                <el-pagination
                                        @size-change="handleSizeChange"
                                        @current-change="handleCurrentChange"
                                        :current-page="currentPage"
                                        :page-sizes="[10, 20, 30, 40]"
                                        :page-size="pageSize"
                                        layout="total, sizes, prev, pager, next, jumper"
                                        :total="total"
                                ></el-pagination>
                            </div>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </div>
        <!-- 删除列表弹窗-->
        <el-dialog title="警告提示" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
            <div>确认删除吗？</div>
            <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="sureDelete">确 定</el-button>
      </span>
        </el-dialog>
        <!-- 编辑列表弹窗-->
        <el-dialog title="提示" :visible.sync="dialogChange" :before-close="handleClose" width="800px">
            <div>
                <div class="tip">
                    <div>编辑系统用户</div>
                    <div>
                        <em style="color: red">*</em>为必填项
                    </div>
                </div>
                <div class="inner-box">
                    <el-form :model="ruleForm" label-width="150px" class="demo-ruleForm">
                        <el-form-item label="系统用户名称:" required>
                            <el-input v-model="ruleForm.realName" style="width: 220px"
                                      placeholder="请输入系统用户名称"></el-input>
                        </el-form-item>
                        <el-form-item label="系统用户账号:" required>
                            <el-input v-model="ruleForm.nickName" style="width: 220px"
                                      readonly onfocus="this.removeAttribute('readonly');" autocomplete="off"
                                      placeholder="请输入系统用户账号"></el-input>
                        </el-form-item>
                        <!-- <el-form-item label="密码:" required>
                            <el-input v-model="ruleForm.pwd" style="width: 220px"
                                      maxlength="24"
                                      readonly onfocus="this.removeAttribute('readonly');" autocomplete="off"
                                      :οninput="ruleForm.pwd=ruleForm.pwd.replace(/[^\w\.\/]/ig,'')"
                                      placeholder="请输入密码"></el-input>
                        </el-form-item>
                        <el-form-item label="确认密码:" required>
                            <el-input v-model="ruleForm.checkpwd" style="width: 220px"
                                      maxlength="24"
                                      :οninput="ruleForm.checkpwd=ruleForm.checkpwd.replace(/[^\w\.\/]/ig,'')"
                                      readonly onfocus="this.removeAttribute('readonly');" autocomplete="off"
                                      placeholder="请确认密码"></el-input>
                        </el-form-item> -->
                        <el-form-item label="系统用户职位:" required>
                            <el-input v-model="ruleForm.postName" style="width: 220px"
                                      placeholder="请输入系统用户职位"></el-input>
                        </el-form-item>
                        <el-form-item label="性别:" required>
                            <el-select v-model="ruleForm.gender" placeholder="请选择性别">
                                <el-option label="男" value="0"></el-option>
                                <el-option label="女" value="1"></el-option>
                                <el-option label="保密" value="2"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="系统用户手机:" required>
                            <el-input maxlength="11" v-model="ruleForm.mobile" style="width: 220px"
                                      placeholder="请输入系统用户手机"></el-input>
                        </el-form-item>
                        <el-form-item label="备注:">
                            <el-input v-model="ruleForm.remarks" style="width: 220px" placeholder="请输入备注"></el-input>
                        </el-form-item>
                        <el-form-item label="角色名称:" prop="resource" required>
                            <div class="radioBox">
                                <el-checkbox-group v-model="ruleForm.user">
                                    <el-checkbox v-for="query in queryAll" :label="query.id">{{query.name}}
                                    </el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </el-form-item>
                        <el-form-item label="启用状态:" prop="resource" required>
                            <el-radio-group v-model="ruleForm.resource">
                                <el-radio label="1">是</el-radio>
                                <el-radio label="0">否</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item>
                            <el-button :loading="loading" type="primary" @click="submitForm" style="width: 120px">确认修改</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
        <el-button @click="dialogChange = false">取 消</el-button>
      </span>
        </el-dialog>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                loading:false,
                realName: "",
                nickName: "",
                roleName: "",
                deleteId: "",
                dialogVisible: false,
                dialogChange: false,
                currentPage: 1,
                total: 1,
                pageSize: 20,
                tableData: [
                    {
                        num: "1",
                        cardNum: "京A12345",
                        carStatus: "空闲",
                        payee: "司机",
                        curDrive: "王小狗",
                        carBelong: "无",
                        status: "已认证",
                        date: "2019-10-10"
                    }
                ],
                treeData: [],
                defaultProps: {
                    children: "children",
                    label: "label",
                    iconClass: "email"
                },
                queryAll: [],
                ruleForm: {
                    realName: "",
                    nickName: "",
                    pwd: "",
                    postName: "",
                    gender: "",
                    mobile: "",
                    remarks: "",
                    user: [],
                    resource: "",
                    checkpwd: "",
                },
                treeId: "",
                departmentId:'',
            };
        },
        methods: {
            /** 架构树 添加图标  **/
            renderContent(h, {node, data, store}) {
                return (
                    < span >
                    < i
            class
                = {data.icon} > < /i>
                    < span > {node.label} < /span>
                    < /span>
            )
                ;
            },
            /** 删除行 获取参数 **/
            deleteRow(row) {
                let id = row.id;
                this.deleteId = id;
                this.dialogVisible = true;
            },
            /** 确认删除 **/
            sureDelete() {
                let form = {
                    id: this.deleteId
                };
                this.$http.post("admin-center-server/sys/user/delete", form).then(res => {
                    let data = res.data;
                    if (data.code === "200") {
                        this.$message.success("删除成功");
                        this.dialogVisible = false;
                        this.getUserList();
                        this.getTreeData();
                    } else {
                        this.$message.warning(data.message)
                    }
                });
            },
            /** 弹窗 上叉号关闭 支持异常回调**/
            handleClose(done) {
                this.dialogVisible = false;
                this.dialogChange = false;
            },
            /** 编辑行 表单回显 **/
            handleClick(row) {
                this.dialogChange = true;
                let id = row.id;

                let form = {
                    id: id
                };
                this.$http
                    .post("/admin-center-server/sys/user/queryById", form)
                    .then(res => {
                        let data = res.data;
                        let resData = data.data;

                        let sysRole = resData.sysRole;
                        let str = [];
                        sysRole.map((item, index) => {
                            if (item === null) return
                            str.push(item.id);
                        });
                        this.ruleForm = {
                            departmentId: resData.departmentId,
                            id: resData.id,
                            realName: resData.realName,
                            nickName: resData.nickName,
                            pwd: resData.pwd,
                            checkpwd: resData.pwd,
                            postName: resData.postName,
                            gender: resData.gender,
                            mobile: resData.mobile,
                            remarks: resData.remarks,
                            user: str,
                            resource: resData.isEnabled
                        };
                    });
            },
            /** 确认修改 **/
            submitForm() {
                // let checkpwd = this.ruleForm.checkpwd;
                let form = {
                    departmentId: this.ruleForm.departmentId, //主键ID
                    id: this.ruleForm.id,
                    realName: this.ruleForm.realName,
                    nickName: this.ruleForm.nickName,
                    pwd: this.ruleForm.pwd,
                    postName: this.ruleForm.postName,
                    gender: this.ruleForm.gender, //性别
                    mobile: this.ruleForm.mobile,
                    remarks: this.ruleForm.remarks,
                    roleIds: this.ruleForm.user,
                    isEnabled: this.ruleForm.resource
                };
                {/* if (form.pwd !== checkpwd) {
                    this.$message.warning('两次输入密码不一致')
                } else if (form.departmentId === '' || form.id === '' || form.realName === "" || form.nickName === '' || form.pwd === "" || form.postName === '' || form.gender === '' || form.mobile === '' || form.roleIds === '' || form.isEnabled === '') {
                    this.$message.warning('请填写完整')
                } else if(form.pwd.length<8||checkpwd.length<8){
                    this.$message.warning('密码长度最小8位')
                }else if(!(/^[A-Z][A-z0-9]*$/).test(form.pwd)){
                    this.$message.warning('密码首字母必须大写')
                }else { */}
                    this.loading=true;
                    this.$http
                        .post("/admin-center-server/sys/user/update", form)
                        .then(res => {
                            let data = res.data;
                            if (data.code === "200") {
                                this.$message.success("修改成功");
                                this.getUserList();
                                this.loading=false;
                                this.dialogChange = false;
                            } else {
                                this.loading=false;
                                this.$message.warning(data.message)
                            }
                        });
                {/* } */}
            },
            onSubmit() {
                this.currentPage = 1;
                this.getUserList();
            },
            /** 清空搜索选项 **/
            resetSubmit() {
                this.realName = "";
                this.nickName = "";
                this.roleName = "";
                this.departmentId = '';
                this.getUserList();
            },
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1;
                this.getUserList();
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getUserList();
            },
            handleNodeClick(data) {
                let treeId = data.id;
                this.treeId = treeId;
                this.departmentId = data.id;
                this.getUserList()
            },
            goAdd() {
                if (this.treeId === "") {
                    this.$message.warning("请选择一个组织架构");
                } else {
                    sessionStorage.setItem("curPid", this.treeId);
                    this.$router.push("addSystemUser");
                }
            },
            /** 获取部门树 **/
            getTreeData() {
                this.$http.get("/admin-center-server/sys/getDepTree").then(res => {
                    let data = res.data;
                    if (data.code === "200") {
                        let tree = data.data;
                        this.treeData = tree;
                    }
                });
            },
            /** 获取用户列表 **/
            getUserList() {
                this.$http
                    .get("/admin-center-server/sys/user/queryListByPage", {
                        params: {
                            pageSize: this.pageSize,
                            pageNumber: this.currentPage,
                            realName: this.realName,
                            departmentId: this.departmentId,
                            nickName: this.nickName,
                            roleName: this.roleName
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.tableData = data.data.list;
                            this.total = Number(data.data.total);
                        }
                    });
            },
            getQueryAll() {
                this.$http.post("/admin-center-server/sys/role/queryAll").then(res => {
                    let data = res.data;
                    if (data.code === "200") {
                        this.queryAll = data.data;
                    }
                });
            }
        },
        activated() {
            this.getTreeData();
            this.getUserList();
            this.getQueryAll();
        }
    };
</script>
<style>
    .el-tree-node:focus > .el-tree-node__content {
        background-color: rgb(77,144,254) !important;
        color: #fff
    }
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
    .tip {
        display: flex;
        flex-direction: row;
        // justify-content: space-between;
        padding: 0 40px 40px 0;
        font-size: 12px;

        em {
            margin-right: 5px;
            margin-left: 20px;
        }
    }

    .inner-box {
        // margin-left: 10%;
        // width: 70%;

        .upload-box {
            width: 100%;
            height: 100%;
            position: relative;

            .icon-XZ {
                width: 92px;
                height: 92px;
                margin: 0 auto;
                background-size: 100% 100%;
            }

            .icon-word {
                width: 100%;
                height: 20px;
                line-height: 20px;
                font-size: 10px;
                position: absolute;
                bottom: 25px;
                left: 0px;
                color: #cccccc;
            }
        }
    }

    .systemUser {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
                display: flex;
                justify-content: space-between;

                .button {
                    margin-right: 20px;
                }
            }

            .select-info {
                padding-top: 30px;
                padding-bottom: 30px;
            }
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;
            overflow: hidden;

            .list-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                div {
                    height: 38px;
                    line-height: 38px;
                }
            }

            .list-left {
                .left-title {
                    font-size: 12px;
                    font-weight: 700;
                }

                .tree-box {
                    border: 1px solid #000000;
                    margin-top: 12px;
                }
            }

            .list-right {
                padding-left: 10px;

                .right-title {
                    font-size: 12px;
                    font-weight: 700;
                }

                .list-main {
                    width: 100%;
                    border: 1px solid #cccccc;
                    margin-top: 10px;
                }

                .paging {
                    margin-top: 10px;
                    float: right;
                }
            }
        }
    }
</style>
