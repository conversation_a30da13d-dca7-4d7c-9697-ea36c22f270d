<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">筛选查询</div>
      <div class="select-info">
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="110px">
          <el-form-item label="订单类型:">
            <el-select class="el-form-item-content" allow-create="" v-model="formInline.type" placeholder="请选择" size="mini" clearable>
              <el-option label="平台单" value="0"></el-option>
              <el-option label="调度单" value="1"></el-option>
              <el-option label="货主单" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="订单状态:">
            <el-select class="el-form-item-content" v-model="formInline.status" placeholder="请选择" size="mini" clearable>
              <el-option value="0" label="待确认"></el-option>
              <el-option value="1" label="待接单"></el-option>
              <el-option value="12" label="待派单"></el-option> 
              <!-- <el-option value="2" label="派车中"></el-option> -->
              <el-option value="3" label="运输中"></el-option>
              <!-- <el-option value="4" label="待充值"></el-option> -->
              <el-option value="5" label="已完成"></el-option>
              <el-option value="6" label="已取消"></el-option>
              <el-option value="7" label="已过期"></el-option>
              <el-option value="13" label="已拒单"></el-option>
              <!-- <el-option value="9" label="订单超时"></el-option> -->
              <!-- <el-option value="10" label="已拒绝接单"></el-option> -->
              <el-option value="11" label="已驳回"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="订单编号:">
            <el-input class="el-form-item-content" v-model.trim="formInline.sn" placeholder="请输入订单编号" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="找车方式:">
            <el-select class="el-form-item-content" v-model="formInline.carAssignChannel" placeholder="请选择找车方式" size="mini" clearable>
              <el-option value="1" label="自己找车"></el-option>
              <el-option value="2" label="供应商找车"></el-option>
              <el-option value="3" label="平台找车"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属项目:">
            <el-input class="el-form-item-content" v-model.trim="formInline.projectName" placeholder="请输入所属项目" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="竞标ID:">
            <el-input class="el-form-item-content" v-model.trim="formInline.competitiveBiddingCode" placeholder="请输入竞标ID" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="合作平台主体:" label-width="110px">
            <el-select class="el-form-item-content" v-model="formInline.baseId" placeholder="请选择" size="mini" clearable>
              <el-option v-for="item in $store.state.user.baseInfo" :key="item.id" :value="item.id" :label="item.baseName"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建人:">
            <el-input class="el-form-item-content" v-model.trim="formInline.orderCreateUserName" placeholder="请输入创建人姓名" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="业务类型:">
            <el-select class="el-form-item-content" v-model="formInline.businessType" size="mini">
              <el-option value="1" label="年标"></el-option>
              <el-option value="2" label="半年标"></el-option>
              <el-option value="3" label="季度标"></el-option>
              <el-option value="4" label="月标"></el-option>
              <el-option value="5" label="临调"></el-option>
              <el-option value="6" label="年标流向"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="货主名称:">
            <el-input class="el-form-item-content" v-model.trim="formInline.businessName" placeholder="请输入货主名称" size="mini"></el-input>
          </el-form-item>
          <el-form-item label="货主手机号:">
            <el-input
              class="el-form-item-content"
              v-model.trim="formInline.businessPhone"
              placeholder="请输入货主手机号"
              size="mini"
              maxlength="11"
              oninput="value=value.replace(/[^\d]/g,'')"
              @input="checkMobile"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建日期:">
            <el-date-picker
              style="width: 360px"
              @change="changeTime"
              v-model="date"
              type="datetimerange"
              start-placeholder="开始日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              range-separator="至"
              size="mini"
            ></el-date-picker>
          </el-form-item>

          <el-form-item size="mini">
            <el-button type="primary" @click="onSubmit">查询</el-button>
            <el-button class="left" @click="resetForm" icon="el-icon-refresh-right">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>订单列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table :data="tableData" 
          border 
          style="width: 100%" 
          :height="tableHeight" 
          ref="table"
          class="table"
          cell-class-name="table_cell_gray"
          header-cell-class-name="table_header_cell_gray">
            <el-table-column type="index" label="序号" width="50"></el-table-column>
            <el-table-column prop="sn" label="订单编号" width="200"></el-table-column>
            <el-table-column prop="businessName" label="货主名称" width="240"></el-table-column>
            <el-table-column prop="baseName" label="合作平台主体" width="240"></el-table-column>
            <el-table-column prop="projectName" label="所属项目">
              <template slot-scope="scope">
                {{scope.row.projectName ? scope.row.projectName : '-'}}
              </template>
            </el-table-column>
            <el-table-column prop="competitiveBiddingCode" label="竞标ID">
              <template slot-scope="scope">
                {{scope.row.competitiveBiddingCode ? scope.row.competitiveBiddingCode : '-'}}
              </template>
            </el-table-column>
            <el-table-column label="货物名称" width="200">
              <template slot-scope="scope">
                {{ scope.row.cargoTypeClassificationValue + ' / ' + scope.row.cargoType }}
              </template>
            </el-table-column>
            <el-table-column prop="paymentTypeEnum" label="支付方式"></el-table-column>
            <el-table-column prop="amount" label="司机运费" width="120"></el-table-column>
            <el-table-column prop="typeParams" label="订单类型" width="120"></el-table-column>
            <el-table-column prop="statusEnum" label="订单状态"></el-table-column>
            <!-- <el-table-column prop="isBrevityName" label="是否短倒"></el-table-column> -->
            <el-table-column prop="consignerName" label="发货人"></el-table-column>
            <el-table-column prop="consignerPhone" label="发货人手机号" width="120"></el-table-column>
            <el-table-column prop="consigneeName" label="收货人"></el-table-column>
            <el-table-column prop="consigneePhone" label="收货人手机号" width="120"></el-table-column>
            <el-table-column prop="orderCreateUserName" label="创建人" width="130"></el-table-column>
            <el-table-column prop="createdDate" label="创建时间" width="130"></el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
              <template slot-scope="scope">
                <el-button @click="goDetail(scope.row)" type="text" size="small">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="page">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInline.pageNumber"
              :page-sizes="[20, 40, 60, 80,100]"
              :page-size="formInline.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              style="margin: 10px auto"
            ></el-pagination>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CarsList",
  data() {
    return {
      formInline: {
        type: "", //订单类型
        status: "", //订单状态
        sn: "", //订单编号
        businessName: "", //客户名称
        businessPhone: "", //客户手机
        createdDate: "", //开始日期
        completeDate: "", //结束日期
        pageNumber: 1,
        pageSize: 20,
        isBrevity: "", //是否短倒   1 否 2是
        creater: "",
        baseId: "" // 选择基地
      },
      businessTypeList: [],
      tableHeight: null, //表格的高度
      date: [], //创建日期
      tableData: [],
      total: null //总数
    };
  },
  activated() {
    this.getData();
    this.tableHeight =
      window.innerHeight - this.$refs.table.$el.offsetTop - 160;
    // console.log(this.tableHeight);

    this.$get('/admin-center-server/order/dict/findDictByType', {
      dictType: "businessType"
    })
      .then(res => {
        this.businessTypeList = res
      })
  },
  methods: {
    /* 选取每页多少条数据 */
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.formInline.pageNumber = 1;
      this.formInline.pageSize = JSON.parse(`${val}`);
      // console.log(this.formInline.pageSize);
      this.getData();
    },
    /* 翻页 */
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.formInline.pageNumber = JSON.parse(`${val}`);
      // console.log(this.formInline.pageNumber);
      this.getData();
    },
    /* 按条件查询*/
    onSubmit() {
      // console.log(this.date);
      // this.formInline.createdDate = this.date[0];
      // this.formInline.completeDate = this.date[1];
      this.formInline.pageNumber = 1;
      this.getData(); //按条件查询
    },
    //重置
    resetForm() {
      this.formInline = {
        type: "", //订单类型
        status: "", //订单状态
        sn: "", //订单编号
        businessName: "", //客户名称
        businessPhone: "", //客户手机
        createdDate: "", //开始日期
        completeDate: "", //结束日期
        businessTypeValue: '',
        pageNumber: 1,
        pageSize: 20
      };
      this.date = []; //日期重置
      this.getData();
    },
    /* 跳转详情页 */
    goDetail(row) {
      console.log(row);
      // console.log(row.orderBusinessId,'----')
      this.$router.push({
        path: "orderManage/orderDetail",
        query: {
          orderBusinessId: row.orderBusinessId,
          status: row.status, //订单状态
          statusEnum: row.statusEnum, //订单状态的文字形式
          type: row.type //订单类型
        }
      });
    },
    /** 获取数据列表 **/
    getData() {
      let postData = this.formInline;
      this.$http
        .post("/admin-center-server/order/queryOrderBusinessList", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.tableData = data.data.list;
            this.total = JSON.parse(data.data.total);
          }
        });
    },
    // 输入值验证
    checkMobile() {
      this.mobile = this.mobile.replace(/[^\d]/g, "");
    },
    /* 控制时间的更改 */
    changeTime() {
      // console.log(this.date);
      if (this.date == null) {
        this.formInline.createdDate = "";
        this.formInline.completeDate = "";
      } else {
        this.formInline.createdDate = this.date[0];
        this.formInline.completeDate = this.date[1];
      }
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.el-form-item {
  margin-bottom: 0;
}

.el-form-item-content {
  width: 180px;
}

.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }
    .page {
      text-align: right;
    }
    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }
  }
}
</style>
