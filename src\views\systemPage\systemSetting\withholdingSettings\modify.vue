<template>
  <div class="app-container addSystemUser">
    <div class="inner-box">
      <el-form :model="ruleForm"
               :rules="rules"
               ref="ruleForm"
               label-width="120px"
               size="mini"
               class="demo-ruleForm">
        <el-form-item label="角色"
                      prop="type">
          <el-radio-group v-model="ruleForm.type">
            <el-radio label="司机"></el-radio>
            <el-radio label="车队长"></el-radio>
            <el-radio label="调度员"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="收入（万元）"
                      prop="region">
          <el-select v-model="ruleForm.region"
                     @change='regionfn'
                     placeholder="请选择收入">
            <el-option v-for="(item,index) in options"
                       :key="index"
                       :label="item.label"
                       :value="item.label"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="计税依据"
                      prop="calculateTaxPointBase">
          <el-input v-model="ruleForm.calculateTaxPointBase"></el-input>
        </el-form-item>
        <el-form-item label="增值税"
                      prop="valueAddedTax">
          <el-input v-model="ruleForm.valueAddedTax"></el-input>
        </el-form-item>
        <el-form-item label="城建税"
                      prop="buildingTax">
          <el-input v-model="ruleForm.buildingTax"></el-input>
        </el-form-item>
        <el-form-item label="教育附加税"
                      prop="educationSurcharge">
          <el-input v-model="ruleForm.educationSurcharge"></el-input>
        </el-form-item>
        <el-form-item label="地方教育附加税"
                      prop="localEducationSurcharge">
          <el-input v-model="ruleForm.localEducationSurcharge"></el-input>
        </el-form-item>
        <el-form-item label="个人所得税"
                      prop="personalIncomeTax">
          <el-input v-model="ruleForm.personalIncomeTax"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     @click="submitForm('ruleForm')">确认</el-button>
          <el-button @click="resetForm('ruleForm')">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
const update = '/admin-center-server/tax/tate/updateTaxRateValue'//修改
const add = '/admin-center-server/tax/tate/insert'//添加
export default {
  data () {
    return {
      type: null,
      id: '',
      options: [
        {
          value: "选项1",
          id: 4,
          code: "10",
          label: "<=10"
        },
        {
          value: "选项2",
          code: "10",
          label: '>10',
          id: 1
        }
      ],
      ruleForm: {
        calculateTaxPointBase: '',//计税依据
        valueAddedTax: '',//增值税
        buildingTax: '',//城建税
        educationSurcharge: '',//教育附加费
        localEducationSurcharge: '',
        personalIncomeTax: '',
        region: '',//收入
        type: '',//角色
        comparisonSymbol: ''
      },
      rules: {
        calculateTaxPointBase: [
          { required: true, message: '请输入计税依据', trigger: 'blur' },
          { required: true, trigger: 'change', validator: this.fuwuReg }
        ],
        valueAddedTax: [
          { required: true, message: '请输入增值税', trigger: 'blur' },
          { required: true, trigger: 'change', validator: this.fuwuReg }
        ],
        buildingTax: [
          { required: true, message: '请输入城建税', trigger: 'blur' },
          { required: true, trigger: 'change', validator: this.fuwuReg }
        ],
        educationSurcharge: [
          { required: true, message: '请输入教育附加费', trigger: 'blur' },
          { required: true, trigger: 'change', validator: this.fuwuReg }
        ],
        localEducationSurcharge: [
          { required: true, message: '请输入地方教育附加费', trigger: 'blur' },
          { required: true, trigger: 'change', validator: this.fuwuReg }
        ],
        personalIncomeTax: [
          { required: true, message: '请输入个人所得税', trigger: 'blur' },
          { required: true, trigger: 'change', validator: this.fuwuReg }
        ],
        region: [
          { required: true, message: '请选择收入', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请至少选择一个角色', trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    regionfn (selVal) {
      // console.log(selVal)
      // if (selVal =='>500') {
      //   this.ruleForm.comparisonSymbol = 1
      //   this.ruleForm.region = 500
      // }else if(selVal =='>500'){

      // }

    },
    submitForm (formName) {
      var that = this
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var v = that.ruleForm;
          var num = '';
          if (v.region == '>10') {
            v.comparisonSymbol = 1
            num = 10
          } else {
            v.comparisonSymbol = 4
            num = 10
          }
          let typeText = ''
          if (v.type == '司机') {
            typeText = '3'
          } else if (v.type == '车队长') {
            typeText = '4'
          } else if (v.type == '调度员') {
            typeText = '2'
          }
          var data = {
            "buildingTax": v.buildingTax,
            "calculateTaxPointBase": v.calculateTaxPointBase,
            "educationSurcharge": v.educationSurcharge,
            "income": num,
            'comparisonSymbol': v.comparisonSymbol,
            "isDelete": 1,
            "localEducationSurcharge": v.localEducationSurcharge,
            "personalIncomeTax": v.personalIncomeTax,
            "roles": typeText,
            "valueAddedTax": v.valueAddedTax
          }
          console.log(data)
          let api
          if (this.type === 'edit') {
            data.id = that.id
            api = update
          } else {
            api = add
          }
          this.$http.post(api, data).then(res => {
            if (res.data.code == '200') {
              this.$message({
                message: '提交成功',
                type: 'success'
              });
              setTimeout(function () {
                that.$router.go(-1);
              }, 3000);
            } else {
              this.$message.error(res.data.message);
            }

          })
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    resetForm (formName) {
      this.$refs[formName].resetFields();
    }
  },
  activated () {
    let type = this.$route.query.type
    this.type = type
    if (type === 'add') {
      this.resetForm('ruleForm')
    } else if (type === 'edit') {
      var v = this.ruleForm;
      var modifyData = JSON.parse(localStorage.getItem('modifyData'));
      console.log(modifyData)
      v.calculateTaxPointBase = modifyData.calculateTaxPointBase
      v.type = modifyData.roles
      if (modifyData.comparisonSymbol == 4) {
        v.region = '<=10'
      } else if (modifyData.comparisonSymbol == 1) {
        v.region = '>10'
        // if (modifyData.income == '10.00') {
        //   v.region = '>30'
        // } else {
        //   v.region = '>500'
        // }
      }

      v.valueAddedTax = modifyData.valueAddedTax
      v.buildingTax = modifyData.buildingTax
      v.educationSurcharge = modifyData.educationSurcharge
      v.localEducationSurcharge = modifyData.localEducationSurcharge
      v.personalIncomeTax = modifyData.personalIncomeTax
      v.comparisonSymbol = modifyData.comparisonSymbol == 0 ? '' : modifyData.comparisonSymbol
      this.id = modifyData.id
    }
  }
}
</script>
<style>
.radioBox {
  width: 410px;
  flex-wrap: wrap;
}
.radioBox .el-checkbox {
  width: 100px;
}
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
.addSystemUser {
  .tip {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 40px 40px 0;
    font-size: 12px;
    em {
      margin-right: 5px;
    }
  }
  .inner-box {
    margin-left: 10%;
    width: 70%;
    .upload-box {
      width: 100%;
      height: 100%;
      position: relative;
      .icon-XZ {
        width: 92px;
        height: 92px;
        margin: 0 auto;
        background-size: 100% 100%;
      }
      .icon-word {
        width: 100%;
        height: 20px;
        line-height: 20px;
        font-size: 10px;
        position: absolute;
        bottom: 25px;
        left: 0px;
        color: #cccccc;
      }
    }
  }
}
</style>
