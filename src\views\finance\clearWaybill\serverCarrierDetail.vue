<template>
  <div class="formModel" v-loading="loading">

    <el-form
      :model="form"
      class="demo-form-inline"
      label-width="120px"
      ref="form"
    >
      <el-row class="global-div-search">
        <el-col :span="24">
          <div class="title">服务方（车主）信息</div>
          <div class="interval-line"></div>
          <div class="interval"></div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="服务方ID" prop="fwfuuid">
            {{ form.fwfuuid || "-" }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="服务方姓名" prop="fwfxm">
            {{ form.fwfxm }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="手机号" prop="yddh">
            {{ form.yddh || "-" }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="身份证号" prop="fwfsfzjhm">
            {{ form.fwfsfzjhm || "-" }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="驾驶证号" prop="jszjhm">
            {{ form.jszjhm || "-" }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="行驶证档案编码" prop="xszjhm">
            {{ form.xszjhm || "-" }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="服务方所属地区区划代码" prop="ssdq">
            {{ form.ssdq || "-" }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="注册平台日期" prop="ptzcsj">
            {{ form.ptzcsj || "-" }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="开户人姓名" prop="khrxm">
            {{ form.khrxm || "-" }}
          </el-form-item>
        </el-col>
         <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="开户人身份证号" prop="khrsfzjhm">
            {{ form.khrsfzjhm || "-" }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="开户银行" prop="khyh">
            {{ form.khyh || "-" }}
          </el-form-item>
        </el-col>
         <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="银行卡号" prop="skzh">
            {{ form.skzh || "-" }}
          </el-form-item>
        </el-col>
         <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="身份证正面照" prop="taxThresholdStr">
            <el-button type="text" @click="openImage(form.smrzfj[0])">查看</el-button>
          </el-form-item>
        </el-col>
         <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="身份证反面照" prop="taxThresholdStr">
            <el-button type="text" @click="openImage(form.smrzfj[1])">查看</el-button>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="驾驶证照片" prop="jszfj">
            <el-button type="text" @click="openImage(form.jszfj[0])">查看</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <div class="interval"></div>
          <div class="title">承运车辆信息</div>
          <div class="interval-line"></div>
          <div class="interval"></div>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="车牌号" prop="cycph">
            {{ form.cycph || "-" }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="车辆类型" prop="cycx">
            {{ form.cycx || "-" }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="外廓尺寸" prop="cycc">
            {{ form.cycc || "-" }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="车辆载重" prop="cyzz">
            {{ form.cyzz || "-" }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="品牌型号" prop="cypp">
            {{ form.cypp || "-" }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="车辆所有人" prop="clsyr">
            {{ form.clsyr || "-" }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="行驶证照片" prop="bankLname">
            <el-button type="text" @click="openImage(form.cycxszfj[0])">查看</el-button>
          </el-form-item>
        </el-col>
         <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="车辆照片" prop="bankLname">
            <el-button type="text" @click="openImage(form.cyczpfj[0])">查看</el-button>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="是否挂靠" prop="cycsfgk">
            {{ form.cycsfgk || "-" }}
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="车主声明" prop="bankLname">
            <el-button type="text" @click="openImage(form.czsmgkfj[0])">查看</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-image-viewer style="z-index: 10000" v-if="showImageUrl" :on-close="closeOilViewer" :url-list="[showImageUrl]"></el-image-viewer>
  </div>
</template>
  
<script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer";

export default {
  components: { ElImageViewer },
  data() {
    return {
      loading: false,
      showImageUrl: '',
      form: {
        clsyr: "",
        cycc: "",
        cycph: "",
        cycsfgk: "",
        cycx: "",
        cycxszfj: [],
        cyczpfj: [],
        cypp: "",
        cyzz: "",
        czsmgkfj: [],
        fwfsfzjhm: "",
        fwfuuid: "",
        fwfxm: "",
        id: 0,
        jszfj: [],
        jszjhm: "",
        khrsfzjhm: "",
        khrxm: "",
        khyh: "",
        ptzcsj: "",
        qysmgkfj: [],
        skzh: "",
        smrzfj: [],
        ssdq: "",
        xszjhm: "",
        yddh: ""
      },
    };
  },
  mounted() {
    this.getDetail()
  },
  methods: {
    /** 获取数据详情 */
    getDetail() {
      this.$get(`/admin-center-server/tax/getFwfPage?id=${this.$route.query.id}&isToB=${this.$route.query.isToB }`).then(
        (res) => {
          this.loading = false;
          this.form = res;
        }, 
        error => {
          this.loading = false
        });
    },
    openImage(url) {
      console.log('-----0------');
      this.showImageUrl = url
    },
    closeOilViewer() {
      this.showImageUrl = ""
    },
  },
};
</script>
  
<style rel="stylesheet/scss" lang="scss" scoped>
.el-form-item {
  margin-bottom: 0;
}

.formModel {
  background-color: #ffffff;
  padding: 30px;
  position: relative;
  .come_back {
    float: right;
  }
  .list-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    div {
      height: 38px;
      line-height: 38px;
    }
  }
  .page {
    text-align: right;
  }
  .list-main {
    width: 100%;
    border: 1px solid #cccccc;
    margin-top: 10px;
  }
  .title {
    font-size: 16px;
    font-weight: bold;
  }
}

.global-div-search {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  /* font-size: 14px; */
  /* color: #555; */
  position: relative;
}

.el-select {
  width: 100% !important;
}

.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100% !important;
}
.el-input-number--medium {
  width: 100%;
}

.interval {
  width: 100%;
  padding: 10px 0px;
}

.interval-line {
  width: 100%;
  padding: 10px 0px;
  border-bottom: 1px solid rgb(238, 238, 238);
}
</style>
  