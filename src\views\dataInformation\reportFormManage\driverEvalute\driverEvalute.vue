<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">
        <div>筛选查询</div>
        <div class="button">
          <el-button
            class="left"
            icon="el-icon-search"
            size="mini"
            type="primary"
            @click="queryFun"
          >查询</el-button>
          <el-button
            class="left"
            icon="el-icon-delete"
            size="mini"
            type="danger"
            @click="resetForm"
          >清空筛选</el-button>
        </div>
      </div>
      <div class="select-info">
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="100px">
          <el-form-item label="运单号:">
            <el-input v-model.trim="formInline.orderItemCode" placeholder="请输入运单号" size="small"></el-input>
          </el-form-item>
          <el-form-item label="司机名称:">
            <el-input v-model.trim="formInline.drinvingName" placeholder="请输入司机名称" size="small"></el-input>
          </el-form-item>
          <el-form-item label="客户名称:">
            <el-input v-model.trim="formInline.customerName" placeholder="请输入客户名称" size="small"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table :data="tableData" border style="width: 100%" :height="tableHeight" ref="table">
            <el-table-column type="index" label="序号" width="50"></el-table-column>
            <el-table-column prop="sn" label="运单号" width="200"></el-table-column>
            <el-table-column prop="driverName" label="被评价司机"></el-table-column>
            <el-table-column prop="loadingOnsite" label="是否按照规定到场装货"></el-table-column>
            <el-table-column prop="seriousDeficit" label="是否有严重亏吨"></el-table-column>
            <el-table-column prop="oddPoundAnomaly" label="榜单是否正常"></el-table-column>
            <el-table-column prop="seriousConsumption" label="途耗是否严重"></el-table-column>
            <el-table-column prop="score" label="评分"></el-table-column>
          </el-table>
          <div class="page">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInline.pageNumber"
              :page-sizes="[20, 40, 60, 80,100]"
              :page-size="formInline.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              style="margin: 10px auto"
            ></el-pagination>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CarsList",
  data() {
    return {
      formInline: {
        orderItemCode: "", //运单号
        drinvingName: "", //司机名称
        customerName: "", //客户名称
        pageSize: 20,
        pageNumber: 1
      },
      tableHeight: null, //表格的高度
      tableData: [],
      total: null //总数
    };
  },
  activated() {
    this.getData();
    this.tableHeight =
      window.innerHeight - this.$refs.table.$el.offsetTop - 160;
    console.log(this.tableHeight);
  },
  methods: {
    onSubmit() {
      console.log("submit!");
    },
    /* 选取每页多少条数据 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.formInline.pageNumber =1;
      this.formInline.pageSize = JSON.parse(`${val}`);
      console.log(this.formInline.pageSize);
      this.getData();
    },
    /* 翻页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.formInline.pageNumber = JSON.parse(`${val}`);
      console.log(this.formInline.pageNumber);
      this.getData();
    },
    /** 获取数据列表 **/
    getData() {
      let postData = this.formInline;
      this.$http
        .post(
          "/admin-center-server/driver/evaluation/list",
          this.$qs.stringify(postData)
        )
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.tableData = data.data.list;
            this.total = Number(data.data.total);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 查询 */
    queryFun() {
      this.formInline.pageNumber =1;
      this.getData();
    },
    /* 清空筛选 */
    resetForm() {
      this.formInline = {
        orderItemCode: "", //运单号
        drinvingName: "", //司机名称
        customerName: "", //客户名称
        pageSize: 20,
        pageNumber: 1
      };
      this.getData();
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.el-form-item {
  margin-bottom: 0;
}

.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
      display: flex;
      justify-content: space-between;

      .button {
        margin-right: 20px;
      }
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }
    .page {
      text-align: right;
    }
    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }
  }
}
</style>
