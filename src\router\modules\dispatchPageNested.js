import Layout from '../../views/layout/Layout'

const dispatchPageNested = {
    path: '/dispatch',
    component: Layout,
    redirect: '/dispatchPage/carManage',
    name: 'dispatch',
    meta: {
        id: 22,
        title: '车辆',
        icon: 'nav-cheliang',
    },
    children: [
        {
            path: 'page',
            component: () => import('@/views/dispatchPage/carManage'),
            name: 'Page',
            meta: {
                id: 23,
                title: '车辆管理',
            },
            redirect: '/dispatchPage/carManage',
            children: [
                {
                    path: 'carsList',
                    component: () => import('@/views/dispatchPage/carManage/carsList'),
                    name: 'CarsList',
                    meta: {
                        id: 24,
                        title: '车辆管理',
                        keepAlive: true,
                    },
                },
                {
                    path: 'gpsList',
                    component: () => import('@/views/dispatchPage/carManage/gpsList'),
                    name: 'gpsList',
                    meta: {
                        id: 24,
                        title: '车辆GPS设备',
                        keepAlive: true
                    },
                    hidden: true
                },
                {
                    path: '/carsList/carDetail',
                    component: () => import('@/views/dispatchPage/carManage/listChild/carDetail'),
                    hidden: true,
                    name: 'carDetail',
                    meta: {
                        id: 24,
                        title: '车辆管理详情',
                        keepAlive: true,
                    },
                },
                {
                    path: '/carsList/lookMore',
                    component: () => import('@/views/dispatchPage/carManage/listChild/lookMore'),
                    hidden: true,
                    name: 'lookMore',
                    meta: {
                        id: 24,
                        keepAlive: true,
                        title: '更多详情'
                    }
                },
                {
                    path: '/carsList/lookMorecarTransfer',
                    component: () => import('@/views/dispatchPage/carManage/listChild/lookMorecarTransfer'),
                    hidden: true,
                    name: 'LookMorecarTransfer',
                    meta: {
                        id: 24,
                        keepAlive: true,
                        title: '转让详情'
                    }
                },
                {
                    path: '/carsList/rejectReason',
                    component: () => import('@/views/dispatchPage/carManage/listChild/carRejectReason'),
                    hidden: true,
                    name: 'carRejectReason',
                    meta: {
                        id: 24,
                        keepAlive: true,
                        title: '驳回原因管理'
                    }
                },
                {
                    path: '/carsList/carRevise',
                    component: () => import('@/views/dispatchPage/carManage/listChild/carRevise'),
                    hidden: true,
                    name: 'carRevise',
                    meta: {
                        id: 24,
                        keepAlive: true,
                        title: '修改车辆信息'
                    }
                },
                {
                    path: '/carlist/carlistExamine',
                    component: () => import('@/views/dispatchPage/carManage/carlistExamine'),
                    hidden: true,
                    name: 'CarlistExamine',
                    meta: {
                        keepAlive: true,
                        id: 24,
                        title: '车辆管理审核'
                    }
                },
                {
                    path: '/carsList/addCar',
                    component: () => import('@/views/dispatchPage/carManage/listChild/addCar'),
                    hidden: true,
                    name: 'addCar',
                    meta: {
                        id: 24,
                        title: '新增车辆',
                    }
                },
                // {
                //     path: 'carsAttestation',
                //     component: () => import('@/views/dispatchPage/carManage/carsAttestation'),
                //     name: 'CarsAttestation',
                //     meta: {
                //         // noCache: true,
                //         keepAlive: true,
                //         id: 25,
                //         title: '车辆认证管理',
                //         num: 0,
                //     }
                // },
                // {
                //     path: '/carsAttestation/attestationExamine',
                //     component: () => import('@/views/dispatchPage/carManage/attestationExamine'),
                //     name: 'AttestationExamine',
                //     hidden: true,
                //     meta: {
                //         id: 25,
                //         keepAlive: true,
                //         title: '车辆认证管理审核'
                //     }
                // },
                {
                    path: 'carsEmpower',
                    component: () => import('@/views/dispatchPage/carManage/carsEmpower'),
                    name: 'CarsEmpower',
                    meta: {
                        id: 26,
                        keepAlive: true,
                        title: '车辆审核',
                        num: 0
                    }
                },
                {
                    path: '/carsEmpower/carsEmpowerExamine',
                    component: () => import('@/views/dispatchPage/carManage/carsEmpowerExamine'),
                    name: 'CarsEmpowerExamine',
                    hidden: true,
                    meta: {
                        id: 26,
                        keepAlive: true,
                        title: '车辆驾驶授权管理审核'
                    }
                },
                // {
                //     path: 'carsBelong',
                //     component: () => import('@/views/dispatchPage/carManage/carsBelong'),
                //     name: 'CarsBelong',
                //     meta: {
                //         id: 27,
                //         keepAlive: true,
                //         title: '车辆归属授权管理',
                //         num: 0
                //     }
                // },
                {
                    path: 'carsBelong/carsBelongExamine',
                    component: () => import('@/views/dispatchPage/carManage/carsBelongExamine'),
                    name: 'CarsBelongExamine',
                    hidden: true,
                    meta: {
                        id: 27,
                        keepAlive: true,
                        title: '车辆归属授权管理审核'
                    }
                },
                {
                    path: 'carsBelong/carsInfoQuery',
                    component: () => import('@/views/dispatchPage/carManage/carInfoQuery'),
                    name: 'CarInfoQuery',
                    hidden: false,
                    meta: {
                        id: 80,
                        keepAlive: true,
                        title: '车辆信息查询'
                    }
                },
            ]
        },]
};
export default dispatchPageNested
