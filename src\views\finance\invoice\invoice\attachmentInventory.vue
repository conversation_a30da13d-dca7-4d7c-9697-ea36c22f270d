<template>
  <div class="app-container">
    <el-table :header-cell-style="{ background: 'rgb(245, 245, 249)' }" :data="data" v-loading="isLoading">
      <el-table-column label="货物货应税劳务名称" prop="hwysName"></el-table-column>
      <el-table-column label="规格型号" prop="specs"></el-table-column>
      <el-table-column label="单位">
        <template slot-scope="scope">
          <template v-if="scope.row.freightCalcType === '0'">{{ scope.row.paymentTon }}吨</template>
          <template v-else-if="scope.row.freightCalcType === '1'">1车</template>
        </template>
      </el-table-column>
      <el-table-column label="数量">
        <template slot-scope="scope">
          {{ scope.row.ton }}
          <template v-if="scope.row.freightCalcType === '0'">吨</template>
          <template v-else-if="scope.row.freightCalcType === '1'">车</template>
        </template>
      </el-table-column>
      <el-table-column label="单价（不含税）" prop="freightkNotax"></el-table-column>
      <el-table-column label="金额（不含税）" prop="amountNotax"></el-table-column>
      <el-table-column label="税率" prop="taxRate"></el-table-column>
      <el-table-column label="操作" fixed="right" width="120">
        <template slot-scope="scope">
          <el-button @click="inventoryDetail(scope.row.id)" type="text">查看清单详情</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { objToFormData } from '@/utils/tools'
export default {
  data() {
    return {
      data: [],
      isLoading: false
    }
  },
  created() {
    let data = this.$route.params.data
    if (data) {
      this.data = data
    }
    // this.id = this.$route.query.id
    // this.getList()
  },
  methods: {
    getList() {
      this.isLoading = true
      let params = objToFormData({
        invoiceNumber: this.id,
        pageSize: 10000,
        pageNumber: 1,
        status: '',
        buyName: '',
        startDate: '',
        endDate: ''
      })
      this.$http.post('/admin-center-server/bill/getInvoiceScheduleBillList', params)
        .then(res => {
          let data = res.data
          if (data.code === '200') {
            this.data = res.list
          } else {
            this.$message.error(data.message)
          }
        })
        .finally(() => {
          this.isLoading = false
        })
    },
    inventoryDetail(id) {
      this.$router.push('/finance/invoice/waybill?id=' + id)
    }
  }
}
</script>

<style scoped src="@/assets/scss/list.scss" lang="scss"></style>