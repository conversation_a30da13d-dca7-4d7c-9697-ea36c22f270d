import Vue from 'vue'
//保留2位小数
const toFixed2 = v => {
  if (typeof v !== 'number') v = Number(v)
  return v.toFixed(2)
}
const isNullValue = v => {
  if (v === null || v === undefined || v === '') return true
}

Vue.prototype.unitMoneyTableFormatter = (row, column) => {
  let value = row[column.property]
  value = toFixed2(value)
  if (isNullValue(value)) return ''
  let unit = row.freightCalcType === '0' ? '元/吨' : '元/车'
  return value + unit
}
Vue.prototype.unitTableFormatter = (row, column) => {
  let value = row[column.property]
  // value = toFixed2(value)
  if (isNullValue(value)) return ''
  let unit = row.freightCalcType === '0' ? '吨' : '车'
  if (row.freightCalcType === '1') value = parseInt(value)
  return value + unit
}
Vue.prototype.unitMoneyFormatter = (type, value) => {
  value = toFixed2(value)
  if (isNullValue(value)) return '-'
  return value + (type === '0' ? '元/吨' : '元/车')
}
Vue.prototype.unitFormatter = (type, value) => {
  // value = toFixed2(value)
  if (isNullValue(value)) return '-'
  if (type === '1') value = parseInt(value)
  return value + (type === '0' ? '吨' : '车')
}
Vue.prototype.moneyFormatter = value => {
  value = toFixed2(value)
  if (isNullValue(value)) return '-'
  return value + '元'
}
Vue.prototype.tonFormatter = value => {
  // value = toFixed2(value)
  if (isNullValue(value)) return '-'
  return value + '吨'
}
Vue.prototype.countOverTime = value => {
  if (value === undefined || value === null) return ''
  if (Number.isNaN(Number(value))) {
      return value
  } else {
      return value + '小时'
  }
}
Vue.prototype.kmFormatter = value => {
  if (value === undefined || value === null) return ''
  return value + 'km'
}
Vue.prototype.nullValueTableFormatter = (row, column) => {
  let value = row[column.property]
  if (isNullValue(value)) return '-'
  return value
}
Vue.prototype.nullValueFormatter = value => {
  if (isNullValue(value)) return '-'
  return value
}
Vue.prototype.cutoffTimeOfDateFormatter = value => {
  if(isNullValue(value) || typeof value !== 'string') return ''
  return value.slice(0, 10)
}
Vue.prototype.cutoffTimeOfDateTableFormatter = (row, column) => {
  let value = row[column.property]
  if(isNullValue(value) || typeof value !== 'string') return ''
  return value.slice(0, 10)
}