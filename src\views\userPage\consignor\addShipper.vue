<template>
  <div>
    <div class='title'>{{title}}</div>
    <el-form :model="ruleForm"
             :rules="rules"
             ref="ruleForm"
             label-width="170px"
             size="medium"
             class="demo-ruleForm">
      <el-form-item v-if='urlType!=1'
                    label="认证类型"
                    prop="authenticateType"
                    class="s-3">
        <el-select :disabled="id?true:false"
                   v-model="ruleForm.authenticateType"
                   @change="authenticateTypefn"
                   placeholder="请选择">
          <el-option v-for="(item, ind) in authenticateTypeData"
                     :label="item.name"
                     :value="item.value"
                     :key="ind"
                     ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if='urlType==1 || ruleForm.authenticateType'
                    :label="difference[0].label"
                    prop="name">
        <el-input v-if='driverType=="company" || driverType=="enterpriseBoss" || (urlType==1 && id!="")'
                  v-model="ruleForm.name"
                  :placeholder="difference[0].placeholder"></el-input>
        <el-input v-else
                  v-model="ruleForm.name"
                  :placeholder="difference[0].placeholder"></el-input>
      </el-form-item>
      <el-form-item v-if='urlType==1' label="公司地址" prop="companyAddress">
        <el-input v-model="ruleForm.companyAddress" placeholder="货主公司地址"></el-input>
      </el-form-item>
      <el-form-item v-if='ruleForm.authenticateType=="driver" || ruleForm.authenticateType=="driverBoss" || ruleForm.authenticateType=="singleBoss" || ruleForm.authenticateType=="personal"'
                    label="身份证号"
                    prop="idCard">
        <el-input v-model="ruleForm.idCard"
                  maxlength="18"
                  placeholder="18位有效数字"></el-input>
      </el-form-item>
      <el-form-item v-if='ruleForm.authenticateType=="driver" || ruleForm.authenticateType=="driverBoss" || ruleForm.authenticateType=="singleBoss" || ruleForm.authenticateType=="personal"'
                    label="身份证有效期">
        <el-checkbox
          v-model="isIdLong">长期</el-checkbox>
        <el-date-picker v-model="idDate"
          type="date"
          :disabled="isIdLong"
          placeholder="请选择截止日期"
          style="width: 100%;"
          value-format="yyyy-MM-dd"> </el-date-picker>
      </el-form-item>
      <el-form-item v-if='urlType==1 || ruleForm.authenticateType'
                    label="手机号"
                    prop="tel">
        <el-input oninput="value=value.replace(/[^\d]/g,'')"
                  maxlength="11"
                  v-model="ruleForm.tel"
                  placeholder="可作为登录名使用，支持全网号段"
                  :disabled="id"></el-input>
      </el-form-item>
      <!-- <el-form-item v-if='urlType==3 && (ruleForm.authenticateType=="driver" || ruleForm.authenticateType=="driverBoss")'
                    label="准驾车型"
                    prop="quasidriving">
        <el-select v-model="ruleForm.quasidriving"
                   placeholder="请选择">
          <el-option v-for="(item, ind) in statusWrap4"
                     :label="item.name"
                     :value="item.id"></el-option>
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item v-if='urlType==1 || ruleForm.authenticateType'
                    label="登录密码"
                    prop="pwd">
        <el-input v-model="ruleForm.pwd"
                  :disabled="id?true:false"
                  placeholder="该账户的登录密码，可输入24位任意字符。"></el-input>
      </el-form-item> -->
      <el-form-item v-if='urlType==1 || ruleForm.authenticateType=="company" || ruleForm.authenticateType=="enterpriseBoss"'
                    label="统一社会信用代码"
                    prop="code">
        <el-input v-model="ruleForm.code"
                  v-if='driverType=="company" || driverType=="enterpriseBoss" || (urlType==1 && id!="")'
                  maxlength="18"
                  placeholder="营业执照中的统一社会信用代码"></el-input>
        <el-input v-else
                  v-model="ruleForm.code"
                  maxlength="18"
                  placeholder="营业执照中的统一社会信用代码"></el-input>
      </el-form-item>
      <el-form-item v-if='urlType==1 || ruleForm.authenticateType'
                    :label="difference[1].label"
                    required
                    >
        <imageUploader
          type="upload"
          :defaultUrl="fileList[0] && fileList[0].url"
          @change="url => Logo1.value = url"></imageUploader>
      </el-form-item>
      <el-form-item
              label="司机身份证照片国徽面"
              prop="net"
              v-if="urlType == 3 && (ruleForm.authenticateType=='driver' || ruleForm.authenticateType === 'driverBoss')">
        <imageUploader
          type="upload"
          :defaultUrl="data.idCardBackImage"
          @change="url => ruleForm.idCardBackImage = url"></imageUploader>
      </el-form-item>
      <el-form-item
              label="从业资格证照片"
              v-if="urlType == 3 && (ruleForm.authenticateType=='driver' || ruleForm.authenticateType === 'driverBoss')">
        <imageUploader
          type="upload"
          :defaultUrl="data.employmentCertImage"
          @change="url => ruleForm.employmentCertImage = url"></imageUploader>
      </el-form-item>
      <el-form-item
        label="从业资格证号"
        v-if="urlType == 3 && (ruleForm.authenticateType=='driver' || ruleForm.authenticateType === 'driverBoss')">
        <el-input v-model="ruleForm.employmentCert"
                  placeholder="请输入从业资格证号"></el-input>
      </el-form-item>
      <el-form-item v-if='urlType==1 || ruleForm.authenticateType=="company" || ruleForm.authenticateType=="driver" || ruleForm.authenticateType=="driverBoss" || ruleForm.authenticateType=="enterpriseBoss"'
                    :label="difference[2].label"
                    required
                    >
        <!-- 营业执照 -->
        <imageUploader
          type="upload"
          :defaultUrl="fileList1[0] && fileList1[0].url"
          @change="changeUrlFn"></imageUploader>
      </el-form-item>
      <el-form-item v-if='urlType==1 || ruleForm.authenticateType=="company"'
                    label="法人姓名"
                    prop="legalPersonName">
        <el-input v-model="ruleForm.legalPersonName"
                  maxlength="34"
                  placeholder="请输入法人姓名"></el-input>
      </el-form-item>
      <el-form-item v-if='urlType==1 || ruleForm.authenticateType=="company"'
                    label="法人身份证号"
                    prop="legalPersonCode">
        <el-input v-model="ruleForm.legalPersonCode"
                  maxlength="18"
                  placeholder="请输入法人身份证号"></el-input>
      </el-form-item>
      <el-form-item v-if='ruleForm.authenticateType=="driver" || ruleForm.authenticateType=="driverBoss" || ruleForm.authenticateType=="singleBoss" || ruleForm.authenticateType=="personal"'
                    label="驾驶证号">
        <el-input v-model="ruleForm.drivingLicencesNumber"
                  maxlength="18"
                  placeholder="18位有效数字"></el-input>
      </el-form-item>
      <el-form-item v-if='urlType==1 || ruleForm.authenticateType=="company" || ruleForm.authenticateType=="driver" || ruleForm.authenticateType=="driverBoss" || ruleForm.authenticateType=="enterpriseBoss"'
                    :label="difference[3].label"
                    prop="date">
        <el-checkbox
                     v-model="checked">长期</el-checkbox>
        <el-date-picker v-model="ruleForm.date"
                        type="date"
                        @change="jkl"
                        :disabled="checked"
                        placeholder="请选择截止日期"
                        style="width: 100%;"
                        value-format="yyyy-MM-dd"
                        :picker-options='pickerOptions'> </el-date-picker>

      </el-form-item>
      <el-form-item v-if='urlType==1 || ruleForm.authenticateType=="company"'
                    label="注册资本"
                    prop="registeredCapital"
                    >
        <el-input v-model="ruleForm.registeredCapital"
                  placeholder="注册资本"></el-input>
      </el-form-item>
      <el-form-item v-if='urlType==1 || ruleForm.authenticateType=="company"'
                    label="成立日期"
                    prop="establishmentDate"
                    >
                    <el-date-picker v-model="ruleForm.establishmentDate"
                        type="date"
                        @change="jkl"
                        placeholder="请选择成立日期"
                        style="width: 100%;"
                        value-format="yyyy-MM-dd"
                        :picker-options='pickerOptions2'>
                        </el-date-picker>
      </el-form-item>
      <el-form-item v-if='urlType==1 || ruleForm.authenticateType=="company"'
                    label="经营范围"
                    prop="businessScope"
                    >
        <el-input v-model="ruleForm.businessScope"
                  type="textarea"
                  :rows="4"
                  placeholder="经营范围"></el-input>
      </el-form-item>
      <el-form-item v-if='urlType==1 || ruleForm.authenticateType=="company"'
                    label="主管税务机关"
                    prop="taxAuthority"
                    >
        <el-input v-model="ruleForm.taxAuthority"
                  placeholder="主管税务机关"></el-input>
      </el-form-item>
      <!-- <el-form-item v-if='urlType==1 || ruleForm.authenticateType'
                    label="是否启用账户"
                    prop='type'>
        <el-switch v-model="ruleForm.type"></el-switch>
        <p style='line-height:16px;color:#999;font-size:12px;'>注：如果选择是，当前添加账户被审核后，客户可直接使用，如果选择否，审核通过后账户将暂时为冻结状态，直至手动解冻</p>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary"
                   @click="submitForm('ruleForm')">{{submitText}}</el-button>

      </el-form-item>
    </el-form>

  </div>
</template>

<script>
import { client, OSS_REGION } from '@/utils/alioss'
import ElImageViewer from 'cf-element-ui/packages/image/src/image-viewer'
//const add = '/admin-center-server/commonUser/add'//添加
const forUpdateInfo = '/admin-center-server/commonUser/forUpdateInfo'//回显
const updateUser = '/admin-center-server/commonUser/updateUser'//编辑-添加
export default {
  components: { ElImageViewer },
  data () {
    return {
      checked: false,
      submitText: '',
      Aliyun: {},
      id: '',
      title: '',
      difference: [],
      fileList: [],
      Logo1: {
        value: ''
      },
      fileList1: [],
      Logo2: {
        value: ''
      },
      urlType: '',
      authenticateTypeData: [],
      addFlag: true,
      showViewer: false,
      showViewer1: false,
      statusWrap4: [
        {
          name: 'A2',
          id: 'A2'
        },
        {
          name: 'B2',
          id: 'B2'
        }
      ],
      ruleForm: {
        authenticateType: '',
        name: '',
        tel: '',
        code: '',
        date: '',
        idCard: '',
        quasidriving: '',
        legalPersonName: '',
        legalPersonCode: '',
        drivingLicencesNumber: '',
        employmentCert: '',
        companyAddress: ''
      },
      rules: {
        authenticateType: [{ required: true, message: '请选择认证状态', trigger: 'change' }],
        quasidriving: [{ required: true, message: '请选择准驾车型', trigger: 'change' }],
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { min: 1, max: 34, message: '长度在 1 到 34 个字符', trigger: 'change' }
        ],
        companyAddress: [
          { required: true, message: '请输入货主公司地址', trigger: 'blur' }
        ],
        idCard: [{ required: true, message: '请输入身份证号', trigger: 'blur' },
        { required: true, trigger: 'blur', validator: this.isCardNo }],
        tel: [
          { required: true, message: '请输入客户电话', trigger: 'blur' },
          { min: 8, max: 11, message: '手机号格式不正确', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: this.telReg }
        ],
        code: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
          { required: true, trigger: 'blur', validator: this.socialCreditCode },
        ],
        date: [
          { required: true,
            validator: (rule, value, cb) => {
              if (!value && !this.checked) {
                cb('请选择期限')
              }
              cb()
            }
          }
        ],
        legalPersonName: [
          { required: true, message: '请输入法人名称，最多34个字符', trigger: 'blur' },
          { min: 1, max: 34, message: '长度在 1 到 34 个字符', trigger: 'blur' }],
        legalPersonCode: [{ required: true, message: '请输入法人身份证号', trigger: 'blur' },
        { required: true, trigger: 'blur', validator: this.isCardNo }],
        registeredCapital: [
          { required: true, message: '请输入注册资本', trigger: 'blur' }
        ],
        establishmentDate: [
          { required: true, message: '请选择成立日期', trigger: 'change' }
        ],
        businessScope: [
          { required: true, message: '请输入经营范围', trigger: 'blur' }
        ]
      },
      dialogVisible: false,
      dialogImageUrl: "",
      isIdLong: false,
      idDate: '',
      data: {},
      //通讯录
      pickerOptions: {
        disabledDate(now) {
          return now.getTime() <= Date.now()
        }
      },
      pickerOptions2: {
        disabledDate(now) {
          return now.getTime() >= Date.now()
        }
      }
    }  },
  methods: {
    changeUrlFn(url){
      this.Logo2.value = url
      if(this.driverType=='company'|| this.driverType=='enterpriseBoss' ||this.driverType=='personal' ||this.urlType=='1'){
        this.licenseImageOcr(url)
      }
    },
    licenseImageOcr(url) {
      // this.$set(this.form, 'licenseImage', url)
      if (!url) return
      this.$post('/order-center-server/ocr/ocrImage', {
        url,
        type: 4
      })
        .then(res => {
          if(res.businessLicenseRes){
            this.$set(this.ruleForm, 'name', res.businessLicenseRes.companyName)
            this.$set(this.ruleForm, 'companyAddress', res.businessLicenseRes.companyAddress)
            this.$set(this.ruleForm, 'code', res.businessLicenseRes.licenseNumber)
            this.$set(this.ruleForm, 'registeredCapital', res.businessLicenseRes.registeredCapital)
            this.$set(this.ruleForm, 'establishmentDate', res.businessLicenseRes.establishmentDate)
            this.$set(this.ruleForm, 'businessScope', res.businessLicenseRes.businessScope)
            this.ruleForm.enterpriseType = res.businessLicenseRes.enterpriseType || 0
          }
        })
    },
    jkl (lo) {
      console.log(lo)
    },
    getAliyunData () {
      const ossApiUrl = "/base-center-server/sts/oss_signature"; // 获取oss签名的地址
      // 获取oss签名
      this.$http.get(ossApiUrl).then(res => {
        if (res.data.code == "200") {
          let data = res.data;
          this.Aliyun = data.data;
          this.Aliyun.region = OSS_REGION;
          // return data
        }
      });
    },
    imgChange (file, fileList) {
      if (fileList.length > 1) {
        fileList.shift()
      }
    },
    Upload1 (param) {
      this.ossUpload(param, 1)
    },
    Upload2 (param) {
      this.ossUpload(param, 2)
    },
    ossUpload (param, type) {
      let file = param.file; // 文件的
      const tmpcnt = file.name.lastIndexOf('.');
      const name = file.name.substring(0, tmpcnt)
      const exname = file.name.substring(tmpcnt + 1);
      const fileName = '/' + this.Aliyun.bucket + '/' + this.Aliyun.dir + this.$md5(name) + '.' + exname;
      client(this.Aliyun).put(fileName, file).then(res => {
        if (res.res.status === 200) {
          let imgUrl = res.res.requestUrls[0];
          /*
               type==1 身份证
               type==2 营业执照
             */
          if (type == 1) {
            this.Logo1.value = imgUrl
          } else {
            this.Logo2.value = imgUrl
          }

          this.$message.success('上传成功')
        } else {
          this.$message.error(res.res.message)
        }
      })
    },
    authenticateTypefn (value) {
      console.log(value)
      this.Logo1.value = ''
      this.Logo2.value = ''
      this.fileList = [];
      this.fileList1 = [];
      if (value == 'company') {
        this.difference = [
          {
            label: '调度员名称',
            placeholder: '调度员公司全称（如含有特殊字符等不符合规范）'
          },
          {
            label: '法人身份证照片'
          },
          {
            label: '营业执照'
          },
          {
            label: '营业期限至'
          }
        ]
      } else if (value == 'personal') {
        this.difference = [
          {
            label: '调度员名称',
            placeholder: '调度员姓名（不含特殊字符）'
          },
          {
            label: '身份证照片'
          },
          {
            label: '营业执照'
          },
          {
            label: '驾驶证有效期限至'
          }
        ]
      } else if (value == 'driver') {
        this.difference = [
          {
            label: '司机名称',
            placeholder: '司机名称（如含有特殊字符等不符合规范）'
          },
          {
            label: '司机身份证照片人像面'
          },
          {
            label: '司机驾驶证照片'
          },
          {
            label: '驾驶证有效期限至'
          }
        ]
      } else if (value == 'driverBoss') {
        this.difference = [
          {
            label: '司机名称/车队长',
            placeholder: '司机名称（如含有特殊字符等不符合规范）'
          },
          {
            label: '司机身份证照片人像面'
          },
          {
            label: '驾驶证照片'
          },
          {
            label: '驾驶证有效期限至'
          }
        ]
      } else if (value == 'singleBoss') {
        this.difference = [
          {
            label: '个人车队长名称',
            placeholder: '个人车队长姓名全称（如含有特殊字符等不符合规范）'
          },
          {
            label: '身份证照片'
          },
          {
            label: ''
          },
          {
            label: ''
          }
        ]
      } else if (value == 'enterpriseBoss') {
        this.difference = [
          {
            label: '车队长公司名称',
            placeholder: '企业车队长公司全称（如含有特殊字符等不符合规范）'
          },
          {
            label: '法人身份证照片'
          },
          {
            label: '营业执照'
          },
          {
            label: '营业期限至'
          }
        ]
      }
    },
    submitForm (formName) {
      var that = this
      console.log(that.difference[0])
      if (that.difference[0].label == '司机名称') {
        var reg = /^[\u4E00-\u9FA5\uf900-\ufa2d·s]{2,34}$/
        if (!reg.test(that.ruleForm.name)) {
          that.$message.error('司机名称最多可输入34个中文和.')
          return false
        }
      }
      this.$refs[formName].validate(valid => {
        var v = that.ruleForm
        var add = '',
          data = {}
        if (valid) {
          if (this.addFlag) {
            this.addFlag = false
            var authenticateTypeNum = ''
            //1货主、2调度员、3司机
            if (that.id) {
              data = {
                userType: that.urlType,
                companyName: v.name,
                mobile: v.tel,
                organizationCode: v.code,
                id: that.id
              }
            } else {
              data = {
                //userType: that.urlType,
                companyName: v.name,

                contactPhone: v.tel,
                mobile: v.tel,
                organizationCode: v.code
              }
            }
            if (v.idCard) {
              data.idCard = v.idCard
            }

            //选择的日期不能早于当天
            // if (!this.checked) {
            //   let now = new Date(new Date().toDateString()).getTime()
            //   let selecedTime = new Date(new Date(v.date).toDateString()).getTime()
            //   if (now > selecedTime) {
            //     this.$message.error('营业期限不能小于当前日期')
            //     this.addFlag = true
            //     return false
            //   }
            // }

            if (this.urlType == 2) {
              if (v.authenticateType == 'company') {
                add = '/admin-center-server/commonUser/updateCompanyBrokerAuth'
                if (!that.Logo1.value) {
                  this.addFlag = true
                  this.$message.error('请上传法人身份证照片');
                  return false
                }
                if (!that.Logo2.value) {
                  this.addFlag = true
                  this.$message.error('请上传营业执照');
                  return false
                }
                authenticateTypeNum = 2
                data.licenseImage = that.Logo2.value ? that.Logo2.value : ''
                data.licenseExpireDate = v.date ? v.date : ''
                data.corporationIdCardImage = that.Logo1.value ? that.Logo1.value : ''
                //新增法人姓名、身份证
                data.corporationIdCard = v.legalPersonCode
                data.corporationName = v.legalPersonName
              } else if (v.authenticateType == 'personal') {
                add = '/admin-center-server/commonUser/updatePersonBrokerAuth'
                if (!that.Logo1.value) {
                  this.addFlag = true
                  this.$message.error('请上传身份证照片');
                  return false
                }
                authenticateTypeNum = 1
                data.idCardImage = that.Logo1.value ? that.Logo1.value : ''
              }
              data.brokerAuthType = authenticateTypeNum
            } else if (this.urlType == 3) {
              //企业车队长enterpriseBoss、个人车队长singleBoss、司机个人车队长driverBoss
              if (v.authenticateType == 'enterpriseBoss') {
                add = '/admin-center-server/commonUser/updateCompanyCarOwnerAuth'
                if (!that.Logo1.value) {
                  this.addFlag = true
                  this.$message.error('请上传法人身份证照片');
                  return false
                }
                if (!that.Logo2.value) {
                  this.addFlag = true
                  this.$message.error('请上传营业执照');
                  return false
                }
                data.licenseExpireDate = v.date ? v.date : ''
                data.corporationIdCardImage = that.Logo1.value ? that.Logo1.value : ''
                data.licenseImage = that.Logo2.value ? that.Logo2.value : ''
                authenticateTypeNum = 4
              } else if (v.authenticateType == 'singleBoss') {
                add = '/admin-center-server/commonUser/updatePersonCarOwnerAuth'
                if (!that.Logo1.value) {
                  this.addFlag = true
                  this.$message.error('请上传身份证照片');
                  return false
                }
                data.idCardImage = that.Logo1.value ? that.Logo1.value : ''
                authenticateTypeNum = 3
              } else if (v.authenticateType == 'driverBoss') {
                add = '/admin-center-server/commonUser/updateDriverAndCarOwnerAuth'
                data.drivenVehicleModel = v.quasidriving
                if (!that.Logo1.value) {
                  this.addFlag = true
                  this.$message.error('请上传身份证照片');
                  return false
                }
                if (!that.Logo2.value) {
                  this.addFlag = true
                  this.$message.error('请上传驾驶证照片');
                  return false
                }
                data.idCardImage = that.Logo1.value ? that.Logo1.value : ''
                data.drivingLicencesImage = that.Logo2.value ? that.Logo2.value : ''
                authenticateTypeNum = 2
                data.idCardImage = that.Logo1.value ? that.Logo1.value : ''
                data.idCardBackImage = this.ruleForm.idCardBackImage
                if (this.isIdLong) {
                  data.idCardExpireDate = '9999-12-31'
                } else {
                  data.idCardExpireDate = this.idDate
                }
                data.employmentCert = this.ruleForm.employmentCert
                data.employmentCertImage = this.ruleForm.employmentCertImage
                data.drivingLicencesNumber = this.ruleForm.drivingLicencesNumber
                data.drivingLicencesImage = that.Logo2.value ? that.Logo2.value : ''
              } else if (v.authenticateType == 'driver') {
                add = '/admin-center-server/commonUser/updateDriverAuth'
                data.drivenVehicleModel = v.quasidriving
                if (!that.Logo1.value) {
                  this.addFlag = true
                  this.$message.error('请上传司机身份证照片');
                  return false
                }
                if (!that.Logo2.value) {
                  this.addFlag = true
                  this.$message.error('请上传司机驾驶证照片');
                  return false
                }
                authenticateTypeNum = 1
                data.idCardImage = that.Logo1.value ? that.Logo1.value : ''
                data.idCardBackImage = this.ruleForm.idCardBackImage
                if (this.isIdLong) {
                  data.idCardExpireDate = '9999-12-31'
                } else {
                  data.idCardExpireDate = this.idDate
                }
                data.employmentCert = this.ruleForm.employmentCert
                data.employmentCertImage = this.ruleForm.employmentCertImage
                data.drivingLicencesNumber = this.ruleForm.drivingLicencesNumber
                data.drivingLicencesImage = that.Logo2.value ? that.Logo2.value : ''
              }
              if (that.id) {
                data.driverType = authenticateTypeNum
              }
            } else if (this.urlType == 1) {
              if (!that.Logo1.value) {
                this.addFlag = true
                this.$message.error('请上传法人身份证照片');
                return false
              }
              if (!that.Logo2.value) {
                this.addFlag = true
                this.$message.error('请上传营业执照');
                return false
              }

              data.corporationIdCardImage = that.Logo1.value ? that.Logo1.value : ''
              data.licenseImage = that.Logo2.value ? that.Logo2.value : ''
              //新增法人姓名、身份证
              data.corporationIdCard = v.legalPersonCode
              data.corporationName = v.legalPersonName
              data.companyAddress = v.companyAddress

              data.registeredCapital = v.registeredCapital
              data.establishmentDate = v.establishmentDate
              data.businessScope = v.businessScope
              data.enterpriseType = v.enterpriseType
              data.taxAuthority = v.taxAuthority
              {
              let key = 'licenseExpireDate'
              if (this.urlType ==='3' && (this.driverType === 'driverBoss' || this.driverType === 'driver')) key = 'drivingLicensesExpireDate'
              if (this.checked) {
                data[key] = '9999-12-31'
              } else {
                data[key] = v.date
              }
            }
              // console.log('this.checked',this.checked)
              // console.log('data',data)
              // return 
              if (this.id) {
                add = '/admin-center-server/commonUser/updateAuth'
              } else {
                add = '/admin-center-server/addBusinessSys'
                data.dType = '1'
                data.pwd = this.$md5('a12345678').toUpperCase()
                data.licenseExpireDate = Date.parse(data.licenseExpireDate)
              }
            }
            if (this.id) {

              this.$http.post(updateUser, data).then(res => {
                var that = this
                if (res.data.code == '200') {
                  this.$message({
                    message: res.data.message,
                    type: 'success'
                  });

                  setTimeout(function () {
                    that.$router.go(-1);
                  }, 3000);


                } else {
                  this.addFlag = true
                  this.$message.error(res.data.message);
                }
              })
            } else {
              //data.pwd = this.$md5(v.pwd).toUpperCase();

              console.log(data)
              this.$http.post(add, data).then(res => {
                var that = this
                if (res.data.code == '200') {
                  this.$message({
                    message: res.data.message,
                    type: 'success'
                  });
                  setTimeout(function () {
                    that.$router.go(-1);
                  }, 3000);
                } else {
                  this.addFlag = true
                  this.$message.error(res.data.message);
                }
              })
            }
          }


        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleRemove (file, fileList) {
      if (file.url == this.Logo1.value) {
        this.Logo1.value = ''
      } else {
        this.Logo2.value = ''
      }
      console.log(file.url);
      console.log(this.Logo1)
    },
    handlePreview (file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    // 关闭查看器
    closeViewer () {
      this.showViewer = false;
      this.showViewer1 = false;
      this.showViewer2 = false;
    },
  },
  mounted () {
    this.urlType = this.$route.query.urlType
    this.id = this.$route.query.id
    this.driverType = this.$route.query.driverType
    if (this.driverType) {
      this.authenticateTypefn(this.driverType)
      this.ruleForm.authenticateType = this.driverType
    }
    if (this.id) {
      this.submitText = '保存'
    } else {
      this.submitText = '提交认证'
    }
    if (this.id) {
      //密码为6个* ,修改时候不传
      let url = this.urlType == '3' ? (this.driverType == 'driver' ? forUpdateInfo + `?id=${this.id}` + '&type=3' : forUpdateInfo + `?id=${this.id}` + '&type=4') : forUpdateInfo + `?id=${this.id}`
      this.$http.get(url).then(res => {
        var data = this.data = res.data.data
        var v = this.ruleForm
        //v.name = data.companyName
        v.name = data.name
        v.tel = data.mobile
        v.pwd = '******'
        v.code = data.organizationCode
        v.idCard = data.idCard
        v.legalPersonName = data.corporationName
        v.legalPersonCode = data.corporationIdCard
        v.quasidriving = data.drivenVehicleModel

        v.employmentCert = data.employmentCert
        v.employmentCertImage = data.employmentCertImage
        v.drivingLicencesNumber = data.drivingLicencesNumber

        this.$set(this.ruleForm, 'registeredCapital', data.registeredCapital)
        this.$set(this.ruleForm, 'establishmentDate', data.establishmentDate)
        this.$set(this.ruleForm, 'businessScope', data.businessScope)
        this.$set(this.ruleForm, 'taxAuthority', data.taxAuthority)
        this.ruleForm.enterpriseType = data.enterpriseType
        

        if (data.idCardExpireDate === '长期') {
          this.isIdLong = true
        } else {
          this.isIdLong = false
          this.idDate = data.idCardExpireDate
        }

        if (this.urlType == 1 || (this.urlType == 2 && this.driverType == 'company')) {
          v.companyAddress = data.companyAddress
          if (data.corporationIdCardImage != null && data.corporationIdCardImage != '') {
            this.fileList = [
              {
                name: '',
                url: data.corporationIdCardImage,
              }
            ];
            this.Logo1.value = data.corporationIdCardImage
          }
          if (data.licenseImage != null && data.licenseImage != '') {
            this.fileList1 = [
              {
                name: '',
                url: data.licenseImage,
              }
            ];
            this.Logo2.value = data.licenseImage
          }
          console.log(this.Logo1.value)
          console.log(this.Logo2.value)
        } else if (this.urlType == 2 && this.driverType == 'personal') {
          if (data.idCardImage != null && data.idCardImage != '') {
            this.fileList = [
              {
                name: '',
                url: data.idCardImage,
              }
            ];
            this.Logo1.value = data.idCardImage
          }
        } else if (this.urlType == 3 && (this.driverType == 'driverBoss' || this.driverType == 'driver' || this.driverType == 'singleBoss')) {
          if (data.idCardImage != null && data.idCardImage != '') {
            this.fileList = [
              {
                name: '',
                url: data.idCardImage,
              }
            ];
            this.Logo1.value = data.idCardImage
          }
          if (data.drivingLicencesImage != null && data.drivingLicencesImage != '') {
            this.fileList1 = [
              {
                name: '',
                url: data.drivingLicencesImage,
              }
            ];
            this.Logo2.value = data.drivingLicencesImage
          }
          if (data.idCardBackImage != null && data.idCardBackImage != '') {
            this.ruleForm.idCardBackImage = data.idCardBackImage
          }
        } else if (this.urlType == 3 && this.driverType == 'enterpriseBoss') {
          if (data.corporationIdCardImage != null && data.corporationIdCardImage != '') {
            this.fileList = [
              {
                name: '',
                url: data.corporationIdCardImage,
              }
            ];
            this.Logo1.value = data.corporationIdCardImage
          }
          if (data.licenseImage != null && data.licenseImage != '') {
            this.fileList1 = [
              {
                name: '',
                url: data.licenseImage,
              }
            ];
            this.Logo2.value = data.licenseImage
          }
        }

        {
          let key = 'licenseExpireDate'
          if (this.urlType ==='3' && (this.driverType === 'driverBoss' || this.driverType === 'driver')) key = 'drivingLicensesExpireDate'
          if (data[key] == '长期') {
            this.checked = true
            v.date = null
          } else {
            v.date = data[key]
            this.checked = false
          }
        }
      })
    }
    //1货主主账号 3、


    //1 添加货主
    if (this.urlType == 1) {
      this.difference = [
        {
          label: '公司名称',
          placeholder: '货主公司全称'
        },
        {
          label: '法人身份证照片'
        },
        {
          label: '营业执照'
        },
        {
          label: '营业期限至'
        }
      ]
      this.title = '货主用户认证'
    } else if (this.urlType == 2) {
      this.title = '调度员用户认证'
      this.authenticateTypeData = [
        {
          name: '公司',
          value: 'company'
        },
        {
          name: '个人',
          value: 'personal'
        }
      ]
    } else if (this.urlType == 3) {
      this.title = '司机用户认证'
      this.authenticateTypeData = [
        {
          name: '司机',
          value: 'driver'
        },
        {
          name: '司机（个人车队长）',
          value: 'driverBoss'
        },
        {
          name: '个人车队长',
          value: 'singleBoss'
        },
        {
          name: '企业车队长',
          value: 'enterpriseBoss'
        }
      ]
    }


  },
  created () {
    this.getAliyunData();
  }

}
</script>

<style lang="scss" scoped>
.demo-ruleForm {
  width: 500px;
  margin-top: 20px;
}
.upload-box {
  width: 100%;
  height: 100%;
  /*border: 1px solid #00cb8a;*/
  position: relative;

  .icon-XZ {
    width: 92px;
    height: 92px;
    margin: 0 auto;
    background: url("./images/xiazai.png") no-repeat;
    background-size: 100% 100%;
  }

  .icon-word {
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 10px;
    position: absolute;
    bottom: 25px;
    left: 0px;
    color: #cccccc;
  }
}
.title {
  font-size: 16px;
  margin: 20px 0 0 20px;
  color: #1898ff;
}
.big-img {
  float: right;
  margin-right: -100px;
  margin-top: -30px;
}
</style>

