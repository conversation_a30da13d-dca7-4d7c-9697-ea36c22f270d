<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
<!--      <keep-alive :include="cachedViews">-->
<!--        <router-view :key="key" />-->
<!--      </keep-alive>-->
      <keep-alive v-if="$route.meta.keepAlive">
        <router-view :key="key" />
      </keep-alive>
      <router-view v-if="!$route.meta.keepAlive" :key="key" />
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    cachedViews() {
      // console.log(this.$store.state.tagsView.cachedViews);
      // console.log(this.$route.fullPath);
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.fullPath
    }
  }
}
</script>

<style scoped>
.app-main {
  flex-grow: 1;
  overflow-y: auto;
  /*84 = navbar + tags-view = 50 +34 */
  /* height: calc(100vh - 84px); */
  height: 0;
  width: 100%;
  position: relative;
  /* overflow: hidden; */
}
</style>

