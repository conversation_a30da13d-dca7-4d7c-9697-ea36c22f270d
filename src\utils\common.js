export default {
  install(Vue) {
    //中文加.
    Vue.prototype.chineseReg = function (rule, value, callback) {
      var reg = /^[\u4E00-\u9FA5\uf900-\ufa2d·s]{2,34}$/
      console.log(!reg.test(value))
      if (!reg.test(value)) {
        callback(new Error('最多可输入34个字符和.'))
      } else {
        callback()
      }
    }
    //小数点后两位,支持负数
    Vue.prototype.lastTwo = function (rule, value, callback) {
      var reg = /(^-?(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d{1,2})?$)/
      if (!reg.test(value)) {
        callback(new Error('小数点后两位'))
      } else {
        callback()
      }
    }
    //小数点后两位 
    Vue.prototype.lastTwos = function (rule, value, callback) {
      var reg = /^(\d+)(.\d{0,2})?$/
      if (!reg.test(value)) {
        callback(new Error('小数点后两位'))
      } else if (value > 100) {
        callback(new Error('最大为100'))
      } else {
        callback()
      }
    }
    //货物单价限制 0.1-1000000.0
    Vue.prototype.unitPrice = function (rule, value, callback) {
      var reg = new RegExp('^(([1-9][0-9]{0,2}|0)(.[1-9])?|1000000.0|1000000)$')
      if (!reg.test(value) || value == 0) {
        callback(new Error('货物单价限制0.1-1000000.0'))
      } else {
        callback()
      }
    }
    //途耗限制0.1至1000.0
    Vue.prototype.consumption = function (rule, value, callback) {
      var reg = new RegExp('^(([1-9][0-9]{0,2}|0)(.[1-9])?|1000.0|1000)$')
      if (!reg.test(value) || value == 0) {
        callback(new Error('途耗限制0.1-1000.0'))
      } else {
        callback()
      }
    }
    //最久待装车时长选填，1-72 整数
    Vue.prototype.longest = function (rule, value, callback) {
      console.log(111)
      var reg = new RegExp('^([1-9]|[1-9]\\d|72)$')
      if (value != '') {
        if (!reg.test(value)) {
          callback(new Error('最久待装车时长1-72整数'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    //装车费选填 1-1000000.0
    Vue.prototype.loadingCharge = function (rule, value, callback) {
      var reg = new RegExp('^(([1-9][0-9]{0,2}|0)(.[1-9])?|1000000.0|1000000)$')
      if (value != '') {
        if (value > 1 || value == 1) {
          if (!reg.test(value)) {
            callback(new Error('限制为1-1000000.0'))
          } else {
            callback()
          }
        } else {
          callback(new Error('限制为1-1000000.0'))
        }
      } else {
        callback()
      }
    }
    //发货总量0-1000000
    Vue.prototype.unitPrice = function (rule, value, callback) {
      var reg = new RegExp('^(([1-9][0-9]{0,2}|0)(.[1-9])?|1000000.0|1000000)$')
      if (!reg.test(value)) {
        callback(new Error('货物单价限制0-1000000.0'))
      } else {
        callback()
      }
    }
    //金额最大500万 小数点后2位
    Vue.prototype.Price500 = function (rule, value, callback) {
      //var reg = /^[+-]?\d+(\.\d{1,6})?$/;
      var reg = /^(\d+)(.\d{0,2})?$/
      if (value != '') {
        if (value == '0.0' || value == '0.00') {
          callback()
        } else {
          if (!reg.test(value) || value == 0) {
            callback(new Error('限制0.01-5000000.00'))
          } else if (value > 5000000) {
            callback(new Error('限制0.01-5000000.00'))
          } else {
            callback()
          }
        }

      } else {
        callback()
      }
    }
    //服务费小于10 小数点后6位
    Vue.prototype.fuwuReg = function (rule, value, callback) {
      var reg = /^[+-]?\d+(\.\d{1,6})?$/;
      if (value != '') {
        if (value == '0.0' || value == '0.00' || value == '0.000' || value == '0.0000' || value == '0.00000' || value == '0.000000') {
          callback()
        } else {
          if (!reg.test(value) || value == 0) {
            callback(new Error('限制0.000001-1000000.000000'))
          } else if (value > 10 || value == 10) {
            callback(new Error('限制0.000001-1000000.000000'))
          } else {
            callback()
          }
        }

      } else {
        callback()
      }
    }
    //中国标准时间转换为年月日时分秒
    Vue.prototype.getDate = function (value) {
      console.log(value)
      if (value === undefined) return ''  
      var d = new Date(value)
      var month = d.getMonth() + 1
      if (month < 10) {
        month = '0' + month
      }
      var date = d.getDate()
      if (date < 10) {
        date = '0' + date
      }
      var datetime = d.getFullYear() + '-' + month + '-' + date + ' ' + d.getHours() + ':' + d.getMinutes() + ':' + d.getSeconds()
      return datetime
    }
    //中国标准时间转换为年月日
    Vue.prototype.getDate1 = function (value) {
      console.log(value)
      var d = new Date(value)
      var month = d.getMonth() + 1
      if (month < 10) {
        month = '0' + month
      }
      var date = d.getDate()
      if (date < 10) {
        date = '0' + date
      }
      var datetime = d.getFullYear() + '-' + month + '-' + date
      return datetime
    }
    //中国标准时间转换为年月
    Vue.prototype.getDate2 = function (value) {
      console.log(value)
      var d = new Date(value)
      var year = d.getFullYear()
      var month = d.getMonth()
      if (month == 0) {
        month = 12
        year = year - 1
      }
      if (month < 10) {
        month = '0' + month
      }
      var datetime = year + '-' + month
      return datetime
    }
    //中国标准时间转换为年
    Vue.prototype.getDate3 = function (value) {
      var d = new Date(value)
      var year = d.getFullYear()
      return year
    }
    //正整数
    Vue.prototype.numtest = function (value) {
      var reg = new RegExp('/^[1-9]d*$/')
      var flag = true
      console.log(value)
      console.log(!reg.test(value))
      if (!reg.test(value)) {
        flag = false
      } else {
        flag = true
      }
      return flag
    }
    //特殊字符
    Vue.prototype.specialfn = function (rule, value, callback) {
      var reg = new RegExp('^[a-zA-Z0-9_\u4e00-\u9fa5]+$')

      if (!reg.test(value) || value == 0) {
        callback(new Error('不可输入特殊字符'))
      } else {
        callback()
      }
    }
    //统一社会信用代码
    Vue.prototype.socialCreditCode = function (rule, value, callback) {
      var reg = new RegExp('[A-Z0-9]')
      if (!reg.test(value) || value == 0) {
        callback(new Error('大写字母和数字'))
      } else {
        callback()
      }
    }
    //身份证
    Vue.prototype.isCardNo = function (rule, value, callback) {
      var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (reg.test(value) === false) {
        callback(new Error('身份证输入不合法'))
      } else {
        callback()
      }
    }
    //去空格
    Vue.prototype.Trim = function (str, is_global) {
      var result;
      result = str.replace(/(^\s+)|(\s+$)/g, "");
      if (is_global.toLowerCase() == "g") {
        result = result.replace(/\s/g, "");
      }
      return result;
    }
    //手机号校验
    Vue.prototype.telReg = function (rule, value, callback) {
      var reg = /(^1(3|4|5|6|7|8|9)\d{9}$)/;
      if (reg.test(value) === false) {
        callback(new Error('手机号格式不正确'))
      } else {
        callback()
      }
    }
  }
}