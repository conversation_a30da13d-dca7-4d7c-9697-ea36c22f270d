<template>
    <div class="navbar">
        <div class="head">
            <img src="@/assets/logo.png">鸿飞达智慧物流管理后台
        </div>
        <!-- <hamburger :toggle-click="toggleSideBar" :is-active="sidebar.opened" class="hamburger-container"/>  -->

        <!--    <breadcrumb class="breadcrumb-container"/>-->

        <div class="right-menu">
            <!--      <template v-if="device!=='mobile'">-->
            <!--        <search class="right-menu-item" />-->

            <!--        <error-log class="errLog-container right-menu-item hover-effect"/>-->

            <!--        <screenfull class="right-menu-item hover-effect"/>-->

            <!--        <el-tooltip :content="$t('navbar.size')" effect="dark" placement="bottom">-->
            <!--          <size-select class="right-menu-item hover-effect"/>-->
            <!--        </el-tooltip>-->

            <!--        <lang-select class="right-menu-item hover-effect"/>-->

            <!--        <el-tooltip :content="$t('navbar.theme')" effect="dark" placement="bottom">-->
            <!--          <theme-picker class="right-menu-item hover-effect"/>-->
            <!--        </el-tooltip>-->
            <!--      </template>-->

            <!-- <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
                <div class="avatar-wrapper">
                    <img src="../../../images/userLogo.png" class="user-avatar">
                    <i class="el-icon-caret-bottom"/>
                </div>
                <el-dropdown-menu slot="dropdown">
                    <router-link to="/">
                        <el-dropdown-item>
                            {{ $t('navbar.dashboard') }}
                        </el-dropdown-item>
                    </router-link>
                    <el-dropdown-item divided>
                        <span style="display:block;" @click="editUserInfo">用户信息</span>
                    </el-dropdown-item>
                    <el-dropdown-item divided>
                        <span style="display:block;" @click="logout">{{ $t('navbar.logOut') }}</span>
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown> -->
            <div class="export" @click="$router.push('/exportTask')">
                <div class="avatar-icon">
                    <i class="el-icon-folder-opened"></i>
                </div>
                <div class="export-text">导出任务</div>
            </div>
            <div class="avatar" @click="editUserInfo">
                <div class="avatar-icon">
                    <i class="el-icon-user"></i>
                </div>
                <div class="avatar-username">hi~{{ form.nickName }}</div>
            </div>
            <div class="out" @click="logout">
                <div class="out-icon">
                    <i class="el-icon-switch-button"></i>
                </div>
                <div class="out-text">注销</div>
            </div>
        </div>
        <el-dialog
                title="账号设置"
                :visible.sync="dialogVisible"
                :before-close="handleClose">
            <div>
                <el-form ref="form" :model="form" label-width="120px">
                    <el-form-item label="系统用户账号:">
                        {{form.nickName}}
                    </el-form-item>
                    <el-form-item label="系统用户名称:">
                        <el-input :disabled="true" v-model="form.realName" style="width: 240px"></el-input>
                    </el-form-item>
                    <el-form-item label="密码:" required>
                        <el-input readonly onfocus="this.removeAttribute('readonly');" autocomplete="off"
                                  maxlength="24"
                                  minlength="8"
                                  type="password"
                                  :οninput="form.pwd=form.pwd.replace(/[^\w\.\/]/ig,'')"
                                  v-model="form.pwd" style="width: 240px"></el-input>
                        <span style="margin-left: 10px;color: red">原密码</span>
                    </el-form-item>
                    <el-form-item label="新密码:" required>
                        <el-input
                                readonly
                                onfocus="this.removeAttribute('readonly');" autocomplete="off"
                                type="password"
                                maxlength="24"
                                minlength="8"
                                :οninput="form.newPwd=form.newPwd.replace(/[^\w\.\/]/ig,'')"
                                v-model="form.newPwd" style="width: 240px"></el-input>
                        <span style="margin-left: 10px;color: red">字母(首字母大写)+数字8-24位</span>
                    </el-form-item>
                    <el-form-item label="确认密码:" required>
                        <el-input
                                readonly
                                onfocus="this.removeAttribute('readonly');" autocomplete="off"
                                maxlength="24"
                                minlength="8"
                                type="password"
                                :οninput="form.confirmNewPwd=form.confirmNewPwd.replace(/[^\w\.\/]/ig,'')"
                                v-model="form.confirmNewPwd" style="width: 240px"></el-input>
                        <span style="margin-left: 10px;color: red">请重复新密码</span>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="sureChange">确 定</el-button>
  </span>
        </el-dialog>
    </div>
</template>

<script>
    import {mapGetters} from 'vuex'
    import Breadcrumb from '@/components/Breadcrumb'
    import Hamburger from '@/components/Hamburger'
    import ErrorLog from '@/components/ErrorLog'
    import Screenfull from '@/components/Screenfull'
    import SizeSelect from '@/components/SizeSelect'
    import LangSelect from '@/components/LangSelect'
    import ThemePicker from '@/components/ThemePicker'
    import Search from '@/components/HeaderSearch'

    export default {

        components: {
            // Breadcrumb,
            Hamburger,
            // ErrorLog,
            // Screenfull,
            // SizeSelect,
            // LangSelect,
            // ThemePicker,
            // Search
        },
        data() {
            return {
                dialogVisible: false,
                form: {
                    nickName: '',
                    realName: '',
                    pwd: '',
                    newPwd: '',
                    confirmNewPwd: '',
                }
            }
        },
        computed: {
            ...mapGetters([
                'sidebar',
                'name',
                'avatar',
                'device'
            ])
        },
        created() {
            this.$http.post('/admin-center-server/sys/detailed').then(res => {
                let data = res.data;
                this.form.nickName = data.data.nickName;
                this.form.realName = data.data.realName
            })
        },
        methods: {
            handleClose() {
                this.dialogVisible = false;
                this.form.pwd = '';
                // this.form.nickName = '';
                this.form.newPwd = '';
                this.form.confirmNewPwd = '';
                this.form.realName = '';
            },
            toggleSideBar() {
                this.$store.dispatch('toggleSideBar')
            },
            logout() {
                this.$store.dispatch('LogOut').then(() => {
                    location.reload()// In order to re-instantiate the vue-router object to avoid bugs
                })
            },
            editUserInfo() {
                this.dialogVisible = true
                this.$http.post('/admin-center-server/sys/detailed').then(res => {
                    let data = res.data;
                    this.form.nickName = data.data.nickName;
                    this.form.realName = data.data.realName
                })
            },
            sureChange() {
                let form = {
                    pwd: this.form.pwd,
                    newPwd: this.form.newPwd,
                    confirmNewPwd: this.form.confirmNewPwd,
                };
                if (form.pwd === '' || form.newPwd === '' || form.confirmNewPwd === '') {
                    this.$message.warning('请填写完整')
                } else if (form.newPwd !== form.confirmNewPwd) {
                    this.$message.warning('两次输入密码不一致')
                } else if (form.pwd.length < 8 || form.newPwd.length < 8) {
                    this.$message.warning('密码长度最小8位')
                } else if (!(/^[A-Z][A-z0-9]*$/).test(form.newPwd)) {
                    this.$message.warning('密码首字母必须大写')
                } else {
                    this.$http.post('/admin-center-server/sys/updatePwd', form).then(res => {
                        let data = res.data;
                        if (data.code === '200') {
                            this.$message.success('修改成功');
                            this.dialogVisible = false;
                            setTimeout(() => {
                                this.logout();
                            }, 1500)
                        } else {
                            this.$message.warning(data.message)
                        }
                    })
                }

            },
        }
    }
</script>
<style>
    .el-drawer__wrapper {
        top: 90px !important;
        left: 200px !important;
        right: 30px !important;
        height: 600px !important;
    }

    .el-drawer {
        height: 800px !important;
    }
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
    .head {
        float: left;
        line-height: 50px;
        font-size: 20px;
        color: #3A3A49;
        img {
            margin-left: 19px;
            margin-right: 15px;
            vertical-align: middle;
        }
    }
    .userChanegPwd {
    }

    .navbar {
        flex-shrink: 0;
        height: 50px;
        overflow: hidden;

        .hamburger-container {
            line-height: 46px;
            height: 100%;
            float: left;
            cursor: pointer;
            transition: background .3s;

            &:hover {
                background: rgba(0, 0, 0, .025)
            }
        }

        .breadcrumb-container {
            float: left;
        }

        .errLog-container {
            display: inline-block;
            vertical-align: top;
        }

        .right-menu {
            float: right;
            height: 100%;
            display: flex;
            .export {
                margin-right: 35px;
                cursor: pointer;
            }
            .export-text {
                font-size: 14px;
                margin-top: 4px;
                color: #333;
            }
            .avatar {
                cursor: pointer;
            }
            .avatar-icon {
                margin-top: 6px;
                height: 19px;
                font-size: 19px;
                text-align: center;
            }
            .avatar-username {
                font-size: 14px;
                margin-top: 4px;
                color: #333;
            }
            .out {
                margin-left: 36px;
                margin-right: 20px;
                cursor: pointer;
            }
            .out-icon {
                margin-top: 6px;
                height: 19px;
                font-size: 19px;
                text-align: center;
                color: #F5212D;
            }
            .out-text {
                font-size: 14px;
                margin-top: 4px;
                color: #333;
            }

            &:focus {
                outline: none;
            }

            .right-menu-item {
                display: inline-block;
                padding: 0 8px;
                height: 100%;
                font-size: 18px;
                color: #5a5e66;
                vertical-align: text-bottom;

                &.hover-effect {
                    cursor: pointer;
                    transition: background .3s;

                    &:hover {
                        background: rgba(0, 0, 0, .025)
                    }
                }
            }

            .avatar-container {
                margin-right: 30px;

                .avatar-wrapper {
                    margin-top: 5px;
                    position: relative;

                    .user-avatar {
                        cursor: pointer;
                        width: 40px;
                        height: 40px;
                        border-radius: 10px;
                    }

                    .el-icon-caret-bottom {
                        cursor: pointer;
                        position: absolute;
                        right: -20px;
                        top: 25px;
                        font-size: 12px;
                    }
                }
            }
        }
    }
</style>
