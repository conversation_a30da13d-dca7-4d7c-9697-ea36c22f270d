<template>
  <div class="app-container carsList">
    <el-tabs v-model="tab" @tab-click="handleTabClick">
      <el-tab-pane label="司机驳回原因" name="1"></el-tab-pane>
      <el-tab-pane label="车队长驳回原因" name="2"></el-tab-pane>
      <el-tab-pane label="运力供应商驳回原因" name="5"></el-tab-pane>
    </el-tabs>
    <el-button class="left"
               icon="el-icon-plus"
               @click="goDetail(0)">添加驳回原因</el-button>
    <div class="list-box">
      <div class="list-title">
        <div>驳回原因管理</div>
      </div>
      <div class="list-main">
        <template>
          <el-table ref="multipleTable"
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                    border>
            <el-table-column type="index"
                             label="序号"
                             width="55">
            </el-table-column>
            <el-table-column show-overflow-tooltip
                             v-for="item in tableLabel"
                             :label="item.label"
                             :width="item.width ? item.width : 150">
              <template slot-scope="scope">
                <span style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
              </template>
            </el-table-column>

            <el-table-column fixed="right"
                             label="操作">
              <template slot-scope="scope">
                <el-button @click="goDetail(1,scope.row)"
                           type="text"
                           size="small">编辑</el-button>
                <el-button type="text"
                           @click='deletefn(scope.$index, scope.row)'
                           size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

        </template>
      </div>
    </div>
    <el-dialog title="驳回原因编辑框"
               :visible.sync="dialogFormVisible">
      <el-form :model="form"
               ref="form"
               :rules="formRules">
        <el-form-item prop='name'>
          <el-input type="textarea"
                    :rows="2"
                    maxlength="20"
                    show-word-limit
                    placeholder="请输入驳回原因"
                    v-model="form.name">
          </el-input>

        </el-form-item>
      </el-form>
      <div slot="footer"
           class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary"
                   @click="addrolefn('form')">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
const rejectReasonList = '/admin-center-server/verify/rejectReasonList'//列表
const deleteRejectReason = '/admin-center-server/verify/deleteRejectReason'//删除
const updateRejectReason = '/admin-center-server/verify/updateRejectReason'//编辑
const addRejectReason = '/admin-center-server/verify/addRejectReason';//新增
export default {
  name: "CarsList",
  comments: {},
  data () {
    return {
      tab: '1',
      currentData: {},
      dialogFormVisible: false,
      addtype: '',//1是编辑 否则是添加
      form: {
        name: ''
      },
      formRules: {
        name: [{ required: true, message: '请输入驳回原因', trigger: 'blur' }]
      },
      tableLabel: [
        {
          prop: 'rejectReason',
          label: '驳回原因',
          width: 400
        }
      ],
      tableData: []
    };
  },
  methods: {
    addrolefn (formName) {
      var that = this
      this.$refs[formName].validate(valid => {
        var v = that.form
        if (valid) {
          var data = {
            rejectReason: v.name,
            "scene": this.tab
          }
          var url = ''
          if (this.addtype == 1) {
            data.id = this.currentData.id
            url = updateRejectReason
          } else {
            url = addRejectReason
          }
          this.$http.post(url, data).then(res => {
            this.pageNumber = 1
            this.getData()
            this.dialogFormVisible = false
            v.name = ''
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    goDetail (type, row) {
      this.addtype = type
      this.dialogFormVisible = true;
      this.currentData = row
      if (type == 0) {
        this.form.name = ''
      } else {
        this.form.name = this.currentData.rejectReason
      }

    },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },
    deletefn (index, row) {
      this.$confirm('是否删除' + row.rejectReason, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$http.post(deleteRejectReason + '?id=' + row.id).then(res => {
            this.pageNumber = 1
            this.getData()
            console.log(res)
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    getData () {
      this.$post(rejectReasonList, {
        scene: this.tab
      }).then(res => {
        this.tableData = res
      })
    },
    handleTabClick() {
      this.getData()
    }
  },
  activated () {
    this.getData()
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
