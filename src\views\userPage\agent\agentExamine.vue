<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">筛选查询</div>
      <div class="select-info">
        <el-form :inline="true"
                 :model="formInline"
                 ref="formInline"
                 class="demo-form-inline"
                 size="mini"
                 label-width="90px">
          <el-form-item label="认证状态："
                        label-width="100px"
                        prop='authenticateStatus'>
            <el-select v-model="formInline.authenticateStatus"
                       placeholder="请选择认证状态"
                       clearable
                       label-width="10px">
              <el-option v-for="(item, lan) in statusWrap"
                         :label="item.name"
                         :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="调度员名称:"
                        prop='name'>
            <el-input v-model="formInline.name"
                      placeholder="调度员公司名称或个人姓名"></el-input>
          </el-form-item>
          <el-form-item label="手机号:"
                        prop='tel'>
            <el-input v-model="formInline.tel"
                      oninput="value=value.replace(/[^\d]/g,'')"
                      maxlength="11"
                      placeholder="请输入手机号"></el-input>
          </el-form-item>
          <el-form-item prop='operatorName'
                        label="操作人姓名:">
            <el-input placeholder="请输入操作人姓名"
                      v-model="formInline.operatorName"></el-input>
          </el-form-item>
          <el-form-item prop='operatorNum'
                        label="操作人账号:">
            <el-input v-model="formInline.operatorNum"
                      placeholder="请输入操作人账号"></el-input>
          </el-form-item>
          <el-form-item prop='dates'
                        label-width="100px">
            <el-select v-model="formInline.dates"
                       placeholder=""
                       label-width="10px">
              <el-option label='注册时间'
                         value="0">注册时间</el-option>
              <el-option label='操作日期'
                         value="1">操作日期</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label-width="100px"
                        prop='date'>
            <el-col>
              <el-date-picker v-model="formInline.date"
                              value-format=""
                              type="datetimerange"
                              range-separator="至"
                              start-placeholder="选择开始时间"
                              end-placeholder="选择结束时间"
                              :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item>
            <el-button class="left"
                       icon="el-icon-search"
                       @click="onSubmit">查询</el-button>
            <el-button class="left"
                       @click="resetForm('formInline')"
                       icon="el-icon-refresh-right">重置</el-button>
            <el-button class="left"
                       @click="refreshfn"
                       icon="el-icon-refresh-right">刷新</el-button>
            <!-- <el-button class="left"
                       @click="deCertificationfn">用户认证</el-button> -->
            <el-button class="left"
                       icon="el-icon-plus"
                       @click="addnewfn">驳回原因管理</el-button>

          </el-form-item>
        </el-form>

      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table ref="multipleTable"
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%"
                    :height="tableHeight"
                    :row-class-name="tableRowClassName"
                    @selection-change="handleSelectionChange"
                    border>
            <el-table-column type="selection"
                             width="55"></el-table-column>
            <el-table-column type="index"
                             label="序号"
                             width="55">
            </el-table-column>
            <el-table-column show-overflow-tooltip
                             v-for="item in tableLabel"
                             :width='item.width?item.width:""'
                             :label="item.label">
              <template slot-scope="scope">
                <span style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
              </template>
            </el-table-column>

            <el-table-column fixed="right"
                             min-width='90'
                             label="操作">
              <template slot-scope="scope"
                        v-if='scope.row.brokerAuthStatus!=1'>
                <el-button @click="goDetail(scope.row)"
                           type="text"
                           size="small">查看</el-button>
                <el-button type="text"
                           @click='examinefn(scope.row)'
                           size="small">审核</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page="pageNumber"
                         :page-sizes="[10,20, 40, 60, 80,100]"
                         :page-size="pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total"
                         class="pagination"></el-pagination>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
const list = '/admin-center-server/verify/list'//列表
export default {
  name: "CarsList",
  comments: {},
  data () {
    return {
      tableHeight: null, //表格的高度
      statusWrap: [
        {
          name: '已认证',
          id: '2'
        },
        {
          name: '认证驳回',
          id: '3'
        },
        {
          name: '审核中',
          id: '4'
        },
        {
          name: '认证资质过期',
          id: '5'
        },
        {
          name: '补充资料认证中',
          id: '6'
        },
        {
          name: '补充资料驳回',
          id: '7'
        }
      ],
      tableLabel: [
        {
          prop: 'name',
          label: '调度员名称',
          width: 200
        },
        {
          prop: 'mobile',
          label: '手机号'
        },
        {
          prop: 'createTime',
          label: '注册时间',
          width: 180
        },
        {
          prop: 'brokerAuthStatusName',
          label: '认证状态'
        },
        {
          prop: 'deleteFlag',
          label: '账户状态'
        },
        {
          prop: 'updateUserName',
          label: '操作人名称',
          width: 180
        },
        {
          prop: 'updateUserNick',
          label: '操作人账号',
          width: 180
        },
        {
          prop: 'updateDateTime',
          label: '操作日期',
          width: 180
        }
      ],
      pageSize: 10,
      total: 0,
      pageNumber: 1,
      formInline: {
        name: "",
        authenticateStatus: "4",
        tel: '',
        operatorName: '',
        operatorNum: '',
        dates: "0",
        date: ''
      },
      tableData: []
    };
  },
  methods: {
    // 刷新当前页
    refreshfn () {
      this.$router.go(0)
    },
    //重置
    resetForm (formName) {
      this.$refs[formName].resetFields();
      this.formInline.date = []
      this.pageNumber = 1
      this.getData()
    },
    deCertificationfn () { this.$router.push("/consignorAccount/addShipper?urlType=2"); },
    addnewfn () {
      this.$router.push("/consignorExamine/rejectReason");
    },
    examinefn (row) {
      //审核
      this.$router.push("/consignorExamine/auditDetails?type=2&id=" + row.id + '&operation=2&authType=' + row.brokerAuthType);
    },
    onSubmit () {
      this.pageNumber = 1
      this.getData()
    },
    //查看
    goDetail (row) {
      //brokerAuthType:调度员认证类型 1 个人、2 公司
      this.$router.push("/consignorExamine/auditDetails?type=2&id=" + row.id + '&operation=1&authType=' + row.brokerAuthType);
    },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },

    handleSizeChange (val) {
      this.pageNumber = 1
      this.pageSize = val
      this.getData()
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange (val) {
      this.pageNumber = val
      this.getData()
      console.log(`当前页: ${val}`);
    },
    getData () {
      var v = this.formInline,
        data1 = '',
        data2 = '';
      if (v.date) {
        data1 = this.getDate(v.date[0])
        data2 = this.getDate(v.date[1])
      } else {
        data1 = ''
        data2 = ''
      }
      console.log(v.operatorName + '/' + v.operatorNum + '/' + v.dates + '/' + data1 + '/' + data2)
      v.name = this.Trim(v.name, 'g')
      var url = list + `?pageNumber=${this.pageNumber}&pageSize=${this.pageSize}&userType=2&brokerAuthStatus=${v.authenticateStatus}&companyName=${v.name}&mobile=${v.tel}&timeType=${v.dates}&updateUserName=${v.operatorName}&updateUserNick=${v.operatorNum}&minCreateTime=${data1}&maxCreateTime=${data2}`
      this.$http.get(url).then(res => {
        this.tableData = res.data.data.list
        this.total = Number(res.data.data.total)
      })
    },
    tableRowClassName ({ row, rowIndex }) {
      console.log(row)
      if (row.brokerAuthStatus === '2') {
        return 'warning-row';
      } else if (row.brokerAuthStatus === '3') {
        return 'success-row';
      } else if (row.brokerAuthStatus === '4') {
        return 'inAudit-row';
      }
      return '';
    }
  },
  activated () {
    this.getData()
    this.tableHeight =
      window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 160;
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
