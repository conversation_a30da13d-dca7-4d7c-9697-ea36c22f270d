<template>
  <div>
    <el-form :model="ruleForm"
             :rules="rules"
             ref="ruleForm"
             label-width="120px"
             size="medium"
             class="demo-ruleForm">
      <h2>添加客户</h2>
      <h2>客户信息</h2>
      <el-form-item label="客户名称"
                    prop="name">
        <el-input v-model="ruleForm.name"
                  maxlength="20"
                  :disabled="type==3?true:false"
                  placeholder="请输入客户名称"></el-input>
      </el-form-item>
      <el-form-item label="客户电话"
                    prop="tel">
        <el-input oninput="value=value.replace(/[^\d]/g,'')"
                  maxlength="11"
                  v-model="ruleForm.tel"
                  :disabled="type==3?true:false"
                  placeholder="请输入客户电话"></el-input>
      </el-form-item>
      <el-form-item label="客户地址"
                    prop="address">
        <el-input v-model="ruleForm.address"
                  maxlength="50"
                  :disabled="type==3?true:false"
                  placeholder="请输入客户地址"></el-input>
      </el-form-item>
      <el-form-item label="是否有合同"
                    prop="contract"
                    class="s-3">
        <el-select v-model="ruleForm.contract"
                   :disabled="type==3?true:false"
                   placeholder="请选择">
          <el-option v-for="(item, ind) in contractData"
                     :label="item.name"
                     :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="客户第二联系人"
                    prop="twoName">
        <el-input v-model="ruleForm.twoName"
                  :disabled="type==3?true:false"
                  maxlength="30"
                  placeholder="请输入客户第二联系人"></el-input>
      </el-form-item>
      <el-form-item label="联系电话"
                    prop="twoTel">
        <el-input oninput="value=value.replace(/[^\d]/g,'')"
                  maxlength="11"
                  :disabled="type==3?true:false"
                  v-model="ruleForm.twoTel"
                  placeholder="请输入联系电话"></el-input>
      </el-form-item>
      <el-form-item label="销售人员"
                    prop="saleName">
        <el-input v-model="ruleForm.saleName"
                  maxlength="30"
                  :disabled="type==3?true:false"
                  placeholder="请输入销售人员"></el-input>
      </el-form-item>
      <el-form-item label="销售第二联系人"
                    prop="twoSaleName">
        <el-input v-model="ruleForm.twoSaleName"
                  maxlength="30"
                  :disabled="type==3?true:false"
                  placeholder="请输入销售第二联系人"></el-input>
      </el-form-item>
      <el-form-item label="联系电话"
                    prop="twoSaleTel">
        <el-input oninput="value=value.replace(/[^\d]/g,'')"
                  maxlength="11"
                  :disabled="type==3?true:false"
                  v-model="ruleForm.twoSaleTel"
                  placeholder="请输入联系电话"></el-input>
      </el-form-item>
      <h2>其他信息</h2>
      <el-form-item label="报备人"
                    prop="prepareName">
        <el-input v-model="ruleForm.prepareName"
                  maxlength="30"
                  :disabled="type==3?true:false"
                  placeholder="请输入报备人"></el-input>
      </el-form-item>
      <el-form-item label="报备时间"
                    prop="prepareTime">
        <el-date-picker v-model="ruleForm.prepareTime"
                        type="datetime"
                        :disabled="type==3?true:false"
                        placeholder="选择报备时间"
                        style="width: 100%;"> </el-date-picker>
      </el-form-item>
      <el-form-item v-if='type!=="1"'
                    label="跟进人"
                    prop="followUp">
        <el-input v-model="ruleForm.followUp"
                  :disabled="type==3?true:false"
                  maxlength="30"
                  placeholder="请输入跟进人"></el-input>
      </el-form-item>
      <el-form-item v-if='type!=="1"'
                    label="跟进时间"
                    prop="followUpTime">
        <el-date-picker v-model="ruleForm.followUpTime"
                        :disabled="type==3?true:false"
                        type="datetime"
                        placeholder="选择跟进时间"
                        style="width: 100%;"> </el-date-picker>
      </el-form-item>
      <el-form-item label="签约时间"
                    prop="signingTime">
        <el-date-picker v-model="ruleForm.signingTime"
                        type="datetime"
                        :disabled="type==3?true:false"
                        placeholder="选择签约时间"
                        style="width: 100%;"> </el-date-picker>
      </el-form-item>

      <el-form-item label="电话状态"
                    prop="telephone"
                    class="s-3">
        <el-select v-model="ruleForm.telephone"
                   :disabled="type==3?true:false"
                   placeholder="请选择">
          <el-option v-for="(item, ind) in telephoneData"
                     :label="item.name"
                     :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="需求状态"
                    prop="demand"
                    class="s-3">
        <el-select v-model="ruleForm.demand"
                   :disabled="type==3?true:false"
                   placeholder="请选择">
          <el-option v-for="(item, ind) in demandData"
                     :label="item.name"
                     :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="意向度"
                    prop="intention"
                    class="s-3">
        <el-select v-model="ruleForm.intention"
                   :disabled="type==3?true:false"
                   placeholder="请选择">
          <el-option v-for="(item, ind) in intentionData"
                     :label="item.name"
                     :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="跟进方式"
                    prop="Follow"
                    class="s-3">
        <el-select v-model="ruleForm.Follow"
                   :disabled="type==3?true:false"
                   placeholder="请选择">
          <el-option v-for="(item, ind) in FollowData"
                     :label="item.name"
                     :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="客户星级"
                    prop="starClass"
                    class="s-3">
        <el-select v-model="ruleForm.starClass"
                   :disabled="type==3?true:false"
                   placeholder="请选择">
          <el-option v-for="(item, ind) in starClassData"
                     :label="item.name"
                     :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注">
        <el-input type="textarea"
                  :disabled="type==3?true:false"
                  maxlength="300"
                  show-word-limit
                  v-model="ruleForm.desc"
                  prop="ruleForm.desc"
                  placeholder="请填写您的备注信息"></el-input>
      </el-form-item>
      <div v-if='type!=1'>
        <h2>合同信息</h2>
        <el-form-item label="合同名称"
                      prop="contractName">
          <el-input v-model="ruleForm.contractName"
                    :disabled="flag_type==2?true:false"
                    placeholder="请输入合同名称"></el-input>
        </el-form-item>
        <el-form-item label="审核人"
                      prop="auditor">
          <el-input v-model="ruleForm.auditor"
                    :disabled="flag_type==2?true:false"
                    placeholder="请输入审核人"></el-input>
        </el-form-item>
        <el-form-item label="审核时间"
                      prop="auditTime">
          <el-date-picker v-model="ruleForm.auditTime"
                          :disabled="flag_type==2?true:false"
                          type="datetime"
                          placeholder="选择审核时间"
                          style="width: 100%;"> </el-date-picker>
        </el-form-item>
      </div>
      <div v-if='type!=1'>
        <h2>佣金信息</h2>
        <el-form-item label="合同金额"
                      prop="contractAmount">
          <el-input v-model="ruleForm.contractAmount"
                    disabled="disabled"></el-input>
        </el-form-item>
        <el-form-item label="提成比例"
                      prop="royaltyRatio">
          <el-input v-model="ruleForm.royaltyRatio"
                    disabled="disabled"></el-input>
        </el-form-item>
        <el-form-item label="佣金"
                      prop="commission">
          <el-input v-model="ruleForm.commission"
                    disabled="disabled"></el-input>
        </el-form-item>
        <el-form-item label="业务员"
                      prop="salesman">
          <el-input v-model="ruleForm.salesman"
                    disabled="disabled"></el-input>
        </el-form-item>
      </div>
      <el-form-item>
        <el-button type="primary"
                   @click="submitForm('ruleForm')">提交</el-button>
      </el-form-item>
    </el-form>

  </div>
</template>

<script>
const addCustomer = '/admin-center-server/customer/addCustomer'//添加
const getByCustomerId = '/admin-center-server/customer/getByCustomerId'//详情
const updageCustomer = '/admin-center-server/customer/updageCustomer'//修改
export default {
  data () {
    return {
      id: '',
      type: '',
      addFlag: true,
      flag_type: '1',
      contractData: [
        {
          name: '是',
          id: '1'
        },
        {
          name: '否',
          id: '0'
        }
      ],
      telephoneData: [
        {
          name: '无效电话',
          id: '0'
        },
        {
          name: '直接拒绝',
          id: '1'
        },
        {
          name: '无人接听',
          id: '2'
        },
        {
          name: '持续跟进',
          id: '3'
        },
        {
          name: '同行',
          id: '4'
        }
      ],
      demandData: [
        {
          name: '有合作方',
          id: '0'
        }, {
          name: '无合作方',
          id: '1'
        }, {
          name: '考虑中',
          id: '2'
        }, {
          name: '不考虑',
          id: '3'
        }],
      intentionData: [
        {
          name: '0%',
          id: '0'
        }, {
          name: '30%',
          id: '1'
        }, {
          name: '60%',
          id: '2'
        }, {
          name: '80%',
          id: '3'
        }, {
          name: '已签约',
          id: '4'
        }],
      FollowData: [
        {
          name: '上门',
          id: '0'
        }, {
          name: '电话',
          id: '1'
        }, {
          name: '微信',
          id: '2'
        }, {
          name: '短信',
          id: '3'
        }, {
          name: '其他',
          id: '4'
        }],
      starClassData: [
        {
          name: '一星',
          id: '0'
        },
        {
          name: '二星',
          id: '1'
        },
        {
          name: '三星',
          id: '3'
        },
        {
          name: '四星',
          id: '4'
        },
        {
          name: '五星',
          id: '5'
        }],
      ruleForm: {
        name: '',
        tel: '',
        twoName: '',
        twoTel: '',
        address: '',
        saleName: '',
        twoSaleName: '',
        twoSaleTel: '',
        prepareName: '',
        prepareTime: '',
        signingTime: '',
        contract: '',
        telephone: '',
        demand: '',
        intention: '',
        Follow: '',
        starClass: '',
        desc: '',
        followUp: '',
        followUpTime: '',
        contractName: '',
        auditor: '',
        auditTime: '',
        contractAmount: '',
        royaltyRatio: '',
        commission: '',
        salesman: ''
      },
      rules: {
        prepareTime: [{ required: true, message: '请选择报备时间', trigger: 'change' }],
        signingTime: [{ required: true, message: '请选择签约时间', trigger: 'change' }],
        followUpTime: [{ required: true, message: '请选择跟进时间', trigger: 'change' }],
        contract: [{ required: true, message: '请选择是否有合同', trigger: 'change' }],
        followUp: [
          { required: true, message: '请输入跟进人', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入客户名称', trigger: 'blur' }
        ],
        saleName: [
          { required: true, message: '请输入销售人员', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
        ],
        twoSaleName: [
          { required: true, message: '请输入销售第二联系人', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
        ],
        prepareName: [
          { required: true, message: '请输入报备人', trigger: 'blur' },
          { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
        ],
        twoSaleTel: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { min: 11, max: 11, message: '手机号格式不正确', trigger: 'blur' }
        ],
        tel: [
          { required: true, message: '请输入客户电话', trigger: 'blur' },
          { min: 11, max: 11, message: '手机号格式不正确', trigger: 'blur' }
        ],
        twoTel: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { min: 11, max: 11, message: '手机号格式不正确', trigger: 'blur' }
        ],
        twoName: [
          { required: true, message: '请输入客户第二联系人', trigger: 'blur' }
        ],
        address: [{ required: true, message: '请输入客户地址', trigger: 'blur' }]
      }
    }
  },
  methods: {
    submitForm (formName) {
      //type 1添加  2修改
      var that = this
      this.$refs[formName].validate(valid => {
        var v = that.ruleForm
        if (valid) {
          if (this.addFlag) {
            this.addFlag = false
            var data = {
              cusName: v.name,
              cusTel: v.tel,
              cusSecondContact: v.twoName,
              cusContactPhone: v.twoTel,
              cusAddress: v.address,
              saleName: v.saleName,
              saleSecondContact: v.twoSaleName,
              saleTel: v.twoSaleTel,//销售电话
              reporter: v.prepareName,//报备人
              reportDate: that.getDate(v.prepareTime),//报备时间
              signingDate: that.getDate(v.signingTime),//签约时间
              isContract: v.contract,//合同
              phoneStatus: v.telephone,//电话状态
              requireStatus: v.demand,//需求状态
              intent: v.intention,//意向度
              followStatus: v.Follow,//跟进方式
              cusStar: v.starClass,//客户星级
              remark: v.desc,
            }
            if (this.type == 2) {
              data.follower = v.followUp//跟进人
              data.followDate = that.getDate(v.followUpTime)//跟进时间
              data.contract = {
                name: v.contractName,
                reviewPerson: v.auditor,
                reviewDate: v.auditTime ? that.getDate(v.auditTime) : ''
              }

              data.id = this.id
              // data.contractAmount = v.contractAmount//合同金额
              // data.royaltyRatio = v.royaltyRatio//提成比例
              // data.commission = v.commission//佣金
              // data.salesman = v.salesman//业务员
            }

            console.log(data)
            if (this.type == 2) {
              this.$http.post(updageCustomer, data).then(res => {
                console.log(res.data.code)
                var that = this
                if (res.data.code == '200') {
                  this.$message({
                    message: res.data.message,
                    type: 'success'
                  });
                  setTimeout(function () {
                    that.$router.go(-1);
                  }, 3000);
                } else {
                  this.addFlag = true
                  this.$message.error(res.data.message);
                }


              })
            } else {

              this.$http.post(addCustomer, data).then(res => {
                console.log(res.data)
                var that = this
                if (res.data.code == '200') {
                  this.$message({
                    message: res.data.message,
                    type: 'success'
                  });
                  setTimeout(function () {
                    that.$router.go(-1);
                  }, 3000);
                } else {
                  this.addFlag = true
                  this.$message.error(res.data.message);
                }
              })
            }
          }


        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //通讯录
    getData () {
      this.$http.get(getByCustomerId + '?id=' + this.id).then(res => {
        var customer = res.data.data.customer
        var contract = res.data.data.contract
        console.log(customer.cusContactPhone)

        this.ruleForm.name = customer.cusName
        this.ruleForm.tel = customer.cusTel
        this.ruleForm.address = customer.cusAddress
        this.ruleForm.twoName = customer.cusSecondContact
        this.ruleForm.twoTel = customer.cusContactPhone
        this.ruleForm.saleName = customer.saleName
        this.ruleForm.twoSaleName = customer.saleSecondContact
        this.ruleForm.twoSaleTel = customer.saleTel
        this.ruleForm.prepareName = customer.reporter
        this.ruleForm.prepareTime = customer.reportDate
        this.ruleForm.signingTime = customer.signingDate
        this.ruleForm.telephone = customer.phoneStatus
        this.ruleForm.contract = customer.isContract
        this.ruleForm.demand = customer.requireStatus
        this.ruleForm.intention = customer.intent
        this.ruleForm.Follow = customer.followStatus
        this.ruleForm.starClass = customer.intent
        this.ruleForm.desc = customer.remark
        this.ruleForm.followUp = customer.follower
        this.ruleForm.followUpTime = customer.followDate
        if (contract) {
          this.ruleForm.contractName = contract.name
          this.ruleForm.auditor = contract.reviewPerson
          this.ruleForm.auditTime = contract.reviewDate
        } else {
          this.flag_type = 2
        }
        console.log(this.type == 3 && this.flag_type == 1)
      })
    }
  },
  activated () {
    this.type = this.$route.query.type;
    this.id = this.$route.query.id;
    this.getData()
    console.log(this.type)
    //1 新增  2修改  3查看
  }
}
</script>

<style lang="scss" scoped>
.iframe-wrap {
  background: #fff;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  .close {
    background: #fafafa;
    width: 46px;
    height: 46px;
    position: absolute;
    line-height: 46px;
    text-align: center;
    font-size: 12px;
  }
  iframe {
    width: 100%;
    height: 100%;
  }
}

.el-date-editor--datetimerange {
  width: 380px !important;
}
.line {
  text-align: center;
}
.agent-wrap {
  background: #fff;
  -moz-box-shadow: 0px 0px 15px #eef2f5;
  -webkit-box-shadow: 0px 0px 15px #f0f4f7;
  box-shadow: 0px 0px 15px #eef2f5;
  border-radius: 10px;
  width: 400px;
  position: fixed;
  top: 120px;
  left: 24%;
  .page {
    float: right;
    clear: both;
  }
  .btn {
    clear: both;
    float: right;
    margin: 10px;
  }
  .serch {
    width: 94%;
    margin-left: 3%;
  }
  h2 {
    line-height: 60px;
    text-align: center;
    position: relative;
    img {
      width: 17px;
      height: 17px;
      position: absolute;
      right: 10px;
      top: 20px;
      cursor: pointer;
    }
  }
}
ul.agent-wraps {
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  li {
    width: 94%;
    height: 40px;
    font-size: 12px;
    margin-left: 3%;
    line-height: 40px;
    position: relative;
    span {
      line-height: 30px;
      font-size: 12px !important;
    }
    .name {
      color: #000;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      width: 100%;
      margin-left: 40px;
      margin-right: 10px;
    }
    .detail-btn {
      margin: 10px 0;
      position: absolute;
      top: 0px;
      right: 10px;
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
    img {
      width: 30px;
      height: 30px;
      position: absolute;
      border-radius: 15px;
      overflow: hidden;
      top: 5px;
    }
    .xiao {
      width: 10px;
      height: 10px;
      position: absolute;
      right: 10px;
      top: 5px;
    }
    .tel {
      color: #888888;
    }
  }
}
.max {
  width: 250px !important;
}
.demo-ruleForm {
  width: 500px;
  margin-top: 20px;

  h2 {
    font-weight: normal;
    font-size: 16px;
    margin: 10px;
    color: #1898ff;
  }
  .tip {
    margin: 20px 0;
    .price {
      font-size: 12px;
      color: #222222;
      margin-left: 60px;
      span {
        color: #ee4127;
        font-size: 24px;
        margin-right: 10px;
      }
    }
  }
  .el-autocomplete {
    width: 100%;
  }
  .s-1 .el-select {
    width: 324px;
  }
  .s-3 .el-select {
    width: 100%;
  }
  .agent {
    font-size: 12px;
    margin-bottom: 10px;
    .el-button {
      margin-left: 10px;
    }
    span {
      color: #00cb8a;
      font-size: 16px;
    }
  }
}
.amap-demo {
  height: 300px;
}

.search-box {
  position: absolute;
  top: 25px;
  left: 20px;
}

.amap-page-container {
  position: relative;
}
.amap-wrapper {
  width: 500px;
  height: 500px;
}
.info-title {
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  height: 40px;
  line-height: 40px;
  background: #f5f5f5;
  padding-left: 10px;
  border-left: 5px solid #00cb8a;
  font-size: 14px;
  padding-right: 50px;
}
.el-checkbox-group {
  width: 440px;
}
.el-checkbox {
  font-size: 12px;
  line-height: 12px;
}
</style>
