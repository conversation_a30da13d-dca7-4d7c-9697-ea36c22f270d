<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">绑卡历史筛选查询</div>
      <div class="select-info">
        <el-form
          :inline="true"
          :model="formInline"
          ref="formInline"
          class="demo-form-inline"
          size="mini"
          label-width="120px"
        >
          <el-form-item label="用户类型：">
            <el-select v-model="formInline.userType" placeholder="请选择" clearable>
              <el-option label="不限" value=""></el-option>
              <el-option label="货主" value="1"></el-option>
              <el-option v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="调度员" value="2"></el-option>
              <el-option label="司机" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户名称:" prop="name">
            <el-input v-model="formInline.name" placeholder="请输入用户名称"></el-input>
          </el-form-item>
          <el-form-item label="用户手机号:">
            <el-input
              v-model="formInline.mobile"
              oninput="value=value.replace(/[^\d]/g,'')"
              maxlength="11"
              placeholder="请输入注册手机号"
            ></el-input>
          </el-form-item>

          <el-form-item label="银行卡号:" prop="tel">
            <el-input
              v-model="formInline.acctNo"
              oninput="value=value.replace(/[^\d]/g,'')"
              placeholder="请输入用户银行卡号"
              maxlength="18"
            ></el-input>
          </el-form-item>
          <el-form-item label="持卡人姓名：">
            <el-input v-model="formInline.cardOwnerName" placeholder="请输入持卡人姓名" maxlength="30"></el-input>
          </el-form-item>
          <el-form-item label="身份证号：">
            <el-input v-model="formInline.idCard" placeholder="请输入身份证号" maxlength="30"></el-input>
          </el-form-item>
          <el-form-item label="开户行名称:" prop="tel">
            <el-input v-model="formInline.bankName" placeholder="请输入开户行名称" maxlength="100"></el-input>
          </el-form-item>

          <el-form-item label="绑卡时间：">
            <el-date-picker
              v-model="value1"
              type="datetimerange"
              start-placeholder="开始日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
            ></el-date-picker>
          </el-form-item>

          <el-form-item>
            <el-button class="left" icon="el-icon-search" @click="onSubmit">查询</el-button>
            <el-button class="left" @click="resetForm" icon="el-icon-refresh-right">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>账户列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="userType" label="用户类型" width="200">
              <template slot-scope="scope">
                <el-row v-if="scope.row.userType == 1">货主</el-row>
                <el-row v-if="scope.row.userType == 2">调度员</el-row>
                <el-row v-if="scope.row.userType == 3">司机</el-row>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="用户名称" width="200"></el-table-column>
            <el-table-column prop="mobile" label="用户手机号" width="300"></el-table-column>
            <el-table-column prop="cardOwnerName" label="持卡人姓名"></el-table-column>
            <el-table-column prop="idCard" label="身份证号" width="200"></el-table-column>
            <el-table-column prop="acctNo" label="银行卡号"></el-table-column>
            <el-table-column prop="bankName" label="开户行"></el-table-column>
            <el-table-column prop="reserveMobile" label="银行预留手机号"></el-table-column>
            <el-table-column prop="isBind" label="当前是否绑定">
              <template slot-scope="scope">
                <el-row v-if="scope.row.isBind == 0">未绑定</el-row>
                <el-row v-else-if="scope.row.isBind == 1">绑定</el-row>
              </template>
            </el-table-column>
            <el-table-column prop="optionTime" label="绑卡时间"></el-table-column>
          </el-table>
        </template>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="formInline.pageNumber"
          :page-sizes="[10,20, 40, 60, 80,100]"
          :page-size="formInline.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "CarsList",
  data() {
    return {
      tableHeight: null, //表格的高度
      value1: "", //日期
      total: 0,
      formInline: {
        idCard: "", //身份证号
        userType: "", //用户类型
        name: "", //用户名字
        mobile: "", //手机号
        acctNo: "", //银行卡号
        cardOwnerName: "", //持卡人姓名
        bankName: "", //开户行名称
        pageSize: 10,
        pageNumber: 1,
        bindBankCardDateBegin: "", // 绑卡开始时间
        bindBankCardDateEnd: "" //绑卡结束时间
      },
      tableData: []
    };
  },
  methods: {
    //重置
    resetForm(formName) {
      this.formInline = {
        idCard: "",
        userType: "", //用户类型
        name: "", //用户名字
        mobile: "", //手机号
        acctNo: "", //银行卡号
        cardOwnerName: "", //持卡人姓名
        bankName: "", //开户行名称
        pageSize: 10,
        pageNumber: 1,
        bindBankCardDateBegin: "", // 绑卡开始时间
        bindBankCardDateEnd: "" //绑卡结束时间
      };
      this.value1 = []
      this.getData();
    },
    // 刷新当前页
    refreshfn() {
      this.$router.go(0);
    },

    onSubmit() {
      this.formInline.pageNumber = 1;
      this.getData();
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSizeChange(val) {
      this.formInline.pageNumber = 1;
      this.formInline.pageSize = val;
      this.getData();
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.getData();
      console.log(`当前页: ${val}`);
    },
    /* 获取列表数据 */
    getData() {
      console.log(this.value1);
      this.formInline.bindBankCardDateBegin = this.value1[0];
      this.formInline.bindBankCardDateEnd = this.value1[1];
      let postData = this.formInline;
      this.$http
        .post(
          "/admin-center-server/bindBankCard/queryBindBankCardList",
          postData
        )
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.tableData = data.data.list;
            this.total = JSON.parse(data.data.total);
          } else {
            this.$message.warning(data.message);
          }
        });
    }
  },
  activated() {
    this.getData();
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
