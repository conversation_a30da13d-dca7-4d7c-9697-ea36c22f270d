<template>
  <div class="app-container">
    <div class="fixed-all">
       <div class="container-top">
        <div>
            <el-checkbox v-model="isTest" @change="changeParams">去掉测试数据</el-checkbox>
        </div>
        <div>
            截止日期：
            <el-date-picker
                v-model="date"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
                style="width: 200px;"
                @change="getDataList"
                size="small"
            />
        </div>
       
        <div class="base">
            当前平台主体：{{ currentBase.baseName }}
            <i @click="changeBase" class="el-icon-sort"></i>
        </div>
    </div>
    </div>
   
    <div style="overflow: hidden;
          width: 1460px;
          margin-top: 50px;
          ">
      <iframe style="width: 100%;height: 1990px;margin-top: -160px;" :key="i" loading="lazy" :src="`https://meta.hongfeida.cn/public/dashboard/695c97b5-17a2-429f-9f6e-be1fdd1b1733?dateparam=${date+' 23:59:59'}&dateparam2=${date}&baseid=${currentBase.id}&startdate=${startDate+' 00:00:00'}&enddate=${date+' 23:59:59'}&testdata=${isTest?0:1}&url=${env =='production'?'admin':env}`" frameborder="0"
      ref="myIframe"
      @load="onIframeLoad"
      ></iframe>
    </div>
    <el-button @click="exportFile(true)" :disabled="canExport" type="primary" size="mini" v-if="showBtn" class="bottom-but1 bottom-class">导出记录</el-button>
    <el-button @click="exportFile(false)" :disabled="canExport" type="primary" size="mini" v-if="showBtn" class="bottom-but2 bottom-class">导出记录</el-button>
    <!-- <el-tooltip class="item" effect="dark" placement="top">
      <i class="el-icon-warning warn-pos"></i>
      <template #content>
        {{ startDate +'至'+ date }} 的充值总额
      </template>
    </el-tooltip> -->
     <el-tooltip class="item" effect="dark" placement="top" v-for="(item,index) in infoList" :key="index">
      <i class="el-icon-warning warn-pos" v-show="showBtn" :style="{left:item.left,top:item.top}"></i>
      <template #content>
        <div v-if="item.name=='期初渠道充油余额'">
          {{ monthDate }}属期的期初余额
        </div>
        <div v-if="item.name=='期末渠道充油余额'">
          {{ monthDate }}属期截止到{{date}}的充油余额
        </div>
        
        <div v-else>
          <span v-if="item.leftText">{{ item.leftText }}</span>
          <span v-if="item.time1">{{ showTime(item.time1) }}</span>
          <span v-if="item.time1 && item.time2">至</span>
          <span v-else>截止至</span>
          <span v-if="item.time2">{{ showTime(item.time2) }}</span>{{ item.showText }}
        </div>
      </template>
    </el-tooltip>
  </div> 
</template>
<script>
import {dateFormat} from '@/utils/date.js'
export default {
  name: 'dataOverview',
  data () {
    return {
        currentBase: {},
        env:'',
        date: '',
        startDate: '',
        endDate: '',
        isTest: true, // 是否去掉测试数据
        showBtn: false, // 是否显示导出按钮
        canExport: false, // 是否可以导出
        baseInfo: [],
        infoList:[
          // 平台账户
          {
            name: '期末平台余额',
            showText: '的余额',
            time1:'',
            time2:'date',
            left: '436px',
            top: '216px',
          },
          {
            name: '本期充值总额',
            showText: '的充值总额',
            time1:'startDate',
            time2:'date',
            left: '262px',
            top: '332px',
          },
          {
            name: '本期提现总额',
            showText: '的提现总额',
            time1:'startDate',
            time2:'date',
            left: '436px',
            top: '332px',
          },
          {
            name: '其中垫款子账户',
            showText: '的余额',
            time1:'',
            time2:'date',
            left: '670px',
            top: '216px',
          },
          {
            name: '其中油费子账户',
            showText: '的余额',
            time1:'',
            time2:'date',
            left: '846px',
            top: '216px',
          },
          {
            name: '其中运费差子账户',
            showText: '的余额',
            time1:'',
            time2:'date',
            left: '670px',
            top: '332px',
          },
          {
            name: '其中服务费子账户',
            showText: '的余额',
            time1:'',
            time2:'date',
            left: '846px',
            top: '332px',
          },
          // 用户账户
          {
            name: '期末货主余额',
            showText: '的余额',
            time1:'',
            time2:'date',
            left: '319px',
            top: '506px',
          },
          {
            name: '本期充值总额',
            showText: '的充值总额',
            time1:'startDate',
            time2:'date',
            left: '204px',
            top: '622px',
          },
          {
            name: '本期提现总额',
            showText: '的提现总额',
            time1:'startDate',
            time2:'date',
            left: '319px',
            top: '622px',
          },
          {
            name: '期末司机/车队长余额',
            showText: '的余额',
            time1:'',
            time2:'date',
            left: '611px',
            top: '506px',
          },
          {
            name: '本期充值总额',
            showText: '的充值总额',
            time1:'startDate',
            time2:'date',
            left: '495px',
            top: '622px',
          },
          {
            name: '本期提现总额',
            showText: '的提现总额',
            time1:'startDate',
            time2:'date',
            left: '611px',
            top: '622px',
          },
          {
            name: '期末运力供应商余额',
            showText: '的余额',
            time1:'',
            time2:'date',
            left: '902px',
            top: '506px',
          },
          {
            name: '本期充值总额',
            showText: '的充值总额',
            time1:'startDate',
            time2:'date',
            left: '787px',
            top: '622px',
          },
          {
            name: '本期提现总额',
            showText: '的提现总额',
            time1:'startDate',
            time2:'date',
            left: '902px',
            top: '622px',
          },
          {
            name: '期末渠道余额',
            showText: '的余额',
            time1:'',
            time2:'date',
            left: '1194px',
            top: '506px',
          },  
          {
            name: '本期充值总额',
            showText: '的充值总额',
            time1:'startDate',
            time2:'date',
            left: '1078px',
            top: '622px',
          },
          {
            name: '本期提现总额',
            showText: '的提现总额',
            time1:'startDate',
            time2:'date',
            left: '1194px',
            top: '622px',
          },
          // 业务概况 - 承运型业务
          {
            name: '本期支付司机',
            showText: '支付到司机账户的总额',
            time1:'startDate',
            time2:'date',
            left: '261px',
            top: '796px',
          },
          {
            name: '历史合计',
            showText: '支付到司机账户的总额',
            time1:'',
            time2:'date',
            left: '261px',
            top: '911px',
          },
          {
            name: '本期支付供应商',
            showText: '支付到供应商账户的总额',
            time1:'startDate',
            time2:'date',
            left: '495px',
            top: '796px',
          },
          {
            name: '历史合计',
            showText: '支付到供应商账户的总额',
            time1:'',
            time2:'date',
            left: '495px',
            top: '911px',
          },
          {
            name: '本期货主还款',
            showText: '货主支付给鸿飞达达总额',
            time1:'startDate',
            time2:'date',
            left: '728px',
            top: '796px',
          },
          {
            name: '历史合计',
            showText: '货主支付给鸿飞达的总额',
            time1:'',
            time2:'date',
            left: '728px',
            top: '911px',
          },
          {
            name: '本期开票金额合计',
            showText: '已为货主开票完成的总额',
            time1:'startDate',
            time2:'date',
            left: '961px',
            top: '911px',
          },
          {
            name: '历史已结算金额合计',
            showText: '货主支付给鸿飞达的总额',
            time1:'',
            time2:'date',
            left: '1370px',
            top: '796px',
          },
          {
            name: '已开票部分',
            showText: '已为货主开票完成的总额',
            time1:'',
            time2:'date',
            left: '1195px',
            top: '911px',
          },
          {
            name: '未开票部分',
            showText: '尚未为货主开票完成的总额',
            time1:'',
            time2:'date',
            left: '1370px',
            top: '911px',
          },
          // 业务概况 - 结算型业务
          {
            name: '本期支付供应商',
            showText: '支付到司机账户的总额',
            time1:'startDate',
            time2:'date',
            left: '261px',
            top: '1085px',
          },
          {
            name: '历史合计',
            showText: '支付到司机账户的总额',
            time1:'',
            time2:'date',
            left: '261px',
            top: '1201px',
          },
            {
            name: '本期支付供应商',
            showText: '支付到供应商账户的总额',
            time1:'startDate',
            time2:'date',
            left: '495px',
            top: '1085px',
          },
          {
            name: '历史合计',
            showText: '支付到供应商账户的总额',
            time1:'',
            time2:'date',
            left: '495px',
            top: '1201px',
          },
          {
            name: '本期货主已开票金额合计',
            showText: '货主已完成开票的总额',
            time1:'startDate',
            time2:'date',
            left: '728px',
            top: '1201px',
          },
          {
            name: '历史货主已结算金额合计',
            showText: '货主已完成结算的总额',
            time1:'',
            time2:'date',
            left: '1136px',
            top: '1085px',
          },
          {
            name: '已开票部分',
            showText: '货主已完成开票的总额',
            time1:'',
            time2:'date',
            left: '961px',
            top: '1201px',
          },
          {
            name: '未开票部分',
            showText: '货主尚未完成开票的总额',
            time1:'',
            time2:'date',
            left: '1136px',
            top: '1201px',
          },
          // 油品概况
          {
            name: '期初渠道充油余额',
            showText: '',
            time1:'',
            time2:'date',
            left: '261px',
            top: '1375px',
          },
          {
            name: '本期渠道充油金额',
            showText: '渠道充值用于充油的总额',
            time1:'startDate',
            time2:'date',
            left: '435px',
            top: '1375px',
          },
          {
            name: '本期渠道还油金额',
            showText: '还渠道油的总额',
            time1:'startDate',
            time2:'date',
            left: '611px',
            top: '1375px',
          },
          {
            name: '期末渠道充油余额',
            showText: '',
            time1:'startDate',
            time2:'date',
            left: '785px',
            top: '1375px',
          },
          {
            name: '本期支付司机油费-结算型',
            showText: '油费付给司机的总额',
            time1:'startDate',
            time2:'date',
            left: '1077px',
            top: '1375px',
          },
          {
            name: '本期支付司机油费-承运型',
            showText: '油费付给司机的总额',
            time1:'startDate',
            time2:'date',
            left: '1370px',
            top: '1375px',
          },
          // 税务概况
          {
            name: '历史已支付司机现金运费合计',
            showText: '计入司机现金收入的总额',
            time1:'',
            time2:'date',
            left: '319px',
            top: '1607px',
          },
          {
            name: '已上报税务',
            showText: '已上报税务的司机收入总额',
            time1:'',
            time2:'date',
            left: '202px',
            top: '1723px',
          },
          {
            name: '未上报税务',
            showText: '未上报税务的司机收入总额',
            time1:'',
            time2:'date',
            left: '319px',
            top: '1723px',
          },
          {
            name: '历史已支付供应商现金运费合计',
            showText: '计入供应商现金收入的总额',
            time1:'',
            time2:'date',
            left: '611px',
            top: '1607px',
          },
          {
            name: '已上报税务',
            showText: '已上报税务的供应商收入总额',
            time1:'',
            time2:'date',
            left: '494px',
            top: '1723px',
          },
          {
            name: '未上报税务',
            showText: '未上报税务的供应商收入总额',
            time1:'',
            time2:'date',
            left: '611px',
            top: '1723px',
          },
          {
            name: '历史已上报税务开票额合计（toC）',
            showText: '已上报税务的开票额总额',
            leftText:'toC业务',
            time1:'',
            time2:'date',
            left: '902px',
            top: '1607px',
          },
          {
            name: '已开票部分',
            showText: '已上报税务且已完成开票的开票额总额',
            leftText:'toC业务',
            time1:'',
            time2:'date',
            left: '786px',
            top: '1723px',
          },
          {
            name: '未开票部分',
            showText: '已上报税务且未完成开票的开票额总额',
            leftText:'toC业务',
            time1:'',
            time2:'date',
            left: '902px',
            top: '1723px',
          },
          {
            name: '历史已上报税务开票额合计（toB）',
            showText: '已上报税务的开票额总额',
            leftText:'toB业务',
            time1:'',
            time2:'date',
            left: '1195px',
            top: '1607px',
          },
          {
            name: '已开票部分',
            showText: '已上报税务且已完成开票的开票额总额',
            leftText:'toB业务',
            time1:'',
            time2:'date',
            left: '1077px',
            top: '1723px',
          },
          {
            name: '未开票部分',
            showText: '已上报税务且未完成开票的开票额总额',
            leftText:'toB业务',
            time1:'',
            time2:'date',
            left: '1195px',
            top: '1723px',
          },
        ],
        monthDate: '',
    }
  },
  components: {

  },
  activated() {
    console.log(this.env)
    if (process.env.VUE_APP_ENV == 'production') {
      this.env = 'admin' 
    }else if (process.env.VUE_APP_ENV == 'development') {
      this.env = 'dev'
    }else {
      this.env = process.env.VUE_APP_ENV 
    }
    let baseInfo = (this.baseInfo = this.$store.state.user.baseInfo);
    let currentBaseId = localStorage.getItem("UserCurrentBaseId");
    if (currentBaseId) {
      this.currentBase = baseInfo.find((v) => v.id == currentBaseId);
    } else {
      this.currentBase = baseInfo.find((v) => v.defaultFlag);
      localStorage.setItem("UserCurrentBaseId", this.currentBase.id);
    }
    this.date = dateFormat(new Date(), 'yyyy-MM-dd');
    this.getDataList();
    // this.getTypeList().then(() => {{{{{}}}}
    //   this.getDataList();
    // });
    // this.getCountInfo();
  },
  mounted() {

  },
  methods: {
    changeBase() {
      let baseInfo = this.baseInfo;
      let currentIndex = baseInfo.findIndex(
        (v) => v.id === this.currentBase.id
      );
      let toggleIndex = null;
      if (currentIndex === baseInfo.length - 1) {
        toggleIndex = 0;
      } else {
        toggleIndex = currentIndex + 1;
      }
      this.currentBase = baseInfo[toggleIndex];
      localStorage.setItem("UserCurrentBaseId", this.currentBase.id);
      this.changeParams()
      setTimeout(() => {
        this.$message.success("切换成功");
      }, 1000);
    },
    getDataList() {
      this.startDate = this.getMonthStart(this.date);
      this.endDate = this.getMonthEnd(this.date);
      this.monthDate = this.getMonth(this.date);
      this.changeParams()
    },
    getMonthStart(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
      return `${firstDay.getFullYear()}-${String(firstDay.getMonth() + 1).padStart(2, '0')}-${String(firstDay.getDate()).padStart(2, '0')}`;
    },
    getMonthEnd(dateStr) {
     if (!dateStr) return '';
      const date = new Date(dateStr);
      const now = new Date();
      // 如果是当前月
      if (date.getFullYear() === now.getFullYear() && 
          date.getMonth() === now.getMonth()) {
        return dateFormat(now, 'yyyy-MM-dd'); // 返回当前日期
      }
      // 其他月份返回月末
      return dateFormat(new Date(date.getFullYear(), date.getMonth() + 1, 0), 'yyyy-MM-dd');
    },
    getMonth(dateStr){
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const now = new Date();
      // 如果是当前月
      if (date.getFullYear() === now.getFullYear() && 
          date.getMonth() === now.getMonth()) {
        return dateFormat(now, 'yyyy-MM'); // 返回当前日期
      }
      // 其他月份返回月末
      return dateFormat(new Date(date.getFullYear(), date.getMonth() + 1, 0), 'yyyy-MM');
    },
    onIframeLoad() {
      this.showBtn = true;
    },
    changeParams(){
      this.showBtn = false;
    },
    // 导出记录
    exportFile(isDriver) {
      if(!this.currentBase.id){
          this.$message.warning('请先选择主体');
          return;
      }
      if(!this.date){
          this.$message.warning('请选择日期');
          return;
      }
       this.canExport=true;
        this.$http.post('/admin-center-server/taxProfile/taxProfileCashExport',{
          "baseId": this.currentBase.id,
          "dateEnd": this.date,
          "dateStart": this.getThreeMonthsBefore(this.date),
          "isDriver": isDriver
        }).then(res=>{
            let data = res.data;
            if(data.code===200){
                // let url = data.data;
                // window.location.href=url;
                this.$message({
                  message:'正在导出，稍后您可在【导出任务】中查看生成进度及下载文件',
                  type: 'success',
                })
                this.canExport=false;
            }else {
                this.$message.warning(data.message);
                this.canExport=false;
            }
        }).catch(error=>{
            this.canExport=false;
        })
    },
    getThreeMonthsBefore(date) {
      const d = new Date(date);
      d.setMonth(d.getMonth() - 3);
      return dateFormat(d, 'yyyy-MM-dd');
    },
    showTime(time){
      console.log(time,this.time)
      if(time == 'startDate'){
        return this.startDate
      }else if(time == 'date'){
        return this.date
      }else if(time == 'historicalTime'){
        return '2022-01-01'
      }
    }
  },
}

</script>
<style scoped lang='scss'>
.app-container{
 position: relative;
}
.fixed-all{
  position: fixed;
  width: calc(100vw - 236px);
  background: #f2f2f2;
  height: 50px;
  top: 93px;
  padding-top: 11px;
  padding-right: 20px;
  z-index: 99999;
}
.container-top{
 
  display: flex;
  justify-content: end;
  align-items: center;
  // margin-bottom: 20px;
  font-size: 14px;
  color: #000;
  ::v-deep .el-checkbox{
    color: #000;
  }
  ::v-deep .el-input__inner{
    height: 28px;
    line-height: 28px;
  }
  >div {
    margin-left: 20px;
  }
}
.base {
  font-size: 14px;
  i {
    transform: rotate(90deg);
    color: #f6a018;
    cursor: pointer;
  }
}
.iframe-class{
  width: 300px;
  height: 400px;
}
.bottom-class{
  position: absolute;
  bottom: 361px;
}
.bottom-but1{
  left: 264px;
}
.bottom-but2{
  left: 546px;
}
.cy-tag{
  background: #f56c6c;
  color: #fff;
  padding: 5px 10px;
  border-radius: 0px 6px 0px 6px;
  font-size: 10px;
  position: absolute;
  white-space: nowrap;
}
.js-tag{
  background: #44b991;
  color: #fff;
  padding: 5px 10px;
  border-radius: 0px 6px 0px 6px;
  font-size: 10px;
  position: absolute;
  white-space: nowrap;
}
::v-deep .el-button--mini{
  padding: 3px 12px;
}
.warn-pos {
  position: absolute;
  // left: 244px;
  // top: 314px;
  cursor: pointer;
  color: #4c5773;
}
.tooltip-class {
  
}
</style>