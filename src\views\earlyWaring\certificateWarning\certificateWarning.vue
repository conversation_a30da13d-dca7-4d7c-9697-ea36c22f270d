<template>
  <div class="waring_content">
    <el-tabs v-model="active" >
      <el-tab-pane label="司机证件" name="1"><DriverWaring/></el-tab-pane>
      <el-tab-pane label="车辆证件" name="2"><CarWaring/></el-tab-pane>
    </el-tabs>
  </div>
    
</template>

<script>
import CarWaring from './carWarning.vue'
import DriverWaring from './driverWarning.vue'
export default {
  components: {
    CarWaring,
    DriverWaring
  },
  data() {
    return {
       active: "1",
      }
  }
    
}
</script>

<style lang="scss" scoped>
.waring_content {
    margin: 20px;
    padding: 10px;
    background-color: white;
}

</style>