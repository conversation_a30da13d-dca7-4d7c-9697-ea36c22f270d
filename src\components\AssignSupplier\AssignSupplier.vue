<template>
  <div>
    <el-dialog
      :visible.sync="isShow"
      title="指派运力供应商"
      width="810px">
      <div v-if="!$store.getters.isOperated" class="warn">
        <i class="el-icon-warning-outline"></i> 首次被添加的运力供应商需输入完整的公司名称
      </div>
      <div class="info">
        <span class="info1">订单总货量：{{ volumeInteger(sum) }}{{ unitText }}</span>
        <span class="info2">剩余货量：{{ volumeInteger(max) }}{{ unitText }}</span>
      </div>
      <el-form
        ref="form"
        :model="form"
        :rules="rules">
        <template v-for="(item, index) in form">
          <el-form-item v-if="item !== null" :key="index" :prop="'item' + item.index">
            <div class="item">
              <div class="item-name">
                运力供应商：
                <el-select
                  v-model="item.transportationCapacityUserId"
                  remote
                  :remote-method="handleSupplierSearch"
                  @change="handleSupplierChange(item.index, item.transportationCapacityUserId)"
                  filterable
                  placeholder="输入公司名称搜索">
                  <el-option v-for="item in supplierList" :value="item.id" :label="item.name" :key="item.id" :disabled="item.disabled">
                    <div class="item-option-name">{{ item.name }}</div>
                    <div v-if="item.disabled" class="item-option-status">已添加</div>
                  </el-option>
                </el-select>
              </div>
              <div class="item-volume">
                分配货量：
                <el-input v-model="item.volume" :placeholder="residue">
                  <template slot="append">{{ unitText }}</template>
                </el-input>
              </div>
              <!-- <div class="item-freight">
                运费单价：
                <el-input v-model="item.freightPrice" placeholder="请输入">
                  <template slot="append">{{ priceUnitText }}</template>
                </el-input>
              </div> -->
              <div @click="del(item.index)" class="item-del">
                <i class="el-icon-minus"></i>
              </div>
            </div>
          </el-form-item>
        </template>
      </el-form>
      <div @click="add" class="add">
        <i class="el-icon-plus"></i>
      </div>
      <template #footer>
        <el-button @click="isShow = false">取消</el-button>
        <el-button @click="comfirm" type="primary">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { Big } from 'big.js'
const defaultItem = {
  transportationCapacityUserId: '',
  transportationCapacityUserName: '',
  volume: '',
  //freightPrice: ''
}
export default {
  data() {
    return {
      isShow: false,
      form: {},
      rules: {},
      index: 0,
      max: 0,
      sum: 0,
      unit: null,
      supplierList: [],
      id: null,
      defaultIdList: []
    }
  },
  computed: {
    volumeSum() {
      let sum = 0
      for (let i in this.form) {
        if (this.form[i]) sum = Big(sum).plus(Number(this.form[i].volume)).toNumber()
      }
      return sum
    },
    residue() {
      let residue = Big(this.max).minus(this.volumeSum).toNumber()
      if (residue < 0) return ''
      return '≤ ' + residue
    },
    unitText() {
      if (this.unit === '0') return '吨'
      else return '车'
    }
  },
  methods: {
    show({ id, left, sum, unit, defaultIdList }) {
      this.isShow = true
      this.id = id
      this.max = left
      this.sum = sum
      this.unit = unit
      this.form = {}
      this.rules = {}
      this.index = 0
      this.supplierList = []
      this.defaultIdList = defaultIdList
      this.add()
    },
    handleSupplierSearch(keyword) {
      if (!keyword) return
      let idList = ''
      for (let i in this.form) {
        let id = this.form[i].transportationCapacityUserId
        if (!id) continue
        idList += id + ','
      }
      idList = idList.slice(0, idList.length - 1) //去掉结尾逗号
      this.$post('/admin-center-server/driver/getCapacityProvider?name='+keyword)
        .then(res => {
          // res.forEach(v => {
          //   if (this.defaultIdList.includes(v.userId)) {
          //     v.disabled = true
          //   }
          // })
          this.supplierList = res.map((item) => {
            item.transportationCapacityUserId = item.id
            return item
          })
          console.log(this.supplierList);
        })
    },
    handleSupplierChange(index, supplierId) {
      let name = this.supplierList.find(item => item.id === supplierId).name
      console.log(name, index, supplierId);
      this.$set(this.form['item' + index], 'transportationCapacityUserName', name)
    },
    add() {
      let index = ++this.index,
        key = 'item' + index
      this.$set(this.form, key, {
        ...defaultItem,
        index
      })
      this.$set(this.rules, key, [{
        validator: (rule, value, cb) => {
          if (!this.form[key].transportationCapacityUserId || !this.form[key].volume) {
            cb('不能为空')
          }
          if (this.volumeSum > this.max) {
            cb('订单货量不足')
          }
          cb()
        },
        trigger: 'blur'
      }])
    },
    del(index) {
      this.$set(this.form, 'item' + index, null)
    },
    comfirm() {
      let list = []
      for (let i in this.form) {
        let item = this.form[i]
        if (!item) continue
        list.push({
          transportationCapacityUserId: item.transportationCapacityUserId,
          transportationCapacityUserName: item.transportationCapacityUserName,
          sumTon: item.volume
        })
      }
      if (list.length === 0) {
        this.isShow = false
        return
      }
      this.$refs.form.validate()
        .then(() => {
          this.$post('/order-center-server/app/orderDispatch/addCapacitySupplier', {
            capacityPublishList: list,
            orderCommonId: this.id
          })
            .then(() => {
              this.$message.success('操作成功')
              this.isShow = false
              this.$emit('assignSupplierSuccess')
            })
        })
    },
    volumeInteger(num) {
      if (this.unit === '1') return Number(num)
      return num
    }
  }
}
</script>

<style lang="scss" scoped>
.warn {
  margin: -25px -20px 20px -20px;
  padding: 5px 0 5px 20px;
  font-size: 12px;
  color: #E19B3B;
  background: rgb(253, 246, 236);
}
.info {
  color: rgb(51, 51, 51);
  font-size: 14px;
  .info1 {
    margin-right: 40px;
  }
}
.add {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40px;
  border: 1px solid rgb(220, 223, 230);
  cursor: pointer;
}
.el-form {
  margin-top: 30px;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 76px;
  padding: 0 15px;
  border: 1px solid rgb(220, 223, 230);
}
.item-name {
  .el-select {
    width:280px;
  }
}
.item-volume {
  margin-left: 20px;
  .el-input {
    width: 140px;
  }
}
.item-freight {
  margin-left: 20px;
  .el-input {
    width: 200px;
  }
}
.item-del {
  height: 40px;
  width: 40px;
  margin-left: 20px;
  border: 1px solid rgb(220, 223, 230);
  text-align: center;
  cursor: pointer;
}
.item-option-name {
  float: left;
}
.item-option-status {
  float: right;
}
</style>