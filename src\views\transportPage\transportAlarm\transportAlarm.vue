<template>
  <div class="app-container">
    <el-form
      ref="searchForm"
      :model="search"
      :inline="true"
      class="search"
      size="mini"
    >
      <el-form-item label="运单号：" prop="orderItemSn">
        <el-input
          placeholder="请输入运单号"
          v-model="search.orderItemSn"
        ></el-input>
      </el-form-item>
      <el-form-item label="司机姓名：" prop="driverName">
        <el-input
          placeholder="请输入司机姓名"
          v-model="search.driverName"
        ></el-input>
      </el-form-item>
      <el-form-item label="司机手机号：" prop="driverPhone">
        <el-input
          placeholder="请输入司机手机号"
          v-model="search.driverPhone"
        ></el-input>
      </el-form-item>
      <el-form-item label="车牌号：" prop="orderCreateUserName">
        <el-input
          placeholder="请输入车牌号"
          v-model="search.plateNumber"
        ></el-input>
      </el-form-item>
      <el-form-item label="报警类型：" prop="earlyWarnType">
        <el-select v-model="search.earlyWarnType" placeholder="请选择报警类型">
          <el-option label="超速" value="1"></el-option>
          <el-option label="疲劳驾驶" value="2"></el-option>
          <el-option label="设备离线" value="3"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button size="mini" @click="doSearch" type="primary">查询</el-button>
        <el-button size="mini" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="wrapper">
      <el-table
        :data="data"
        class="table"
        style="width: 100%"
        cell-class-name="table_cell_gray"
        header-cell-class-name="table_header_cell_gray"
      >
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column label="运单号" prop="orderItemSn">
          <template slot-scope="scope">
            <el-button type="text" @click="$router.push('/transport/transportListDetail?orderItemId=' + scope.row.orderItemId)">{{ scope.row.orderItemSn }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="报警类型" prop="earlyWarnType"></el-table-column>
        <el-table-column label="报警结果" prop="earlyWarnResult"></el-table-column>
        <el-table-column label="报警时间" prop="earlyWarnTime"></el-table-column>
        <el-table-column label="货主" prop="businessName"></el-table-column>
        <el-table-column label="司机姓名" prop="driverName"></el-table-column>
        <el-table-column label="司机手机号" prop="driverMobile"></el-table-column>
        <el-table-column label="车辆" prop="plateNumber"></el-table-column>
        <el-table-column label="运单状态" prop="orderItemStatusEnum"></el-table-column>

      </el-table>
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :total="total"
        :current-page.sync="page.pageNumber"
        @current-change="getList"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      page: {
        pageSize: 10,
        pageNumber: 1,
      },
      isSearching: false,
      search: {
        orderItemSn: "",
        driverName: "",
        driverPhone: "",
        plateNumber: "",
        earlyWarnType: '',
      },
      data: [],
      total: 0
    };
  },
  activated() {
    this.getList();
  },
  methods: {
    getList() {
      let params = {
        ...this.page,
        tab: '0',
      };
      if (this.isSearching) {
        Object.assign(params, this.search);
      }
      this.$post("/admin-center-server/orderItem/orderEarlyWarningList", params)
        .then((res) => {
          this.data = res.list;
          this.total = Number(res.total);
        })
        .catch(() => {
          this.data = [];
          this.total = 0;
        });
    },
    handleSizeChange(v) {
      this.page.pageSize = v;
      this.getList();
    },
    doSearch() {
      this.isSearching = true;
      this.page.pageNumber = 1;
      this.getList();
    },
    reset() {
      this.isSearching = false;
      this.$refs.searchForm.resetFields();
      this.search = {
        orderItemSn: "",
        driverName: "",
        driverPhone: "",
        plateNumber: "",
        earlyWarnType: '',
      }
      this.page.pageNumber = 1;
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
.search {
  padding: 20px;
  background: #fff;
}
.wrapper {
  margin-top: 10px;
  background: #fff;
}
.el-pagination {
  margin: 10px 0;
  text-align: right;
}
</style>