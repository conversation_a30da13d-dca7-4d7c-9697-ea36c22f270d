<template>
  <div class="app-container carsList">
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
        <div>
          <el-button class="releaseMessage" @click="addClassify">添加</el-button>
        </div>
      </div>
      <div class="list-main">
        <template>
          <el-table
            ref="multipleTable"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            border
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="maxName" label="分类名称"></el-table-column>
            <el-table-column prop="count" label="专题数量"></el-table-column>
            <el-table-column prop="categoryLocationEnum" label="所在客户端"></el-table-column>
            <el-table-column prop="sort" label="排序"></el-table-column>
            <el-table-column property="isShow" label="是否显示">
              <template scope="scope">
                <el-switch
                  on-text="是"
                  off-text="否"
                  on-color="#5B7BFA"
                  off-color="#dadde5"
                  v-model="scope.row.isShow"
                  @change="change(scope.$index,scope.row)"
                ></el-switch>
              </template>
            </el-table-column>

            <el-table-column fixed="right" label="操作" width="160">
              <template slot-scope="scope">
                <!-- <el-button @click="goDetail(scope.row)" type="text" size="small">查看</el-button> -->
                <el-button type="text" size="small" @click="edit(scope.row)">编辑</el-button>
                <el-button type="text" size="small" @click="delItem(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="flex">
            <div class="delAll">
              <el-button @click="deleteAll">批量删除</el-button>
            </div>
            <div class="pageSize">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="formInline.pageNumber"
                :page-sizes="[20, 40, 60, 80,100]"
                :page-size="formInline.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                style="margin: 10px auto"
              ></el-pagination>
            </div>
          </div>

          <!-- 添加的弹窗 -->
        </template>

        <el-dialog
          :title="dialogTitle"
          :visible.sync="dialogForClassify"
          class="addClassify"
          @close="closeDialog"
        >
          <el-form :model="ruleForm" label-width="100px" class="demo-ruleForm">
            <el-form-item label="类型名称" required>
              <el-input v-model="ruleForm.typeName" style="width: 200px" maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="分类位置" required>
              <el-select
                v-model="ruleForm.categoryLocation"
                placeholder="请选择分类位置"
                style="width: 200px"
                clearable
              >
                <el-option label="调度员端" value="0"></el-option>
                <el-option label="货主端" value="1"></el-option>
                <el-option label="司机端" value="2"></el-option>
                <el-option label="公共" value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="分类图标:">
              <el-upload
                :v-model="ruleForm.icon"
                action
                :file-list="fileList"
                :limit="1"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemove"
                :http-request="ossUpload"
                :class="{hide:hideUpload}"
              >
                <div class="upload-box">
                  <div class="icon-XZ"></div>
                  <div class="icon-word">支持jpg、jpeg、png 格式不超过10M</div>
                </div>
              </el-upload>
              <el-dialog :visible.sync="dialogVisible" size="tiny">
                <img width="100%" :src="dialogImageUrl" alt />
              </el-dialog>
            </el-form-item>
            <el-form-item label="是否显示">
              <el-switch v-model="ruleForm.isShow"></el-switch>
            </el-form-item>
            <el-form-item label="分类排序" required>
              <el-input
                v-model="ruleForm.sort"
                placeholder="请输入分类排序"
                style="width: 200px"
                maxlength="20"
                oninput="value=value.replace(/[^\d]/g,'')"
              ></el-input>
            </el-form-item>
          </el-form>

          <div slot="footer" class="dialog-footer">
            <el-button @click="dialogForClassify = false">取 消</el-button>
            <el-button type="primary" @click="addClassifyInfo" v-if="isFlag">确 定</el-button>
            <el-button type="primary" @click="update" v-if='!isFlag'>确 定</el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import { uploadFile } from '@/utils/file.js'
export default {
  name: "CarsList",
  comments: {},
  data() {
    return {
      isFlag:true, // 控制是否是 新增 和 编辑
      dialogVisible: false,
      hideUpload: false, //控制是否显示的样式
      fileList: [], //回显的图片
      ruleForm: {
        categoryLocation: "", //分类位置
        typeName: "",
        icon: "", //上传的图片
        isShow: true, //默认显示
        sort:''
      },

      dialogTitle: "添加分类", //对话框的标题
      dialogForClassify: false, //添加的弹窗
      total: null,
      formInline: {
        pageNumber: 1,
        pageSize: 20,
        order: "desc"
      },
      tableData: [],
      dialogImageUrl: ""
    };
  },
  activated() {
    this.getDataList();
  },
  methods: {
    onSubmit() {
      // console.log("submit!");
    },
    goDetail(row) {
      // console.log(row);
      // window.location.href=''
      this.$router.push("/carsList/carDetail");
      // this.$router.push('/dispatch/page/carsAttestation')
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList);
      this.hideUpload = false;
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
   /* 选取每页多少条数据 */
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.formInline.pageNumber =1;
      this.formInline.pageSize = JSON.parse(`${val}`);
      console.log(this.formInline.pageSize);
      this.getDataList();
    },
    /* 翻页 */
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.formInline.pageNumber = JSON.parse(`${val}`);
      // console.log(this.formInline.pageNumber);
      this.getDataList();
    },

    /* 编辑回显*/
    edit(row) {
      this.dialogForClassify = true;
      this.isFlag = false;
      this.dialogTitle = "编辑分类";
      //编辑的时候调用回显的接口
      let id = row.id;
      // let postData = {
      //   typeName: this.ruleForm.typeName, // 分类名称
      //   categoryLocation: this.ruleForm.categoryLocation, //分类位置
      //   sort: Number(this.ruleForm.sort), //排序
      //   isShow: this.ruleForm.isShow
      // };
      this.$http
        .get("/admin-center-server/articel/getByIdCusArticelSortId?id=" + id)
        .then(res => {
          let data = res.data;

          if (data.code === "200") {
            this.ruleForm.typeName = res.data.data.typeName; // 分类名称
            // console.log(res.data.data.icon);
            this.ruleForm.categoryLocation = res.data.data.categoryLocation; //分类位置
            this.ruleForm.sort = res.data.data.sort; //排序
            this.ruleForm.isShow = res.data.data.isShow;
            this.ruleForm.icon = res.data.data.icon; //图标

            //图片的回显
            this.fileList.push({
              url: res.data.data.icon
            });
             if(this.fileList.length >=1){
              this.hideUpload = true
            }
          }
        });
    },
    /* 单个删除功能 */
    delItem(row) {
      let id = row.id;
      // console.log(row.id, "-----id");
      let ids = [];
      ids[0] = Number(row.id);

      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http
            .post("/admin-center-server/articel/deleteByCusArticelSortIds", ids)
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
                this.getDataList();
              }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },

    /* 批量删除 */
    deleteAll() {
      var deleteAllData = this.multipleSelection; //批量删除的数据
      let ids = deleteAllData.map(obj => {
        return obj.id;
      });
      // console.log(ids, "----");
      this.dialogVisible = true;
      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http
            .post("/admin-center-server/articel/deleteByCusArticelSortIds", ids)
            .then(res => {
              let data = res.data;
              if (data.code == "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
                this.getDataList();
              }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },

    /* 表格中的开关 */
    change: function(index, row) {

      var postData = {
        ids: row.id,
        isShow: row.isShow
      };
      this.$http
        .post(
          "/admin-center-server/articel/updateByCusArticelSortIds",
          this.$qs.stringify(postData)
        )
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.getDataList();
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 获取数据 */
    getDataList() {
      var postData = this.formInline;
      // console.log(postData, "postData");
      this.$http
        .post("/admin-center-server/articel/queryCusArticelSortList", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.tableData = data.data.list;
            this.total = JSON.parse(data.data.total);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 上传图片 */
    ossUpload(param) {
      let file = param.file; // 得到文件的内容
      uploadFile(file).then(res => {
        if (res.res.status === 200) {
            // 上传
            let imgUrl = res.res.requestUrls[0];
            this.ruleForm.icon = imgUrl;

            console.log(this.ruleForm.icon,file, "----图片的地址");
            this.$message({
              type: "success",
              message: "上传成功"
            });
            this.hideUpload = true;
        } else {
            this.$message.error(res.res.message)
        }
      })
      // let name = file.name;
      // let index = file.name.indexOf(".");
      // let nameEnd = file.name.substr(index);
      // const ossApiUrl = "/base-center-server/sts/oss_signature"; // 获取oss签名的地址
      // // 获取oss签名
      // this.$http.get(ossApiUrl).then(res => {
      //   if (res.data.code == "200") {
      //     let data = res.data;
      //     let ossUrl = res.endpoint;
      //     name = this.$md5(name);
      //     let fileName =
      //       "/" + data.data.bucket + data.data.dir + name + nameEnd;
      //     let ossClient = new OSS.Wrapper({
      //       accessKeyId: data.data.accessKeyId,
      //       accessKeySecret: data.data.accessKeySecret,
      //       stsToken: data.data.securityToken,
      //       region: OSS_REGION,
      //       bucket: data.data.bucket
      //     });

      //     ossClient
      //       .multipartUpload(fileName, file)
      //       .then(res => {
      //         // 上传
      //         let imgUrl = res.res.requestUrls[0];
      //         this.ruleForm.icon = imgUrl;

      //         // console.log(this.ruleForm.icon, "----图片的地址");
      //         this.$message({
      //           type: "success",
      //           message: "上传成功"
      //         });
      //         this.hideUpload = true;
      //       })
      //       .catch(err => {
      //         this.$message({
      //           type: "fail",
      //           message: "上传失败"
      //         });
      //       });
        // }
      // });
    },


    /* 添加分类 */
    addClassify() {
      this.dialogForClassify = true;
      this.isFlag = true;
      this.dialogTitle = "添加分类";
    },

    /* 添加确定按钮 */
    addClassifyInfo() {
      if (this.ruleForm.typeName.trim() == "") {
        this.$message({
          type: "warning",
          message: "请输入类型名称"
        });
        return;
      }
      // console.log(this.ruleForm.categoryLocation);
      if (this.ruleForm.categoryLocation == "") {
        this.$message({
          type: "warning",
          message: "请选择分类位置"
        });
        return;
      }
      if (this.ruleForm.sort.trim() == "") {
        this.$message({
          type: "warning",
          message: "请输入分类排序"
        });
        return;
      }

      let postData = {
        typeName: this.ruleForm.typeName, // 分类名称
        categoryLocation: this.ruleForm.categoryLocation, //分类位置
        isShow: this.ruleForm.isShow,
        icon: this.ruleForm.icon,
        sort:this.ruleForm.sort
      };

      this.$http
        .post("/admin-center-server/articel/addCategory", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "添加成功!"
            });
            this.ruleForm = {};
            this.dialogForClassify = false;
            this.getDataList();
          } else {
            this.$message.warning(data.message);
          }
        });
    },

    /* 关闭对话框事件 */
    closeDialog() {
      this.ruleForm.typeName = ""; // 分类名称
      this.ruleForm.categoryLocation = ""; //分类位置
      this.ruleForm.sort = ""; //排序
      this.ruleForm.isShow = true;
      this.ruleForm.icon = true;
      this.fileList=[]
    },

    /* 编辑完提交 */
    update(){
      let postData=this.ruleForm;
      // console.log(postData)
       this.$http
        .post("/admin-center-server/articel/updateCusArticelSort", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "更新成功!"
            });
            this.dialogForClassify = false; //关闭弹窗
            this.getDataList() //刷新数据
          } else {
            this.$message.warning(data.message);
          }
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.hide .el-upload--picture-card {
  display: none;
}

.addClassify el-form {
  margin: 100px;
  display: block;
}
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 10px;
    }
  }
  .flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }

  .upload-box {
    width: 100%;
    height: 100%;
    position: relative;
    .icon-XZ {
      width: 92px;
      height: 92px;
      margin: 0 auto;
      background: url("../../../../assets/xiazai.png") no-repeat;
      background-size: 100% 100%;
    }
    .icon-word {
      width: 100%;
      height: 20px;
      line-height: 20px;
      font-size: 10px;
      position: absolute;
      bottom: 25px;
      left: 0px;
      color: #cccccc;
    }
  }
}
</style>
