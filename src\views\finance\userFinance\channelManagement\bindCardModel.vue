<template>
    <el-dialog
        :close-on-click-modal="false"
        title="绑定银行卡"
        :visible.sync="open"
        destroy-on-close
        @close="cancel"
        width="800px"
    >
        <el-row v-loading="loading">
            <el-form
                ref="form"
                label-width="100px"
                :model="form"
                :rules="rules"
            >
                <el-row class="global-div-search">
                    <el-col :span="24">
                        <el-form-item label="姓名" prop="cardOwnerName">
                            <el-input
                                v-model="form.cardOwnerName"
                                clearable
                                placeholder="请输入持卡人姓名"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="身份证" prop="idCard">
                            <el-input
                                v-model="form.idCard"
                                clearable
                                placeholder="请输入持卡人身份证号"
                                maxlength="18"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="卡号" prop="acctNo">
                            <el-input
                                @input="
                                    () => {
                                        form.acctNo = form.acctNo.replace(
                                            /^0{1}|[^\d]/g,
                                            ''
                                        );
                                    }
                                "
                                v-model="form.acctNo"
                                clearable
                                placeholder="请输入持卡人银行卡号"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="开户行" prop="bankName">
                            <el-select
                                v-model="form.eiconBankBranchId"
                                clearable
                                filterable
                                remote
                                placeholder="请输入关键字搜索"
                                :remote-method="querySearch"
                                @clear="querySearch"
                                @change="handleSelect"
                            >
                                <el-option
                                    v-for="(item, index) in bankNameList"
                                    :key="index"
                                    :label="item.bankname"
                                    :value="item.bankno"
                                >
                                </el-option>
                            </el-select>
                            <!-- <el-autocomplete
                                clearable
                                class="inline-input"
                                v-model="form.bankName"
                                :fetch-suggestions="querySearch"
                                :trigger-on-focus="false"
                                @select="handleSelect"
                                placeholder="请输入关键字搜索"
                                @input="
                                    () => {
                                        form.bankClscode = undefined;
                                        form.addrProvince = undefined;
                                        form.addrCity = undefined;
                                        form.bankKeyWord = undefined;
                                        form.cnapsBranchId = undefined;
                                        form.eiconBankBranchId = undefined;
                                        areaList.citys = [];
                                    }
                                "
                            ></el-autocomplete> -->
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="手机号" prop="mobile">
                            <el-input
                                @input="
                                    () => {
                                        form.mobile = form.mobile.replace(
                                            /[^\d]/g,
                                            ''
                                        );
                                    }
                                "
                                v-model="form.mobile"
                                clearable
                                placeholder="请输入开户人手机号"
                                maxlength="11"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-form
                ref="form2"
                label-width="100px"
                :model="form"
                :rules="rules"
            >
                <el-row :gutter="10">
                    <el-col :span="18">
                        <el-form-item
                            label="手机验证码"
                            prop="verificationCode"
                        >
                            <el-input
                                @input="
                                    () => {
                                        form.verificationCode =
                                            form.verificationCode.replace(
                                                /[^\d]/g,
                                                ''
                                            );
                                    }
                                "
                                v-model="form.verificationCode"
                                clearable
                                placeholder="请输入验证码"
                                maxlength="6"
                            ></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-button
                            type="primary"
                            style="width: 100%"
                            plain
                            @click="handleCode"
                            :disabled="verificationCodeTitle != '获取验证码'"
                            :loading="verificationCodeTitle == '获取验证码 '"
                            >{{ verificationCodeTitle }}</el-button
                        >
                    </el-col>
                </el-row>
            </el-form>
        </el-row>
        <div slot="footer" class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
import userDealVue from "../userDeal/userDeal.vue";
export default {
    name: "bindCardModel",
    props: {},
    data() {
        return {
            open: true,
            loading: false,
            verificationCodeTitle: "获取验证码",
            timer: null,
            form: {
                userSn: undefined,
                cardOwnerName: undefined,
                idCard: undefined,
                acctNo: undefined,
                bankName: undefined,
                addrProvince: undefined,
                addrCity: undefined,
                bankKeyWord: undefined,
                mobile: undefined,
                verificationCode: undefined,
                bankClscode: undefined,
                cnapsBranchId: undefined,
                eiconBankBranchId: undefined,
                bankLogo: undefined,
            },
            bankNameList: [],
            bankKeyWordList: [],
            areaList: {
                provinces: [],
                citys: [],
            },
            rules: {
                cardOwnerName: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (!value) {
                                callback(new Error("请输入开户人姓名"));
                            } else if (
                                !/^([\u4e00-\u9fa5]{2,20}|[a-zA-Z.\s]{2,20})$/.test(
                                    value
                                )
                            ) {
                                callback(
                                    new Error("只允许输入2~20位中文或英文")
                                );
                            } else {
                                callback();
                            }
                        },
                        trigger: "blur",
                    },
                ],
                idCard: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (!value) {
                                callback(new Error("请输入身份证号"));
                            } else if (
                                !/^[1-9]\d{5}(18|19|20|(3\d))\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(
                                    value
                                )
                            ) {
                                callback(new Error("请输入正确格式的身份证号"));
                            } else {
                                callback();
                            }
                        },
                        trigger: "blur",
                    },
                ],
                acctNo: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (!value) {
                                callback(new Error("请输入持卡人银行卡号"));
                            } else if (!/^[1-9]{1}[0-9]{13,18}$/.test(value)) {
                                callback(new Error("请输入14~19位的银行卡号"));
                            } else {
                                callback();
                            }
                        },
                        trigger: "blur",
                    },
                ],
                bankName: [
                    {
                        required: true,
                        message: "请输入关键字搜索",
                    },
                ],
                // address: [
                //     {
                //         required: true,
                //         validator: (rule, value, callback) => {
                //             if (!this.form.addrProvince) {
                //                 callback("请选择省");
                //             } else if (!this.form.addrCity) {
                //                 callback("请选择市");
                //             } else if (!this.form.cnapsBranchId) {
                //                 callback("请输入关键字搜索");
                //             } else {
                //                 callback();
                //             }
                //         },
                //     },
                // ],
                mobile: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (!value) {
                                callback(new Error("请输入开户人手机号"));
                            } else if (
                                !/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(value)
                            ) {
                                callback(new Error("请输入正确格式的手机号"));
                            } else {
                                callback();
                            }
                        },
                        trigger: "blur",
                    },
                ],
                verificationCode: [
                    {
                        required: true,
                        validator: async (rule, value, callback) => {
                            if (!value) {
                                callback(new Error("请输入验证码"));
                            } else if ("" + value.length < 6) {
                                callback(new Error("请输入六位验证码"));
                            } else {
                                callback();
                            }
                        },
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    /** 事件监听 */
    watch: {},

    /** 计算属性 */
    computed: {},

    /** 生命周期 -- 实例创建后调用 */
    created() {},

    /** 生命周期 -- 实例挂载后调用 */
    mounted() {},

    /** 生命周期 -- 实例销毁后调用 */
    destroyed() {
        clearInterval(this.timer);
    },

    methods: {
        async bind(row) {
            this.reset(4);
            this.form.userSn = row.userSn;
            this.areaList.provinces = await this.$get(
                "/pay-center-server/pubPayNode/find"
            ).then((res) => res);
        },

        cancel() {
            this.reset(4);
            this.$emit("cancel");
        },

        // 表单重置
        reset(status) {
            this.form = {
                userSn: undefined,
                cardOwnerName: undefined,
                idCard: undefined,
                acctNo: undefined,
                bankName: undefined,
                addrProvince: undefined,
                addrCity: undefined,
                bankKeyWord: undefined,
                mobile: undefined,
                verificationCode: undefined,
                bankClscode: undefined,
                cnapsBranchId: undefined,
                eiconBankBranchId: undefined,
                bankLogo: "",
            };
            // this.$refs.form.resetFields();
            this.status = status;
            switch (status) {
                case 1: // 新增
                    break;
                case 2: // 编辑
                    break;
                case 3: // 详情
                    break;
                case 4: // 取消
                    break;
            }
        },

        /** 提交按钮 */
        submitForm: function () {
            this.$refs["form"].validate((valid) => {
                this.$refs["form2"].validate((valid2) => {
                    if (valid && valid2) {
                        // if (!this.form.eiconBankBranchId) {
                        //     this.$message({
                        //         showClose: true,
                        //         message:
                        //             "注意！您所选择的开户支行超级网银号获取失败，请联系管理人员核实！！！",
                        //         type: "error",
                        //         duration: 6000,
                        //     });
                        //     return;
                        // }
                        this.loading = true;
                        this.$post(
                            "/admin-center-server/commonUser/bindBankCard/insert",
                            this.form
                        )
                            .then((res) => {
                                this.$message.success("绑定成功");
                                this.loading = false;
                                clearInterval(this.timer);
                                this.reset();
                                this.$emit("success");
                            })
                            .catch(() => {
                                this.loading = false;
                            });
                    }
                });
            });
        },

        async querySearch(queryString) {
            this.bankNameList = queryString
                ? (await this.$get("/pay-center-server/pubPayBanka/getSuperbankcodeByName", {
                      bankName: queryString,
                  }).then((res) => res)) || []
                : [];
        },

        handleSelect(value) {
            for (let i = 0; i < this.bankNameList.length; i++) {
                 if (this.bankNameList[i].bankno == value) {
                     this.form.bankName = this.bankNameList[i].bankname
                 }
            }
            this.form.addrProvince = undefined;
            this.form.addrCity = undefined;
            this.form.bankKeyWord = undefined;
            this.form.cnapsBranchId = undefined;
            this.areaList.citys = [];
            this.bankKeyWordList = [];
        },

        // async querySearch(queryString, cb) {
        //     var restaurants = queryString
        //         ? (await this.$get("/pay-center-server/pubApppar/find", {
        //               aprCode: "BANK_TYPE_ALL",
        //               aprShowmsg: queryString,
        //           }).then((res) =>
        //               res.map((item) => ({
        //                   value: item.aprShowmsg,
        //                   name: item.aprValue,
        //               }))
        //           )) || []
        //         : [];
        //     var results = queryString
        //         ? restaurants.filter(
        //               (item) =>
        //                   item.value
        //                       .toLowerCase()
        //                       .indexOf(queryString.toLowerCase()) > -1
        //           )
        //         : restaurants;
        //     cb(results);
        // },

        // //搜索选中
        // handleSelect(item) {
        //     this.form.bankClscode = item.name;
        // },

        async querySearchTwo(queryString) {
            this.bankKeyWordList =
                queryString && this.form.bankClscode && this.form.addrCity
                    ? (await this.$get("/pay-center-server/pubPayBanka/find", {
                          bankClscode: this.form.bankClscode,
                          bankCitycode: this.form.addrCity,
                          bankLname: queryString,
                      }).then((res) => res)) || []
                    : [];
        },

        handleSelectTwo(value) {
            if (value) {
                let item = this.bankKeyWordList.find(
                    (item) => item.bankBnkcode == value
                );
                this.form.bankKeyWord = item.bankLname;
                this.$http
                    .get("/pay-center-server/pubPayBanka/getSuperbankcode", {
                        params: {
                            bankDreccode: item.bankDreccode,
                            bankLname: item.bankLname,
                            bankBnkcode: item.bankBnkcode,
                        },
                    })
                    .then((res) => {
                        if (!res.data.data) {
                            this.$message({
                                showClose: true,
                                message:
                                    "注意！您所选择的开户支行超级网银号获取失败，请联系管理人员核实！！！",
                                type: "error",
                                duration: 6000,
                            });
                            return;
                        }
                        this.form.eiconBankBranchId = res.data.data;
                    });
            } else {
                this.form.bankKeyWord = undefined;
                this.form.eiconBankBranchId = undefined;
            }
        },

        // async querySearchTwo(queryString, cb) {
        //     var restaurants =
        //         queryString && this.form.bankClscode && this.form.addrCity
        //             ? (await this.$get("/pay-center-server/pubPayBanka/find", {
        //                   bankClscode: this.form.bankClscode,
        //                   bankCitycode: this.form.addrCity,
        //                   bankLname: queryString,
        //               }).then((res) =>
        //                   res.map((item) => ({
        //                       value: item.bankLname,
        //                       bankBnkcode: item.bankBnkcode,
        //                       bankDreccode: item.bankDreccode,
        //                   }))
        //               )) || []
        //             : [];
        //     var results = queryString
        //         ? restaurants.filter(
        //               (item) =>
        //                   item.value
        //                       .toLowerCase()
        //                       .indexOf(queryString.toLowerCase()) > -1
        //           )
        //         : restaurants;
        //     cb(results);
        // },

        // handleSelectTwo(item) {
        //     this.form.cnapsBranchId = item.bankBnkcode;
        //     this.$http
        //         .get("/pay-center-server/pubPayBanka/getSuperbankcode", {
        //             params: {
        //                 bankDreccode: item.bankDreccode,
        //                 bankLname: item.value,
        //                 bankBnkcode: item.bankBnkcode,
        //             },
        //         })
        //         .then((res) => {
        //             this.form.eiconBankBranchId = res.data.data;
        //         });
        // },

        handleCode() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    // if (!this.form.eiconBankBranchId) {
                    //     this.$message({
                    //         showClose: true,
                    //         message:
                    //             "注意！您所选择的开户支行超级网银号获取失败，请联系管理人员核实！！！",
                    //         type: "error",
                    //         duration: 6000,
                    //     });
                    //     return;
                    // }
                    this.verificationCodeTitle = "获取验证码 ";
                    const {
                        userSn,
                        cardOwnerName,
                        idCard,
                        acctNo,
                        bankName,
                        mobile,
                        eiconBankBranchId,
                        cnapsBranchId,
                    } = this.form;
                    this.$post(
                        "/admin-center-server/commonUser/bindBankCard/sendVerificationCode",
                        {
                            userSn,
                            acctNo,
                            bankName,
                            cardOwnerName,
                            idCard,
                            mobile,
                            cnapsBranchId,
                            eiconBankBranchId,
                        }
                    )
                        .then((res) => {
                            this.$message.success("发送成功");
                            let second = 60;
                            this.timer = setInterval(() => {
                                second--;
                                this.verificationCodeTitle =
                                    second + "s 后重新发送";
                                if (second == 0) {
                                    this.verificationCodeTitle = "获取验证码";
                                    clearInterval(this.timer);
                                }
                            }, 1000);
                        })
                        .catch((err) => {
                            if (this.verificationCodeTitle == "获取验证码 ") {
                                this.verificationCodeTitle = "获取验证码";
                            }
                        });
                }
            });
        },
        async onProvinces(value) {
            this.areaList.citys = [];
            this.bankKeyWordList = [];
            this.form.addrCity = undefined;
            this.form.bankKeyWord = undefined;
            this.form.cnapsBranchId = undefined;
            this.form.eiconBankBranchId = undefined;
            this.areaList.citys =
                value && this.form.bankClscode
                    ? (await this.$get("/pay-center-server/pubPayCity/find", {
                          bankClscode: this.form.bankClscode,
                          cityNodecode: value,
                      }).then((res) => res)) || []
                    : [];
        },

        onCity() {
            this.form.bankKeyWord = undefined;
            this.form.cnapsBranchId = undefined;
            this.form.eiconBankBranchId = undefined;
            this.bankKeyWordList = [];
        },

        handleFocus() {
            if (!this.form.bankClscode) {
                this.$refs.form.validateField("bankClscode");
                return;
            }
        },
    },
};
</script>
<style scoped type="scss">
.warning {
    color: #ff0000;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
}
.global-div-search {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    /* font-size: 14px; */
    /* color: #555; */
    position: relative;
}

.el-select {
    width: 100% !important;
}

.el-date-editor.el-input,
.el-date-editor.el-input__inner {
    width: 100% !important;
}
.el-input-number--medium,
.inline-input {
    width: 100%;
}
</style>
