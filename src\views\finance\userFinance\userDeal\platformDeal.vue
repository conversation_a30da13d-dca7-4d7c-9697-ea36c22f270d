<template>
  <div class="userDeal">
    <div class="select-box">
      <el-button type="primary" class="imgBtn" @click="showImg">{{activeNameText}}收支示意图</el-button>
      <el-image class="imgShow" ref="bigImg" :preview-src-list="srcList" :src="require('@/assets/images/warn.png')"></el-image>

      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="垫款账户" name="first">
          <SubAccount v-show="activeName=='first'" :activeName='"first"' :baseId='baseId' :subsidyList="subsidyList" :allTransactionType="allTransactionType"></SubAccount>
        </el-tab-pane>
        <el-tab-pane label="运费差额账户" name="second">
          <SubAccount v-show="activeName=='second'" :activeName='"second"' :baseId='baseId' :subsidyList="subsidyList" :allTransactionType="allTransactionType"></SubAccount>
        </el-tab-pane>
        <el-tab-pane label="服务费账户" name="third">
          <SubAccount v-show="activeName=='third'" :activeName='"third"' :baseId='baseId' :subsidyList="subsidyList" :allTransactionType="allTransactionType"></SubAccount>
        </el-tab-pane>
        <el-tab-pane label="油费账户" name="fourth">
          <SubAccount v-show="activeName=='fourth'" :activeName='"fourth"' :baseId='baseId' :subsidyList="subsidyList" :allTransactionType="allTransactionType"></SubAccount>
        </el-tab-pane>
      </el-tabs>
    </div>

  </div>
</template>
<script>
import SubAccount from "./subAccount";
export default {
  components: { SubAccount },
  data() {
    return {
      activeName: 'first',
      total: 1,
      pageSize: 20,
      pageNumber: 1,
      date: [],
      payTypeList: [],
      exportData: {},
      dataSum: "",
      currentBaseId: '',
      // 收入/支出全选
      inAllSelect: false,
      outAllSelect: false,
      // 收入/支出手动转账全选
      manualInAll: false,
      manualOutAll: false,
      // 全部交易类型
      allTransactionType: [],
      //收入/支出类型
      inListSelect: [],
      outListSelect: [],
      //补贴不确定状态标志
      isIndeterminate: false,
      // 手动转账不确定状态标志
      manualOutStatus: false,
      manualInStatus: false,
      // 补贴类型
      subsidyList: [],
      // 手动转账类型列表-支出
      manualTransferList: [],
      // 手动转账类型列表-收入
      manualTransferInList: [],
      // 手动-支出列表
      manualOutList: [],
      //手动-收入列表
      manualInList: [],
      showTips: false,
      srcList: [
        require('@/assets/images/warn.png')
      ],
      allImg: {
        'first': require('@/assets/images/note/diankuan.png'),
        'second': require('@/assets/images/note/yunfei.png'),
        'third': require('@/assets/images/note/fuwu.png'),
        'fourth': require('@/assets/images/note/youfei.png')
      }




    };
  },
  computed: {

    // 类型
    accountTypeAct() {
      switch (this.activeName) {
        case 'first':
          return '3'

        case 'second':
          return '4'

        case 'third':
          return '2'

        case 'fourth':
          return '1'

        default:
          return ''
            ;
      }
    },
    activeNameText() {
      switch (this.activeName) {
        case 'first':
          return '垫款账户'
        case 'second':
          return '运费差额账户'
        case 'third':
          return '服务费账户'
        case 'fourth':
          return '油费账户'
        default:
          return ''
      }
    },

  },
  props: ['baseId'],
  watch: {

  },
  methods: {
    // 获取下游补贴类型
    getSubsidyDownList() {
      this.$get('/order-center-server/order/dict/findDictByType?dictType=subsidyDown').then(res => {
        this.subsidyList = res
      })
    },
    // 获取交易类型
    getAllTransactionType() {
      this.$get(
        "/admin-center-server/transaction/flow/queryPlatformAccountTransactionType"
      ).then((res) => {
        this.allTransactionType = res
      });

    },
    // 点击预览
    showImg() {
      this.$refs.bigImg.showViewer = true
      this.srcList = [this.allImg[this.activeName]]
    }





  },
  created() {
    this.getSubsidyDownList()
    this.getAllTransactionType()
    // 获取url 子类型参数
    this.activeName = this.$route.query.accountSonType || 'first'

  },
  mounted() {

  },
  activated() {

  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.userDeal {
  .select-box {
    background-color: #ffffff;
    position: relative;
    .select-info {
      // padding-bottom: 15px;
    }
    .imgBtn {
      position: absolute;
      right: 0;
      z-index: 5;
    }
    ::v-deep.imgShow {
      position: absolute;
      right: -999px;
      .el-image-viewer__mask {
        background-color: #fff;
        opacity: 1;
      }
    }
  }
  .list-box {
    background-color: #ffffff;
    overflow: hidden;
    .list-title {
      // display: flex;
      // flex-direction: row;
      // justify-content: space-between;
      // align-items: flex-start;
      .list-title-left {
        .inList,
        .outList {
          margin-top: 15px;
          display: flex;
          .checkText {
            flex-shrink: 0;
            font-weight: 700;
            font-size: 14px;
            color: #606266;
            margin-right: 3px;
          }
          .symbol {
            margin-right: 10px;
          }
        }
      }

      .list-title-right {
        margin-top: 15px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
    }
    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .paging {
      margin-top: 10px;
      float: right;
    }
  }
}
.el-icon-warning {
  //橘色
  color: #f6a018;
}
.el-icon-arrow-down {
  font-size: 10px;
  cursor: pointer;
  margin-left: 3px;
}
.mr15 {
  margin-right: 12px;
}
.popBox {
  .el-checkbox-group {
    padding-top: 10px;
    display: flex;
    flex-wrap: wrap;
  }
  .el-checkbox {
    width: 150px;
    margin-right: 5px;
    margin-bottom: 10px;
  }
}
.manual.popBox {
  .el-checkbox-group {
    padding-top: 10px;
    display: flex;
    flex-wrap: wrap;
  }
  .el-checkbox {
    width: 19%;
    margin-right: 5px;
    margin-bottom: 10px;
  }
}
.text {
  color: #e32c4d;
  font-size: 14px;
  text-align: right;
  flex: 1;
  margin-right: 10px;
}
</style>
