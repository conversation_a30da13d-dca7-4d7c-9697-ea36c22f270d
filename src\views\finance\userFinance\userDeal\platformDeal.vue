<template>
  <div class="userDeal">
    <div class="select-box">
      <el-tabs v-model="activeName" type="card" @tab-click="handleTabClick">
        <el-tab-pane label="垫款账户" name="first"></el-tab-pane>
        <el-tab-pane label="运费差额账户" name="second"></el-tab-pane>
        <el-tab-pane label="服务费账户" name="third"></el-tab-pane>
        <el-tab-pane label="油费账户" name="fourth"></el-tab-pane>
      </el-tabs>

      <div class="select-info">
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          label-width="75px"
          size="mini"
        >
          <el-form-item label="流水号:">
            <el-input class="form-item-content-width" v-model="formInline.transactionSn" placeholder="请输入流水号"></el-input>
          </el-form-item>
          <el-form-item label="收支类型:" >
            <el-select class="form-item-content-width" v-model="formInline.inOrOut" placeholder="不限">
              <el-option label="收入" value="1"></el-option>
              <el-option label="支出" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="交易类型:">
            <el-select class="form-item-content-width" v-model="formInline.transactionType" @visible-change="handlePayTypeSelectorClick" placeholder="不限">
              <el-option v-for="item in payTypeList" :key="item.value" :label="item.comment" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          
          
          <el-form-item label="付款人:">
            <el-input class="form-item-content-width" placeholder="请输入付款人" v-model="formInline.payer"> </el-input>
          </el-form-item>
          <el-form-item label="付款账号:">
            <el-input class="form-item-content-width"  v-model="formInline.paymentAccount" placeholder="请输入付款账号" ></el-input>
          </el-form-item>
          <el-form-item label="收款人:">
            <el-input class="form-item-content-width" v-model="formInline.payee" placeholder="请输入收款人" ></el-input>
          </el-form-item>
          <el-form-item label="收款账号:">
            <el-input class="form-item-content-width" v-model="formInline.receiptAccount" placeholder="请输入收款账号"></el-input>
          </el-form-item>
          <el-form-item label="交易时间:">
            <el-date-picker
              v-model="date"
              :clearable="false"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="onSubmit" size="mini">查询</el-button>
            <el-button icon="el-icon-delete" type="danger" @click="clearForm" size="mini">清空筛选</el-button >
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
       <div class="list-title">
        <div>数据列表</div>
        <div class="list-title-right">
          <div v-if="dataSum" style="font-size: 14px">
            当前列表中'交易金额'合计：￥{{ dataSum }}
          </div>
          <el-button
            type="primary"
            plain
            style="margin-left: 10px; margin-right: 10px"
            @click="getSum"
            >计算合计交易金额</el-button
          >
        </div>
      </div>
      <div class="list-main">
        <template>
          <el-table
            :data="tableData"
            border
            style="width: 100%"
            class="table"
            cell-class-name="table_cell_gray"
            header-cell-class-name="table_header_cell_gray"
          >
            <el-table-column fixed type="index" label="序号" width="50"></el-table-column>
            <el-table-column fixed  prop="transactionSn" label="流水号"></el-table-column>
            <el-table-column  prop="transactionAmount" label="交易金额"></el-table-column>
            <el-table-column  prop="transactionTypeValue" label="交易类型"></el-table-column>
            <el-table-column  prop="inOrOutValue" label="收支类型"></el-table-column>
            <el-table-column  prop="payer" label="付款人"></el-table-column>
            <el-table-column  prop="paymentAccount" label="付款账户"></el-table-column>
            <el-table-column  prop="paymentAccountBank" label="开户行"></el-table-column>
            <el-table-column  prop="payee" label="收款人"></el-table-column>
            <el-table-column  prop="receiptAccount" label="收款账户"></el-table-column>
            <el-table-column  prop="receiptAccountBank" label="开户行"></el-table-column>
            <el-table-column  prop="remark" label="备注"></el-table-column>
            <el-table-column  prop="transactionTime" label="交易时间"></el-table-column>
            <el-table-column
            fixed="right"
            label="操作"
            width="100">
            <template slot-scope="scope">
                <el-button @click="handleClick(scope.row)" type="text" size="small">详情</el-button>
            </template>
            </el-table-column>
          </el-table>
        </template>
      </div>
      <div class="paging">
        <div class="block">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pageNumber"
            :page-sizes="[10, 20, 30, 40, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      activeName: 'first',
      formInline: {},
      tableData: [],
      total: 1,
      pageSize: 20,
      pageNumber: 1,
      date: [],
      payTypeList: [],
      exportData: {},
      dataSum: "",
      currentBaseId: ''
    };
  },
  props: ['baseId'],
  watch: {
    baseId(newVal, oldVal) {
      this.formInline = {};
      this.pageNumber = 1
      this.currentBaseId = newVal
      this.getDataList()
    }
  },
  methods: {
    handleTabClick() {
      this.formInline = {};
      this.getDataList();
    },
    /** 清空筛选 **/
    clearForm() {
      this.formInline = {};
      this.date = []
      this.getDataList();
    },
    /** 按条件查询 **/
    onSubmit() {
      this.pageNumber = 1;
      this.getDataList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNumber = 1;
      this.getDataList();
    },
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.getDataList();
    },
    getDataList() {
      let params = {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        ...this.formInline,
      };
      if (this.date && this.date.length !== 0) {
        params.startTime = this.date[0];
        params.endTime = this.date[1];
      }
      let accountType = ''
      switch (this.activeName) {
        case 'first':
          accountType = '3'
          break
        case 'second':
          accountType = '4'
          break
        case 'third':
          accountType = '2'
          break
        case 'fourth':
          accountType = '1'
          break
        default:
          accountType = ''
          break;
      }
      params.accountType = accountType
      params.baseId = this.currentBaseId
      if (this.activeName === 'first' && params.transactionType) {
        this.payTypeList.forEach(item => {
          if (item.value === params.transactionType) {
            if (item.isAdd) {
              params.transactionTypeString = params.transactionType
              params.transactionType = null
            }
          }
        })
      }

      this.$post(
          "/admin-center-server/transaction/flow/querySubAccountTransactionPage",
          params
        ).then((res) => {
          this.tableData = res.list;
          this.total = Number(res.total);
          this.getSum()
      });
    },
    getPayTypeList(){
      this.$get(
          "/order-center-server/order/dict/findDictByType?dictType=subsidyDown"
        ).then((res) => {
          if (res && res.length > 0) {
            res.forEach(element => {
              this.payTypeList.push({
                comment: element.label,
                value: element.value,
                isAdd:true
              });
            });
          }
        });
    },
    handlePayTypeSelectorClick(type) {
      switch (this.activeName) {
        case 'first':
          this.payTypeList = [{value: '1', comment: '垫运费'}, 
                              {value: '2', comment: '装货付油费'},
                              {value: '3', comment: '卸货付'},
                              {value: '4', comment: '结算付'},
                              {value: '5', comment: '还运费'},
                              {value: '6', comment: '回单付'},
                              {value: '7', comment: '退还油费'}
                            ]
          this.getPayTypeList()                  
          break
        case 'second':
          this.payTypeList = [{value: '1', comment: '运费差'}, {value: '2', comment: '运费差提现'},]
          break
        case 'third':
          this.payTypeList = [{value: '1', comment: '服务费'}, {value: '2', comment: '服务费提现'}]  
          break
        case 'fourth':
          this.payTypeList = [{value: '1', comment: '装货付油费'}, 
                              {value: '2', comment: '充油费'}, 
                              {value: '3', comment: '油费提现'},  
                              {value: '4', comment: '退还油费'},
                              {value: '5', comment: '油费转现金'},
                              ]
          break
        default:
          this.payTypeList = []
          break;
      }
      // console.log(type);
      // if (!type) return;
      // this.$post(
      //   "/admin-center-server/transaction/flow/getTransactionDetailType"
      // ).then((res) => {
      //   this.payTypeList = res;
      //   if (this.$store.state.user.userInfo2.hasStandardModeFlag) {
      //     this.payTypeList = res.filter((item) =>
      //       ["0", "1", "4"].includes(item.value)
      //     );
      //   } else {
      //     this.payTypeList = res;
      //   }
      // });
    },
    handleClick(item) {
      this.$router.push({
        path: 'dealDetail',
        query: {
          ...item,
          accountType: 2
        }
      })
    },
    getSum() {
      let accountType = ''
      switch (this.activeName) {
        case 'first':
          accountType = '3'
          break
        case 'second':
          accountType = '4'
          break
        case 'third':
          accountType = '2'
          break
        case 'fourth':
          accountType = '1'
          break
        default:
          accountType = ''
          break;
      }
      let params = {
        accountType: accountType,
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        baseId: this.currentBaseId,
        ...this.formInline,
      };
      if (this.date && this.date.length !== 0) {
        params.startTime = this.date[0];
        params.endTime = this.date[1];
      }
      if (this.activeName === 'first' && params.transactionType) {
        this.payTypeList.forEach(item => {
          if (item.value === params.transactionType) {
            if (item.isAdd) {
              params.transactionTypeString = params.transactionType
              params.transactionType = null
            }
          }
        })
      }
      this.$post(
        "/admin-center-server/transaction/flow/querySubAccountTransactionSum",
        params
      ).then((res) => {
        this.dataSum = res;
      });
    }
  },
  activated() {
    this.getDataList();
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.userDeal {
  .select-box {
    background-color: #ffffff;
    .select-info {
      padding-bottom: 30px;
    }
  }
  .list-box {
    background-color: #ffffff;
    overflow: hidden;
    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .list-title-right {
        display: flex;
      }
      div {
        height: 38px;
        line-height: 38px;
      }
    }
    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .paging {
      margin-top: 10px;
      float: right;
    }
  }
}

</style>
