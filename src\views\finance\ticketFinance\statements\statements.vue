/**
结算单管理 列表页
**/
<template>
    <div class="app-container carsList">
        <div class="select-box">
            <div class="top-title">
                <div>筛选查询</div>
                <div class="button">
                    <el-button
                            class="left"
                            icon="el-icon-refresh-right"
                            size="mini"
                            type="success"
                            @click="()=>{this.$router.go(0)}"
                    >刷新
                    </el-button>
                    <el-button
                            class="left"
                            icon="el-icon-search"
                            size="mini"
                            type="primary"
                            @click="onSubmit"
                    >查询
                    </el-button>
                    <el-button
                            class="left"
                            icon="el-icon-delete"
                            size="mini"
                            type="danger"
                            @click="resetSubmit"
                    >清空筛选
                    </el-button>
                </div>
            </div>
            <div class="select-info">
                <el-form
                        class="demo-form-inline"
                        :inline="true"
                        :model="formInline"
                        label-width="90px"
                        size="mini"
                >
                    <el-form-item label="结算单号:">
                        <el-input v-model="formInline.sn" placeholder="请输入结算单号"
                                  :οnkeyup="formInline.sn=formInline.sn.replace(/\s/g, '')"
                        ></el-input>
                    </el-form-item>

                    <el-form-item label="客户名称:">
                        <el-input v-model="formInline.businessName" placeholder="请输入客户名称"
                                  :οnkeyup="formInline.businessName=formInline.businessName.replace(/\s/g, '')"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="日期筛选:">
                        <el-date-picker
                                :clearable="false"
                                @blur="selectTime"
                                v-model="formInline.date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetimerange"
                                :default-time="['00:00:00', '00:00:00']"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                        ></el-date-picker>
                    </el-form-item>
                </el-form>
                <el-form
                        :inline="true"
                        :model="formInline"
                        class="demo-form-inline"
                        label-width="90px"
                        size="mini"
                >
                    <el-form-item label="结算单状态:">
                        <el-select v-model="formInline.invoiceStatus" placeholder="全部" style="width: 178px">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="待确认" value="0"></el-option>
                            <el-option label="已确认" value="1"></el-option>
                            <el-option label="已驳回" value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="开票状态:">
                        <el-select v-model="formInline.invoiceBill" placeholder="全部" style="width: 178px">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="待开票" value="0"></el-option>
                            <el-option label="已开票" value="1"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="list-box">
            <div class="list-title">
                <div>数据列表</div>
                <div>当前搜索结果总计：合计吨数： <span style="color: red">{{headerDate.totalTon}}</span> 吨；结算金额：<span style="color: red">{{headerDate.totalAmounts}}</span> 元</div>
                <div>
                    <el-button size="mini" type="primary" @click="dialogAdd=true">
                        <i class="el-icon-plus"></i>
                        添加结算单
                    </el-button>
                </div>
            </div>
            <div class="list-main">
                <template>
                    <el-table :data="tableData" border style="width: 100%" height="930px"
                              :row-class-name="tableRowClassName">
                        <el-table-column label="序号" type="index" width="50"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="sn" label="结算单号" width="200"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="businessName" label="客户名称"
                                         width="150"></el-table-column>
                        <el-table-column prop="ton" label="结算单总吨数" width="120"></el-table-column>
                        <el-table-column prop="amount" label="结算单总金额" width="120"></el-table-column>
                        <el-table-column prop="invoiceTon" label="可开票吨数(吨)" width="120"
                                         :formatter="kekaipiaoTon"></el-table-column>
                        <el-table-column prop="invoiceAmount" label="可开票金额(元)" width="120"
                                         :formatter="kekaipiaoMoney"></el-table-column>
                        <el-table-column prop="waitPayMoney" label="待支付金额(元)" width="120"
                        ></el-table-column>
                        <el-table-column prop="invoiceStatusEnum" label="结算单状态" width="120"></el-table-column>
                        <el-table-column prop="invoiceBillEnum" label="开票状态"
                                         width="120"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="invoiceDate" label="开票日期"
                                         width="160"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="createdDate" label="创建日期"
                                         width="160"></el-table-column>
                        <el-table-column show-overflow-tooltip prop="creatorName" label="创建人"
                                         width="130"></el-table-column>
                        <el-table-column fixed="right" label="操作" width="260">
                            <template slot-scope="scope">
                                <div v-if="scope.row.invoiceStatus==='0'&&scope.row.invoiceBill==='0'">
                                    <el-button style="color: #00cb8a" type="text" size="small"
                                               @click="changeRow(scope.row)">编辑
                                    </el-button>
                                    <el-button style="color: #00cb8a" type="text" size="small"
                                               @click='goList(scope.row)'>清单
                                    </el-button>
                                    <el-button style="color: #00cb8a" :disabled="canTicket" type="text" size="small"
                                               @click="QRsettlement(scope.row)">确认
                                    </el-button>
                                    <el-button style="color: #00cb8a" :disabled="canExport" type="text" size="small"
                                               @click="exportRow(scope.row)">导出
                                    </el-button>
                                    <el-button :disabled="canMark" style="color: #00cb8a" type="text" size="small"
                                               @click="markKP(scope.row)">标记开票
                                    </el-button>
                                    <el-button :disabled="canMark" style="color: #00cb8a" type="text" size="small"
                                               @click="receiptFn(scope.row)">回单
                                    </el-button>
                                </div>
                                <div v-if="scope.row.invoiceStatus==='1'&&scope.row.invoiceBill==='0'">
                                    <el-button style="color: #00cb8a" type="text" size="small"
                                               @click="lookAllList(scope.row)">查看
                                    </el-button>
                                    <el-button style="color: #00cb8a" type="text" size="small"
                                               @click='goList(scope.row)'>清单
                                    </el-button>
                                    <el-button style="color: #00cb8a" :disabled="canExport" type="text" size="small"
                                               @click="exportRow(scope.row)">导出
                                    </el-button>
                                    <el-button :disabled="canMark" style="color: #00cb8a" type="text" size="small"
                                               @click="markKP(scope.row)">标记开票
                                    </el-button>
                                    <el-button :disabled="canMark" style="color: #00cb8a" type="text" size="small"
                                               @click="receiptFn(scope.row)">回单
                                    </el-button>
                                </div>
                                <div v-if="scope.row.invoiceStatus==='1'&&scope.row.invoiceBill==='1'">
                                    <el-button style="color: #00cb8a" type="text" size="small"
                                               @click="lookAllList(scope.row)">查看
                                    </el-button>
                                    <el-button style="color: #00cb8a" type="text" size="small"
                                               @click='goList(scope.row)'>清单
                                    </el-button>
                                    <el-button style="color: #00cb8a" :disabled="canExport" type="text" size="small"
                                               @click="exportRow(scope.row)">导出
                                    </el-button>
                                    <el-button :disabled="canMark" style="color: #00cb8a" type="text" size="small"
                                               @click="receiptFn(scope.row)">回单
                                    </el-button>
                                </div>
                                <div v-if="scope.row.invoiceStatus==='0'&&scope.row.invoiceBill==='1'">
                                    <el-button style="color: #00cb8a" type="text" size="small"
                                               @click="changeRow(scope.row)">编辑
                                    </el-button>
                                    <el-button style="color: #00cb8a" type="text" size="small"
                                               @click='goList(scope.row)'>清单
                                    </el-button>
                                    <el-button style="color: #00cb8a" :disabled="canTicket" type="text" size="small"
                                               @click="QRsettlement(scope.row)">确认
                                    </el-button>
                                    <el-button style="color: #00cb8a" :disabled="canExport" type="text" size="small"
                                               @click="exportRow(scope.row)">导出
                                    </el-button>
                                    <el-button :disabled="canMark" style="color: #00cb8a" type="text" size="small"
                                               @click="receiptFn(scope.row)">回单
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </div>
            <div class="pagination" style="float: right;margin-top: 20px">
                <el-pagination
                        background
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 40,50,100]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"
                ></el-pagination>
            </div>
        </div>
        <!-- 添加计结算单弹窗 -->
        <el-dialog title="添加结算单" :visible.sync="dialogAdd" width="30%" :before-close="handleClose">
            <el-form ref="form" label-width="80px">
                <el-form-item label="客户名称:">
                    <el-autocomplete
                            class="inline-input"
                            v-model="addUserName"
                            :fetch-suggestions="querySearch"
                            placeholder="请输入客户名称"
                            :trigger-on-focus="false"
                            @select="handleSelect"
                    ></el-autocomplete>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
        <el-button @click="dialogAdd = false">取 消</el-button>
        <el-button type="primary" @click="addList">确 定</el-button>
      </span>
        </el-dialog>
        <!-- 驳回该结算单-->
        <el-dialog
                title="驳回确认"
                :visible.sync="dialogVisibleBH"
                width="30%"
                :before-close="handleClose">
            <div style="height: 40px;">
                <i class="el-icon-info" style="color: red;"></i> <span style=" margin-left: 10px;
">是否确认驳回该结算单？</span>
            </div>
            <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleBH = false">取 消</el-button>
        <el-button :disabled="canReject" type="primary" @click="sureBoHui">确 定</el-button>
      </span>
        </el-dialog>

        <!-- 确认该结算单-->
        <el-dialog
                title="标记确认"
                :visible.sync="dialogVisibleQR"
                width="30%"
                :before-close="handleClose">
            <div style="height: 40px;">
                <i class="el-icon-info" style="color: red;"></i> <span style=" margin-left: 10px;
">确定要确认该结算单吗？</span>
            </div>
            <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleQR = false">取 消</el-button>
        <el-button :disabled="canTicket" type="primary" @click="QRsettlementFn">确 定</el-button>
      </span>
        </el-dialog>

        <!-- 该结算单标记为开票 -->
        <el-dialog
                title="标记确认"
                :visible.sync="dialogVisibleKP"
                width="30%"
                :before-close="handleClose">
            <div style="height: 40px;">
                <i class="el-icon-info" style="color: red;"></i> <span style=" margin-left: 10px;
">确定要标记开票吗？</span>
            </div>
            <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleKP = false">取 消</el-button>
        <el-button :disabled="canTicket" type="primary" @click="sureMarkKp">确 定</el-button>
      </span>
        </el-dialog>
    </div>
</template>

<script>
    export default {
        name: "CarsList",
        data() {
            return {
                headerDate: {
                    totalTon: '',
                    totalAmounts: '',
                },
                bohuiId: '',
                kaiPiaoId: '',
                QRid: '',
                dialogVisibleKP: false,
                dialogVisibleBH: false,
                dialogVisibleQR: false,
                canExport: false,
                canReject: false,
                canTicket: false,
                canMark: false,
                dialogAdd: false,
                addUserName: "",
                restaurants: [], //模糊查询
                addId: "",

                formInline: {
                    //筛选查询
                    sn: "",
                    businessName: "",
                    invoiceStatus: '',
                    invoiceBill: '',
                    date: [],
                },
                startTime: "",
                endTime: "",
                tableData: [],
                currentPage: 1,
                pageSize: 20,
                total: 1
            };
        },
        methods: {
            /** 查看结算单的回单 **/
            receiptFn(row){
                this.$http.get('/admin-center-server/app/final_statement/getOrderItemSns',{
                    params:{
                        id:row.id
                    }
                }).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                        let snList = data.data;
                        sessionStorage.setItem('snList',JSON.stringify(snList));
                        this.$router.push({
                            path:'statements_receipt',
                            query:{
                                sn:row.sn,
                                businessName:row.businessName,
                                id:row.id
                            }
                        })
                    }else {
                        this.$message.warning(data.message)
                    }
                });
            },
            /** 已生成清单的结算单 颜色区分 **/
            tableRowClassName({row, rowIndex}) {
                if (row.scheduleCount > 0) {
                    return 'warning-row'; //红色 有清单
                } else {
                    return ''; //  无清单
                }
            },
            /** 标记开票 **/
            markKP(row) {
                this.kaiPiaoId = row.id;
                this.dialogVisibleKP = true;
            },
            /** 确认标记开票 **/
            sureMarkKp() {
                let id = this.kaiPiaoId;
                this.$http.post('/admin-center-server/app/final_statement/edit_invoice_bill?finalStatementId=' + id + "&invoiceBill=" + 1).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.$message.success("已标记为开票");
                        this.getDataList();
                        this.dialogVisibleKP = false;
                    } else {
                        this.$message.warning(data.message);
                    }
                })
            },
            /** 可开票吨数 **/
            kekaipiaoTon(row) {
                if (row.invoiceTon === null) {
                    return '0'
                } else {
                    return row.invoiceTon
                }
            },
            kekaipiaoMoney(row) {
                if (row.invoiceAmount === null) {
                    return '0'
                } else {
                    return row.invoiceAmount
                }
            },
            lookAllList(row) {
                let id = row.id;
                let amount = row.amount;
                let ton = row.ton;
                let businessName = row.businessName;
                let sn = row.sn;
                sessionStorage.setItem('lookCurListId', id);
                this.$router.push({
                    path: 'lookList',
                    query: {
                        id: id,
                        ton: ton,
                        amount: amount,
                        businessName: businessName,
                        sn: sn,
                    }
                })
            },
            /** 确认结算单 **/
            QRsettlement(row) {
                let id = row.id;
                this.QRid = id;
                this.dialogVisibleQR = true;

            },
            /** 确认结算单弹窗 **/
            QRsettlementFn() {
                this.canTicket = true;
                let id = this.QRid;
                this.$http
                    .post(
                        "/admin-center-server/app/final_statement/edit_invoice_?finalStatementId=" +
                        id +
                        "&invoiceStatus=" +
                        1
                    )
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.$message.success("该结算单已确认");
                            this.getDataList();
                            this.canTicket = false;
                            this.dialogVisibleQR = false;
                        } else {
                            this.$message.warning(data.message);
                            this.canTicket = false;
                        }
                    });
            },
            /** 驳回结算单 **/
            rowReject(row) {
                let id = row.id;
                this.bohuiId = id;
                this.dialogVisibleBH = true;

            },
            /** 确认驳回 **/
            sureBoHui() {
                this.canReject = true;
                let id = this.bohuiId;
                this.$http
                    .post(
                        "/admin-center-server/app/final_statement/edit_invoice_?finalStatementId=" +
                        id +
                        "&invoiceStatus=" +
                        2
                    )
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.$message.success("驳回成功");
                            this.dialogVisibleBH = false;
                            this.getDataList();
                            this.canReject = false;
                        } else {
                            this.$message.warning(data.message);
                            this.canReject = false;
                        }
                    });
            },
            /** 导出结算单 **/
            exportRow(row) {
                let id = row.id;
                this.canExport = true;
                this.$http.get('/admin-center-server/app/final_statement/exportFinalStatement', {
                    params: {
                        finalStatementId: id

                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === 200) {
                        window.location.href = data.data;
                        this.canExport = false;
                    } else if (data.code === '422') {
                        this.$message.warning(data.message);
                        this.canExport = false;
                    }
                })
            },
            /** 清单列表 **/
            goList(row) {
                let id = row.id;
                let ownerId = row.ownerId;
                let invoiceStatus = row.invoiceStatus;
                this.$router.push({
                    path: 'statementList',
                    query: {
                        id: id,
                        ownerId: ownerId,
                        invoiceStatus: invoiceStatus,
                    }
                });
            },
            /** 结算单编辑 **/
            changeRow(row) {
                let id = row.id;
                let editRowUserName = row.businessName;
                let userId = row.ownerId;
                this.$router.push({
                    path: 'statementEdit',
                    query: {
                        id: id,
                        editRowUserName: editRowUserName,
                        userId: userId,
                    }
                });
            },
            /** 添加结算单模糊查询 **/
            querySearch(queryString, cb) {
                var restaurants = this.restaurants;
                var results = queryString
                    ? restaurants.filter(this.createFilter(queryString))
                    : restaurants;
                // 调用 callback 返回建议列表的数据
                cb(results);
            },
            createFilter(queryString) {
                return restaurant => {
                    return (
                        restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
                        0
                    );
                };
            },
            /** 模糊搜索 获取ID **/
            handleSelect(item) {
                let id = item.id;
                this.addId = id;
            },
            /** 添加结算单 **/
            addList() {
                this.dialogAdd = false;
                this.$http
                    .post(
                        "/admin-center-server/app/final_statement/create?userId=" + this.addId
                    )
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            let finalStatementId = data.data.finalStatementId;
                            let finalStatementSn = data.data.finalStatementSn;
                            sessionStorage.setItem("finalStatementId", finalStatementId);
                            sessionStorage.setItem('finalStatementSn', finalStatementSn);
                            // this.$router.push("addStatement");
                            this.$router.push({
                                path: 'addStatement',
                                query: {
                                    id: this.addId,
                                }
                            });
                        } else if (data.code === '500') {
                            this.$message.warning(data.message)
                        }
                    });
            },
            handleClose(done) {
                this.dialogAdd = false;
                this.dialogVisibleBH = false;
                this.dialogVisibleKP = false;
                this.dialogVisibleQR = false;
            },
            /** 分页方法 **/
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1;
                this.getDataList();
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getDataList();
            },

            /** 根据时间态搜索 **/
            selectTime() {
                if(this.formInline.date!==null){
                    let startTime = this.formInline.date[0];
                    let endTime = this.formInline.date[1];
                    this.startTime = startTime;
                    this.endTime = endTime;
                }else {
                    this.formInline.date=[];
                }

            },
            /** 搜索提交 结算单号 客户名称 **/
            onSubmit() {
                this.currentPage = 1;
                this.getDataList();
                this.getDataHeader();
            },
            /** 清空搜索选项 **/
            resetSubmit() {
                this.formInline = {
                    sn: "",
                    businessName: "",
                    date: "",
                    invoiceBill: '',
                    invoiceStatus: '',
                };
                this.startTime = "";
                this.endTime = "";
                this.getDataList();
                this.getDataHeader();
            },
            /** 拉取列表 **/
            getDataList() {
                this.$http
                    .get("/admin-center-server/app/final_statement/list", {
                        params: {
                            pageNumber: this.currentPage,
                            pageSize: this.pageSize,
                            sn: this.formInline.sn,
                            businessName: this.formInline.businessName,
                            invoiceBill: this.formInline.invoiceBill,
                            invoiceStatus: this.formInline.invoiceStatus,
                            startDate: this.startTime,
                            endDate: this.endTime
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.total = Number(data.data.total);
                            let useList = data.data.list;
                            useList.map((item, index) => {
                                if (item.shouldAmount === null) {
                                    item.shouldAmount = 0;
                                }
                                if (item.taxAmount === null) {
                                    item.taxAmount = 0;
                                }
                            });
                            useList.map((item, index) => {
                                item.waitPayMoney = this.plus(item.shouldAmount, item.taxAmount)
                            });
                            this.tableData = useList;
                        }
                    });
            },
            /** 拉取模糊查询数据 **/
            getUnknowData() {
                this.$http
                    .get("/admin-center-server/commonUser/getUserInfoByRealName", {
                        params: {
                            name: ""
                        }
                    })
                    .then(res => {
                        let data = res.data;

                        if (data.code === "200") {
                            let resData = data.data;
                            let str = [];
                            resData.map((item, index) => {
                                if (item.name != null) {
                                    let obj = {
                                        value: item.name,
                                        id: item.id
                                    };
                                    str.push(obj);
                                }
                            });
                            this.restaurants = str;
                        }
                    });
            },
            /** 解决浮点数计算 **/
            times(num1, num2) {
                const num1String = num1.toString();
                const num2String = num2.toString();
                const num1Digits = (num1String.split('.')[1] || '').length;
                const num2Digits = (num2String.split('.')[1] || '').length;
                const baseNum = Math.pow(10, num1Digits + num2Digits);
                return Number(num1String.replace('.', '')) * Number(num2String.replace('.', '')) / baseNum
            },
            plus(num1, num2) {
                const num1Digits = (num1.toString().split('.')[1] || '').length;
                const num2Digits = (num2.toString().split('.')[1] || '').length;
                const baseNum = Math.pow(10, Math.max(num1Digits, num2Digits));
                return (this.times(num1, baseNum) + this.times(num2, baseNum)) / baseNum
            },

            /** 结算单列表抬头 **/
            getDataHeader() {
                this.$http.get('/admin-center-server/app/final_statement/listHead', {
                    params: {
                        sn: this.formInline.sn,
                        businessName: this.formInline.businessName,
                        invoiceBill: this.formInline.invoiceBill,
                        invoiceStatus: this.formInline.invoiceStatus,
                        startDate: this.startTime,
                        endDate: this.endTime
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.data === null) {
                        this.headerDate = {
                            totalTon: '0',
                            totalAmounts: '0',
                        }
                    } else {
                        this.headerDate = data.data;
                    }
                })
            },

            /** 时间 三十天 **/
            fun_date(aa) {
                let date1 = new Date(),
                    time1 = date1.getFullYear() + "-" + (date1.getMonth() + 1) + "-" + date1.getDate();
                let date2 = new Date(date1);
                date2.setDate(date1.getDate() + aa);
                let time2 = date2.getFullYear() + "-" + (date2.getMonth() + 1) + "-" + date2.getDate()+" "+"00:"+"00:"+"00";
                return time2;
            }
        },
        activated() {
            this.endTime = this.fun_date(0);
            this.startTime = this.fun_date(-30);
            this.formInline.date.push(this.fun_date(-30));
            this.formInline.date.push(this.fun_date(0));
            this.getDataList();
            this.getUnknowData();
            this.getDataHeader();
        }
    };
</script>

<style>
    .el-range-editor.el-input__inner {
        width: 400px;
    }
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
    /*.el-form-item {*/
    /*    margin-bottom: 0;*/
    /*}*/

    .carsList {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
                display: flex;
                justify-content: space-between;

                .button {
                    margin-right: 20px;
                }
            }

            .select-info {
                padding-top: 30px;
                padding-bottom: 30px;
            }
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;
            overflow: hidden;

            .list-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                div {
                    height: 38px;
                    line-height: 38px;
                }
            }

            .list-main {
                width: 100%;
                border: 1px solid #cccccc;
                margin-top: 10px;
            }
        }
    }
</style>
