<template>
  <div class="subAccount">
    <div class="select-box">
      <div class="select-info">
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="75px" size="mini">
          <el-form-item label="流水号:">
            <el-input class="form-item-content-width" v-model="formInline.transactionSn" placeholder="请输入流水号"></el-input>
          </el-form-item>
          <!-- <el-form-item label="收支类型:" >
            <el-select class="form-item-content-width" v-model="formInline.inOrOut" placeholder="不限">
              <el-option label="收入" value="1"></el-option>
              <el-option label="支出" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="交易类型:">
            <el-select class="form-item-content-width" v-model="formInline.transactionType" @visible-change="handlePayTypeSelectorClick" placeholder="不限">
              <el-option v-for="item in payTypeList" :key="item.value" :label="item.comment" :value="item.value"></el-option>
            </el-select>
          </el-form-item> -->

          <el-form-item label="付款人:">
            <el-input class="form-item-content-width" placeholder="请输入付款人" v-model="formInline.payer"> </el-input>
          </el-form-item>
          <el-form-item label="付款账号:">
            <el-input class="form-item-content-width" v-model="formInline.paymentAccount" placeholder="请输入付款账号"></el-input>
          </el-form-item>
          <el-form-item label="收款人:">
            <el-input class="form-item-content-width" v-model="formInline.payee" placeholder="请输入收款人"></el-input>
          </el-form-item>
          <el-form-item label="收款账号:">
            <el-input class="form-item-content-width" v-model="formInline.receiptAccount" placeholder="请输入收款账号"></el-input>
          </el-form-item>
          <el-form-item label="交易时间:">
            <el-date-picker v-model="date" :clearable="false" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="onSubmit" size="mini">查询</el-button>
            <el-button icon="el-icon-delete" type="danger" @click="clearForm" size="mini">清空筛选</el-button>
          </el-form-item>
        </el-form>
      </div><br>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div class="list-title-left">
          <!-- 收入 -->
          <div class="inList">
            <span class="checkText">收入</span>
            <el-tooltip class="symbol"  placement="right" popper-class='prompt' v-if="activeName!='third'">
              <div slot="content" class="tipContent" v-if="activeName=='first'">
                <div> <span>垫运费：</span>鸿飞达充值至平台垫款账户，用于支付下游承运方运费</div>
                <div> <span>还运费：</span>货主回款至平台垫款账户，由运营端代货主支付产生</div>
                <div> <span>暂估运费差：</span>货主阶段回款至平台垫款账户后，计算出平台亏损金额，属暂估亏损</div>
                <div> <span>暂估运费差冲回：</span>货主阶段回款至平台垫款账户后重新计算平台亏损，将之前的暂估亏损冲回，重新入账本次暂估亏损</div>
                <div> <span>运费差：</span>货主最后一次回款（尾款）至平台垫款账户后计算平台亏损利润，属实际亏损</div>
                <div> <span>退还油费：</span>因运单取消等原因系统自动退还已支付的油费（装货付油费）</div>
                <!-- <div> <span>手动转账：</span>因业务中的特殊情况需要手动转账，通过【后台 - 财务管理 - 手动转账】转账至平台垫款账户</div>
                <div> <span>手动转账 - 退还油费：</span>人工操作退还油费，通过【后台 - 财务管理 - 手动转账 - 转账类型“退还油费”】转账至平台垫款账户</div>
                <div> <span>手动转账 - 其他：</span>因业务需要，人工操作【后台 - 财务管理 - 手动转账 - 转账类型“其他”】转账至平台垫款账户</div> -->
              </div>
              <div slot="content" class="tipContent" v-if="activeName=='second'">
                <div> <span>运费差：</span>货主最后一次回款（尾款）至平台垫款账户后计算平台盈利利润，属实际利润</div>
                <div> <span>暂估运费差：</span>货主阶段回款至平台垫款账户后，计算出平台盈利利润，属暂估利润</div>
                <div> <span>暂估运费差冲回：</span>货主阶段回款至平台垫款账户后重新计算平台盈利利润，将之前的暂估利润冲回，重新入账本次暂估利润</div>
              </div>
              <div slot="content" class="tipContent" v-if="activeName=='fourth'">
                <div> <span>充油费：</span>渠道充油至平台油费账户</div>
                <div> <span>油费付：</span>运单装货后，鸿飞达/平台货主支付给下游的油费，油费统一进入平台油费账户，由财务提现后到中石化为司机油卡充值</div>
                <!-- <div> <span>手动转账：</span>因业务中的特殊情况需要手动转账，通过【后台 - 财务管理 - 手动转账】转账至平台油费账户</div>
                <div> <span>手动转账 - 其他：</span>因业务需要，人工操作【后台 - 财务管理 - 手动转账 - 转账类型“其他”】转账至平台油费账户</div> -->
              </div>
              <i class="el-icon-warning"></i>
            </el-tooltip>
            <el-checkbox class="mr15" v-show="activeName!='third'" v-model="inAllSelect" @change="(val)=>handelAllChange(val,'in')">全部</el-checkbox>
            <el-checkbox-group v-model="formInline.incomeTypes" @change="(val)=>handSelectChange(val,'in')">
              <span class="mr15" v-for="(item, index) in inListSelect" :key="index">
                <el-checkbox :label="item.transactionType" :name="item.transactionTypeName">
                  {{ item.transactionTypeName }}
                  <!-- <el-tooltip v-if="tips(item.transactionType)"  effect="dark" :content="tips(item.transactionType)" placement="top">
                    <i class="el-icon-warning" ></i>
                  </el-tooltip> -->
                </el-checkbox>
              </span>
            </el-checkbox-group>
            <!-- 收入-手动转账 -->
            <el-checkbox v-if="false" :indeterminate="manualInStatus" v-model="manualInAll" @change="(val)=>handelAllChange(val,'manualIn')">手动转账
              <!-- <el-tooltip   effect="dark" content="通过【后台 - 财务管理 - 手动转账】转账至平台账户" placement="top">
                    <i class="el-icon-warning" ></i>
              </el-tooltip> -->
            </el-checkbox>
            <el-popover v-if="false" popper-class='popBox' trigger="click" placement="bottom" :append-to-body="false">
              <i class="el-icon-arrow-down" slot="reference"></i>
              <el-checkbox-group v-model="manualInList" @change="(val)=>handSelectChange(val,'manualIn')">
                <el-checkbox v-for="(item, index) in manualTransferInList" :key="index" :label="item.transactionType" :name="item.transactionTypeName">
                  {{ item.transactionTypeName }}
                  <!-- <el-tooltip v-if="tips(item.transactionType)"  effect="dark" :content="tips(item.transactionType)" placement="top">
                    <i class="el-icon-warning" ></i>
                  </el-tooltip> -->
                </el-checkbox>
              </el-checkbox-group>
            </el-popover>

          </div>
          <!-- 支出 -->
          <div class="outList">
            <span class="checkText">支出</span>
            <el-tooltip class="symbol"  placement="right" popper-class='prompt' v-if="activeName!='third'">
              <div slot="content" class="tipContent" v-if="activeName=='first'">
                <div> <span>垫款提现：</span>鸿飞达财务从平台垫款账户提现</div>
                <div> <span>油费付：</span>司机装货后，鸿飞达支付给下游的油费</div>
                <div> <span>卸货付：</span>司机卸货后，鸿飞达支付给下游的卸货付</div>
                <div> <span>回单付：</span>司机上传回单且回单收回后，鸿飞达支付给下游的回单押金</div>
                <div> <span>结算付：</span>运单通过平台审核后，鸿飞达支付给下游的结算付</div>
                <div> <span>下游补贴费用：</span>鸿飞达支付给下游的补贴</div>
                <div> <span>还运费：</span>向货主请款金额过多，导致货主支付尾款时需平台退还超额部分</div>
                <div> <span>暂估运费差：</span>货主阶段回款至平台垫款账户后，计算出平台盈利利润，属暂估利润</div>
                <div> <span>暂估运费差冲回：</span>货主阶段回款至平台垫款账户后重新计算平台盈利利润，将之前的暂估利润冲回，重新入账本次暂估利润</div>
                <div> <span>运费差：</span>货主最后一次回款（尾款）至平台垫款账户后计算平台盈利利润，属实际利润</div>
                <!-- <div> <span>手动转账：</span>因业务中的特殊情况需要手动转账，通过【后台 - 财务管理 - 手动转账】从平台垫款账户转出</div>
                <div> <span>手动转账 - 其他：</span>因业务需要，人工操作【后台 - 财务管理 - 手动转账 - 转账类型“其他”】从平台垫款账户转出</div> -->
              </div>
              <div slot="content" class="tipContent" v-if="activeName=='second'">
                <div> <span>运费差提现：</span>鸿飞达财务从平台运费差账户提现</div>
                <div> <span>运费差：</span>货主最后一次回款（尾款）至平台垫款账户后计算平台亏损利润，属实际亏损</div>
                <div> <span>暂估运费差：</span>货主阶段回款至平台垫款账户后，计算出平台亏损金额，属暂估亏损</div>
                <div> <span>暂估运费差冲回：</span>货主阶段回款至平台垫款账户后重新计算平台亏损，将之前的暂估亏损冲回，重新入账本次暂估亏损</div>
              </div>
              <div slot="content" class="tipContent" v-if="activeName=='fourth'">
                <div> <span>油费提现：</span>鸿飞达财务从平台油费账户提现</div>
                <div> <span>退还油费：</span>因运单取消等原因系统自动退还已支付的油费（装货付油费）</div>
                <!-- <div> <span>手动转账：</span>因业务中的特殊情况需要手动转账，通过【后台 - 财务管理 - 手动转账】从平台油费账户转出</div>
                <div> <span>手动转账 - 退还油费：</span>人工操作退还油费，通过【后台 - 财务管理 - 手动转账 - 转账类型“退还油费”】从平台油费账户转出</div>
                <div> <span>手动转账 - 油费转现金：</span>因业务需要，将司机油卡转为现金，通过【后台 - 财务管理 - 手动转账 - 转账类型“油费转现金”】从平台油费账户转出至司机账户</div>
                <div> <span>手动转账 - 其他：</span>因业务需要，人工操作【后台 - 财务管理 - 手动转账 - 转账类型“其他”】从平台油费账户转出</div> -->
              </div>
              <i class="el-icon-warning"></i>
            </el-tooltip>
            <el-checkbox class="mr15" v-show="activeName!='third'" v-model="outAllSelect" @change="(val)=>handelAllChange(val,'out')">全部</el-checkbox>
            <el-checkbox-group class="listRow" v-model="formInline.expenseTypes" @change="(val)=>handSelectChange(val,'out')">
              <span class="mr15" v-for="(item, index) in outListSelect" :key="index">
                <el-checkbox v-if="item.transactionType!='52'" :label="item.transactionType" :name="item.transactionTypeName">
                  {{ item.transactionTypeName }}
                  <!-- <el-tooltip v-if="tips(item.transactionType)" effect="dark" :content="tips(item.transactionType)" placement="top">
                    <i class="el-icon-warning"></i>
                  </el-tooltip> -->
                </el-checkbox>
                <!-- 补贴相关 -->
                <el-checkbox v-if="item.transactionType=='52'" :indeterminate="isIndeterminate" :label="item.transactionType" :name="item.transactionTypeName" @change="(val)=>handelAllChange(val,'subsidy')">{{ item.transactionTypeName }}</el-checkbox>
                <el-popover popper-class='popBox manual' v-if="item.transactionType=='52'" trigger="click" placement="bottom" :append-to-body="false">
                  <i class="el-icon-arrow-down" slot="reference"></i>
                  <el-checkbox-group v-model="formInline.subsidyDownList" @change="(val)=>handSelectChange(val,'subsidy')">
                    <el-checkbox v-for="(item, index) in subsidyList" :key="index" :label="item.value" :name="item.label">
                      {{ item.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-popover>
              </span>
            </el-checkbox-group>
            <!-- 支出-手动转账 -->
            <el-checkbox v-if="false" :indeterminate="manualOutStatus" v-model="manualOutAll" @change="(val)=>handelAllChange(val,'manualOut')">手动转账
              <!-- <el-tooltip   effect="dark" content="通过【后台 - 财务管理 - 手动转账】从平台账户转出" placement="top">
                    <i class="el-icon-warning" ></i>
              </el-tooltip> -->
            </el-checkbox>
            <el-popover v-if="false" popper-class='popBox' trigger="click" placement="bottom" :append-to-body="false">
              <i class="el-icon-arrow-down" slot="reference"></i>
              <el-checkbox-group v-model="manualOutList" @change="(val)=>handSelectChange(val,'manualOut')">
                <el-checkbox v-for="(item, index) in manualTransferList" :key="index" :label="item.transactionType" :name="item.transactionTypeName">
                  {{ item.transactionTypeName }}
                  <!-- <el-tooltip v-if="tips(item.transactionType)" effect="dark" :content="tips(item.transactionType)" placement="top">
                    <i class="el-icon-warning"></i>
                  </el-tooltip> -->
                </el-checkbox>
              </el-checkbox-group>
            </el-popover>

          </div>

        </div>
        <div class="list-title-right">
          <el-button type="primary" @click="exportFile">导出</el-button>
          <el-button type="primary" plain style="margin-left: 10px; margin-right: 10px" @click="getSum">计算合计交易金额</el-button>
          <div v-if="dataSum" style="font-size: 14px;flex-shrink: 0;">
            当前列表中：收入合计：￥{{ incomeTotal }}， 支出合计：￥{{ expenseTotal }}， 净收入(收入-支出):￥{{ dataSum }}
          </div>
          <div class="text" v-if="showTips">说明:当前列表数据同时包含承运型、结算型业务的收支明细</div>

        </div>
      </div>
      <div class="list-main">
        <template>
          <el-table :data="tableData" border style="width: 100%" class="table" cell-class-name="table_cell_gray" header-cell-class-name="table_header_cell_gray">
            <el-table-column fixed type="index" label="序号" width="50"></el-table-column>
            <el-table-column fixed prop="transactionSn" label="流水号"></el-table-column>
            <el-table-column prop="transactionAmount" label="交易金额"></el-table-column>
            <el-table-column prop="transactionTypeValue" label="交易类型"></el-table-column>
            <el-table-column prop="inOrOutValue" label="收支类型"></el-table-column>
            <el-table-column prop="payer" label="付款人"></el-table-column>
            <el-table-column prop="paymentAccount" label="付款账户"></el-table-column>
            <el-table-column prop="paymentAccountBank" label="开户行"></el-table-column>
            <el-table-column prop="payee" label="收款人"></el-table-column>
            <el-table-column prop="receiptAccount" label="收款账户"></el-table-column>
            <el-table-column prop="receiptAccountBank" label="开户行"></el-table-column>
            <el-table-column prop="remark" label="备注"></el-table-column>
            <el-table-column prop="transactionTime" label="交易时间"></el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
              <template slot-scope="scope">
                <el-button @click="handleClick(scope.row)" type="text" size="small">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </div>
      <div class="paging">
        <div class="block">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pageNumber" :page-sizes="[10, 20, 30, 40, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      formInline: {
        incomeTypes: [],
        expenseTypes: [],
        subsidyDownList: []
      },
      tableData: [],
      total: 1,
      pageSize: 20,
      pageNumber: 1,
      date: [],
      payTypeList: [],
      exportData: {},
      dataSum: "",
      currentBaseId: '',
      // 收入/支出全选
      inAllSelect: false,
      outAllSelect: false,
      // 收入/支出手动转账全选
      manualInAll: false,
      manualOutAll: false,
      //收入/支出类型
      inListSelect: [],
      outListSelect: [],
      //补贴不确定状态标志
      isIndeterminate: false,
      // 手动转账不确定状态标志
      manualOutStatus: false,
      manualInStatus: false,
      // 手动转账类型列表-支出
      manualTransferList: [],
      // 手动转账类型列表-收入
      manualTransferInList: [],
      // 手动-支出列表
      manualOutList: [],
      //手动-收入列表
      manualInList: [],
      showTips: false,
      // 收入合计
      incomeTotal:'',
      // 支出合计
      expenseTotal:'',

    };
  },
  computed: {
    inStr() {
      switch (this.activeName) {
        case 'first':
          return 'advanceIncomeTypes'
        case 'second':
          return 'freightIncomeTypes'
        case 'third':
          return 'serviceIncomeTypes'
        case 'fourth':
          return 'oilIncomeTypes'
        default:
          return 'advanceIncomeTypes'
      }
    },
    outStr() {
      switch (this.activeName) {
        case 'first':
          return 'advanceOutcomeTypes'
        case 'second':
          return 'freightOutcomeTypes'
        case 'third':
          return 'serviceOutcomeTypes'
        case 'fourth':
          return 'oilOutcomeTypes'
        default:
          return 'advanceOutcomeTypes'
      }
    },
    // 类型
    accountTypeAct() {
      switch (this.activeName) {
        case 'first':
          return '3'

        case 'second':
          return '4'

        case 'third':
          return '2'

        case 'fourth':
          return '1'

        default:
          return ''
            ;
      }
    },
    // tips
    tips() {
      return (val) => {
        if (this.activeName == 'first') {
          switch (val) {
            case '0':
              return '鸿飞达充值至平台账户'
            case '21':
              return '运营单在运单取消后退回已支付的油费'
            case '20':
              return '平台支付司机油费'
            default:
              return '';
          }
        } else if (this.activeName == 'fourth') {
          switch (val) {
            case '21':
              return '①货主单中退回货主已支付的油费；②运营单中运单取消后退回已支付的油费'
            case '20':
              return '货主油费付至平台账户，财务提现后到中石化为司机充油卡'
            case '26':
              return '通过【后台 - 财务管理 - 手动转账】将油费转成的现金支付至司机账户'
            default:
              return '';
          }
        }

      }
    },

  },
  props: ['baseId','activeName','subsidyList','allTransactionType'],
  watch: {
    baseId(newVal, oldVal) {
      this.pageNumber = 1
      this.currentBaseId = newVal
      this.getDataList()
    },
    allTransactionType(){
       this.handleAccountTypeList()
    }
  },
  methods: {
    // handleTabClick() {
    //   // this.showTips=this.activeName=='fourth'&&this.$route.query.accountSonType=='fourth'
    //   this.pageNumber = 1
    //   // 重置全选
    //   this.resetData()
    //   this.handleAccountTypeList()
    //   this.formInline = this.$options.data().formInline
    //   this.getDataList();
    // },
    /** 清空筛选 **/
    clearForm() {
      this.formInline = this.$options.data().formInline
      this.date = []
      this.resetData()
      this.getDataList();
    },
    /** 按条件查询 **/
    onSubmit() {
      this.pageNumber = 1;
      this.getDataList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNumber = 1;
      this.getDataList();
    },
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.getDataList();
    },
    getDataList() {
      let params = {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        ...this.formInline,
      };
      params.incomeTypes = Array.from(new Set([...this.formInline.incomeTypes, ...this.manualInList]))
      params.expenseTypes = Array.from(new Set([...this.formInline.expenseTypes, ...this.manualOutList]))
      if (this.date && this.date.length !== 0) {
        params.startTime = this.date[0];
        params.endTime = this.date[1];
      }
      params.accountType = this.accountTypeAct
      params.baseId = this.currentBaseId
      if (this.activeName === 'first' && params.transactionType) {
        this.payTypeList.forEach(item => {
          if (item.value === params.transactionType) {
            if (item.isAdd) {
              params.transactionTypeString = params.transactionType
              params.transactionType = null
            }
          }
        })
      }

      this.$post(
        "/admin-center-server/transaction/flow/querySubAccountTransactionPage",
        params
      ).then((res) => {
        this.tableData = res.list;
        this.total = Number(res.total);
        this.getSum()
      });
    },
    getPayTypeList() {
      this.$get(
        "/order-center-server/order/dict/findDictByType?dictType=subsidyDown"
      ).then((res) => {
        if (res && res.length > 0) {
          res.forEach(element => {
            this.payTypeList.push({
              comment: element.label,
              value: element.value,
              isAdd: true
            });
          });
        }
      });
    },
    handlePayTypeSelectorClick(type) {
      switch (this.activeName) {
        case 'first':
          this.payTypeList = [{ value: '1', comment: '垫运费' },
          { value: '2', comment: '装货付油费' },
          { value: '3', comment: '卸货付' },
          { value: '4', comment: '结算付' },
          { value: '5', comment: '还运费' },
          { value: '6', comment: '回单付' },
          { value: '7', comment: '退还油费' }
          ]
          this.getPayTypeList()
          break
        case 'second':
          this.payTypeList = [{ value: '1', comment: '运费差' }, { value: '2', comment: '运费差提现' },]
          break
        case 'third':
          this.payTypeList = [{ value: '1', comment: '服务费' }, { value: '2', comment: '服务费提现' }]
          break
        case 'fourth':
          this.payTypeList = [{ value: '1', comment: '装货付油费' },
          { value: '2', comment: '充油费' },
          { value: '3', comment: '油费提现' },
          { value: '4', comment: '退还油费' },
          { value: '5', comment: '油费转现金' },
          ]
          break
        default:
          this.payTypeList = []
          break;
      }
      // console.log(type);
      // if (!type) return;
      // this.$post(
      //   "/admin-center-server/transaction/flow/getTransactionDetailType"
      // ).then((res) => {
      //   this.payTypeList = res;
      //   if (this.$store.state.user.userInfo2.hasStandardModeFlag) {
      //     this.payTypeList = res.filter((item) =>
      //       ["0", "1", "4"].includes(item.value)
      //     );
      //   } else {
      //     this.payTypeList = res;
      //   }
      // });
    },
    handleClick(item) {
      this.$router.push({
        path: 'dealDetail',
        query: {
          ...item,
          accountType: 2
        }
      })
    },
    getSum() {
      let params = {
        accountType: this.accountTypeAct,
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        baseId: this.currentBaseId,
        ...this.formInline,
      };
      params.incomeTypes = Array.from(new Set([...this.formInline.incomeTypes, ...this.manualInList]))
      params.expenseTypes = Array.from(new Set([...this.formInline.expenseTypes, ...this.manualOutList]))
      if (this.date && this.date.length !== 0) {
        params.startTime = this.date[0];
        params.endTime = this.date[1];
      }
      if (this.activeName === 'first' && params.transactionType) {
        this.payTypeList.forEach(item => {
          if (item.value === params.transactionType) {
            if (item.isAdd) {
              params.transactionTypeString = params.transactionType
              params.transactionType = null
            }
          }
        })
      }
      this.$post(
        "/admin-center-server/transaction/flow/querySubAccountTransactionSum",
        params
      ).then((res) => {
        this.expenseTotal=res.expenseSum
        this.incomeTotal=res.incomeSum
        this.dataSum = res.netIncome;
      });
    },
    // 获取下游补贴类型
    getSubsidyDownList() {
      this.$get('/order-center-server/order/dict/findDictByType?dictType=subsidyDown').then(res => {
        this.subsidyList = res
      })
    },
    // 获取交易类型
    getAllTransactionType() {
      this.$get(
        "/admin-center-server/transaction/flow/queryPlatformAccountTransactionType"
      ).then((res) => {
        this.allTransactionType = res
        this.handleAccountTypeList()
      });

    },

    // 根据子账户类型处理收入支出类型列表
    handleAccountTypeList() {
      //获取类型支出收入类型
      this.inListSelect = this.allTransactionType[this.inStr].filter(el => el.method == '0')
      this.outListSelect = this.allTransactionType[this.outStr].filter(el => el.method == '0')
      // 手动转账类型
      this.manualTransferInList = this.allTransactionType[this.inStr].filter(el => el.method == '1')
      this.manualTransferList = this.allTransactionType[this.outStr].filter(el => el.method == '1')
    },
    //全选
    handelAllChange(val, type) {
      if (type == 'in') {
        this.formInline.incomeTypes = val ? this.inListSelect.map(item => item.transactionType) : []
        this.manualInList = val ? this.manualTransferInList.map(item => item.transactionType) : []
        this.manualInAll = val
        this.manualInStatus = false
      } else if (type == 'out') {
        this.formInline.expenseTypes = val ? this.outListSelect.map(item => item.transactionType) : []
        // 补贴也更新状态
        if (this.activeName == 'first') {
          this.formInline.subsidyDownList = val ? this.subsidyList.map(item => item.value) : []
          this.isIndeterminate = false
        }
        this.manualOutList = val ? this.manualTransferList.map(item => item.transactionType) : []
        this.manualOutAll = val
        // 不确定状态关闭
        this.manualOutStatus = false
      } else if (type == 'subsidy') { //补贴
        this.isIndeterminate = false
        this.formInline.subsidyDownList = val ? this.subsidyList.map(item => item.value) : []
      } else if (type == 'manualOut') { //手动转账-支出
        this.manualOutStatus = false
        this.manualOutList = val ? this.manualTransferList.map(item => item.transactionType) : []
        this.outAllSelect = val && this.formInline.expenseTypes.length === this.outListSelect.length
      } else if (type == 'manualIn') { //手动转账-收入
        this.manualInStatus = false
        this.manualInList = val ? this.manualTransferInList.map(item => item.transactionType) : []
        this.inAllSelect = val && this.formInline.incomeTypes.length === this.inListSelect.length
      }

    },
    // 关联全选
    handSelectChange(val, type) {
      if (type == 'in') {
        this.inAllSelect = (val.length === this.inListSelect.length && this.manualInAll)
      } else if (type == 'out') {
        this.outAllSelect = (val.length === this.outListSelect.length && this.manualOutAll)
      } else if (type == 'subsidy') {
        this.isIndeterminate = val.length > 0 && val.length < this.subsidyList.length
        if (val.length === this.subsidyList.length) {
          this.formInline.expenseTypes.push('52')
        } else {
          this.formInline.expenseTypes = this.formInline.expenseTypes.filter(item => item != '52')
        }
        // 全选状态更新
        this.outAllSelect = this.formInline.expenseTypes.length === this.outListSelect.length
      } else if (type == 'manualOut') {
        this.manualOutStatus = val.length > 0 && val.length < this.manualTransferList.length
        this.outAllSelect = false
        if (val.length == this.manualTransferList.length) {
          this.manualOutAll = true
          this.outAllSelect = this.formInline.expenseTypes.length === this.outListSelect.length
        } else {
          this.manualOutAll = false
        }
      } else if (type == 'manualIn') {
        this.manualInStatus = val.length > 0 && val.length < this.manualTransferInList.length
        this.inAllSelect = false
        if (val.length == this.manualTransferInList.length) {
          this.manualInAll = true
          this.inAllSelect = this.formInline.incomeTypes.length === this.inListSelect.length
        } else {
          this.manualInAll = false
        }
      }
    },
    // 导出
    exportFile() {
      let params = {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        ...this.formInline,
      };
      params.incomeTypes = Array.from(new Set([...this.formInline.incomeTypes, ...this.manualInList]))
      params.expenseTypes = Array.from(new Set([...this.formInline.expenseTypes, ...this.manualOutList]))
      if (this.date && this.date.length !== 0) {
        params.startTime = this.date[0];
        params.endTime = this.date[1];
      }
      params.accountType = this.accountTypeAct
      params.baseId = this.currentBaseId
      if (this.activeName === 'first' && params.transactionType) {
        this.payTypeList.forEach(item => {
          if (item.value === params.transactionType) {
            if (item.isAdd) {
              params.transactionTypeString = params.transactionType
              params.transactionType = null
            }
          }
        })
      }
      this.$post(
        "/admin-center-server/transaction/flow/exportSubAccountTransaction",
        params
      ).then((res) => {
        this.$message.success(
          "正在导出，稍后您可在【导出任务】中查看导出进度及导出文件"
        );
      });

    },
    // 部分数据重置
    resetData() {
      this.showTips = false
      this.inAllSelect = false
      this.outAllSelect = false
      this.manualInAll = false
      this.manualOutAll = false
      this.manualInList = []
      this.manualOutList = []
      // 不确定状态
      this.manualInStatus = false
      this.manualOutStatus = false
      this.isIndeterminate = false
    }




  },
  created() {

    // 获取交易开始时间 结束时间 
    let { startTime, endTime } = this.$route.query
    if (startTime && endTime) {
      this.date = [startTime, endTime]
    }
    // 获取交易类型 incomeTypes expenseTypes
    if (this.$route.query.accountSonType == this.activeName) {
      this.formInline.incomeTypes = this.$route.query.incomeTypes ? this.$route.query.incomeTypes.split(',') : []
      this.formInline.expenseTypes = this.$route.query.expenseTypes ? this.$route.query.expenseTypes.split(',') : []
    } 
    if (this.$route.query.accountSonType == 'fourth') {
      this.showTips = true
    }

  },
  mounted() {




  },
  activated() {
    if (this.baseId) {
      this.getDataList();
    }
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.subAccount {
  .select-box {
    background-color: #ffffff;
    .select-info {
      // padding-bottom: 15px;
    }
  }
  .list-box {
    background-color: #ffffff;
    overflow: hidden;
    .list-title {
      // display: flex;
      // flex-direction: row;
      // justify-content: space-between;
      // align-items: flex-start;
      .list-title-left {
        .inList,
        .outList {
          margin-top: 15px;
          display: flex;
          .checkText {
            flex-shrink: 0;
            font-weight: 700;
            font-size: 14px;
            color: #606266;
            margin-right: 3px;
          }
          .symbol {
            margin-right: 10px;
          }
        }
      }

      .list-title-right {
        margin-top: 15px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
    }
    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .paging {
      margin-top: 10px;
      float: right;
    }
  }
}
.el-icon-warning {
  //橘色
  color: #f6a018;
}
.el-icon-arrow-down {
  font-size: 10px;
  cursor: pointer;
  margin-left: 3px;
}
.mr15 {
  margin-right: 12px;
}
.popBox {
  .el-checkbox-group {
    padding-top: 10px;
    display: flex;
    flex-wrap: wrap;
  }
  .el-checkbox {
    width: 150px;
    margin-right: 5px;
    margin-bottom: 10px;
  }
}
.manual.popBox {
  .el-checkbox-group {
    padding-top: 10px;
    display: flex;
    flex-wrap: wrap;
  }
  .el-checkbox {
    width: 19%;
    margin-right: 5px;
    margin-bottom: 10px;
  }
}
.text {
  color: #e32c4d;
  font-size: 14px;
  text-align: right;
  flex: 1;
  margin-right: 10px;
}
</style>
<style lang="scss">
.prompt {
  max-width: 1000px !important;
  .tipContent{
    >div{
      margin-bottom: 5px;
      font-size: 14px;
      // css黑体
      font-family: "黑体", sans-serif;
      >span{
        font-weight: bold;
      }
    }
  }
}
</style>