<template>
  <el-dialog
    :visible.sync="isShow"
    class="dialog"
    title="配置电子围栏"
    width="90%"
    :before-close="handleClosed">
    <div class="map-wrapper">
      <div class="map" id="map"></div>
  </div>
  </el-dialog>
</template>

<script>
import { shapeTypes } from './FenceConfiguratorTools'
import loadIcon from '@/components/Track/images/start.png'
import unloadIcon from '@/components/Track/images/end.png'
export default {
  data() {
    return {
      types: [
        { label: '', value: '' }
      ],
      shapeTypesArr: [
        { label: '圆形围栏', value: shapeTypes.circle },
        { label: '矩形围栏', value: shapeTypes.rectangle },
        { label: '多边形围栏', value: shapeTypes.polygon }
      ],
      isShow: true,
      isStart: false,
      shapeType: null,
      maxRaduis: 0,
      maxDistance: 0,
      shapes: []
    }
  },
  mounted() {
    //TODO为什么不放在$nextTick获取不到#map
    this.$nextTick(() => {
      //创建地图
      let map = this.map = new AMap.Map("map")
      let idList = [this.deliveryFenceId, this.receiveFenceId]
      let promises = [this.deliveryPoint, this.receivePoint].map(async (v, i) => {
        //如果传了围栏id，获取围栏数据
        let hasFence = false,
          fenceData
        if (idList[i]) {
          fenceData = await this.$post('/order-center-server/gps/fence/query?id=' + idList[i])
          hasFence = true
        }

        //获取后台配置的最大距离
        this.maxDistance = this.maxRaduis = await this.$post('/base-center-server/settings/get?group=Function&code=maxDistanceFenceToPlace')
          .then(res => Number(res.value))

        let pointType = i === 0 ? 'start' : 'end'
        
        let loadMarker = new AMap.Marker({
          position: new AMap.LngLat(...v),
          icon: pointType === 'start' ? loadIcon : unloadIcon
        })
        let fontColor = pointType === 'start' ? 'rgb(20, 119, 18)' : 'red'
        let text = pointType === 'start' ? '装' : '卸'
        let loadTextMarker = new AMap.Marker({
          position: new AMap.LngLat(...v),
          content: `<div style="width:100px; margin-top:15px; margin-left:20px; color:${fontColor}; font-size:14px; font-weight:bold">${text}货地</div>`
        })
        map.add(loadMarker)
        map.add(loadTextMarker)

        // 创建省份图层
        var disProvince;
        function initPro(code, dep) {
          dep = typeof dep == 'undefined' ? 2 : dep;

          disProvince && disProvince.setMap(null);

          disProvince = new AMap.DistrictLayer.Province({
            zIndex: 12,
            adcode: [code],
            depth: dep,
            styles: {
              'fill': 'rgba(145, 155, 255, 0.5)',
              'province-stroke': 'cornflowerblue',
              'city-stroke': 'white', // 中国地级市边界
              'county-stroke': 'rgba(255,255,255,0.5)' // 中国区县边界
            }
          });

          disProvince.setMap(map);
        }

        AMap.plugin('AMap.Geocoder', () => {
          var geocoder = new AMap.Geocoder()
          geocoder.getAddress(v, (status, result) => {
            console.log(result)
            if (status === 'complete' && result.info === 'OK') {
                // result为对应的地理位置详细信息
                let adcode = result.regeocode.addressComponent.adcode
                initPro(adcode, 2)
            }
          })
        })
        
        //加载已有围栏
        if (hasFence) {
          let shapeType = this.shapeType = fenceData.fenceShape
          let data = {}
          if (shapeType === shapeTypes.circle) {
            data.center = [fenceData.centerLng, fenceData.centerLat]
            data.radius = fenceData.radius
          } else {
            data.paths = JSON.parse(fenceData.points).map(v => [v.lng, v.lat])
          }
          this.createShape(this.shapeType, data, v, pointType)
        } else {
          //加载默认围栏
          this.shapeType = shapeTypes.circle
          this.createShape(this.shapeType, undefined, v, pointType)
        }
      })
      Promise.all(promises).then(() => {
        this.map.setFitView(this.shapes)
      })
    })
  },
  methods: {
    createShape(type, data = {}, point, pointType) {
      let shape
      let center = point
      let style = pointType === 'start' ? {
        strokeColor: 'green',
        strokeWeight: 1,
        fillColor: 'green',
        fillOpacity: 0.3,
        strokeStyle: 'solid'
      } : {
        strokeColor: 'green',
        strokeWeight: 1,
        fillColor: 'red',
        fillOpacity: 0.3,
        strokeStyle: 'solid'
      }

      switch (type) {
        case shapeTypes.circle:
          shape = new AMap.Circle({
            center: data.center || center,
            radius: data.radius || this.defaltRadius,
            ...style
          })
          break
        case shapeTypes.rectangle:
          let southWest, northEast
          if (data.paths) {
            southWest = new AMap.LngLat(data.paths[3][0], data.paths[3][1])
            northEast = new AMap.LngLat(data.paths[1][0], data.paths[1][1])
          } else {
            southWest = new AMap.LngLat(center[0] - 0.01, center[1] + 0.01)
            northEast = new AMap.LngLat(center[0] + 0.01, center[1] - 0.01)
          }
          let bounds = new AMap.Bounds(southWest, northEast)
          shape = new AMap.Rectangle({
            bounds,
            ...style
          })
          break
        case shapeTypes.polygon:
          let polygonCoordinates
          if (data.paths) {
            polygonCoordinates = data.paths.map(v => {
              return new AMap.LngLat(v[0], v[1])
            })
          } else {
            polygonCoordinates = [
              new AMap.LngLat(center[0] - 0.01, center[1] + 0.01),
              new AMap.LngLat(center[0] + 0.01, center[1] + 0.01),
              new AMap.LngLat(center[0] + 0.017, center[1]),
              new AMap.LngLat(center[0] + 0.01, center[1] - 0.01),
              new AMap.LngLat(center[0] - 0.01, center[1] - 0.01),
              new AMap.LngLat(center[0] - 0.017, center[1])
            ]
          }
          shape = new AMap.Polygon({
            path: polygonCoordinates,
            ...style
          })
      }
      shape.setMap(this.map)
      this.shapes.push(shape)
      return shape
    },
    handleClosed(done) {
      this.onClose()
      done()
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog ::v-deep .el-dialog__body {
  padding-top: 10px;
}
.matters {
  font-size: 14px;
  line-height: 22px;
  color: #888;
}
.map-wrapper {
  position: relative;
  height: 500px;
  ::v-deep .center-marker {
    // margin-left: -15px;
    // margin-top: -15px;
    font-size: 30px;
    color: red;
  }
}
.map {
  height: 500px;
}
</style>