<template>
  <div>
    <el-tabs type="border-card"
             v-model="activeName"
             @tab-click="handleClick">
      <el-row style="text-align:right;display:inline-block" v-if="activeName==='first' || activeName==='second'">
        <el-button size="mini"
                   type="primary"
                   class="btn"
                   @click="cancellatefn">注销账户
        </el-button>
        <el-button size="mini"
                   type="primary"
                   class="btn"
                   @click="enablefn">{{deleteFlagText}}
        </el-button>
      </el-row>
      <el-tab-pane name='first'
                   label="账户详情">
        <div class="tableList">
          <h2 class="keyName">基本信息</h2>
          <detail-row>
            <detail-col :span="6" label="二维码">
              <el-button @click="seefn" size="mini">查看</el-button>
            </detail-col>
            <detail-col :span="6" label="手机号码（注册）" :value="tableData[0]?tableData[0].mobile:''"></detail-col>
            <detail-col :span="6" :label="type=='5'?'公司名称':'司机名称'" :value="tableData[0].name"></detail-col>
            <template v-if="isRegardedAsDriver || isLeader">
              <detail-col :span="6" label="认证类型" :value="tableData[0].authType"></detail-col>
              <detail-col :span="6" label="身份证照片(国徽面)">
                <el-button @click="showedPic = tableData[0].idCardBackImage" size="mini" v-if="tableData[0].idCardBackImage">查看</el-button>
                <span v-else>-</span>
              </detail-col>
              <detail-col :span="6" label="身份证照片(人像面)">
                <el-button @click="showedPic = tableData[0].idCardImage" size="mini" v-if="tableData[0].idCardImage">查看</el-button>
                <span v-else>-</span>
              </detail-col>
              <detail-col v-if="isRegardedAsDriver" :span="6" label="驾驶证照片" >
                <el-button @click="showedPic = tableData[0].drivingLicencesImage" size="mini" v-if="tableData[0].drivingLicencesImage">查看</el-button>
                <span v-else>-</span>
              </detail-col>
              <!-- v-if="isRegardedAsDriver" -->
              
              <detail-col :span="6" label="身份证姓名" :value="tableData[0].idCardName"></detail-col>
              <detail-col :span="6" label="身份证号" :value="tableData[0].idCard"></detail-col>
              <template v-if="isRegardedAsDriver">
                <detail-col :span="6" label="驾驶证号" :value="tableData[0].drivingLicencesNumber"></detail-col>
                <detail-col :span="6" label="驾驶证有效期" :value="tableData[0].drivingLicensesExpireDate"></detail-col>
                <detail-col :span="6" label="准驾车型" :value="tableData[0].drivenVehicleModel"></detail-col>
                <detail-col :span="6" label="驾驶证发证机关" :value="tableData[0].drivingLicensesIssueOrg"></detail-col>
                <detail-col :span="6" label="从业资格证照片">
                  <el-button @click="showedPic = tableData[0].employmentCertImage" size="mini" v-if="tableData[0].employmentCertImage">查看</el-button>
                  <span v-else>-</span>
                </detail-col>
                <detail-col :span="6" label="从业资格证号" :value="tableData[0].employmentCert"></detail-col>
                <detail-col :span="6" label="从业资格证有效期" :value="tableData[0].employmentCertExpireDate"></detail-col>
              </template>
              <detail-col :span="6" label="注册时间" :value="tableData[0].createdTime"></detail-col>
              <detail-col :span="6" label="账户状态" :value="tableData[0].deleteFlagTxt"></detail-col>
              <detail-col :span="6" label="工作状态" :value="tableData[0].jobStatus"></detail-col>
            </template>
            <template v-if="isSupplier">
              <detail-col :span="6" label="统一社会信用代码" :value="tableData[0].organizationCode"></detail-col>
              <detail-col :span="6" label="营业执照">
                <el-button @click="showedPic = tableData[0].licenseImage" size="mini" v-if="tableData[0].licenseImage">查看</el-button>
                <span v-else>-</span>
              </detail-col>
              <detail-col :span="6" label="道路运输经营许可证">
                <el-button @click="showedPic = tableData[0].roadTransportOperationLicenseImage" size="mini" v-if="tableData[0].roadTransportOperationLicenseImage">查看</el-button>
                <span v-else>-</span>
              </detail-col>
              <detail-col v-if="tableData[0].legalPersonCertificationFlag === '0'" :span="6" label="法人身份证">
                <el-button @click="showedPic = tableData[0].corporationIdCardImage" size="mini" v-if="tableData[0].corporationIdCardImage">查看</el-button>
                <span v-else>-</span>
              </detail-col>
              <detail-col :span="6" :label="tableData[0].legalPersonCertificationFlag === '0' ? '代办人身份证' : '法人身份证'">
                <el-button @click="showedPic = tableData[0].idCardImage" size="mini" v-if="tableData[0].idCardImage">查看</el-button>
                <span v-else>-</span>
              </detail-col>
              <detail-col v-if="tableData[0].legalPersonCertificationFlag === '1'" :span="6" label="代办人身份证">
                <!-- <el-button size="mini">查看</el-button> -->
                <span>-</span>
              </detail-col>
              <detail-col :span="6" label="授权委托书">
                <el-button @click="showedPic = tableData[0].powerOfAttorney" size="mini" v-if="tableData[0].powerOfAttorney">查看</el-button>
                <span v-else>-</span>
              </detail-col>
              <detail-col :span="6" label="道路运输经营许可证号" :value="tableData[0].roadTransportOperationLicenseNumber"></detail-col>
              <detail-col :span="6" label="道路运输经营许可证有效期止" :value="tableData[0].roadTransportOperationLicenseDate"></detail-col>
              <template v-if="tableData[0].legalPersonCertificationFlag === '0'">
                <detail-col :span="6" label="法人身份证姓名" :value="tableData[0].corporationName"></detail-col>
                <detail-col :span="6" label="法人身份证号" :value="tableData[0].corporationIdCard"></detail-col>
              </template>
              <detail-col :span="6" label="公司地址" :value="tableData[0].companyAddress"></detail-col>
              <detail-col :span="6" :label="tableData[0].legalPersonCertificationFlag === '0' ? '代办人身份证姓名' : '法人身份证姓名'" :value="tableData[0].idCardName"></detail-col>
              <detail-col :span="6" :label="tableData[0].legalPersonCertificationFlag === '0' ? '代办人身份证号' : '法人身份证号'" :value="tableData[0].idCard"></detail-col>
              <detail-col :span="6" label="紧急联系电话" :value="tableData[0].contactPhone"></detail-col>
              <detail-col :span="6" label="注册时间" :value="tableData[0].createdTime"></detail-col>
              <detail-col :span="6" label="是否法人注册" :value="tableData[0].legalPersonCertificationFlagStr"></detail-col>
              <detail-col :span="6" label="账户状态" :value="tableData[0].deleteFlagTxt"></detail-col>
            </template>
          </detail-row>
        </div>
        <template>
          <h2 class="keyName">{{title2}}</h2>
          <el-table :data="tableData1"
                    border
                    style="width: 100%"
                    highlight-current-row>
            <el-table-column show-overflow-tooltip
                            v-for="item in tableLabel1"
                            :key="item.id"
                            :label="item.label"
                            :width="item.width">
              <template slot-scope="scope">
                <div style="width: 100%;text-align: center;">{{ scope.row[item.prop] }}</div>
              </template>
            </el-table-column>
          </el-table>
          <div>
            <h2 class="keyName">车辆详情</h2>
          <el-table :data="tableData2"
                    border
                    style="width: 100%"
                    highlight-current-row>
            <el-table-column show-overflow-tooltip
                            v-for="item in tableLabel2"
                            :key="item.id"
                            :label="item.label"
                            :width="item.width">
              <template slot-scope="scope">
                <div style="width: 100%;text-align: center;">{{ scope.row[item.prop] }}</div>
              </template>
            </el-table-column>
          </el-table>
          <h3 v-if='tableData2.length==5 || tableData2.length>5'
              @click="carMorefn(1)"
              class="keyMore">查看更多</h3>
          <h2 v-if='
              type!=1'
              class="keyName">司机详情</h2>
          <el-table v-if='type!=1 && type !== "5"'
                    :data="tableData7"
                    border
                    style="width: 100%"
                    highlight-current-row>
            <el-table-column show-overflow-tooltip
                            v-for="item in tableLabel7"
                            :key="item.id"
                            :label="item.label"
                            :width="item.width">
              <template slot-scope="scope">
                <div style="width: 100%;text-align: center;">{{ scope.row[item.prop] }}</div>
              </template>
            </el-table-column>
          </el-table>
          <!-- 司机账户详情 -->
           <div v-if='type==1 || type==2'>
            <h2  class="keyName">接单押金信息</h2>
            <el-table :data="tableData8"
                      border
                      style="width: 100%"
                      highlight-current-row>
                      <el-table-column 
                        show-overflow-tooltip
                        v-for="item in tableLabel8"
                        :key="item.id"
                        :label="item.label"
                        :width="item.width">
                      <template slot-scope="scope">
                        <div style="width: 100%;text-align: center;" v-if="item.prop !='amount'">{{ scope.row[item.prop] }}</div>
                        <div style="width: 100%;text-align: center;" v-else>{{ scope.row[item.prop]?scope.row[item.prop]+'元':'' }} </div>
                      </template>
                </el-table-column>
            </el-table>
            <el-pagination
              @size-change="handleSizeChange2"
              @current-change="handleCurrentChange2"
              :current-page="depositPageNumber"
              :page-sizes="[10,20, 40, 60, 80,100]"
              :page-size="depositPageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="table8total"
              class="page-pagination"
              background
            ></el-pagination>
           </div>
          <h3 v-if='tableData7.length==5 || tableData7.length>5'
              @click="carMorefn(2)"
              class="keyMore">查看更多</h3>
          <h2 class="keyName">业务概况</h2>
          <detail-row>
            <template v-if="type == 1 || type == 2">
              <detail-col :span="6" label="接单总数量" :value="tableData3[0].orderCount"></detail-col>
              <detail-col :span="6" label="接单总吨数" :value="tableData3[0].orderItemTon"></detail-col>
              <detail-col :span="6" label="进行中的运单数" :value="tableData3[0].ongoingOrderItem"></detail-col>
              <detail-col :span="6" label="账户余额" :value="tableData3[0].accountLeave"></detail-col>
              <detail-col :span="6" label="评价" :value="tableData3[0].assess"></detail-col>
              <detail-col :span="6" label="司机星级" :value="tableData3[0].level"></detail-col>
              <detail-col :span="6" label="好友数量" :value="tableData3[0].friendNum"></detail-col>
              <detail-col :span="6" label="上一次接单时间" :value="tableData3[0].lastOrderTime"></detail-col>
              <detail-col :span="6" label="上一次登录时间" :value="tableData3[0].lastLoginTime"></detail-col>
              <detail-col :span="6" label="待结算运单数" :value="tableData3[0].itemNotInvoice"></detail-col>
              <detail-col :span="6" label="待结算总金额" :value="tableData3[0].notInviceMoney"></detail-col>
              <detail-col :span="6" label="首要联系人" :value="tableData3[0].primaryContact"></detail-col>
              <detail-col :span="6" label="首要联系人电话" :value="tableData3[0].primaryContactPhone"></detail-col>
              <detail-col :span="6" label="代理商名称" :value="tableData3[0].factorName"></detail-col>
            </template>
            <template v-else-if="type == 3 || type == 4">
              <detail-col :span="6" label="代抢运单总数量" :value="tableData3[0].orderCount"></detail-col>
              <detail-col :span="6" label="代抢运单总吨数" :value="tableData3[0].orderItemTon"></detail-col>
              <detail-col :span="6" label="账户余额" :value="tableData3[0].accountLeave"></detail-col>
              <detail-col :span="6" label="评价" :value="tableData3[0].assess"></detail-col>
              <detail-col :span="6" label="车队长星级" :value="tableData3[0].level"></detail-col>
              <detail-col :span="6" label="好友数量" :value="tableData3[0].friendNum"></detail-col>
              <detail-col :span="6" label="上一次登录时间" :value="tableData3[0].lastLoginTime"></detail-col>
              <detail-col :span="6" label="首要联系人" :value="tableData3[0].primaryContact"></detail-col>
              <detail-col :span="6" label="首要联系人电话" :value="tableData3[0].primaryContactPhone"></detail-col>
              <detail-col :span="6" label="代理商名称" :value="tableData3[0].factorName"></detail-col>
            </template>
          </detail-row>
          </div>
         
        </template>
      </el-tab-pane>
      <el-tab-pane name='second'
                   label="登录日志">
        <h2 class="keyName">登录日志</h2>
        <el-table :data="tableData"
                  border
                  style="width: 100%"
                  highlight-current-row>
          <el-table-column show-overflow-tooltip
                           v-for="item in tableLabel5"
                           :label="item.label"
                           :width="item.width">
            <template slot-scope="scope">
              <div style="width: 100%;text-align: center;">{{ scope.row[item.prop] }}</div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageNumber"
          :page-sizes="[10, 20, 40, 60, 80, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"></el-pagination>
      </el-tab-pane>
      <el-tab-pane name='three' label="配置管理" v-if="type==5">
                <!-- <h2 class="keyName" >配置管理</h2> -->
        <el-form
          :model="threeForm"
          :rules="threeFormRules"
          ref="threeForm"
          label-width="140px"
          class="threeForm">
          <el-form-item label="运费第一收款方:" prop="firstPayee">
            <el-radio-group v-model="threeForm.firstPayee" @change="changeFn">
              <el-radio label="0">司机本人收款</el-radio>
              <el-tooltip :disabled="tableData[0].roadTransportOperationLicenseNumber!=''" placement="top" content="没有道路运输证的运力供应商无法作为收款方">
                <el-radio label="1" :disabled="!tableData[0].roadTransportOperationLicenseNumber">运力供应商收款</el-radio>
              </el-tooltip>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="账期设置:" prop="billingPeriodFlag">
            <el-switch v-model="threeForm.billingPeriodFlag" :disabled="threeForm.firstPayee=='1'"  @change="openFn"></el-switch><span class="text-span">未开启状态，运单实时结算；开启后，按账期设置自动生成账单，对账完成后支付</span> 
          </el-form-item>
          <el-form-item label="" prop="billingPeriodMethod" v-if="threeForm.billingPeriodFlag">
            <el-radio-group v-model="threeForm.billingPeriodMethod" class="black-radio">
              <el-radio label="0">账期为月结（每月5日0点0分0秒系统自动生成对账单）</el-radio>
              <el-radio label="1">账期为周结（每周一0点0分0秒系统自动生成对账单）</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-button size="mini"
                   type="primary"
                   class="save-btn"
                   @click="saveFn">保存</el-button>
      </el-tab-pane>
      
    </el-tabs>
    <el-dialog title="二维码"
               :visible.sync="dialogFormVisible">
      <span id="qrCode"
            ref="qrCodeDiv"></span>
      <div slot="footer"
           class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary"
                   @click="dialogFormVisible = false">确 定
        </el-button>
      </div>
    </el-dialog>
    <elImageViewer
      v-if="showedPic"
      :on-close="handlePicClose"
      :url-list="[showedPic]"/>
  </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

const info = '/admin-center-server/commonUser/info'//详情
const logList = '/admin-center-server/commonUser/logList'//日志
const updateUserStatus = '/admin-center-server/commonUser/updateUserStatus'//启用、禁用
const cancelDriver = '/admin-center-server/commonUser/cancelDriver'//注销
import QRCode from 'qrcodejs2';

export default {
  components: { ElImageViewer },
  data () {
    return {
      showedPic: '',
      activeName: 'first',
      dialogFormVisible: false,
      deleteFlags: '',
      deleteFlagText: '',
      type: '',
      id: '',
      title2: '',
      tableData: [],
      tableData1: [],
      tableData2: [],
      tableData3: [],
      tableData7: [],
      tableData8: [],
      tableLabel: [],
      tableLabel1: [
        {
          prop: 'name',
          label: '司机名称'
        },
        { prop: 'openingBank', label: '开户行' },
        {
          prop: 'accountNumber',
          label: '卡号'
        },
        {
          prop: 'authFristTime',
          label: '首次鉴权时间'
        }],
      tableLabel2: [],
      tableLabel5: [
        {
          prop: 'loginTime',
          label: '时间'
        },
        { prop: 'loginIp', label: 'IP' },
        {
          prop: 'loginTerminal',
          label: '登录设备'
        },
        {
          prop: 'systemVersion',
          label: '系统版本'
        },
        {
          prop: 'appVersion',
          label: 'App版本'
        },
        { prop: 'area', label: '地区' },
        {
          prop: 'loginType',
          label: '登录方式'
        }
      ],
      tableLabel7: [
        {
          prop: 'driverName',
          label: '司机名称'
        },
        {
          prop: 'useCarNumber',
          label: '当前驾驶车辆'
        },
        {
          prop: 'mobile',
          label: '司机手机'
        },
        {
          prop: 'jobStataus',
          label: '工作状态'
        },
        {
          prop: 'usableCar',
          label: '当前司机可驾驶车辆'
        }
      ],
      tableLabel8: [
        {
          prop: 'createTime',
          label: '交易时间'
        },
        {
          prop: 'transactionTypeStr',
          label: '交易类型'
        },
        {
          prop: 'amount',
          label: '交易金额'
        },
        {
          prop: 'remark',
          label: '备注'
        }
      ],
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      threeForm:{
        billingPeriodFlag:false
      },
      threeFormRules:{},
      depositPageNumber:1,
      depositPageSize:10,
      table8total:0,
    };
  },
  methods: {
    changeFn(){
      if(this.threeForm.firstPayee == 1){
        this.threeForm.billingPeriodFlag = true
      }
      if(this.threeForm.firstPayee == 1 && !this.tableData[0].roadTransportOperationLicenseNumber){
        this.threeForm.firstPayee = 0
        this.$message.warning('没有道路运输证的运力供应商无法作为收款方')
      }
    },
    saveFn(){
      this.$http.post('/admin-center-server/commonUser/updateUserSetting',this.threeForm).then(res => {
        if (res.data.code == 200) {
            this.$message({
              type: 'success',
              message: '操作成功'
            });
          } else {
            this.$message.error(res.data.message);
          }
      }).catch(err => {
        this.getUserSetting();
      })
    },
    openFn(){
      if(this.threeForm.billingPeriodFlag && !this.threeForm.billingPeriodMethod){
        this.threeForm.billingPeriodMethod = 0
      }
    },
    handlePicClose() {
      this.showedPic = ''
    },
    seefn () {
      this.dialogFormVisible = true
      var userid = this.tableData[0].userId
      this.$nextTick(function () {
        this.bindQRCode(userid);
      })
    },
    bindQRCode: function (id) {
      document.getElementById("qrCode").innerHTML = ''
      new QRCode(this.$refs.qrCodeDiv, {
        text: 'type:1,id:' + id,
        width: 80,
        height: 80,
        colorDark: "#333333", //二维码颜色
        colorLight: "#ffffff", //二维码背景色
        correctLevel: QRCode.CorrectLevel.L//容错率，L/M/H
      })

    },
    //车辆详情
    carMorefn (num) {
      this.$router.push("/driverAccount/keyMore?type=" + num + "&id=" + this.id + "&driverType=" + this.type);
    },
    handleClick (tab, event) {
      if (tab.name == 'second') {
        // 触发‘配置管理’事件
        this.second();
      } else if(tab.name == 'first') {
        // 触发‘用户管理’事件
        this.getData();
      } else if(tab.name == 'three') {
        // 触发‘管理配置’事件
        this.getUserSetting();
      }

    },
    /**
     * 触发‘配置管理’事件
     */
    second () {
      this.$http.get(logList + `?order=desc&pageNumber=${this.pageNumber}&pageSize=${this.pageSize}&userId=${this.id}`).then(res => {
        this.tableData = res.data.data.list
        this.total = Number(res.data.data.total)
      })
    },
    //注销账户
    cancellatefn () {
      var that = this
      this.$confirm('注销操作为不可逆操作，您确认注销当前手机号为' + that.tableData[0].mobile + '的用户吗？', '注销提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.post(cancelDriver + `?id=${this.id}&mobile=${that.tableData[0].mobile}`).then(res => {
          if (res.data.code == 200) {
            this.$message({
              message: res.data.message,
              type: 'success'
            });
            setTimeout(function () {
              that.$router.go(-1);
            }, 3000);
          } else {
            this.$message.error(res.data.message);
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消注销'
        });
      });
    },
    handleTime(time){
      if (time && time.indexOf('.')!== -1) {
        return time.split('.')[0]
      }
      return time
    },
    getData () {
      if (this.type == 1 || this.type == 2) {
        this.getDeposit()
      }
      this.$http.get(info + '?id=' + this.id + '&userType=3&driverType=' + this.type).then(res => {
        this.tableData = []
        this.tableData1 = []
        this.tableData3 = []
        var userDate = res.data.data.userBasicinformation
        var bankInformation = res.data.data.bankInformation
        var carDetailVO = res.data.data.carDetailVO
        var orderBusinessInfo = res.data.data.orderBusinessInfo
        var driverDetailVO = res.data.data.driverDetailVO
        if (userDate.deleteFlag == -1) {
          this.deleteFlagText = '启动账户'
          this.deleteFlags = 0
        } else {
          this.deleteFlagText = '禁用账户'
          this.deleteFlags = -1
        }
        var userid = userDate.userId
        this.tableData.push(userDate)
        this.tableData[0].createdTime =this.handleTime(this.tableData[0].createdTime)
        if (bankInformation != '' && bankInformation != null) {
          this.tableData1 = bankInformation
        }
        //车辆详情
        if (carDetailVO != "" && carDetailVO != null) {
          this.tableData2 = carDetailVO
        }
        //司机详情
        if (driverDetailVO != "" && driverDetailVO != null) {
          this.tableData7 = driverDetailVO
        }
        if (orderBusinessInfo != '' && orderBusinessInfo != null) {
          this.tableData3.push(orderBusinessInfo)
        }
      })
    },
    // 获取接单押金列表
    getDeposit(){
      let data = {
        pageNumber: this.depositPageNumber,
        pageSize: this.depositPageSize,
        userId: this.id
      }
      this.$http.post('/admin-center-server/depositInformation/pageList',data).then(res => {
        this.tableData8 = res.data.data.list
        this.table8total =Number(res.data.data.total)|| 0 
      })
    },
    getUserSetting() {
      this.$http.get('/admin-center-server/commonUser/getUserSettingById?userId='+this.id).then(res => {
        this.threeForm = res.data.data
      })
    },
    enablefn () {
      this.$http.post(updateUserStatus + `/${this.id}?deleteFlag=${this.deleteFlags}`).then(res => {
        this.getData()
      })
    },
    handleSizeChange(v) {
      this.pageNumber = 1
      this.pageSize = v
      this.second()
    },
    handleCurrentChange(v) {
      this.pageNumber = v
      this.second()
    },
    handleSizeChange2(v) {
      this.depositPageNumber = 1
      this.depositPageSize = v
      this.getDeposit()
    },
    handleCurrentChange2(v) {
      this.depositPageNumber = v
      this.getDeposit()
    }
  },
  activated () {
    //type 1司机、2 司机/个人车队长 3个人车队长 4企业车队长 5运力供应商
    this.type = this.$route.query.type
    this.id = this.$route.query.id
    this.getData()
    
    if (this.$store.state.user.userInfo2.hasStandardModeFlag) {
      this.tableLabel2 = [
        {
          prop: 'plateNumber',
          label: '车牌号'
        },
        { prop: 'driverName', label: '当前驾驶司机' },
        {
          prop: 'isFreeTxt',
          label: '使用状态'
        }
      ]
    } else {
      this.tableLabel2 = [
        {
          prop: 'plateNumber',
          label: '车牌号'
        },
        { prop: 'driverName', label: '当前驾驶司机' },
        {
          prop: 'typeEnum',
          label: '车辆车队长'
        },
        {
          prop: 'isFreeTxt',
          label: '使用状态'
        },
        {
          prop: 'payeeAgentTxt',
          label: '收款方'
        }
      ]
    }

    if (this.type == 1) {
      this.tableLabel = [
        {
          prop: 'qrCodeId',
          label: '二维码'
        },
        {
          prop: 'name',
          label: '司机名称'
        },
        { prop: '', label: '驾驶证照片' },
        {
          prop: 'mobile',
          label: '手机号码（注册）'
        }
      ]
    } else if (this.type == 2) {
      this.tableLabel = [
        {
          prop: '无',
          label: '二维码'
        },
        {
          prop: 'name',
          label: '司机名称'
        },
        {
          prop: 'organizationCode',
          label: '统一社会信用代码'
        },
        { prop: '', label: '驾驶证照片' },
        {
          prop: 'mobile',
          label: '手机号码（注册）'
        }
      ]
    }
    if (this.type == 3 || this.type == 2) {
      this.tableLabel1[0].label = '个人车队长名称'
      this.tableLabel1[2].label = '卡号'
    } else if (this.isSupplier) {
      this.tableLabel1[0].label = '物流企业名称'
      this.tableLabel1[2].label = '账号'
    }
    if (this.type == 1 || this.type == 3 || this.type == 2) {
      this.title2 = '银行卡信息'
    } else {
      this.title2 = '开户行信息'
    }
    if (this.type == 4 || this.type == 3 || this.type == 2) {
      this.tableLabel2 = [
        {
          prop: 'plateNumber',
          label: '车牌号'
        },
        { prop: 'driverName', label: '当前驾驶司机' },
        {
          prop: 'isFreeTxt',
          label: '使用状态'
        },
        {
          prop: 'payeeAgentTxt',
          label: '收款方'
        }
      ]
      if (this.$store.state.user.userInfo2.hasStandardModeFlag) {
        this.tableLabel2 = [
          {
            prop: 'plateNumber',
            label: '车牌号'
          },
          { prop: 'driverName', label: '当前驾驶司机' },
          {
            prop: 'isFreeTxt',
            label: '使用状态'
          }
        ]
      }
    }
    if (this.type == 3) {
      this.tableLabel = [
        {
          prop: '无',
          label: '二维码'
        },
        {
          prop: 'name',
          label: '司机名称'
        },
        { prop: 'organizationCode', label: '统一社会信用代码' },
        {
          prop: 'mobile',
          label: '手机号码（注册）'
        },
        {
          prop: 'licenseImage',
          label: '营业执照'
        }
      ]
    }
    if (this.type == 4) {
      this.tableLabel = [
        {
          prop: '无',
          label: '二维码'
        },
        {
          prop: 'name',
          label: '司机名称'
        },
        { prop: 'organizationCode', label: '统一社会信用代码' },
        {
          prop: 'mobile',
          label: '手机号码（注册）'
        }
      ]
    }
  },
  computed: {
    isRegardedAsDriver() {
      //type为空表示没有身份 2表示车队长&司机身份 这两种情况都使用司机的信息
      return !this.type || this.type === '1' || this.type === '2'
    },
    isLeader() {
      return  this.type === '3'
    },
    isSupplier() {
      return this.type === '5'
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
h2 {
  font-weight: normal;
  font-size: 16px;
  margin: 10px 0;
  color: #1898ff;
}

.el-row {
  width: 100%;
}
.keyMore {
  font-size: 14px;
  text-align: right;
  cursor: pointer;
  color: #1898ff;
}
.tableList {
  margin-bottom: 30px;
}
.pagination {
  text-align: right;
  margin-top: 10px;
}
.save-btn{
  margin-top: 40px;
}
.text-span{
  color: #abaaab;
  vertical-align: middle;
  margin-left: 10px;
}
.black-radio{
  label{
    display: block;
    margin-bottom: 20px;
  }
}
.page-pagination{
  margin: 10px 0px;
  text-align: right;
}
</style>
