<template>
  <div class="app-container">
    <div class="list-box">
      <div class="list-title">列表中的”数量“合计：{{totalCount()}}</div>
      <div class="list-main">
        <el-table 
        border 
        :data="data"
        cell-class-name="table_cell_gray"
        header-cell-class-name="table_header_cell_gray">
          <el-table-column label="序号" type="index"></el-table-column>
          <el-table-column label="清单号" prop="qsn"></el-table-column>
          <el-table-column label="运单号" prop="sn"></el-table-column>
          <el-table-column label="订单号" prop="orderSn"></el-table-column>
          <el-table-column label="所属项目" prop="projectName">
            <template slot-scope="scope">
              {{scope.row.projectName ? scope.row.projectName : '-'}}
            </template>
          </el-table-column>
          <el-table-column label="货物名称" prop="cargoType"></el-table-column>
          <el-table-column label="数量" prop="paymentTon"></el-table-column>
          <el-table-column label="车牌号" prop="plateNumber"></el-table-column>
          <el-table-column label="运单金额" prop="amount"></el-table-column>
          <el-table-column label="发货地点" prop="deliveryPlace"></el-table-column>
          <el-table-column label="卸货地点" prop="receivePlace"></el-table-column>
          <el-table-column label="结算时间" prop="endTime"></el-table-column>
          <el-table-column label="操作" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" @click="waybillDetail(scope.row.orderItemId)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { objToFormData } from '@/utils/tools'
import { Big } from 'big.js'

export default {
  data() {
    return {
      data: []
    }
  },
  activated() {
    this.id = this.$route.query.id
    if (this.id === 'all') {
      this.getAllList()
    } else {
      this.getList()
    }
    
  },
  methods: {
    getList() {
      this.$http.get('/admin-center-server/bill/getInvoiceOrderItemList?id=' + this.id)
        .then(res => {
          let data = res.data
          if (data.code === '200') {
            data = data.data
            this.data = data
          } else {
            this.$message.error(data.message)
          }
        })
    },
    async getAllList() {
      let result = await this.$http.get('/admin-center-server/bill/getInvoiceOrderItemListByInvoiceId?id=' + this.$route.query.invoiceId)
      if (result.data.code == '200') {
        this.data = result.data.data
      }
    },
    searchReset() {
      this.searchForm = { ...this.searchDefault }
      this.date = []
      this.searchFinal = { ...this.searchDefault }
      this.page.pageNumber = 1
      this.getList()
    },
    handleSizeChange() {
      this.getList()
    },
    waybillDetail(id) {
      this.$router.push('/transport/transportListDetail?orderItemId=' + id)
    },
    totalCount() {
      let result = new Big(0)
      if (this.data.length > 0) {
        for (let item of this.data) {
          result = result.plus(new Big(item.paymentTon))
        }
      }
      return result.toFixed(2)
    }
  }
}
</script>

<style src="@/assets/scss/list.scss" scoped lang="scss"></style>
<style scoped lang="scss">
.list-title {
  color: #333;
  font-size: 15px;
}
</style>