<template>
  <div class="app-container addCar">
    <div class="inner-box">
      <el-form label-width="200px" class="demo-ruleForm">
        <el-form-item label="合同协议名称" required>
          <el-input v-model="protocolName" style="width: 320px" placeholder="请输入合同协议名称"></el-input>
        </el-form-item>

        <el-form-item label="内容">
          <div class="editor-wrapper">
            <div ref="editor" class="editContent"></div>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button @click="commitInfo">修改</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import { client, OSS_REGION } from "@/utils/alioss";

// 引入wangEditor富文本
import E from "wangeditor";
import "wangeditor/release/wangEditor.min.css";

export default {
  data() {
    return {
      options: [],
      value: "",
      aaaa: "",
      editor: "",
      info: "",
      typeFlag: "",
      dialogVisible: false,
      menus: [
        // 'head',  // 标题
        "bold", // 粗体
        "fontSize", // 字号
        "fontName", // 字体
        "italic", // 斜体
        "underline", // 下划线
        "strikeThrough", // 删除线
        "foreColor", // 文字颜色
        "backColor", // 背景颜色
        // "link", // 插入链接
        "list", // 列表
        "justify", // 对齐方式
        "quote", // 引用
        // 'emoticon',  // 表情
        // "image", // 插入图片
        // 'table',  // 表格
        // 'video',  // 插入视频
        // 'code',  // 插入代码
        "undo", // 撤销
        "redo" // 重复
      ],
      protocolName: "", //合同协议名字
      protocolPosition: "", //合同协议的位置
      operatorAccount: "", //操作人账户
      operatorName: "", //修改人账号姓名
      protocolId: "", //修改数据ID
      editorContent: "",
      ruleForm: {
        content: "" //文章内容
      }
    };
  },

  created() {
    // this.getAliyunData();
  },

  mounted() {
    this.protocolName = this.$route.query.protocolName;
    this.protocolPosition = this.$route.query.protocolPosition;
    this.getText(); //获取合同内容
    this.initEditor();
    this.getUserInfo(); //获取账号信息
  },
  methods: {
    initEditor() {
      this.editor = new E(this.$refs.editor);
      this.editor.customConfig.menus = this.menus; // menu菜单
      this.editor.customConfig.uploadImgMaxSize = 2 * 1024 * 1024;
      // 限制最多上传6张图片
      this.editor.customConfig.uploadImgMaxLength = 6;
      // 设置超时
      this.editor.customConfig.uploadImgTimeout = 3 * 60 * 1000;
      // 关闭粘贴样式的过滤
      this.editor.customConfig.pasteFilterStyle = false;
      // 忽略粘贴内容中的图片
      this.editor.customConfig.pasteIgnoreImg = true;
      this.editor.customConfig.customUploadImg = async (files, insert) => {
        await this.ossUpload(files, insert);
        // insert(this.picUrl);
      };
      this.editor.customConfig.onchange = html => {
        this.editorContent = html;
      };
      this.editor.create();
      this.getHtml();
    },
    // 获取html
    async getHtml() {
      if (!this.html) return;
      let html = this.html;
      if (isUrl(this.html)) {
        const res = await CommonServer.getHtml(this.html);
        html = res.data;
      }
      this.$nextTick(() => {
        this.editorContent = html;
        this.editor.txt.html(html);
      });
    },
    // 获取编辑器内容
    getContent() {
      // this.$emit('getContent', editor.txt.html())
    },
    // 清除内容
    clearHtml() {
      this.editor && this.editor.txt.clear();
    },
    /* 再次调用接口获取内容 */
    getText() {
      let postData = {
        protocolName: this.$route.query.protocolName,
        protocolPosition: this.$route.query.protocolPosition
      };

      this.$http
        .post("/base-center-server/protocol/searchAgreementList", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            let content = res.data.data[0].protocolCopywriting;
            this.ruleForm.content = this.editor.txt.html(content);
            this.protocolName = data.data[0].protocolName;
            this.protocolId = data.data[0].protocolId;
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 提交编辑的内容 */
    commitInfo() {
      this.ruleForm.content = this.editor.txt.html();
      if (this.protocolName.trim() == "") {
        this.$message.warning("请填写协议名称");
        return;
      }
      let postData = {
        operatorName: this.operatorName,
        protocolCopywriting: this.ruleForm.content,
        protocolId: this.protocolId,
        operatorAccount: this.operatorAccount,
        operatorTitle: this.protocolName
      };
      this.$http
        .post(
          "/base-center-server/protocol/updateProtocol",postData
        )
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message.success("更改成功");
            this.$router.go(-1);
          } else {
            this.$message.warning(data.message);
          }
        });
    },

    /* 获取用户信息 */
    getUserInfo() {
      this.$http.post("/admin-center-server/sys/detailed").then(res => {
        let data = res.data;
        this.operatorAccount = data.data.nickName;
        this.operatorName = data.data.realName;
        // this.form.realName = data.data.realName
      });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss">
.editContent {
  // height: 1000px;
}
.addCar {
  background-color: #ffffff;
  .tip {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 40px 40px 0;
    font-size: 12px;
    em {
      margin-right: 5px;
    }
  }
  .inner-box {
    // margin-left: 10%;
    width: 70%;
    .upload-box {
      width: 100%;
      height: 100%;
      position: relative;
      .icon-XZ {
        width: 92px;
        height: 92px;
        margin: 0 auto;
        background: url("../../../assets/xiazai.png") no-repeat;
        background-size: 100% 100%;
      }
      .icon-word {
        width: 100%;
        height: 20px;
        line-height: 20px;
        font-size: 10px;
        position: absolute;
        bottom: 25px;
        left: 0px;
        color: #cccccc;
      }
    }
  }
}
.w-e-menu {
  z-index: 2 !important;
}
.w-e-text-container {
  z-index: 1 !important;
}
.editor {
  margin-top: 30px;
}

.myQuillEditor {
  height: 400px;
}
.editCommit {
  margin: 100px;
}
</style>
