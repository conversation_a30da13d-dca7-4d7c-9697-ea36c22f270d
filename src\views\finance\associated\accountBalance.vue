<template>
  <div class="app-container accountCancellation">
    <el-tabs v-model="activeName"
             @tab-click="handleClick">
      <el-tab-pane label="账户余额"
                   name="first">
        <div class="select-box">
          <div class="top-title">
            <div>
              筛选查询
            </div>
            <div>
              <el-button icon="el-icon-search"
                         type="primary"
                         @click="onSubmit"
                         size="mini">查询</el-button>
              <el-button icon="el-icon-delete"
                         type="danger"
                         @click="clearForm"
                         size="mini">清空筛选</el-button>
            </div>
          </div>
          <div class="select-info">
            <el-form :inline="true"
                     :model="formInline"
                     class="demo-form-inline"
                     label-width="140px"
                     size="mini">
              <el-form-item label="平台名称:">
                <el-input v-model="formInline.userName"
                          placeholder="请输入司机名称"></el-input>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="list-box">
          <div class="list-title">
            <div>明细列表</div>
          </div>
          <div class="list-main">
            <template>
              <el-table :data="tableData"
                        border
                        style="width: 100%">
                <el-table-column type="index"
                                 label="序号"
                                 width="50">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="platformName"
                                 label="对接平台名称"
                                 width="140">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="accountAmount"
                                 label="账户余额（元）"
                                 width="120">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="consumerAmount"
                                 label="消费合计（元）"
                                 width="200">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="accountTotalAmount"
                                 label="油气费总计（元）"
                                 width="200">
                </el-table-column>
                <el-table-column prop="dieselDiscountsRatio"
                                 show-overflow-tooltip
                                 :formatter="dieselRatio"
                                 width="120"
                                 label="柴油优惠比例">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="gasDiscountsRatio"
                                 :formatter="naturalGasRatio"
                                 label="天然气优惠比例"
                                 width="140">
                </el-table-column>
                <el-table-column fixed="right"
                                 label="操作">
                  <template slot-scope="scope">
                    <el-button type="text"
                               @click="adjustBalancefn(scope.row)"
                               size="small">调整余额
                    </el-button>
                    <el-button type="text"
                               @click="proportefn(scope.row)"
                               size="small">调整比例
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </template>

          </div>
          <div class="paging">
            <div class="block">
              <el-pagination @size-change="handleSizeChange"
                             @current-change="handleCurrentChange"
                             :current-page="pageNumber"
                             :page-sizes="[10, 20, 30, 40,50,100]"
                             :page-size="pageSize"
                             layout="total, sizes, prev, pager, next, jumper"
                             :total="total">
              </el-pagination>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="操作日志"
                   name="second">
        <div class="select-box">
          <div class="top-title">
            <div>
              筛选查询
            </div>
            <div>
              <el-button icon="el-icon-search"
                         type="primary"
                         @click="onSubmit"
                         size="mini">查询</el-button>
              <el-button icon="el-icon-delete"
                         type="danger"
                         @click="clearForm"
                         size="mini">清空筛选</el-button>
            </div>
          </div>
          <div class="select-info">
            <el-form :inline="true"
                     :model="journalInline"
                     class="demo-form-inline"
                     label-width="140px"
                     size="mini">
              <el-form-item label="平台名称:">
                <el-input v-model="journalInline.userName"
                          placeholder="请输入平台名称"></el-input>
              </el-form-item>
              <el-form-item label="时间筛选:">
                <el-col>
                  <el-date-picker v-model="journalInline.date"
                                  value-format=""
                                  type="datetimerange"
                                  range-separator="至"
                                  start-placeholder="选择开始时间"
                                  end-placeholder="选择结束时间">
                  </el-date-picker>
                </el-col>
              </el-form-item>
              <el-form-item label="操作类型："
                            label-width="100px"
                            prop='authenticateStatus'>
                <el-select v-model="journalInline.authenticateStatus"
                           placeholder="请选择操作类型"
                           clearable
                           label-width="10px">
                  <el-option v-for="(item, lan) in statusWrap"
                             :label="item.name"
                             :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="list-box">
          <div class="list-title">
            <div>明细列表</div>
          </div>
          <div class="list-main">
            <template>
              <el-table :data="tableData"
                        border
                        style="width: 100%">
                <el-table-column type="index"
                                 label="序号"
                                 width="50">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="platformName"
                                 label="对接平台名称"
                                 width="140">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="type"
                                 :formatter="isStopStatus"
                                 label="操作类型">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="remark"
                                 label="备注">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="voucherUrl"
                                 label="凭证图片">
                  <template slot-scope="scope">
                    <span style="color: blue;cursor: pointer"
                          @click="handleRowClick(scope.row)">查看图片</span>
                  </template>
                </el-table-column>
                <el-table-column prop="createdUsername"
                                 show-overflow-tooltip
                                 label="操作人">
                </el-table-column>
                <el-table-column show-overflow-tooltip
                                 prop="createdDatetime"
                                 label="操作日期">
                </el-table-column>
              </el-table>
            </template>

          </div>
          <div class="paging">
            <div class="block">
              <el-pagination @size-change="handleSizeChange"
                             @current-change="handleCurrentChange"
                             :current-page="pageNumber"
                             :page-sizes="[10, 20, 30, 40,50,100]"
                             :page-size="pageSize"
                             layout="total, sizes, prev, pager, next, jumper"
                             :total="total">
              </el-pagination>
            </div>
          </div>
        </div>
      </el-tab-pane>

    </el-tabs>

    <!-- 多张图片弹窗 -->
    <el-dialog title="图片详情"
               :visible.sync="dialogPhoto">
      <ul class="photoList">
        <li v-for="(item,index) in imgArr"
            :key="index">
          <img :src="item"
               alt />
        </li>
      </ul>
    </el-dialog>
    <!--  调整余额 弹窗-->
    <el-dialog title="调整余额"
               :visible.sync="dialogVisible"
               :before-close="handleClose">
      <div class="">
        <el-form ref="formCars"
                 :model="formCars"
                 :rules="formCarsRules"
                 label-width="120px">
          <el-form-item class='title-wrap1'>
            <span>平台名称：{{currentData.platformName}}</span>
            <span>当前余额：{{currentData.accountAmount}}元</span>
          </el-form-item>
          <el-form-item label="调整金额:"
                        required
                        prop='name'>
            <el-input placeholder="请填写调整金额"
                      style="width: 300px"
                      v-model="formCars.name"><template slot="append">元</template></el-input>
            <span class='tip'>若向下调整请在数字前输入"-"号</span>
          </el-form-item>
          <el-form-item label="凭证图片:">
            <el-upload action
                       :limit="6"
                       multiple
                       :on-exceed="handleExceed"
                       list-type="picture-card"
                       :http-request="ossUpload"
                       :file-list="fileList"
                       :on-remove='handleRemove'>
              <div class="upload-box">
                <div class="icon-XZ"></div>
                <div class="icon-word">支持jpg、jpeg、png 格式不超过10M</div>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="操作备注:"
                        prop='content'>
            <el-input type="textarea"
                      maxlength="60"
                      show-word-limit
                      v-model="formCars.content"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary"
                   @click="suretransfer('formCars')">确 定</el-button>
      </span>
    </el-dialog>
    <!--  调整比例 弹窗-->
    <el-dialog title="调整比例"
               :visible.sync="proporteVisible"
               :before-close="handleClose">
      <div class="">
        <el-form ref="formproportion"
                 :model="formproportion"
                 :rules="formproportionRules"
                 label-width="170px">
          <el-form-item class='title-wrap'>
            <span>平台名称：{{currentData.platformName}}</span>
            <span>当前柴油比例：{{currentData.dieselDiscountsRatio}}%</span>
            <span>当前天然气比例：{{currentData.gasDiscountsRatio}}%</span>
          </el-form-item>
          <el-form-item label="调整后柴油优惠比例:"
                        prop='name'
                        required>
            <el-input placeholder="请填写调整后比例"
                      style="width: 300px"
                      v-model="formproportion.name"><template slot="append">%</template></el-input>
          </el-form-item>
          <el-form-item label="调整后天然气优惠比例:"
                        prop='proporte'
                        required>
            <el-input placeholder="请填写调整后比例"
                      style="width: 300px"
                      v-model="formproportion.proporte"><template slot="append">%</template></el-input>
          </el-form-item>
          <el-form-item label="凭证图片:">
            <el-upload action
                       :limit="6"
                       multiple
                       :on-exceed="handleExceed"
                       list-type="picture-card"
                       :file-list="fileList"
                       :http-request="ossUpload"
                       :on-remove='handleRemove'>
              <div class="upload-box">
                <div class="icon-XZ"></div>
                <div class="icon-word">支持jpg、jpeg、png 格式不超过10M</div>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="操作备注:"
                        prop='content'>
            <el-input type="textarea"
                      maxlength="60"
                      show-word-limit
                      v-model="formproportion.content"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary"
                   @click="formproporteOk('formproportion')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { client, OSS_REGION } from '@/utils/alioss'
const listOilPlatform = '/admin-center-server/oilPlatform/listOilPlatform'//账户余额
const listOilPlatformLog = '/admin-center-server/oilPlatform/listOilPlatformLog'//日志
const OilPlatformAccountAmount = '/admin-center-server/oilPlatform/updateOilPlatformAccountAmount'//调整余额
const OilPlatformRatio = '/admin-center-server/oilPlatform/updateOilPlatformRatio'//调整比例
export default {
  data () {
    return {
      Aliyun: {},
      currentData: [],
      formCars: {
        content: '',
        name: '',
      },
      formCarsRules: {
        name: [
          { required: true, message: '请输入金额', trigger: 'change' },
          { required: true, trigger: 'change', validator: this.lastTwo }
        ]
      },
      formproportion: {
        content: '',
        proporte: '',
        name: '',
      },
      formproportionRules: {
        name: [
          { required: true, message: '请输入柴油优惠比例', trigger: 'blur' },
          { required: true, trigger: 'change', validator: this.lastTwos }
        ],
        proporte: [
          { required: true, message: '请输入天然气优惠比例', trigger: 'blur' },
          { required: true, trigger: 'change', validator: this.lastTwos }
        ],
      },
      examineId: '',
      imgArr: [],
      statusWrap: [{
        name: '调整比例',
        id: '1'
      }, {
        name: '调整金额',
        id: '0'
      }],
      activeName: 'first',
      agreeMask: false,
      dialogImg: false,
      dialogPhoto: false,
      dialogVisible: false,
      examineMask: false,
      proporteVisible: false,
      formInline: {
        userName: '',
        userMobile: '',
        idCard: '',
        operatorName: '',
        dataSource: '',
        closeStatus: '',
        operationTimeStart: '',
        operationTimeEnd: '',
      },
      journalInline: {
        userName: '',
        date: '',
        authenticateStatus: ''
      },
      tableData: [],
      total: 0,
      pageSize: 10,
      pageNumber: 1,
      date: [],
      images: [],
      fileList: []
    }
  },
  methods: {
    /** 操作类型 **/
    isStopStatus (row) {
      if (row.type === '0') {
        return '调整金额'
      } else if (row.type === '1') {
        return '调整比例'
      }
    },
    handleClick (tab, event) {
      this.formInline = {
        userName: ''
      };
      this.journalInline = {
        userName: '',
        date: '',
        authenticateStatus: ''
      };
      this.date = [];
      if (tab.name == 'first') {
        this.getDataList()
      } else {
        this.listOilPlatformLogData()
      }
    },
    /* 查看图大图 */
    pictureDetail () {
      this.dialogImg = true;
    },
    /** 清空筛选 **/
    clearForm () {
      this.formInline = {
        userName: ''
      };
      this.journalInline = {
        userName: '',
        date: '',
        authenticateStatus: ''
      };
      this.date = [];
      if (this.activeName == 'first') {
        this.getDataList()
      } else {
        this.listOilPlatformLogData()
      }
    },
    /** 按条件查询 **/
    onSubmit () {
      this.pageNumber = 1
      this.pageSize = 10
      this.date = [];
      if (this.activeName == 'first') {
        this.getDataList()
      } else {
        this.listOilPlatformLogData()
      }
    },
    dieselRatio (row) {
      return row.dieselDiscountsRatio + '%'
    },
    naturalGasRatio (row) { return row.gasDiscountsRatio + '%' },
    handleSizeChange (val) {
      this.pageSize = val;
      this.pageNumber = 1;
      this.date = [];
      if (this.activeName == 'first') {
        this.getDataList()
      } else {
        this.listOilPlatformLogData()
      }
    },
    handleCurrentChange (val) {
      this.pageNumber = val;
      this.date = [];
      if (this.activeName == 'first') {
        this.getDataList()
      } else {
        this.listOilPlatformLogData()
      }
    },
    getDataList () {
      let postData = {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        platformName: this.formInline.userName,
      };
      this.$http.post(listOilPlatform, postData).then(res => {
        let data = res.data;
        if (data.code === '200') {

          this.total = Number(data.data.total);
          let resData = data.data.list;
          this.tableData = resData;
        }
      })
    },
    listOilPlatformLogData () {
      var v = this.journalInline,
        data1 = '',
        data2 = '';
      if (v.date) {
        data1 = this.getDate(v.date[0])
        data2 = this.getDate(v.date[1])
      } else {
        data1 = ''
        data2 = ''
      }
      console.log(v)
      let postData = {
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        platformName: v.userName,
        operDateBegin: data1,
        operDateEnd: data2,
        type: v.authenticateStatus
      };
      this.$http.post(listOilPlatformLog, postData).then(res => {
        let data = res.data;
        if (data.code === '200') {
          this.total = Number(data.data.total);
          let resData = data.data.list;
          resData.map((item, index) => {
            let str = [];

            str = item.voucherUrl ? item.voucherUrl.split(',') : [];
            item.voucherUrl = str;
            console.log()
          });
          this.tableData = resData;
        }
      })
    },
    /** 表格 点击查看图片列 **/
    handleRowClick (row) {
      this.dialogPhoto = true;
      this.imgArr = row.voucherUrl
    },
    handleClose () {
      this.dialogVisible = false;
      this.examineMask = false;
      this.dialogPhoto = false;
      this.proporteVisible = false
      this.fileList = []
    },
    handleClose1 () {
      this.agreeMask = false;
    },
    getAliyunData () {
      const ossApiUrl = "/base-center-server/sts/oss_signature"; // 获取oss签名的地址
      // 获取oss签名
      this.$http.get(ossApiUrl).then(res => {
        if (res.data.code == "200") {
          let data = res.data;
          this.Aliyun = data.data;
          this.Aliyun.region = OSS_REGION;
        }
      });
    },
    /** 上传数量限制 **/
    handleExceed () {
      this.$message.warning('最多上传6张图片')
    },
    /** 单个上传 重组上传集合 **/
    ossUpload (param) {
      let file = param.file; // 文件的
      let uid = file.uid;
      const tmpcnt = file.name.lastIndexOf('.');
      const exname = file.name.substring(tmpcnt + 1);
      const fileName = '/' + this.Aliyun.bucket + '/' + this.Aliyun.dir + this.$md5(file.name) + '.' + exname;
      client(this.Aliyun).put(fileName, file).then(res => {
        if (res.res.status === 200) {
          let imgUrl = res.res.requestUrls[0];
          let obj = { imgUrl: imgUrl, uid: uid };
          this.images.push(obj);
          this.$message.success('上传成功')
        } else {
          this.$message.error(res.res.message)
        }
      })
    },
    /** 上传成功后 删除 重新上传集合 **/
    handleRemove (file, fileList) {
      let uid = file.uid;
      let str = [];
      this.images.map((item, index) => {
        if (item.uid !== uid) {
          str.push(item)
        }
      });
      this.images = str;
    },
    /** 调整余额 提交 **/
    suretransfer (formName) {
      var that = this
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (this.images.length == 0) {
            this.$message.error('请上传凭证')
            return false
          }
          let imgStr = this.images.map((item, index) => {
            return item.imgUrl
          });
          var v = this.formCars
          var data = {
            "accountAmount": v.name,
            "id": this.currentData.id,
            "remark": v.content,
            "voucherUrl": imgStr.toString()
          }
          this.$http.post(OilPlatformAccountAmount, data).then(res => {
            if (res.data.code == "200") {
              this.dialogVisible = false;
              this.fileList = []
              this.$message.success(res.data.message)
              setTimeout(function () { that.getDataList() }, 3000);
            } else {
              this.$message.error(res.data.message)
            }
          });
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    /** 调整比例 提交 **/
    formproporteOk (formName) {
      var that = this
      this.$refs[formName].validate(valid => {
        if (valid) {
          // console.log(this.formCars)
          let imgStr = this.images.map((item, index) => {
            return item.imgUrl
          });
          if (this.images.length == 0) {
            this.$message.error('请上传凭证')
            return false
          }
          var v = this.formproportion
          var data = {
            "dieselDiscountsRatio": v.name,
            "gasDiscountsRatio": v.proporte,
            "id": this.currentData.id,
            "remark": v.content,
            "voucherUrls": imgStr.toString()
          }
          console.log(data)
          this.$http.post(OilPlatformRatio, data).then(res => {
            if (res.data.code == "200") {
              this.proporteVisible = false;
              this.fileList = []
              this.$message.success(res.data.message)
              setTimeout(function () { that.getDataList() }, 3000);
            } else {
              this.$message.error(res.data.message)
            }
          });
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    adjustBalancefn (row) {
      this.formCars = {
        name: '',
        content: ''
      }
      this.images = []
      this.fileList = []
      this.dialogVisible = true
      this.currentData = row
    },
    proportefn (row) {
      this.formproportion = {
        content: '',
        proporte: '',
        name: '',
      }
      this.images = []
      this.proporteVisible = true
      this.currentData = row
    }
  },
  activated () {
    this.date = [];
    if (this.activeName == 'first') {
      this.getDataList()
    } else {
      this.listOilPlatformLogData()
    }
    this.getAliyunData()
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.accountCancellation {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      padding-right: 10px;
      border-bottom: 1px solid #cccccc;
      display: flex;
      flex-flow: row nowrap;
      justify-content: space-between;
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;
    overflow: hidden;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }

    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }

    .paging {
      margin-top: 10px;
      float: right;
    }
  }

  .photoList {
    li {
      display: inline-block;

      img {
        padding: 10px;
        background: #cfcfcf;
        width: 200px;
        margin: 20px;
      }
    }
  }
}
.upload-box {
  width: 100%;
  height: 100%;
  position: relative;

  .icon-XZ {
    width: 92px;
    height: 92px;
    margin: 0 auto;
    background: url("../../dispatchPage/carManage/images/xiazai.png") no-repeat;
    background-size: 100% 100%;
  }

  .icon-word {
    width: 100%;
    height: 20px;
    line-height: 20px;
    font-size: 10px;
    position: absolute;
    bottom: 25px;
    left: 0px;
    color: #cccccc;
  }
}
.title-wrap {
  margin-left: -140px;
  span {
    margin-right: 20px;
  }
}
.title-wrap1 {
  margin-left: -75px;
  span {
    margin-right: 20px;
  }
}
.tip {
  font-size: 12px;
  color: red;
  margin-left: 10px;
}
</style>
