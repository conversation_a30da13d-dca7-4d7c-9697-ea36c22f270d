<template>
    <div class="app-container carsList">
        <div class="select-box">
            <div class="top-title">
                <div>筛选查询</div>
                <div class="button">
                    <el-button
                            class="left"
                            icon="el-icon-refresh-right"
                            size="mini"
                            type="success"
                            @click="()=>{this.$router.go(0)}"
                    >刷新
                    </el-button>
                    <el-button class="left"
                               icon="el-icon-search"
                               size="mini"
                               type="primary"
                               @click="onSubmit">查询
                    </el-button>
                    <el-button class="left"
                               icon="el-icon-delete"
                               size="mini"
                               type="danger"
                               @click="resetSubmit">清空筛选
                    </el-button>
                </div>
            </div>
            <div class="select-info">
                <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="150px" size="mini">
                    <el-form-item label="车牌号:">
                        <el-input v-model="formInline.plateNumber" placeholder="请输入车牌号" :οnkeyup="formInline.plateNumber=formInline.plateNumber.replace(/\s/g, '')"></el-input>
                    </el-form-item>
                    <el-form-item label="车辆使用状态:">
                        <el-select v-model="formInline.isFree" placeholder="不限" @change="isFreeChange">
                            <el-option label="不限" value=""></el-option>
                            <el-option label="空闲" value="0"></el-option>
                            <el-option label="使用中" value="1"></el-option>
                            <el-option label="停用" value="2"></el-option>
                            <el-option label="冻结" value="3"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="认证状态">
                        <el-select v-model="formInline.authStatus">
                            <el-option label="不限" value=""></el-option>
                            <el-option value="3" label="认证中"></el-option>
                            <el-option value="1" label="认证成功"></el-option>
                            <el-option value="2" label="认证驳回"></el-option>
                            <el-option value="4" label="认证过期"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="临期状态">
                        <el-select v-model="formInline.nearExpirationStatus">
                            <el-option label="不限" value=""></el-option>
                            <el-option value="1" label="即将到期"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="所属车队长:">
                        <el-input :disabled="isDisabled"
                                  v-model="boss" placeholder="请输入车队长名称" :οnkeyup="boss=boss.replace(/\s/g, '')"></el-input>
                    </el-form-item>
                    <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="">
                        <el-radio :disabled="isRadioDisabled" v-model="resource" label="100">无车队长</el-radio>
                    </el-form-item>
                </el-form>
                <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="150px" size="mini">
                    <el-form-item label="创建日期:">
                        <el-date-picker
                                :clearable="false"
                                @blur="selectTime"
                                v-model="formInline.date"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="datetimerange"
                                :default-time="['00:00:00', '23:59:59']"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="所属车队长手机号:">
                        <el-input v-model="formInline.ownerMobile" placeholder="请输入所属车队长手机号" :οnkeyup="formInline.ownerMobile=formInline.ownerMobile.replace(/\s/g, '')"></el-input>
                    </el-form-item>
                    <el-form-item label="道路运输证是否异常：" label-width="180px">
                        <el-select v-model="formInline.shippingCertWarnFlag">
                            <el-option label="不限" value=""></el-option>
                            <el-option label="是" value="1"></el-option>
                            <el-option label="否" value="0"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="list-box">
            <div class="list-title">
                <div>数据列表</div>
                <div>
                    <el-button @click="$router.push('/dispatch/page/gpsList')" size="small" type="primary">车辆GPS设备</el-button>
                    <el-button v-if="!$store.state.user.userInfo2.hasStandardModeFlag" size="small" icon="el-icon-sort" type="primary" @click="transferCars">转让车辆</el-button>
                    <el-button size="small" icon="el-icon-plus" type="success" @click="addCar">新增车辆</el-button>
                    <el-button size="small" icon="el-icon-s-order" type="danger" @click="rejectReason">驳回原因管理</el-button>
                </div>
            </div>
            <div class="list-main">
                <template>
                    <el-table
                            :data="tableData"
                            border
                            @selection-change="handleSelectionChange"
                            :row-class-name="tableRowClassName"
                            style="width: 100%">
                        <el-table-column
                                type="selection"
                                width="55">
                        </el-table-column>
                        <el-table-column
                                prop="num"
                                label="序号"
                                type="index"
                                width="50">
                        </el-table-column>
                        <el-table-column
                                prop="plateNumber"
                                show-overflow-tooltip
                                label="车牌号"
                        >
                        </el-table-column>
                        <el-table-column label="车长车型" prop="carLengthName">
                            <template scope="scope">
                                {{(scope.row.carLength || '') + (scope.row.carLength ? '米' : '') + (scope.row.carTypeName || '')}}
                            </template>
                        </el-table-column>                       
                        <el-table-column label="车辆类型" prop="carModelName"></el-table-column>
                        <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="GPS设备状态">
                            <template slot-scope="scope">
                                <div class="gps-status" :class="getGpsStatusClass(scope.row.deviceStatus)">{{ getGpsStatusText(scope.row.deviceStatus) }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column
                                v-if="!$store.state.user.userInfo2.hasStandardModeFlag"
                                prop="payeeAgent"
                                :formatter="payeeAgentStatus"
                                show-overflow-tooltip
                                label="收款方"
                        >
                        </el-table-column>
                        <el-table-column prop="memberName" label="认证信息所属" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="authStatus" :formatter="jiashiStatus" label="认证状态" show-overflow-tooltip></el-table-column>
                        <el-table-column
                                show-overflow-tooltip
                                prop="driverName"
                                label="当前驾驶司机"
                        >
                        </el-table-column>
                        <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag"
                                show-overflow-tooltip
                                prop="ownerName"
                                label="所属车队长"
                        >
                        </el-table-column>
                        <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag"
                                prop="driverMobile"
                                show-overflow-tooltip
                                label="所属车队长手机号"
                                width="140">
                        </el-table-column>
                        <el-table-column label="道路运输证异常" width="140">
                            <template slot-scope="scope">
                                {{ scope.row.shippingCertWarnFlag === '1' ? '是' : '否' }}
                            </template>
                        </el-table-column>
                        <el-table-column
                                prop="createdDate"
                                show-overflow-tooltip
                                label="创建日期"
                        >
                        </el-table-column>
                        <el-table-column
                                fixed="right"
                                label="操作"
                                width="100">
                            <template slot-scope="scope">
                                <el-button @click="goDetail(scope.row)" type="text" size="small">查看</el-button>
                                <!-- <el-button type="text" size="small" @click="goAttestation(scope.row)">审核</el-button> -->
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </div>
            <div class="pagination" style="float: right;margin-top: 20px">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 40,50,100,200]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total=total>
                </el-pagination>
            </div>
        </div>
        <!--  转让车辆 弹窗-->
        <el-dialog
                title="转让车辆确认"
                :visible.sync="dialogVisible"
                :before-close="handleClose">
            <div class="">
                <el-form ref="form" :model="formCars" label-width="120px">
                    <el-form-item label="选中车辆:">
                        <el-tag
                                v-for="tag in multipleSelection"
                                :key="tag.id"
                                closable
                                :disable-transitions="false"
                                @close="closeTag(tag)"
                                type="success">
                            {{tag.plateNumber}}
                        </el-tag>
                    </el-form-item>
                    <el-form-item label="转让理由:">
                        <el-input type="textarea" v-model="formCars.content"></el-input>
                    </el-form-item>
                    <el-form-item label="转让凭证:">
                        <el-upload
                                action
                                :limit="6"
                                multiple
                                :on-exceed="handleExceed"
                                list-type="picture-card"
                                :http-request="ossUpload"
                                :on-remove='handleRemove'
                                ref="uploader"
                        >
                            <div class="upload-box">
                                <div class="icon-XZ"></div>
                                <div class="icon-word">支持jpg、jpeg、png 格式不超过10M</div>
                            </div>
                        </el-upload>
                    </el-form-item>
                    <el-form-item label="车队长名称:" required>
                        <el-input style="width: 300px" v-model="formCars.name"></el-input>
                    </el-form-item>
                    <el-form-item label="车队长手机号:" required>
                        <el-input style="width: 300px" v-model="formCars.mobile"></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button @click="handleClose">取 消</el-button>
    <el-button type="primary" @click="suretransfer">确 定</el-button>
  </span>
        </el-dialog>

    </div>
</template>

<script>
    import {client, OSS_REGION} from '@/utils/alioss'

    export default {
        name: 'CarsList',
        data() {
            return {
                Aliyun: {},
                formCars: {
                    content: '',
                    mobile: '',
                    name: '',
                },
                dialogVisible: false,
                multipleSelection: [],
                isDisabled: false,
                isRadioDisabled: false,
                formInline: {
                    plateNumber: '',
                    isFree: '',
                    authStatus: '',
                    nearExpirationStatus: '',
                    user: '',
                    ownerMobile: '',
                    date:[],
                },
                tableData: [],
                currentPage: 1,
                pageSize: 20,
                total: 1,
                boss: '',
                resource: '',
                startTime: '',
                endTime: '',
                isFree: '',
                plateNumber: '',
                existCarBoss: '',
                images: [],
            }
        },
        methods: {
            addCar() {
                this.$router.push('/carsList/addCar')
            },
            handleClose() {
                this.dialogVisible = false;
                this.getCarData();
                this.formCars = {
                    content: '',
                    mobile: '',
                    name: '',
                };
                this.images=[];
                this.multipleSelection = [];
                this.$refs.uploader.clearFiles()
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            closeTag(tag) {
                if(this.multipleSelection.length===1){
                    this.$message.warning('至少有一辆车')
                }else {
                    this.multipleSelection.splice(this.multipleSelection.indexOf(tag), 1);
                }
            },
            transferCars() {
                let cars = this.multipleSelection;
                if (cars.length < 1) {
                    this.$message.warning('至少选择一辆车')
                } else {
                    this.dialogVisible = true;
                }
            },
            /** 拉取车辆列表 **/
            getCarData() {
                this.$http.get('/admin-center-server/car/list_search', {
                    params: {
                        pageNumber: this.currentPage,
                        pageSize: this.pageSize,
                        boss: this.boss,
                        mobile: this.formInline.ownerMobile,
                        startTime: this.startTime,
                        endTime: this.endTime,
                        isFree: this.isFree,
                        authStatus: this.formInline.authStatus,
                        nearExpirationStatus: this.formInline.nearExpirationStatus,
                        plateNumber: this.plateNumber,
                        existCarBoss: this.resource,
                        shippingCertWarnFlag: this.formInline.shippingCertWarnFlag
                    }
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.tableData = data.data.list;
                        this.total = Number(data.data.total)
                    }
                })
            },
            /** 搜索提交 车牌号 车队长名字 **/
            onSubmit() {
                this.plateNumber = this.formInline.plateNumber;
                // this.boss = this.formInline.boss;
                this.currentPage = 1;
                this.getCarData()
            },
            /** 车辆使用状态 **/
            isStopStatus(row) {
                if (row.currentCarState === '0') {
                    return '空闲'
                } else if (row.currentCarState === '1') {
                    return '使用中'
                } else if (row.currentCarState === '2') {
                    return '停用'
                } else if (row.currentCarState === '3') {
                    return '冻结'
                }
            },
            /** 收款方 **/
            payeeAgentStatus(row) {
                if (row.payeeAgent === '0') {
                    return '司机'
                } else if (row.payeeAgent === '1') {
                    return '车主'
                }
            },

            /** 分页方法 **/
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1;
                this.getCarData()
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getCarData()
            },

            /** 根据车辆使用状态搜索 **/
            isFreeChange(value) {
                this.isFree = value
            },
            /** 根据时间态搜索 **/
            selectTime() {
                if(this.formInline.date!==null){
                    let startTime = this.formInline.date[0];
                    let endTime = this.formInline.date[1];
                    this.startTime = startTime;
                    this.endTime = endTime
                }else {
                    this.formInline.date=[];
                }

            },
            /** 清空搜索选项 **/
            resetSubmit() {
                this.isDisabled = false;
                this.isRadioDisabled = false;
                this.formInline = {
                    plateNumber: '',
                    isFree: '',
                    authStatus: '',
                    nearExpirationStatus: '',
                    user: '',
                    ownerMobile:'',
                };
                this.resource = '';
                this.boss = '';
                this.currentPage = 1;
                this.pageSize = 10;
                this.total = 1;
                this.boss = '';
                this.startTime = '';
                this.endTime = '';
                this.isFree = '';
                this.plateNumber = '';
                this.existCarBoss = '';
                this.getCarData()
            },
            /**  查看车辆详情 **/
            goDetail(row) {
                this.$router.push({
                    path: '/carsList/carDetail',
                    query: {
                        carId: row.id,
                    }
                })
            },
            /**  车辆审核 **/
            goAttestation(row) {
                this.$router.push({
                    path: '/carlist/carlistExamine',
                    query: {
                        carId: row.id,
                    }
                })
            },
            /** 驳回原因管理 **/
            rejectReason() {
                this.$router.push('/carsList/rejectReason')
            },
            getAliyunData() {
                const ossApiUrl = "/base-center-server/sts/oss_signature"; // 获取oss签名的地址
                // 获取oss签名
                this.$http.get(ossApiUrl).then(res => {
                    if (res.data.code == "200") {
                        let data = res.data;
                        this.Aliyun = data.data;
                        this.Aliyun.region = OSS_REGION;
                    }
                });
            },
            /** 上传数量限制 **/
            handleExceed() {
                this.$message.warning('最多上传6张图片')
            },
            /** 单个上传 重组上传集合 **/
            ossUpload(param) {
                let file = param.file; // 文件的
                let uid = file.uid;
                const tmpcnt = file.name.lastIndexOf('.');
                const exname = file.name.substring(tmpcnt + 1);
                const fileName = '/' + this.Aliyun.bucket + '/' + this.Aliyun.dir + this.$md5(file.name) + '.' + exname;
                client(this.Aliyun).put(fileName, file).then(res => {
                    if (res.res.status === 200) {
                        let imgUrl = res.res.requestUrls[0];
                        let obj = {imgUrl: imgUrl, uid: uid};
                        this.images.push(obj);
                        this.$message.success('上传成功')
                    } else {
                        this.$message.error(res.res.message)
                    }
                })
            },
            /** 上传成功后 删除 重新上传集合 **/
            handleRemove(file, fileList) {
                let uid = file.uid;
                let str = [];
                this.images.map((item, index) => {
                    if (item.uid !== uid) {
                        str.push(item)
                    }
                });
                this.images = str;
            },
            /** 确认转让 **/
            suretransfer() {
                let idStr = this.multipleSelection.map((item, index) => {
                    return item.id
                });
                let imgStr = this.images.map((item, index) => {
                    return item.imgUrl
                });
                let carIds = idStr.toString();
                let images = imgStr.toString();
                let mobile = this.formCars.mobile;
                let name = this.formCars.name;
                let content = this.formCars.content;
                this.$http.post('/admin-center-server/app/mycar/manager/applicationCar?carIds=' + carIds + '&images=' + images + '&mobile=' + mobile + '&name=' + name + '&content=' + content).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.getCarData();
                        this.$message.success('转让成功');
                        this.dialogVisible = false;
                        this.formCars = {
                            content: '',
                            mobile: '',
                            name: '',
                        };
                        this.images=[];
                        this.multipleSelection = [];
                        this.handleClose()
                    } else {
                        this.$message.warning(data.message)
                    }
                })
            },
            getGpsStatusClass(status) {
                switch (status) {
                    case '-1':
                        return 'gps-status-unbind'
                    case '0':
                        return 'gps-status-offline'
                    case '1':
                        return 'gps-status-online'
                }
            },
            getGpsStatusText(status) {
                switch (status) {
                    case '-1':
                        return '未绑定'
                    case '0':
                        return '离线'
                    case '1':
                        return '在线'
                }
            },
            jiashiStatus(row){
                switch (row.authStatus) {
                    case '1': return '认证成功'
                    case '2': return '认证驳回'
                    case '3': return '认证中'
                    case '4': return '认证过期'
                }
            },
            tableRowClassName ({ row, rowIndex }) {
                if (row.authStatus === '3'||row.authStatus === '0') {
                    return 'inAudit-row '; //失败 和未
                } else if (row.authStatus === '2') {
                    return 'success-row'; //成功
                } else if (row.authStatus === '1') {
                    return 'warning-row'; //认证中
                }
                return '';
            }
        },
        activated() {
            this.getCarData();
            this.getAliyunData()
        },
        watch: {
            boss(val) {
                if (val === '') {
                    this.isDisabled = false;
                    this.isRadioDisabled = false;
                    this.resource = ''
                } else if (val !== '') {
                    this.isRadioDisabled = true;
                }
            },
        }

    }
</script>

<style>
    .el-range-editor.el-input__inner {
        width: 400px;
    }
    .clear-icon{
        display: none;
    }
</style>
<style src="@/assets/scss/gpsStatus.scss" lang="scss" scoped></style>
<style rel="stylesheet/scss" lang="scss" scoped>
    .carsList {
        .select-box {
            .el-form-item {
                // margin-bottom: 0;
            }

            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
                display: flex;
                justify-content: space-between;

                .button {
                    margin-right: 20px;
                }
            }

            .select-info {
                padding-top: 30px;
                padding-bottom: 30px;
            }
        }

        .upload-box {
            width: 100%;
            height: 100%;
            position: relative;

            .icon-XZ {
                width: 92px;
                height: 92px;
                margin: 0 auto;
                background: url("./images/xiazai.png") no-repeat;
                background-size: 100% 100%;
            }

            .icon-word {
                width: 100%;
                height: 20px;
                line-height: 20px;
                font-size: 10px;
                position: absolute;
                bottom: 25px;
                left: 0px;
                color: #cccccc;
            }
        }

        .list-box {

            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;
            overflow: hidden;

            .list-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                div {
                    height: 38px;
                    line-height: 38px;
                }
            }

            .list-main {
                width: 100%;
                border: 1px solid #cccccc;
                margin-top: 10px;
            }
        }
    }
</style>
