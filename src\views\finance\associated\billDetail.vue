/**
对账单管理详情 小火车 2020/6/15
**/
<template>
    <div class="app-container carsList">
        <div class="select-box">
            <div class="top-title">
                <div>筛选查询</div>
                <div class="button">
                    <el-button
                            class="left"
                            icon="el-icon-search"
                            size="mini"
                            type="primary"
                            @click="()=>{this.getDataList()}"
                    >查询
                    </el-button>
                    <el-button
                            class="left"
                            icon="el-icon-refresh-right"
                            size="mini"
                            type="success"
                            @click="clearForm"
                    >清空筛选
                    </el-button>
                </div>
            </div>
            <div class="select-info">
                <el-form
                        class="demo-form-inline"
                        :inline="true"
                        :model="formInline"
                        label-width="90px"
                        size="mini"
                >
                    <el-form-item label="订单状态:">
                        <el-select v-model="formInline.status" placeholder="全部" style="width: 178px">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="支付成功" value="1"></el-option>
                            <el-option label="退款成功" value="7"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="创建日期:">
                        <el-date-picker
                                :clearable="false"
                                v-model="creatDate"
                                @blur="selectCreatDate"
                                format="yyyy 年 MM 月 dd 日"
                                value-format="yyyy-MM-dd"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                        ></el-date-picker>
                    </el-form-item>
                </el-form>
                <el-form
                        :inline="true"
                        :model="formInline"
                        class="demo-form-inline"
                        label-width="90px"
                        size="mini"
                >
                    <el-form-item label="司机名称:">
                        <el-input v-model="formInline.driverName" :οnkeyup="formInline.driverName=formInline.driverName.replace(/\s/g, '')" placeholder="请输入司机名称"></el-input>
                    </el-form-item>
                    <el-form-item label="司机电话:">
                        <el-input v-model="formInline.driverPhone" :οnkeyup="formInline.driverPhone=formInline.driverPhone.replace(/\s/g, '')" placeholder="请输入司机电话"></el-input>
                    </el-form-item>
                    <el-form-item label="操作日期:">
                        <el-date-picker
                                :clearable="false"
                                v-model="operateDate"
                                @blur="selectOperateDate"
                                format="yyyy 年 MM 月 dd 日"
                                value-format="yyyy-MM-dd"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                        ></el-date-picker>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div class="list-box">
            <div class="list-title">
                <div>明细列表</div>
                <div style="font-size: 14px;">
                    <div>
                        <div>消费平台： <span style="color: red">{{platformName}} &nbsp;</span> 时间：<span
                                style="color: red">{{startTime}}</span> 至 <span style="color: red">{{endTime}}&nbsp;</span>
                            总计:订单合计：<span style="color: red">{{totalAumont}}</span> 元
                        </div>
                    </div>
                </div>
            </div>
            <div class="list-main">
                <el-table
                        :data="tableData"
                        border
                        style="width: 100%">
                    <el-table-column
                            type="index"
                            show-overflow-tooltip
                            label="序号"
                            width="50"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="driverName"
                            show-overflow-tooltip
                            label="司机姓名"
                            width="120"
                    >
                    </el-table-column>
                    <el-table-column
                            prop="driverPhone"
                            show-overflow-tooltip
                            label="司机手机号"
                            width="120">
                    </el-table-column>
                    <el-table-column
                            prop="platformName"
                            show-overflow-tooltip
                            width="120"
                            label="消费平台">
                    </el-table-column>
                    <el-table-column
                            prop="gasName"
                            show-overflow-tooltip
                            width="120"
                            label="油站名称">
                    </el-table-column>
                    <el-table-column
                            prop="gasPayAmount"
                            show-overflow-tooltip
                            width="120"
                            label="订单金额(元)">
                    </el-table-column>
                    <el-table-column
                            prop="payTypeText"
                            show-overflow-tooltip
                            width="120"
                            label="订单状态">
                    </el-table-column>
                    <el-table-column
                            prop="outerOrderId"
                            show-overflow-tooltip
                            width="180"
                            label="平台油气流水号">
                    </el-table-column>
                    <el-table-column
                            prop="orderId"
                            show-overflow-tooltip
                            width="180"
                            label="油气订单号">
                    </el-table-column>
                    <el-table-column
                            prop="operateTime"
                            show-overflow-tooltip
                            label="操作日期">
                    </el-table-column>
                </el-table>
            </div>
            <div class="pagination" style="float: right;margin-top: 20px">
                <el-pagination
                        background
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 40,50,100]"
                        :page-size="pageSize"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: "CarsList",
        data() {
            return {
                platformName:'',
                startTime:'',
                endTime:'',
                totalAumont:'',
                total: 0,
                currentPage: 1,
                pageSize: 20,
                creatDate: [],
                operateDate: [],
                formInline: {
                    status: '',
                    driverName: '',
                    driverPhone: '',
                    creatStartTime: '',
                    creatEndTime: '',
                    operateStartTime: '',
                    operateEndTime: '',
                },
                tableData: [],
            };
        },
        methods: {
            /** 清空搜索 **/
            clearForm() {
                this.formInline = {
                    status: '',
                    driverName: '',
                    driverPhone: '',
                    creatStartTime: '',
                    creatEndTime: '',
                    operateStartTime: '',
                    operateEndTime: '',
                };
                this.operateDate = [];
                this.creatDate = [];
                this.getDataList();
            },
            /** 选择创建日期 **/
            selectCreatDate(){
                let startTime = this.creatDate[0];
                let endTime = this.creatDate[1];
                this.formInline.creatStartTime = startTime;
                this.formInline.creatEndTime = endTime;
            },
            /** 选择操作日期 **/
            selectOperateDate(){
                let startTime = this.operateDate[0];
                let endTime = this.operateDate[1];
                this.formInline.operateStartTime = startTime;
                this.formInline.operateEndTime = endTime;
            },
            /** 分页 **/
            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1;
                this.getDataList();
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getDataList();
            },

            getDataList() {
                let id = this.$route.query.id;
                let form = {
                    oilStatementId: id,
                    pageNumber: this.currentPage,
                    pageSize: this.pageSize,

                    status: this.formInline.status,
                    driverName: this.formInline.driverName,
                    driverPhone: this.formInline.driverPhone,
                    creatStartTime: this.formInline.creatStartTime,
                    creatEndTime: this.formInline.creatEndTime,
                    operateStartTime: this.formInline.operateStartTime,
                    operateEndTime: this.formInline.operateEndTime,
                    order: 'desc',
                };
                this.$http.post('/admin-center-server/oilStatement/oilStatementDetail', form).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.tableData = data.data.listOilStatementVo.list;
                        this.total = Number(data.data.listOilStatementVo.total);

                        this.platformName = data.data.platformName;
                        this.startTime = data.data.startTime;
                        this.endTime = data.data.endTime;
                        this.totalAumont = data.data.totalAumont;
                    } else {
                        this.$message.warning(data.message)
                    }
                })
            },
        },
        activated() {
            this.getDataList()
        }
    };
</script>

<style>
    .el-range-editor.el-input__inner {
        width: 400px;
    }
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
    /*.el-form-item {*/
    /*    margin-bottom: 0;*/
    /*}*/

    .carsList {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
                display: flex;
                justify-content: space-between;

                .button {
                    margin-right: 20px;
                }
            }

            .select-info {
                padding-top: 30px;
                padding-bottom: 30px;
            }
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;
            overflow: hidden;

            .list-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                div {
                    height: 38px;
                    line-height: 38px;
                }
            }

            .list-main {
                width: 100%;
                border: 1px solid #cccccc;
                margin-top: 10px;
            }
        }
    }
</style>
