<template>
  <div class="app-container addCar">
    <div class="tip">
      <div>添加车辆</div>
      <div>
        <em style="color: red">*</em>为必填项
      </div>
    </div>
    <div class="inner-box">
      <el-form :inline="true" label-width="160px" :model="ruleForm">
        <el-form-item label="车牌号:" required>
          <el-input
            placeholder="请输入车牌号"
            v-model="ruleForm.vehiclenumber"
            class="input-with-select"
            style="width: 220px"
          ></el-input>
        </el-form-item>

        <el-form-item label="车型:" required>
          <el-select v-model="ruleForm.vehicleClassification" placeholder="请选择车型">
            <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.name"></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <el-form :inline="true" label-width="160px" :model="ruleForm" ref="ruleForm">
        <el-form-item label="类型代码:" required>
          <el-input
            v-model="ruleForm.licenseplatetypecode"
            style="width: 220px"
            placeholder="请输入类型代码"
          ></el-input>
        </el-form-item>
        <el-form-item label="总重量:" required>
          <el-input
            v-model="ruleForm.sumCapacityTonnage"
            style="width: 220px"
            placeholder="请输入载重量(0-100吨)"
            oninput="value=value.replace(/[^\d]/g,'')"
          ></el-input>
        </el-form-item>
      </el-form>

      <el-form :inline="true" label-width="160px" :model="ruleForm" ref="ruleForm">
        <el-form-item label="载重:" required>
          <el-input
            v-model="ruleForm.capacityTonnage"
            style="width: 220px"
            placeholder="请输入载重量(0-100吨)"
            oninput="value=value.replace(/[^\d]/g,'')"
          ></el-input>
        </el-form-item>
        <el-form-item label="发动机编号:" required>
          <el-input
            v-model="ruleForm.vehicleEngineNumber"
            style="width: 220px"
            placeholder="请输入发动机编号"
          ></el-input>
        </el-form-item>
      </el-form>

      <el-form :inline="true" label-width="160px" :model="ruleForm" ref="ruleForm">
        <el-form-item label="车辆品牌:">
          <el-input v-model="ruleForm.vehicleBrandModel" style="width: 220px" placeholder="请输入车辆品牌"></el-input>
        </el-form-item>
        <el-form-item label="运输证编号:" required>
          <el-input
            v-model="ruleForm.transportBookNumber"
            style="width: 220px"
            placeholder="请输入运输证编号"
          ></el-input>
        </el-form-item>
      </el-form>

      <el-form :inline="true" label-width="160px" :model="ruleForm">
        <el-form-item label="最大牵引力重量:" required>
          <el-input
            v-model="ruleForm.maximumTractionw"
            style="width: 220px"
            placeholder="请输入最大牵引重量(0-100吨)"
            oninput="value=value.replace(/[^\d]/g,'')"
          ></el-input>
        </el-form-item>
        <el-form-item label="整车宽度:" required>
          <el-input
            v-model="ruleForm.vehicleWidth"
            style="width: 220px"
            placeholder="请输入整车宽度"
            maxlength="10"
            oninput="value=value.replace(/[^\d]/g,'')"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-form :inline="true" label-width="160px" :model="ruleForm" ref="ruleForm">
        <el-form-item label="整车长度:" required>
          <el-input
            v-model="ruleForm.vehicleLength"
            style="width: 220px"
            placeholder="请输入整车长度"
            oninput="value=value.replace(/[^\d]/g,'')"
            maxlength="10"
          ></el-input>
        </el-form-item>
        <el-form-item label="营业范围:" required>
          <el-input v-model="ruleForm.businessScope" style="width: 220px" placeholder="请输入营业范围"></el-input>
        </el-form-item>
      </el-form>

      <el-form :inline="true" label-width="160px" :model="ruleForm">
        <el-form-item label="整车高度:" required>
          <el-input
            v-model="ruleForm.vehicleHeight"
            style="width: 220px"
            oninput="value=value.replace(/[^\d]/g,'')"
            maxlength="10"
            placeholder="请输入整车高度"
          ></el-input>
        </el-form-item>
        <el-form-item label="认证单位:" required>
          <el-input v-model="ruleForm.certificationUnit" style="width: 220px" placeholder="请输入认证单位"></el-input>
        </el-form-item>
      </el-form>
      <el-form :inline="true" label-width="160px" :model="ruleForm">
        <el-form-item label="下发时间:">
          <el-date-picker
            v-model="ruleForm.licenseInitialDate"
            type="date"
            placeholder="选择下发时间"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="截止日期:">
          <el-date-picker
            v-model="ruleForm.periodEndDate"
            type="date"
            placeholder="选择截止日期"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <el-form :inline="true" label-width="160px" :model="ruleForm">
        <el-form-item label="起始日期:">
          <el-date-picker
            v-model="ruleForm.periodStartDate"
            type="date"
            placeholder="选择起始日期"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="营运状态码:" required>
          <el-input
            v-model="ruleForm.businessStateCode"
            style="width: 220px"
            placeholder="请输入营运状态码"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-form :inline="true" label-width="160px" :model="ruleForm.businessState">
        <el-form-item label="营运状态:" required>
          <el-input
            v-model="ruleForm.businessState"
            style="width: 220px"
            placeholder="请输入营运状态"
            maxlength="10"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-form :inline="true" label-width="160px" :model="ruleForm">
        <el-form-item label="车辆行驶证照片:">
          <el-upload
            :v-model="ruleForm.photo"
            action="https://jsonplaceholder.typicode.com/posts/"
            :file-list="fileList"
            :limit="1"
            list-type="picture-card"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :http-request="ossUpload"
            :class="{hide:hideUpload}"
          >
            <div class="upload-box">
              <div class="icon-XZ"></div>
              <div class="icon-word">支持jpg、jpeg、png 格式不超过10M</div>
            </div>
          </el-upload>
          <el-dialog :visible.sync="dialogVisible" size="tiny">
            <img width="100%" :src="dialogImageUrl" alt />
          </el-dialog>
        </el-form-item>
      </el-form>
      <el-form style="text-align: center">
        <!-- 新增 -->
        <el-button class="commitInfo" @click="commitInfo()" v-if="flag==0">提交</el-button>
        <!-- 更新（编辑） -->
        <el-button class="commitInfo" @click="editInfo()" v-if="flag==1">提交</el-button>
      </el-form>
    </div>
  </div>
</template>
<script>
import { client, OSS_REGION } from "@/utils/alioss";
export default {
  data() {
    return {
      Aliyun: {},
      hideUpload: false,
      flag: "", // 0 新增，1 编辑
      people: "司机手机号:",
      dialogImageUrl: "",
      fileList: [], //图片回显
      dialogVisible: false,
      ruleForm: {
        vehiclenumber: "", //车牌号
        transportBookNumber: "", //运输证编号
        vehicleClassification: "", //车型
        licenseplatetypecode: "", //类型代码
        maximumTractionw: "", // 最大牵引力重量
        vehicleWidth: "", //整车宽度
        vehicleLength: "", //整车长度
        businessScope: "", //营业范围
        vehicleHeight: "", //整车高度
        certificationUnit: "", //认证单位
        businessStateCode: "", //营运状态码
        businessState: "", //营运状态
        photo: "", //车辆行驶证照片
        sumCapacityTonnage: "", //总重量
        capacityTonnage: "", //载重
        licenseInitialDate: "", //下发时间
        periodEndDate: "", //截止日期
        periodStartDate: "", //起始日期
        vehicleEngineNumber: "", // 发动机编号
        vehicleBrandModel: "" //车辆品牌
      },
      options: []
    };
  },
  activated() {
    this.getCarType(); //获取车型
    console.log(this.$route.query.flag);

    this.flag = this.$route.query.flag;
    if (this.flag == 0) {
      return;
    } else if (this.flag == 1) {
      console.log("编辑");
      this.getEditInfo(); //编辑回显
    }
  },
  methods: {

    getAliyunData() {
      const ossApiUrl = "/base-center-server/sts/oss_signature"; // 获取oss签名的地址
      // 获取oss签名
      this.$http.get(ossApiUrl).then(res => {
        if (res.data.code == "200") {
          let data = res.data;
          this.Aliyun = data.data;
          this.Aliyun.region = OSS_REGION;
          // return data
        }
      });
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList);
      this.hideUpload = false;
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          alert("submit!");
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },

    /* 获取车型 */
    getCarType() {
      this.$http
        .get("/admin-center-server/carmodel/list", {
          params: {
            pageNumber: 1,
            pageSize: 1000
          }
        })
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            console.log(data);
            this.options = data.data.list;
          }
        });
    },
    /* oss上传 */
    /* 上传车辆行驶证照片*/
      /* 上传图片 */
    ossUpload(param) {
      let file = param.file; // 文件的
      const tmpcnt = file.name.lastIndexOf(".");
      const exname = file.name.substring(tmpcnt + 1);
      const fileName =
        "/" +
        this.Aliyun.bucket +
        "/" +
        this.Aliyun.dir +
        this.$md5(name) +
        "." +
        exname;
      client(this.Aliyun)
        .put(fileName, file)
        .then(res => {
          if (res.res.status === 200) {
             // 上传
              let imgUrl = res.res.requestUrls[0];
              this.ruleForm.photo = imgUrl;
              console.log(this.ruleForm.photo, "----图片的地址");
              this.$message({
                type: "success",
                message: "上传成功"
              });
              this.hideUpload = true;

          } else {
            this.$message.error(res.res.message);
          }
        });
    },

    /* 提交车辆信息 */
    commitInfo() {
      if (this.ruleForm.vehiclenumber == "") {
        this.$message.warning("请输入车牌号");
        return;
      }
      if (this.ruleForm.licenseplatetypecode == "") {
        this.$message.warning("类型代码");
        return;
      }

      if (this.ruleForm.sumCapacityTonnage == "") {
        this.$message.warning("请输入总重量");
        return;
      }
      if (this.ruleForm.capacityTonnage == "") {
        this.$message.warning("请输入载重");
        return;
      }
      if (this.ruleForm.vehicleEngineNumber == "") {
        this.$message.warning("请输入发动机编号");
        return;
      }
      if (this.ruleForm.transportBookNumber == "") {
        this.$message.warning("请输入运输证编号");
        return;
      }
      if (this.ruleForm.vehicleWidth == "") {
        this.$message.warning("请输入整车宽度");
        return;
      }

      if (this.ruleForm.vehicleLength == "") {
        this.$message.warning("请输入整车长度");
        return;
      }
      if (this.ruleForm.businessScope == "") {
        this.$message.warning("请输入营业范围");
        return;
      }
      if (this.ruleForm.vehicleHeight == "") {
        this.$message.warning("请输入整车高度");
        return;
      }
      if (this.ruleForm.certificationUnit == "") {
        this.$message.warning("请输入认证单位");
        return;
      }
      if (this.ruleForm.businessStateCode == "") {
        this.$message.warning("请输入营运状态码");
        return;
      }
      if (this.ruleForm.businessState == "") {
        this.$message.warning("请输入营运状态");
        return;
      }
      if (this.ruleForm.vehicleClassification == "") {
        this.$message.warning("请选择车型");
        return;
      }

      var postData = {
        drivinglicenceinfo: {
          maximumTractionw: Number(this.ruleForm.maximumTractionw), //最大牵引量
          photo: this.ruleForm.photo, //照片地址
          vehicleBrandModel: this.ruleForm.vehicleBrandModel,
          vehicleEngineNumber: this.ruleForm.vehicleEngineNumber
        },
        transportbookinfo: {
          vehicleClassification: this.ruleForm.vehicleClassification, //车型 现在传汉子
          businessScope: this.ruleForm.businessScope,
          businessState: this.ruleForm.businessState,
          businessStateCode: this.ruleForm.businessStateCode,
          certificationUnit: this.ruleForm.certificationUnit,
          licenseInitialDate: this.ruleForm.licenseInitialDate,
          periodEndDate: this.ruleForm.periodEndDate,
          periodStartDate: this.ruleForm.periodEndDate,
          transportBookNumber: this.ruleForm.transportBookNumber,
          vehicleHeight: Number(this.ruleForm.vehicleHeight),
          vehicleLength: Number(this.ruleForm.vehicleLength),
          licenseInitialDate: this.ruleForm.licenseInitialDate,
          periodEndDate: this.ruleForm.periodEndDate,
          periodStartDate: this.ruleForm.periodStartDate, //起始日期
          vehicleWidth: Number(this.ruleForm.vehicleWidth)
        },
        licenseplatetypecode: this.ruleForm.licenseplatetypecode,
        capacityTonnage: this.ruleForm.capacityTonnage,
        sumCapacityTonnage: this.ruleForm.sumCapacityTonnage,
        vehiclenumber: this.ruleForm.vehiclenumber
      };

      this.$http
        .post("/admin-center-server/admin/db_car/save", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$router.go(-1);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 编辑的回显接口 */
    getEditInfo() {
      let id = this.$route.query.id;
      this.$http
        .get("/admin-center-server/admin/db_car/edit?id=" + id)
        .then(res => {
          let data = res.data;
          console.log(data.data.vehiclenumber, "-----vehiclenumber");
          if (data.code === "200") {
            this.fileList = [{
              url: data.data.drivinglicenceinfo.photo
            }];
            if (this.fileList.length >= 1) {
              this.hideUpload = true;
            }
            this.ruleForm = {
              vehiclenumber: data.data.vehiclenumber, //车牌号
              transportBookNumber:
                data.data.transportbookinfo.transportBookNumber, //运输证编号
              vehicleClassification:
                data.data.transportbookinfo.vehicleClassification, //车型
              licenseplatetypecode: data.data.licenseplatetypecode, //类型代码
              maximumTractionw: data.data.drivinglicenceinfo.maximumTractionw, // 最大牵引力重量
              vehicleWidth: data.data.transportbookinfo.vehicleWidth, //整车宽度
              vehicleLength: data.data.transportbookinfo.vehicleLength, //整车长度
              businessScope: data.data.transportbookinfo.businessScope, //营业范围
              vehicleHeight: data.data.transportbookinfo.vehicleHeight, //整车高度
              certificationUnit: data.data.transportbookinfo.certificationUnit, //认证单位
              businessStateCode: data.data.transportbookinfo.businessStateCode, //营运状态码
              businessState: data.data.transportbookinfo.businessState, //营运状态
              photo: data.data.drivinglicenceinfo.photo, //车辆行驶证照片
              sumCapacityTonnage: data.data.sumCapacityTonnage, //总重量
              capacityTonnage: data.data.capacityTonnage, //载重
              licenseInitialDate:
                data.data.transportbookinfo.licenseInitialDate,
              periodEndDate: data.data.transportbookinfo.periodStartDate,
              periodStartDate: data.data.transportbookinfo.periodStartDate, //起始日期
              vehicleBrandModel: data.data.drivinglicenceinfo.vehicleBrandModel, //车辆品牌
              vehicleEngineNumber:
                data.data.drivinglicenceinfo.vehicleEngineNumber
              // photo:data.data.drivinglicenceinfo.photo
            };
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 编辑（更新） */
    editInfo() {
      console.log(this.ruleForm);
      let id = this.$route.query.id;
      if (this.ruleForm.vehiclenumber == "") {
        this.$message.warning("请输入车牌号");
        return;
      }
      if (this.ruleForm.licenseplatetypecode == "") {
        this.$message.warning("类型代码");
        return;
      }

      if (this.ruleForm.sumCapacityTonnage == "") {
        this.$message.warning("请输入总重量");
        return;
      }
      if (this.ruleForm.capacityTonnage == "") {
        this.$message.warning("请输入载重");
        return;
      }
      if (this.ruleForm.vehicleEngineNumber == "") {
        this.$message.warning("请输入发动机编号");
        return;
      }
      if (this.ruleForm.transportBookNumber == "") {
        this.$message.warning("请输入运输证编号");
        return;
      }
      if (this.ruleForm.vehicleWidth == "") {
        this.$message.warning("请输入整车宽度");
        return;
      }

      if (this.ruleForm.vehicleLength == "") {
        this.$message.warning("请输入整车长度");
        return;
      }
      if (this.ruleForm.businessScope == "") {
        this.$message.warning("请输入营业范围");
        return;
      }
      if (this.ruleForm.vehicleHeight == "") {
        this.$message.warning("请输入整车高度");
        return;
      }
      if (this.ruleForm.certificationUnit == "") {
        this.$message.warning("请输入认证单位");
        return;
      }
      if (this.ruleForm.businessState == "") {
        this.$message.warning("请输入营运状态码");
        return;
      }
      if (this.ruleForm.businessState == "") {
        this.$message.warning("请输入营运状态");
        return;
      }
      if (this.ruleForm.vehicleClassification == "") {
        this.$message.warning("请选择车型");
        return;
      }
      var postData = {
        id: id,
        drivinglicenceinfo: {
          maximumTractionw: Number(this.ruleForm.maximumTractionw), //最大牵引量
          photo: this.ruleForm.photo, //照片地址
          vehicleBrandModel: this.ruleForm.vehicleBrandModel,
          vehicleEngineNumber: this.ruleForm.vehicleEngineNumber
        },
        vehiclenumber: this.ruleForm.vehiclenumber,
        licenseplatetypecode: this.ruleForm.licenseplatetypecode,
        capacityTonnage: this.ruleForm.capacityTonnage,
        sumCapacityTonnage: this.ruleForm.sumCapacityTonnage,

        transportbookinfo: {
          vehicleClassification: this.ruleForm.vehicleClassification, //车型
          businessScope: this.ruleForm.businessScope,
          businessState: this.ruleForm.businessState,
          businessStateCode: this.ruleForm.businessStateCode,
          certificationUnit: this.ruleForm.certificationUnit,
          licenseInitialDate: this.ruleForm.licenseInitialDate,
          periodEndDate: this.ruleForm.periodEndDate,
          periodStartDate: this.ruleForm.periodEndDate,
          transportBookNumber: this.ruleForm.transportBookNumber,
          vehicleHeight: Number(this.ruleForm.vehicleHeight),
          vehicleLength: Number(this.ruleForm.vehicleLength),
          vehicleWidth: Number(this.ruleForm.vehicleWidth)
        }
      };
      console.log(postData);
      this.$http
        .post("/admin-center-server/admin/db_car/update", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$router.go(-1);
          } else {
            this.$message.warning(data.message);
          }
        });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss">
.hide .el-upload--picture-card {
  display: none;
}
.addCar {
  background-color: #ffffff;
  .tip {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 40px 40px 0;
    font-size: 12px;
    em {
      margin-right: 5px;
    }
  }

  .inner-box {
    margin-left: 10%;
    width: 70%;
    .upload-box {
      width: 100%;
      height: 100%;
      position: relative;
      .icon-XZ {
        width: 92px;
        height: 92px;
        margin: 0 auto;
        background: url("../../../assets/xiazai.png") no-repeat;
        background-size: 100% 100%;
      }
      .icon-word {
        width: 100%;
        height: 20px;
        line-height: 20px;
        font-size: 10px;
        position: absolute;
        bottom: 25px;
        left: 0px;
        color: #cccccc;
      }
    }
  }
}
</style>
