import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'
import { grayFn } from './index.js'
let base_url = process.env.VUE_APP_BASE_API
// if (process.env.NODE_ENV === 'production') {

//     base_url = 'https://dev.hongfeida.cn'
//     // base_url = 'http://127.0.0.1:7015'

// } else if (process.env.NODE_ENV === 'development') {
//     base_url = '/api'
// }
// create an axios instance
const serviceHeader = axios.create({
    baseURL: base_url, // api 的 base_url
    timeout: 1000*30 // request timeout
});

// request interceptor
serviceHeader.interceptors.request.use(
    config => {
        // 灰度标识
        if(grayFn()){
            config.headers['Env'] = 'gray'
        }
        config.headers['AuthMgr'] = getToken();
        return config
    },
    error => {
        // Do something with request error
        console.log(error) // for debug
        Promise.reject(error)
    }
)

// response interceptor
serviceHeader.interceptors.response.use(
    response => response,
    error => {
        console.log('err' + error); // for debug
        Message({
            message: '服务器错误',
            type: 'error',
            duration: 5 * 1000
        })
        return Promise.reject(error)
    }
)

export default serviceHeader
