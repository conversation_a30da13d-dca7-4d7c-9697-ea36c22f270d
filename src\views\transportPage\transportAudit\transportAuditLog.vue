<template>
  <div class="list-box">
    <el-table
      :data="listData"
      style="width: 100%"
      ref="table"
      :cell-style="{ 'text-align': 'center', border: '0.5px solid #EAF0FB' }"
      :header-cell-style="{
        'text-align': 'center',
        border: '0.3px solid #EAF0FB',
        'background-color': '#F5F6F9',
        height: '60px',
      }"
    >
      <el-table-column label="运单状态" prop="statusEnum"></el-table-column>
      <el-table-column label="操作人" prop="operator"></el-table-column>
      <el-table-column label="备注" prop="memo"></el-table-column>
      <el-table-column label="操作日期" prop="lastModifiedDate"></el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
    props: ['listData']
};
</script>

<style lang="scss" scoped>
  .list-box {
      min-height: 400px;
  }
</style>