import {asyncRouterMap, constantRouterMap} from '@/router'

/**
 * 通过meta.role判断是否与当前用户权限匹配
 * @param meta.id
 * @param route
 */
function hasPermission(permissionList, route) {
    if (route.meta && route.meta.id) {
        return permissionList.some(id => route.meta.id===id)
    } else {
        return true
    }
}

/**
 * 递归过滤异步路由表，返回符合用户角色权限的路由表
 * @param routes asyncRouterMap
 * @param permissionList
 */
function filterAsyncRouter(routes, permissionList) {
    const res = [];
    routes.forEach(route => {
        const tmp = {...route};
        if (hasPermission(permissionList, tmp)) {
            if (tmp.children) {
                tmp.children = filterAsyncRouter(tmp.children, permissionList)
            }
            res.push(tmp)
        }
    });
    return res
}

function modifyStrandardModeRoutes(routes) {
    let newRoutes = []
    routes.forEach(route => {
        //只有司机的子账号管理名称为“子帐户管理”
        if (route.meta && ([6, 17, 103, 102, 79, 84, 58, 66, 38, 74, 28, 77].includes(route.meta.id) || ['货主系统设置', '子帐户管理', '预警邮箱管理'].includes(route.meta.title) )) return
        if (route.meta && route.meta.id === 63) {
            route.meta.title = '资金流水'
        }
        if (route.children) {
            route.children = modifyStrandardModeRoutes(route.children)
        }
        newRoutes.push(route)
    })
    return newRoutes
}

const permission = {
    state: {
        routers: [],
        addRouters: []
    },
    mutations: {
        SET_ROUTERS: (state, routers) => {
            state.addRouters = routers;
            state.routers = constantRouterMap.concat(routers);
        }
    },
    actions: {
        GenerateRoutes({commit}, { codeIds, hasStandardModeFlag }) {
            return new Promise(resolve => {
                let accessedRouters = filterAsyncRouter(asyncRouterMap, codeIds);
                if (hasStandardModeFlag) accessedRouters = modifyStrandardModeRoutes(accessedRouters)
                commit('SET_ROUTERS', accessedRouters);
                resolve()
            })
        }
    }
}

export default permission
