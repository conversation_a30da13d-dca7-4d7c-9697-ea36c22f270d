<template>
  <div class="car-revise">
    <div class="container">
      <el-form :model="form" :rules="formRules" ref="form" label-width="140px" class="form">
        <div class="panel">
          <div class="panel-main">
            <div class="panel-form">
              <el-form-item label="关联司机">
                <el-input :value="form.relevanceDriverName && (form.relevanceDriverName + '/' + form.relevanceDriverMobile)" disabled></el-input>
              </el-form-item>
              <el-form-item label="关联车辆" prop="linkCar">
                <el-input v-model="form.linkCar" :disabled="true"></el-input>
              </el-form-item>
              <el-form-item label="车长车型" prop="typeAndLength" v-if="form.carModelName && !form.carModelName.includes('挂车')" key="car_type_length_edit">
                <div class="car_type_length">
                  <el-select style="width: 138px" v-model="form.carTypeId"  @visible-change="selectCarTypes" placeholder="请选择车型">
                    <el-option v-for="item in carTypes" :key="item.id" :value="item.id" :label="item.name"></el-option>
                  </el-select>
                  <el-select v-if="hasCarLength" style="width: 138px; margin-left: 4px" v-model="form.carLength"  @visible-change="selectCarLength" placeholder="请选择车长">
                    <el-option v-for="item in carLengths" :key="item.id" :value="item.name" :label="item.name"></el-option>
                  </el-select>
                  <el-input v-else style="width: 138px; margin-left: 4px" v-model="form.carLength" placeholder="请输入车长"></el-input>
                </div>
                <div class="no_length" @click="hasCarLength=!hasCarLength">{{hasCarLength ? '没有可选长度？' : '选择车长'}}</div>
                
              </el-form-item>  
              <el-form-item label="车牌号" prop="plateNumber">
                <el-input v-model="form.plateNumber"></el-input>
              </el-form-item>
              <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="是否挂靠" prop="affiliatedFlag">
                <el-select v-model="form.affiliatedFlag">
                  <el-option value="1" label="是"></el-option>
                  <el-option value="0" label="否"></el-option>
                </el-select>
                <div class="tip">非挂车必填</div>
              </el-form-item>
              <el-form-item label="行驶证所有人" prop="licenseOwner">
                <el-input v-model="form.licenseOwner"></el-input>
              </el-form-item>
              <el-form-item label="车辆类型" prop="carModelId">
                <CarTypeSelector v-model="form.carModelId" @changeName="name => form.carModelName = name" class="car-type-selector" ref="carTypeSelector"></CarTypeSelector>
              </el-form-item>
              <el-form-item label="使用性质" prop="licensesUseCharacter">
                <el-input v-model="form.licensesUseCharacter"></el-input>
                <div class="tip">总质量4.5吨及以下普通货运车辆必填</div>
              </el-form-item>
              <el-form-item label="车辆品牌型号" prop="cypp">
                <el-input v-model="form.cypp"></el-input>
              </el-form-item>
              <el-form-item label="车辆识别代号" prop="licensesVin">
                <el-input v-model="form.licensesVin"></el-input>
                <div class="tip">总质量4.5吨及以下普通货运车辆必填</div>
              </el-form-item>
              <el-form-item label="发证机关" prop="licensesIssueOrg">
                <el-input v-model="form.licensesIssueOrg"></el-input>
                <div class="tip">总质量4.5吨及以下普通货运车辆必填</div>
              </el-form-item>
              <el-form-item label="注册日期:" prop="licensesRegisterDate">
                <el-date-picker
                  v-model="form.licensesRegisterDate"
                  type="date"
                  class="form-input"
                  value-format="yyyy-MM-dd"></el-date-picker>
                  <div class="tip">总质量4.5吨及以下普通货运车辆必填</div>
              </el-form-item>
              <el-form-item label="发证日期:" prop="licensesIssueDate">
                <el-date-picker
                  v-model="form.licensesIssueDate"
                  type="date"
                  class="form-input"
                  value-format="yyyy-MM-dd"></el-date-picker>
                  <div class="tip">总质量4.5吨及以下普通货运车辆必填</div>
              </el-form-item>
              <el-form-item label="行驶证档案编号" porp="licenseNumber">
                <el-input v-model="form.licenseNumber"></el-input>
              </el-form-item>
              <el-form-item label="总质量" prop="sumCapacityTonnage">
                <el-input v-model="form.sumCapacityTonnage">
                  <template #append>kg</template>
                </el-input>
                <div class="tip">非牵引车必填</div>
              </el-form-item>
              <el-form-item label="核定载质量" prop="capacityTonnage">
                <el-input v-model="form.capacityTonnage">
                  <template #append>kg</template>
                </el-input>
                <div class="tip">非牵引车必填</div>
              </el-form-item>
              <el-form-item label="整备质量" prop="curbWeight">
                <el-input v-model="form.curbWeight">
                  <template #append>kg</template>
                </el-input>
                <!-- <div class="tip">牵引车必填</div> -->
              </el-form-item>
              <el-form-item label="准牵引总质量" prop="tractionMass">
                <el-input v-model="form.tractionMass">
                  <template #append>kg</template>
                </el-input>
                <div class="tip">牵引车必填</div>
              </el-form-item>
              <el-form-item prop="size" class="size-form">
                <template #label><span class="required-asterisk">*</span> 外廓尺寸:</template>
                <el-input v-model="form.carLong" class="form-input-size" placeholder="长"></el-input><span class="size-text">*</span>
                <el-input v-model="form.carWeight" class="form-input-size" placeholder="宽"></el-input><span class="size-text">*</span>
                <el-input v-model="form.carHigh" class="form-input-size" placeholder="高"></el-input><span class="size-text">mm</span>
              </el-form-item>
              <el-form-item label="行驶证有效期:" prop="licenseExpire">
                <el-date-picker
                  v-model="form.licenseExpire"
                  type="date"
                  class="form-input"
                  value-format="yyyy-MM-dd"
                  :picker-options='mainLicenseExpirePickerOptions'></el-date-picker>
                  <div class="expire-text">
                    <template v-if="licenseExpireStatus === -1">（已过期）</template>
                    <template v-else-if="licenseExpireStatus !== -2">（{{ licenseExpireStatus }}天后到期）</template>
                  </div>
              </el-form-item>
              <el-form-item label="能源类型" prop="energyType">
                <el-select v-model="form.energyType">
                  <el-option v-for="item in energyList" :value="item.value" :label="item.label"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="车牌颜色" prop="plateColor">
                <el-select v-model="form.plateColor">
                  <el-option 
                    v-for="item in plateColorList"
                    :key="item.plateColorCode"
                    :label="item.plateColorMessage"
                    :value="item.plateColorCode"
                  ></el-option>
                </el-select>
                <div class="tip">非挂车必填</div>
              </el-form-item>
              <el-form-item label="道路运输证号" prop="shippingCert">
                <el-input v-model="form.shippingCert"></el-input>
                <div class="tip">牵引车，总质量4.5吨以上普通货车必填</div>
                <div class="abnormal">
                  <el-checkbox v-model="isShippingAbnormal">存在异常</el-checkbox>
                  <el-select v-if="isShippingAbnormal" v-model="form.shippingCertWarnMemo">
                    <el-option value="网站无相关信息"></el-option>
                    <el-option value="网站查询过期"></el-option>
                    <el-option value="从业资格证号与网站不一致"></el-option>
                  </el-select>
                </div>
              </el-form-item>
              <el-form-item label="道路运输证有效期" prop="shippingCertExpire">
                <el-date-picker
                  v-model="form.shippingCertExpire"
                  type="date"
                  class="form-input"
                  value-format="yyyy-MM-dd"></el-date-picker>
                  <div class="expire-text">
                    <template v-if="shippingCertExpireStatus === -1">（已过期）</template>
                    <template v-else-if="shippingCertExpireStatus !== -2">（{{ shippingCertExpireStatus }}天后到期）</template>
                  </div>
              </el-form-item>
              <el-form-item v-if="form.carModelName && !form.carModelName.includes('挂车')" label="从业资格证号：" prop="qualificationCertificate">
                <el-input v-model="form.employmentCert"></el-input>
                <div class="abnormal">
                  <el-checkbox v-model="isCertAbnormal">存在异常</el-checkbox>
                  <el-select v-if="isCertAbnormal" v-model="form.employmentCertWarnMemo">
                    <el-option value="网站无相关信息"></el-option>
                    <el-option value="网站查询过期"></el-option>
                    <el-option value="从业资格证号与网站不一致"></el-option>
                  </el-select>
                </div>
              </el-form-item>
              <el-form-item v-if="form.carModelName && !form.carModelName.includes('挂车')" label="从业资格证有效期：" prop="employmentCertExpireDate">
                <el-date-picker v-model="form.employmentCertExpireDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
                <div class="expire-text">
                  <template v-if="employmentCertExpireDateStatus === -1">（已过期）</template>
                  <template v-else-if="employmentCertExpireDateStatus !== -2">（{{ employmentCertExpireDateStatus }}天后到期）</template>
                </div>
              </el-form-item>
              <el-form-item>
                <el-button @click="submit" type="primary">保存</el-button>
              </el-form-item>
            </div>
            <div class="panel-view">
              <img-list ref="imgList" :static="true">
                <el-form label-width="160px">
                  <el-form-item label="行驶证正页：">
                    <ImageUploader2
                      :defaultUrl="form.licenseImage"
                      @change="licenseImageOcr"
                      type="upload"></ImageUploader2>
                  </el-form-item>
                  <el-form-item label="行驶证副页：">
                    <ImageUploader2
                      :defaultUrl="form.licenseBackImage"
                      @change="licenseBackImageOcr"
                      type="upload"></ImageUploader2>
                  </el-form-item>
                  <el-form-item label="行驶证有效期页：">
                    <ImageUploader2
                      :defaultUrl="form.licenseExpireImage"
                      @change="licenseExpireImageOcr"
                      type="upload"></ImageUploader2>
                  </el-form-item>
                  <el-form-item label="车辆道路运输证：">
                    <ImageUploader2
                      :defaultUrl="form.shippingCertImage"
                      @change="shippingCertImageOcr"
                      type="upload"></ImageUploader2>
                  </el-form-item>
                  <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="车主声明：">
                    <ImageUploader2
                      :defaultUrl="form.statementImage"
                      @change="url => form.statementImage = url"
                      type="upload"></ImageUploader2>
                  </el-form-item>
                  <el-form-item label="人车合照：">
                    <ImageUploader2
                      :defaultUrl="form.carImage"
                      @change="url => form.carImage = url"
                      type="upload"></ImageUploader2>
                  </el-form-item>
                  <el-form-item v-if="form.carModelName && !form.carModelName.includes('挂车')" label="从业资格证：">
                    <ImageUploader2
                      :defaultUrl="form.employmentCertImage"
                      @change="url => form.employmentCertImage = url"
                      type="upload"></ImageUploader2>
                  </el-form-item>
                </el-form>
              </img-list>
            </div>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import ImgList from '@/components/ImgList/ImgList'
import CarTypeSelector from '../components/CarTypeSelector'
import carReviseRulesMixin from './carReviseRules'
import { checkExpire } from '@/utils/date'
export default {
  mixins: [carReviseRulesMixin],
  components: {
    ImgList,
    CarTypeSelector
  },
  data() {
    return {
      loading: false,
      form: {},
      mainLicenseExpirePickerOptions: {
        disabledDate(now) {
          return now.getTime() <= Date.now()
        }
      },
      carModelText: '',
      energyList: [
        { value: 'A', label: '汽油' },
        { value: 'B', label: '柴油' },
        { value: 'C', label: '电' },
        { value: 'D', label: '混合油' },
        { value: 'E', label: '天然气' },
        { value: 'F', label: '液化石油气' },
        { value: 'L', label: '甲醇' },
        { value: 'M', label: '乙醇' },
        { value: 'N', label: '太阳能' },
        { value: 'O', label: '混合动力' },
        { value: 'Y', label: '无' },
        { value: 'Z', label: '其他' },
      ],
      isShippingAbnormal: false,
      shippingCert: '',
      plateColorList: [],
      licenseExpireStatus: null,
      shippingCertExpireStatus: null,
      isDanger:false,
      carTypes: [],
      carLengths: [],
      hasCarLength: true,
      isCertAbnormal: false,
      employmentCertExpireDateStatus: -2
    }
  },
  activated() {
    this.shippingCert = null
    this.getData()
    this.getPlateColorList()

    this.$watch('form.licenseExpire', {
      deep: true,
      handler(v) {
        checkExpire(v)
          .then(res => this.licenseExpireStatus = res)
        
      }
    })

    this.$watch('form.shippingCertExpire', {
      deep: true,
      handler(v) {
        checkExpire(v)
          .then(res => this.shippingCertExpireStatus = res)
        
      }
    })

    this.$watch('form.employmentCertExpireDate', {
      deep: true,
      handler(v) {
        checkExpire(v)
          .then(res => this.employmentCertExpireDateStatus = res)
      }
    })
  },
  computed: {
    isMainTractor() {
      return typeof this.form.carModelName === 'string' && this.form.carModelName.includes('牵引车')
    },
    isTrailer() {
      return typeof this.form.carModelName === 'string' && this.form.carModelName.includes('挂车')
    }
  },
  methods: {
    getData() {
      this.$post('/admin-center-server/car/carDesc?carId=' + this.$route.query.carId)
        .then(res => {
          this.form = res
          if (this.form.sumCapacityTonnage) this.form.sumCapacityTonnage = parseInt(this.form.sumCapacityTonnage)
          if (this.form.capacityTonnage) this.form.capacityTonnage = parseInt(this.form.capacityTonnage)
          if (this.form.curbWeight) this.form.curbWeight = parseInt(this.form.curbWeight)
          if (this.form.tractionMass) this.form.tractionMass = parseInt(this.form.tractionMass)
          this.$refs.carTypeSelector.selectByName(res.carModelName)
          this.isShippingAbnormal = res.shippingCertWarnFlag === '1' ? true : false
          this.isCertAbnormal = res.employmentCertWarnFlag === '1' ? true : false

          this.$get("/admin-center-server/carmodel/getCarModelListByCategory?category=2").then(res => {
            this.carTypes = res
          });
          this.$get("/admin-center-server/carmodel/getCarModelListByCategory?category=1").then(res => {
            this.carLengths = res
            let lengths = res.map(item => item.name)
            this.hasCarLength = lengths.includes(this.form.carLength)
          });
        })
    },
    getPlateColorList() {
      this.$get('/admin-center-server/car/driving/getPlateColorList').then(
        res => {
          this.plateColorList = res
        }
      )
    },
    selectCarTypes() {
      
    },
    selectCarLength() {
    
    },
    showPic(pic) {
      this.$refs.imgList.showPic(pic)
    },
    //检测是否大于
    checkHeavierThan(value) {
      //只有在填写了值，并且大于4500时返回true，也就是说没有填时将返回false
      if (value !== null && value !== '' && value > 4500) {
        return true
      }
      return false
    },
    //检测是否小于等于
    checkLighterThan(value) {
      if (value !== null && value !== '' && value <= 4500) {
        return true
      }
      return false
    },
    isTractor(name) {
      return typeof name === 'string' && name.inclues('牵引车')
    },
    licenseImageOcr(url) {
      if (!url) return
      this.$post('/order-center-server/ocr/ocrImage', {
        type: 3,
        url,
        side: 'front'
      })
        .then(res => {
          let data = res.vehicleLicenseRes
          this.form.licenseImage = url
          if(data.errorMsg){
            this.$message.error(data.errorMsg)
            return
          }
          this.form.plateNumber = data.plateNumber
          this.$refs.carTypeSelector.selectByName(data.carModelName)
          this.form.licenseOwner = data.licenseOwner
          if(data.licensesUseCharacter){
            if(data.licensesUseCharacter.includes('客运')){
              this.isDanger = true
              this.$message.error('车辆使用性质不符合平台要求')
            }else{
              this.isDanger = false
            }
          }
          this.form.licensesUseCharacter = data.licensesUseCharacter
          this.form.cypp = data.cypp
          this.form.licensesVin = data.licensesVin
          if (data.licensesVin && !this.shippingCert) this.form.licenseNumber = data.licensesVin
          this.form.licensesIssueOrg = data.licensesIssueOrg
          this.form.licensesRegisterDate = data.licensesRegisterDate
          this.form.licensesIssueDate = data.licensesIssueDate
        })
    },
    licenseBackImageOcr(url) {
      this.form.licenseBackImage = url
      if (!url) return
      this.$post('/order-center-server/ocr/ocrImage', {
        type: 3,
        url,
        side: 'back'
      })
        .then(res => {
          let data = res.vehicleLicenseRes
          //licenseNumber 先取副页的shippingCert，如果是null或者是空，取正页的licensesVin
          if (data.shippingCert) {
            this.form.licenseNumber = data.shippingCert
            this.shippingCert = data.shippingCert
          }
          if (data.sumCapacityTonnage) this.form.sumCapacityTonnage = data.sumCapacityTonnage * 1000
          if (data.curbWeight) this.form.curbWeight = data.curbWeight * 1000
          if (data.capacityTonnage) this.form.capacityTonnage = data.capacityTonnage * 1000
          if (data.tractionMass) this.form.tractionMass = data.tractionMass * 1000
          if (data.carSize) {
            let carSizeStr = data.carSize.slice(0, -2)
            let arr = carSizeStr.split('*')
            this.form.carLong = arr[0]
            this.form.carWeight = arr[1]
            this.form.carHigh = arr[2]
          }
          this.form.energyType = data.energyType
        })
    },
    licenseExpireImageOcr(url) {
      this.form.licenseExpireImage = url
      if (!url) return
      this.$post('/order-center-server/ocr/ocrImage', {
        type: 3,
        side: 'expire',
        url
      })
        .then(res => {
          let data = res.vehicleLicenseRes
          this.form.licenseExpire = data.deputyLicenseExpire
        })
    },
    shippingCertImageOcr(url) {
      this.form.shippingCertImage = url
      if (!url) return
      this.$post('/order-center-server/ocr/ocrImage', {
        type: 6,
        url
      })
        .then(res => {
          let data = res.roadTransportCertificateRes
          this.form.shippingCert = data.permitNumber
        })
    },
    submit() {
      if(this.isDanger){
        this.$message.error('保存失败，原因：车辆使用性质不符合平台要求')
        return
      }
      if(this.form.licensesUseCharacter){
        if(this.form.licensesUseCharacter.includes('客运')){
          this.$message.error('保存失败，原因：车辆使用性质不符合平台要求')
          return
        }
      }
      this.$refs.form.validate()
        .then(() => {
          let params = { ...this.form }
          params.shippingCertWarnFlag = this.isShippingAbnormal ? 1 : 0
          params.employmentCertWarnFlag = this.isCertAbnormal ? '1' : '0'
          this.$post('/admin-center-server/car/updateDesc', {
            ...params
          })
            .then(() => {
              this.$message.success('修改成功')
              this.$router.push('/carsList/carDetail?carId=' + this.$route.query.carId)
            })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.car-revise {
  padding-bottom: 110px;
}
.container {
  margin: 10px;
  padding: 20px 10px;
  background: #fff;
}
.tab-title {
  position: relative;
  height: 30px;
  margin-bottom: 10px;
  line-height: 30px;
  padding-left: 10px;
  font-size: 14px;
  &::after {
    content: '';
    position: absolute;
    top: 7px;
    left: 0;
    width: 4px;
    height: 14px;
    background-color: #F6A018;
    border-radius: 2px;
  }
}
.panel-title {
  position: relative;
  margin-top: 10px;
  margin-bottom: 20px;
  color: #333;
  font-size: 14px;
  line-height: 14px;
  text-align: center;
  div {
    position: relative;
    z-index: 10;
    display: inline-block;
    padding: 0 10px;
    background-color: #fff;
    color: rgba(51, 51, 51, 0.7);
  }
  &::after {
    content: '';
    position: absolute;
    width: 100%;
    left: 0;
    top: 6px;
    border-top: 1px solid #d8dce3;
  }
}
.panel-main {
  display: flex;
}
.panel-form {
  flex-grow: 1;
}
.panel-view {
  flex-grow: 1;
}
.form {
  .el-input,
  .el-select {
    width: 280px;
  }
}
.tip {
  margin-left: 10px;
  display: inline-block;
  color: rgb(221, 32, 66);
}
.car-type-selector {
  width: 280px;
}
.size-form .el-input {
  width: 70px;
}
.size-text {
  margin: 0 5px;
}
.abnormal {
  margin-top: 10px;
  .el-checkbox {
    margin-right: 10px;
  }
}
.expire-text {
  color: #D92929;
}
.car_type_length {
  display: flex;

}
.no_length {
  text-align: right;
  color: #ED970F;
  width: 280px;
  height: 20px;
  line-height: 20px;
  font-size: 14px;
  cursor: pointer;
}
</style>