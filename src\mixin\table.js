/*
 * @Description: 消除固定到列表左右的列错位
 * @Author: 李智伟
 * @Date: 2024-08-09 09:25:08
 * @LastEditTime: 2024-08-09 09:49:08
 */
export const mixins = {
    data() {
      return {
    
      };
    },
    created() {
    },
    mounted() {
    },
    activated() {
        window.onresize = () => {
            if (this.$refs.multipleTable && this.$refs.multipleTable.doLayout) {
                this.$refs.multipleTable.doLayout();
            }
        }
        this.$nextTick(() => {
            if (this.$refs.multipleTable && this.$refs.multipleTable.doLayout) {
              this.$refs.multipleTable.doLayout();
            }
      })
    },
    updated() {
        if (this.$refs.multipleTable && this.$refs.multipleTable.doLayout) {
            this.$refs.multipleTable.doLayout();
        }
    },
    methods: {
    },
    deactived(){
        window.onresize = null;
    },
    destroyed() {
        window.onresize = null;
    }
  };