<template>
  <div class="audit-content">
    <div class="audit-top">
      <div class="top-content">
        <div class="top-title">货主审核 <span class="top-sn"> {{dataObj.companyName}}</span></div>
        <div class="top-button">
          <div class="top-button-info" :class="{'top-button-select': selectIndex == 0}" @click="selectIndex = 0">基本信息</div>
          <div class="top-button-log" :class="{'top-button-select': selectIndex == 1}" @click="selectIndex = 1">审核日志</div>
        </div>
      </div>
    </div>
    <div class="audit-bottom" v-if="operation==2">
      <span>货主审核状态：{{ dataObj.authStatusName}}</span>
      <el-button v-if='dataObj.authStatus!=2'
                   size="mini"
                   @click="adoptfn"
                   type="primary">通过审核</el-button>
        <el-button size="mini"
                   @click="dialogFormVisible = true"
                   type="danger">驳回</el-button>
    </div>
    <div v-if="selectIndex == 0">
      <div v-if='type==1'
         class="wrap">
      <p>
        <span class='span-1'>公司名称：</span>
        <span class='span-2'>{{dataObj.companyName}}</span>
      </p>
      <p>
        <span class='span-1'>手机号：</span>
        <span class='span-2'>{{dataObj.mobile}}</span>
      </p>
      <p>
        <span class='span-1'>统一社会信用代码：</span>
        <span class='span-2'>{{dataObj.organizationCode}}</span>
      </p>
      <p>
        <span class='span-1'>公司地址：</span>
        <span class='span-2'>{{dataObj.companyAddress}}</span>
      </p>
      <p>
        <span class='span-1'>法人姓名：</span>
        <span class='span-2'>{{dataObj.corporationName}}</span>
      </p>
      <p>
        <span class='span-1'>法人身份证号：</span>
        <span class='span-2'>{{dataObj.corporationIdCard}}</span>
      </p>
      <p>
        <span class='span-1'>营业期限至：</span>
        <span class='span-2'>{{dataObj.licenseExpireDate}}</span>
        <span v-if='dataObj.historyList && operation==2'
              class='span-2'
              style='margin-left:225px'>{{dataObj.historyList.licenseExpireDate}}</span>

      </p>
      <p v-if='dataObj.expireDays==0'>
        <span class='span-1'>提示：</span>
        <span class='span-2'>今天后过期（未过期）</span>

      </p>
      <p v-if='dataObj.expireDays>0 && dataObj.expireDays!=**********'>
        <span class='span-1'>提示：</span>
        <span class='span-2'>{{dataObj.expireDays}}天后过期（未过期）</span>
      </p>
      <p v-if='dataObj.expireDays<0'>
        <span class='span-1'>提示：</span>
        <span class='span-2'>（已过期）</span>
      </p>
      <p>
        <span class='span-1'>认证状态：</span>
        <span class='span-2'>{{dataObj.authStatusName}}</span>
      </p>
      <!-- 隐藏之前的-->
      <p class='wrap-btn'
         v-if='false'> 
        <el-button v-if='dataObj.authStatus!=2'
                   size="mini"
                   @click="adoptfn"
                   type="primary">通过审核</el-button>
        <el-button size="mini"
                   @click="dialogFormVisible = true"
                   type="danger">驳回</el-button>
        <!-- <el-button size="mini" type="success" @click="resetUserSn">重置用户编号</el-button> -->
      </p>
    </div>
    <div v-if='type==2 && authType==1'
         class="wrap">
      <p>
        <span class='span-1'>调度员名称：</span>
        <span class='span-2'>{{dataObj.companyName}}</span>
      </p>
      <p>
        <span class='span-1'>手机号：</span>
        <span class='span-2'>{{dataObj.mobile}}</span>
      </p>
      <p>
        <span class='span-1'>身份证号码：</span>
        <span class='span-2'>{{dataObj.idCard}}</span>
      </p>
      <p>
        <span class='span-1'>认证状态：</span>
        <span v-if='type==2'
              class='span-2'>{{dataObj.brokerAuthStatusName}}</span>
      </p>
      <p class='wrap-btn'
         v-if='operation==2'>
        <el-button v-if='dataObj.brokerAuthStatus!=2'
                   size="mini"
                   @click="adoptfn"
                   type="primary">通过审核</el-button>
        <el-button size="mini"
                   @click="dialogFormVisible = true"
                   type="danger">驳回</el-button>
        <!-- <el-button size="mini" type="success" @click="resetUserSn">重置用户编号</el-button> -->
      </p>
    </div>
    <div v-if='type==2 && authType==2'
         class="wrap">
      <p>
        <span class='span-1'>调度员公司名称：</span>
        <span class='span-2'>{{dataObj.companyName}}</span>
      </p>
      <p>
        <span class='span-1'>手机号：</span>
        <span class='span-2'>{{dataObj.mobile}}</span>
      </p>
      <p>
        <span class='span-1'>统一社会信用代码：</span>
        <span class='span-2'>{{dataObj.organizationCode}}</span>
      </p>
      <p>
        <span class='span-1'>营业期限至：</span>
        <span class='span-2'>{{dataObj.licenseExpireDate}}</span>
        <span v-if='dataObj.historyList && operation==2'
              class='span-2'
              style='margin-left:225px'>{{dataObj.historyList.licenseExpireDate}}</span>
      </p>
      <p v-if='dataObj.expireDays==0'>
        <span class='span-1'>提示：</span>
        <span class='span-2'>今天后过期（未过期）</span>

      </p>
      <p v-if='dataObj.expireDays>0'>
        <span class='span-1'>提示：</span>
        <span class='span-2'>{{dataObj.expireDays}}天后过期（未过期）</span>
      </p>
      <p v-if='dataObj.expireDays<0'>
        <span class='span-1'>提示：</span>
        <span class='span-2'>（已过期）</span>
      </p>
      <p>
        <span class='span-1'>认证状态：</span>
        <span class='span-2'>{{dataObj.brokerAuthStatusName}}</span>
      </p>
      <p class='wrap-btn'
         v-if='operation==2'>
        <el-button v-if='dataObj.brokerAuthStatus!=2'
                   size="mini"
                   @click="adoptfn"
                   type="primary">通过审核</el-button>
        <el-button size="mini"
                   type="danger"
                   @click="dialogFormVisible = true">驳回</el-button>
        <!-- <el-button size="mini" type="success" @click="resetUserSn">重置用户编号</el-button> -->
      </p>
    </div>
    <el-dialog title="驳回原因"
               :visible.sync="dialogFormVisible">
      <el-form :model="form">
        <el-form-item label="请选择驳回原因"
                      :label-width="formLabelWidth">
          <el-checkbox-group v-model="form.region">
            <div v-for='item in rejectReasonList' :key="item.id">
              <el-checkbox :label="item.rejectReason" >{{ item.rejectReason }}</el-checkbox>
            </div>
          </el-checkbox-group>
          <!-- <el-select v-model="form.region"
                     placeholder="请选择活动区域">
            <el-option :label="item.rejectReason"
                       v-for='(item,lan) in rejectReasonList'
                       :value="item.rejectReason"></el-option>
          </el-select> -->
        </el-form-item>
        <el-form-item label="其他原因"
                      :label-width="formLabelWidth">
          <el-input type="textarea"
                    :rows="2"
                    placeholder="请输入其他驳回原因"
                    v-model="form.textarea">
          </el-input>
        </el-form-item>

      </el-form>
      <div slot="footer"
           class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary"
                   @click="rejectfn">确 定</el-button>
      </div>
    </el-dialog>
    <img-list class="right-images" ref="imgList">
      <template v-if="type==1 || (type==2 && authType==2)">
        <el-form label-width="160px">
          <el-form-item label="法人身份证原图：" v-if="hasHistory">
            <template>
              <div class="item-box" @click="viewPic(dataObj.historyList.corporationIdCardImage)">
                <div class="img-box">
                  <img :src="dataObj.historyList.corporationIdCardImage">
                </div>
              </div>
            </template>
          </el-form-item>
          <el-form-item :label="hasHistory ? '法人身份证现图：' : '法人身份证：'">
            <template>
              <div class="item-box" @click="viewPic(dataObj.corporationIdCardImage)">
                <div class="img-box">
                  <img :src="dataObj.corporationIdCardImage">
                </div>
              </div>
            </template>
          </el-form-item>
          <el-form-item label="营业执照原图：" v-if="hasHistory">
            <template>
              <div class="item-box" @click="viewPic(dataObj.historyList.licenseImage)">
                <div class="img-box">
                  <img :src="dataObj.historyList.licenseImage">
                </div>
              </div>
            </template>
          </el-form-item>
          <el-form-item :label="hasHistory ? '营业执照现图：' : '营业执照：'">
            <template>
              <div class="item-box" @click="viewPic(dataObj.licenseImage)">
                <div class="img-box">
                  <img :src="dataObj.licenseImage">
                </div>
              </div>
            </template>
          </el-form-item>
        </el-form>
      </template>
      <template v-else-if="type==2 && authType==1">
        <el-form label-width="160px">
          <el-form-item label="用户身份证：">
            <div class="item-box" @click="viewPic(dataObj.idCardImage)">
              <div class="img-box">
                <img :src="dataObj.idCardImage">
              </div>
            </div>
          </el-form-item>
        </el-form>
      </template>
    </img-list>
    </div>
    <LogList v-if="selectIndex == 1" :listData="dataObj.userVerifyLogDTO"></LogList>

  </div>
</template>

<script>
// 1：未认证，2：认证成功，3：认证失败(驳回)，4：认证中，5：认证过期，6:补充资源认证中，7：补充资料驳回
//已认证：通过 驳回    未认证：没有   认证失败：通过 驳回  认证中：
import ElImageViewer from 'cf-element-ui/packages/image/src/image-viewer'
import ImgList from '@/components/ImgList/ImgList'
import LogList from '@/components/AuditLog/index.vue'

const forUpdateInfo = '/admin-center-server/commonUser/forUpdateInfo'//详情
const verify = '/admin-center-server/verify'//驳回
const rejectReasonList = '/admin-center-server/verify/rejectReasonList'//驳回原因
export default {
  components: { ElImageViewer, ImgList, LogList },
  data () {
    return {
      rejectReasonList: [],
      selectIndex: 0,
      formLabelWidth: '120px',
      form: {
        textarea: '',
        region: []
      },
      addFlag: true,
      dialogFormVisible: false,
      dataObj: {},
      id: '',
      type: '',
      authType: '',
      operation: '',
      showViewer: false, // 显示查看器
      showViewer1: false,
      showViewer2: false, // 显示查看器
      showViewer3: false
    };
  },
  methods: {
    onPreview () {
      this.showViewer = true
    },
    // 关闭查看器
    closeViewer () {
      this.showViewer = false
    },
    onPreview1 () {
      this.showViewer1 = true
    },
    // 关闭查看器
    closeViewer1 () {
      this.showViewer1 = false
    },
    onPreview2 () {
      this.showViewer2 = true
    },
    // 关闭查看器
    closeViewer2 () {
      this.showViewer2 = false
    },
    onPreview3 () {
      this.showViewer3 = true
    },
    // 关闭查看器
    closeViewer3 () {
      this.showViewer3 = false
    },
    //通过审核
    adoptfn () {
      var that = this
      this.$confirm('确认通过审核', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (that.addFlag) {
          that.addFlag = false;
          if (that.type == 2) {
            var data = {
              id: that.id,
              brokerAuthStatus: 2
            }
          } else {
            var data = {
              id: that.id,
              authStatus: 2
            }
          }
          this.$http.post(verify, data).then(res => {
            if (res.data.code == '200') {
              this.$message({
                message: res.data.message,
                type: 'success'
              });
              this.dialogFormVisible = false
              if (that.type == 1) {
                that.getClientauthapplychannel()
              } else if (that.type == 2) {
                that.getBrokerauthapplychannel()
              }

              //that.getData()
              setTimeout(() => {
                window.close()
              }, 1000)

            } else {
              that.addFlag = true;
              this.$message.error(res.data.message);
            }
          })
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消通过审核'
        });
      });


    },
    //驳回
    rejectfn () {
      var that = this
      if (this.addFlag) {
        this.addFlag = false
        if (this.form.region.length === 0 && this.form.textarea === '') {
          this.$message.error('请选择或输入驳回原因');
          this.addFlag = true
          return false
        }
        
        let selectReason = this.form.region.join(',')
        let rejectReason = ''
        if (selectReason !== '' && this.form.textarea !== '') {
          rejectReason = selectReason + ',' + this.form.textarea
        } else {
          if (selectReason === '') {
            rejectReason = this.form.textarea
          } else {
            rejectReason = selectReason
          }
        }

        var num = ''
        if (this.type == 2) {
          if (this.dataObj.brokerAuthStatus == 6) {
            num = 7
          } else {
            num = 3
          }
          var data = {
            id: this.id,
            brokerAuthStatus: num,
            rejectReason: rejectReason
          }

          this.$http.post(verify, data).then(res => {

            if (res.data.code == '200') {

              this.$message({
                message: res.data.message,
                type: 'success'
              });
              that.getBrokerauthapplychannel()
              this.dialogFormVisible = false
              that.$router.go(-1);

            } else {
              this.addFlag = true
              this.$message.error(res.data.message);
            }
          })
        } else if (this.type == 1) {
          if (this.dataObj.authStatus == 6) {
            num = 7
          } else {
            num = 3
          }
          var data1 = {
            id: this.id,
            authStatus: num,
            rejectReason: rejectReason
          }
          this.$http.post(verify, data1).then(res => {
            if (res.data.code == '200') {
              this.$message({
                message: res.data.message,
                type: 'success'
              });
              this.dialogFormVisible = false
              that.getClientauthapplychannel()
              setTimeout(() => {
                window.close()
              }, 1000)
            } else {
              this.addFlag = true
              this.$message.error(res.data.message);
            }
          })
        }
      }


    },
    //重置userSn
    resetUserSn () {
      let id = this.$route.query.id;
      this.$http.post('/admin-center-server/commonUser/updateUserSn?userId=' + id).then(res => {
        let data = res.data;
        if (data.code === '200') {
          this.$message('重置成功')
        } else {
          this.$message.error(data.message)
        }
      })
    },
    getData () {
      this.$http.get(forUpdateInfo + '?id=' + this.id).then(res => {
        this.dataObj = res.data.data
        document.title = "货主审核-" + res.data.data.companyName
      })
      this.$http.post(rejectReasonList, {
        scene: Number(this.type) + 2
      }).then(res => {
        this.rejectReasonList = res.data.data
      })
    },
    //货主  10
    getClientauthapplychannel () {
      this.$http.get('/admin-center-server/verify/getVerifySumMsgByType', {
        params: {
          userType: 1
        }
      }).then(res => {
        let data = Number(res.data.data);
        let type = 10;
        this.getMenu(type, data)
      })
    },
    //调度员  20
    getBrokerauthapplychannel () {
      this.$http.get('/admin-center-server/verify/getVerifySumMsgByType', {
        params: {
          userType: 2
        }
      }).then(res => {
        let data = Number(res.data.data);
        let type = 20;
        this.getMenu(type, data)
      })
    },
    getMenu (type, num) {
      this.$store.state.permission.addRouters.map((item, index) => {
        if (item.meta) {
          if (item.meta.id === 22) {
            let changeNum = item.children[0].children;
            changeNum.map((item, index) => {
              if (!item.hidden && item.meta.id !== 24) {
                if (item.meta.id === 25 && type === 0) {
                  item.meta.num = num
                } else if (item.meta.id === 26 && type === 1) {
                  item.meta.num = num
                } else if (item.meta.id === 27 && type === 2) {
                  item.meta.num = num
                }
              }
            })
          } else if (item.meta.id === 1) {
            let clientNum = item.children[0].children;
            let brokerNum = item.children[1].children;
            let driverNum = item.children[2].children;
            clientNum.map((item, index) => {
              if (!item.hidden) {
                if (item.meta.id === 5 && type === 10) {
                  item.meta.num = num
                }
              }
            });
            brokerNum.map((item, index) => {
              if (!item.hidden) {
                if (item.meta.id === 10 && type === 20) {
                  item.meta.num = num
                }
              }
            });
            driverNum.map((item, index) => {
              if (!item.hidden) {
                if (item.meta.id === 13 && type === 30) {
                  item.meta.num = num
                }
              }
            })
          }
        }
      });
    },
    regectDialog () {
      this.dialogFormVisible = true
    },
    viewPic(pic) {
      this.$refs.imgList.showPic(pic)
    },
  },
  activated () {
    this.id = this.$route.query.id;
    this.type = this.$route.query.type;
    this.operation = this.$route.query.operation;
    this.authType = this.$route.query.authType
    this.getData()
  },
  computed: {
    hasHistory() {
      return this.dataObj.historyList && this.operation == 2
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.l100 {
  margin-left: 100px !important;
}
.audit-content {
  margin: 0px 10px;
  padding-top: 93px;
  background-color: white;
  min-height: 1000px;
}
.audit-top {
  position: fixed;
  top: 93px;
  left: 210px;
  right: 25px;
  z-index: 99;
  // background-color: rgb(249, 249, 249);
  background-color: white;
  .top-content {
    background-color: rgb(249, 249, 249);
    margin:0px 1px;
  }
  .top-title {
    padding: 20px;
    font-size: 18px;
    font-weight: 800;
  }
  .top-sn {
    font-size: 18px;
    font-weight: 400;
  }

  .top-button {
    display: flex;
    .top-button-info {
      width: 108px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
    }
    .top-button-log {
      width: 108px;
      text-align: center;
      height: 30px;
      line-height: 30px;
      cursor: pointer;
    }

    .top-button-select {
      background-color: white;
    }
  }
}

.audit-bottom {
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 100;
  box-sizing: border-box;
  width: calc(100% - 180px);
  height: 80px;
  padding: 10px 60px 0px;
  background: #fff;
  display: flex;
  align-items: center;
  span {
    flex-grow: 1;
  }
}
.right-images {
  margin-top: 80px;
}
.wrap {
  margin-top: 20px;
  width: 35%;
  font-size: 14px;
  .span-2 {
    color: #333;
  }
  > p {
    overflow: hidden;
    position: relative;
  }
}
#app {
  background: #fff;
}
.span-1 {
  position: absolute;
  top: 0;
  left: 0;
  width: 150px;
  display: inline-block;
  text-align: right;
  margin-right: 20px;
}
.span-2 {
  float: left;
  padding-left: 150px;
  color: #666 !important;
  word-break: break-all;
}
.el-image {
  margin-left: 190px;
}
// .tips {
//   margin-left: 85px;
// }
.wrap-btn {
  margin-left: 190px;
}
</style>
