<template>
    <div class="app-container systemUserRole">
        <div class="select-box">
            <div class="top-title">筛选查询</div>
            <div class="select-info">
                <el-form
                        :inline="true"
                        size="mini"
                        :model="formInline"
                        class="demo-form-inline"
                        label-width="150px"
                >
                    <el-form-item label="角色名称:">
                        <el-input v-model="formInline.name" placeholder="请输入角色名称"></el-input>
                    </el-form-item>
                    <!-- <el-form-item label="登记人账号:">
                                  <el-input v-model="formInline.user" placeholder="请输入登记人ID"></el-input>
                    </el-form-item>-->
                    <el-form-item>
                        <el-button type="primary" @click="onSubmit" size="mini" icon="el-icon-search">查询</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button icon="el-icon-delete" size="mini" type="danger" @click="clearSearch">清空筛选</el-button>
                    </el-form-item>
                    <el-form-item></el-form-item>
                </el-form>
            </div>
        </div>
        <div class="list-box">
            <el-row>
                <el-col :span="19">
                    <div class="list-right">
                        <div class="right-title">
                            <div>角色列表</div>
                            <div>
                                <el-button size="mini" @click="dialogAdd = true">
                                    <i class="el-icon-plus"></i>
                                    添加
                                </el-button>
                                <el-button :loading="saveLoading" size="mini" @click="saveList">保存权限数据</el-button>
                            </div>
                        </div>
                        <div class="list-main">
                            <template>
                                <el-table
                                        class="tablessp"
                                        highlight-current-row
                                        ref="multipleTable"
                                        :data="tableData"
                                        tooltip-effect="dark"
                                        border
                                        style="width: 100%"
                                        @select="handleSelect"
                                >
                                    <el-table-column
                                            label="单选"
                                            width="55"
                                            type="selection"
                                    >
                                    </el-table-column>
                                    <el-table-column prop="name" label="角色名称" width="240"></el-table-column>
                                    <el-table-column prop="description" label="描述"></el-table-column>
                                    <el-table-column prop="updateTime" label="最后修改时间" width="180"></el-table-column>
                                    <el-table-column fixed="right" label="操作" width="100">
                                        <template slot-scope="scope">
                                            <el-button type="text" size="small" @click="deleteRow(scope.row)">删除
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </template>
                        </div>
                        <div class="paging">
                            <div class="block">
                                <el-pagination
                                        @size-change="handleSizeChange"
                                        @current-change="handleCurrentChange"
                                        :current-page="currentPage"
                                        :page-sizes="[10, 20, 30, 40]"
                                        :page-size="pageSize"
                                        layout="total, sizes, prev, pager, next, jumper"
                                        :total="total"
                                ></el-pagination>
                            </div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="5">
                    <div class="list-left">
                        <div class="left-title">权限列表</div>
                        <div class="tree-box">
                            <el-tree :data="data"
                                     show-checkbox
                                     ref="tree"
                                     node-key="id"
                                     :default-checked-keys="checkedID"
                                     :default-expanded-keys="expandedID"
                                     @check="handleNodeClick"
                                     :props="defaultProps">
                            </el-tree>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </div>
        <!-- 添加弹窗 -->
        <el-dialog title="提示" :visible.sync="dialogAdd" width="30%" :before-close="handleClose">
            <el-form ref="formAdd" :model="formAdd" label-width="80px">
                <el-form-item label="角色名称:">
                    <el-input v-model="formAdd.name"></el-input>
                </el-form-item>
                <el-form-item label="角色描述:">
                    <el-input type="textarea" v-model="formAdd.desc"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
        <el-button @click="dialogAdd = false">取 消</el-button>
        <el-button :loading="loading" type="primary" @click="addUser">确 定</el-button>
      </span>
        </el-dialog>
        <!-- 删除列表弹窗-->
        <el-dialog title="警告提示" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
            <div>确认删除吗？</div>
            <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="sureDelete">确 定</el-button>
      </span>
        </el-dialog>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                loading:false,
                saveLoading:false,
                dialogVisible:false,
                deleteId:'',
                getTreecheckedID: [],
                chioceTreeIds: [],
                checkedID: [],
                expandedID: [],
                curId: '',
                dialogAdd: false,
                currentPage: 1,
                pageSize: 20,
                total: 1,
                tableData: [],
                multipleSelection: [],
                formInline: {
                    name: "",
                },
                data: [],
                defaultProps: {
                    children: 'children',
                    label: 'name'
                },
                formAdd: {
                    name: '',
                    desc: ''
                }
            };
        },
        methods: {
            handleClose(done) {
                this.dialogAdd = false;
                this.dialogVisible = false;
            },
            /** 添加角色 **/
            addUser() {
                let form = {
                    name: this.formAdd.name,
                    available: true,
                    description: this.formAdd.desc
                };
                if(form.name===''||form.description===''){
                    this.$message.warning('请填写完整')
                }else {
                    this.loading=true;
                    this.$http.post('/admin-center-server/sys/role/insert', form).then(res => {
                        let data = res.data;
                        if (data.code === '200') {
                            this.formAdd = {
                                name: '',
                                desc: ''
                            };
                            this.$message.success('添加成功');
                            this.loading=false;
                            this.getDataList();
                            this.dialogAdd = false
                        } else {
                            this.loading=false;
                            this.$message.warning(data.message)
                        }

                    })
                }
            },

            /** 单选 **/
            handleSelect(val, row) {
                if (val.length > 1) {
                    this.$refs.multipleTable.clearSelection(); // 清空所有选择
                    val.shift();
                    this.$refs.multipleTable.toggleRowSelection(row) //  选中当前选择
                }
                if(val.length!==0){
                    this.checkedID =[];
                    this.expandedID = [];
                    this.getTreeData();
                    let id = val[0].id;
                    this.curId = id;
                    this.$http.post('/admin-center-server/sys/resource/queryResourceByRoleId?roleId=' + id).then(res => {
                        let data = res.data;
                        if(data.code==='200'){
                            if(data.data.length!==0){
                                let resIds= data.data.map(item=>{
                                    return item.id
                                });
                                let treeList = this.data;
                                let checkTree = this.filterAsyncTree(treeList,resIds);
                                let checkId = [];
                                checkTree.map((item,index)=>{
                                    if(item.children.length!==0){
                                        let fChildren = item.children;
                                        fChildren.map((itemf,index)=>{
                                            if(itemf.children.length!==0){
                                                let secChildren = itemf.children;
                                                secChildren.map((itemT,index)=>{
                                                    checkId.push(itemT.id)
                                                })
                                            }else {
                                                checkId.push(itemf.id)
                                            }
                                        })
                                    }else {
                                        checkId.push(item.id)
                                    }
                                });
                                this.checkedID =checkId;
                                this.expandedID = checkId
                            }else {
                                this.$message.warning('该角色暂无权限')
                            }
                        }else {
                            this.$message.warning(data.message)
                        }

                    })
                }
            },
            hasParent(ids, tree) {
                if (tree.id) {
                    return ids.some(id => tree.id===id)
                } else {
                    return true
                }
            },
            filterAsyncTree(treeList, ids) {
                const res = [];
                treeList.forEach(tree => {
                    const tmp = {...tree};
                    if (this.hasParent(ids, tmp)) {
                        if (tmp.children) {
                            tmp.children = this.filterAsyncTree(tmp.children, ids)
                        }
                        res.push(tmp)
                    }
                });
                return res
            },
            /** 已勾选菜单ID **/
            handleNodeClick() {
                let res = this.$refs.tree.getCheckedKeys().concat(this.$refs.tree.getHalfCheckedKeys());
                this.chioceTreeIds = res
            },
            /** 保存已选树 **/
            saveList() {
                if (this.curId === '') {
                    this.$message.warning('请选择一个角色信息')
                } else {
                    this.saveLoading=true;
                    let permissions = this.chioceTreeIds;
                    let str = ['59'];
                    permissions = permissions.concat(str);
                    let roleId = this.curId;
                    this.$http.post('/admin-center-server/sys/role/updateRolePermission', {
                        permissions: permissions,
                        roleId: roleId,
                    }).then(res => {
                        let data = res.data;
                        if (data.code === '200') {
                            this.$message.success(data.message);
                            this.checkedID =[];
                            this.expandedID = [];
                            this.getDataList();
                            this.getTreeData();
                            this.saveLoading=false;
                        } else {
                            this.saveLoading=false;
                            this.$message.warning(data.message)
                        }
                    })
                }
            },
            /** 查询 **/
            onSubmit() {
                this.currentPage = 1;
                this.getDataList();
            },
            clearSearch() {
                this.formInline.name = '';
                this.getDataList();
            },
            /** 删除 **/
            deleteRow(row) {
                let id = row.id;
                this.deleteId= id;
                this.dialogVisible=true

            },
            /** 确认删除 **/
            sureDelete(){
                let id = this.deleteId;
                this.$http.post('/admin-center-server/sys/role/delete', {
                    id: id
                }).then(res => {
                    let data = res.data;
                    if (data.code === '200') {
                        this.$message.success('删除成功');
                        this.getDataList()
                        this.dialogVisible=false
                    } else {
                        this.$message.warning(data.message)
                    }
                })
            },

            handleSizeChange(val) {
                this.pageSize = val;
                this.currentPage = 1;
                this.getDataList();
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getDataList();
            },
            getDataList() {
                this.$http
                    .get("/admin-center-server/sys/role/queryListByPage", {
                        params: {
                            pageSize: this.pageSize,
                            pageNumber: this.currentPage,
                            name: this.formInline.name
                        }
                    })
                    .then(res => {
                        let data = res.data;
                        if (data.code === "200") {
                            this.tableData = data.data.list;
                            this.total = Number(data.data.total)
                        }
                    });
            },
            getTreeData() {
                this.$http.get('/admin-center-server/home/<USER>').then(res => {
                    let data = res.data.data
                    for (let i = 0; i < data.length; i++) {
                        if (data[i].permission == 'FORBID') {
                            data.splice(i, 1)
                        }
                    }
                    this.data = data
                })
            },
        },
        activated() {
            this.getDataList();
            this.getTreeData();
        },
    };
</script>
<style>
    .el-button--mini,
    .el-button--mini.is-round {
        padding: 5px 15px;
    }

    .tablessp thead tr .el-checkbox__input {
        display: none;
    }
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
    .tip {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 0 40px 40px 0;
        font-size: 12px;

        em {
            margin-right: 5px;
        }
    }

    .inner-box {
        margin-left: 10%;
        width: 70%;

        .upload-box {
            width: 100%;
            height: 100%;
            position: relative;

            .icon-XZ {
                width: 92px;
                height: 92px;
                margin: 0 auto;
                background-size: 100% 100%;
            }

            .icon-word {
                width: 100%;
                height: 20px;
                line-height: 20px;
                font-size: 10px;
                position: absolute;
                bottom: 25px;
                left: 0px;
                color: #cccccc;
            }
        }
    }

    .systemUserRole {
        .select-box {
            /*height: 260px;*/
            background-color: #ffffff;

            .top-title {
                font-size: 16px;
                font-weight: 700;
                height: 40px;
                line-height: 40px;
                padding-left: 10px;
                border-bottom: 1px solid #cccccc;
            }

            .select-info {
                padding-top: 30px;
                padding-bottom: 30px;
            }
        }

        .list-box {
            background-color: #ffffff;
            margin-top: 20px;
            padding: 10px;

            .list-title {
                display: flex;
                flex-direction: row;
                justify-content: space-between;

                div {
                    height: 38px;
                    line-height: 38px;
                }
            }

            .list-left {
                padding-left: 10px;
                /*height: 30px;*/
                /*line-height: 30px;*/
                .left-title {
                    font-size: 12px;
                    font-weight: 700;
                }

                .tree-box {
                    margin-top: 12px;
                }
            }

            .list-right {
                padding-right: 20px;

                .right-title {
                    height: 30px;
                    line-height: 30px;
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    font-size: 12px;
                    font-weight: 700;
                }

                .list-main {
                    width: 100%;
                    border: 1px solid #cccccc;
                    margin-top: 10px;
                }

                .paging {
                    margin-top: 10px;
                    float: right;
                }
            }
        }
    }
</style>
