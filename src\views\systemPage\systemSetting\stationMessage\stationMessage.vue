<template>
  <div class="app-container operationLog">
    <div class="select-box">
      <div class="top-title">
        <div>筛选查询</div>
        <div class="button">
          <el-button
            class="left"
            icon="el-icon-search"
            size="mini"
            type="primary"
            @click="onSubmit"
          >查询</el-button>
          <el-button
            class="left"
            icon="el-icon-delete"
            size="mini"
            type="danger"
            @click="resetSubmit"
          >清空筛选</el-button>
        </div>
      </div>
      <div class="select-info">
        <el-form
          :inline="true"
          size="small"
          :model="formInline"
          class="demo-form-inline"
          label-width="150px"
        >
          <el-form-item label="消息类型">
            <el-select v-model="sysMessageType" placeholder="请选择消息类型" clearable>
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"

              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
        <div>
          <el-button class="releaseMessage" @click="addStationMessage">新增站内消息</el-button>
        </div>
      </div>
      <div class="list-main">
        <template>
          <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="num" label="序号" type="index" width="50"></el-table-column>
            <el-table-column prop="sysMessageTypeName" label="消息类型"></el-table-column>
            <el-table-column prop="sysTitle" label="消息标题"></el-table-column>
            <el-table-column prop="sysMessageRecive" label="接收方" ></el-table-column>
            <el-table-column prop="createTime" label="发布时间"></el-table-column>
            <el-table-column prop="operateName" label="操作人姓名"></el-table-column>
            <el-table-column prop="operateAccount" label="操作人登陆账号"></el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
              <template slot-scope="scope">
                <el-button @click="handleClick(scope.row)" type="text" size="small">修改</el-button>
                <el-button type="text" size="small" @click="delItem(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </div>
      <div class="paging">
        <div class="block">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 40]"
            :page-size="10"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      currentPage: 1,
      pageSize: 20,
      formInline: {
        action: "",
        operateName: "",
        operateAccount: "",
        date: ""
      },
      sysMessageType: "", //
      options: [
        {
          value: "0",
          label: "产品消息"
        },
        {
          value: "1",
          label: "安全消息"
        },
        {
          value: "2",
          label: "服务消息"
        },
        {
          value: "3",
          label: "活动消息"
        },
        {
          value: "4",
          label: "历史消息"
        },
        {
          value: "5",
          label: "故障消息"
        }
      ],
      value: "",
      tableData: [],

      total: null
    };
  },
  activated() {
    this.getDataList();
  },
  methods: {
    /** 分页方法 **/
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.pageSize = val;
      this.getDataList();
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.currentPage = val;
      this.getDataList();
    },
    /* table右侧操作按钮 */
    handleClick(row) {
      console.log(row);
       this.$router.push({
        path: "addStationMessage",
        query:{
          messageFlag:1,
          id:row.id   //站内消息id
        }
      });

    },
    /** 根据时间态搜索 **/
    selectTime() {
      let startTime = this.formInline.date[0];
      let endTime = this.formInline.date[1];
      this.startTime = startTime;
      this.endTime = endTime;
    },
    /** 搜索**/
    onSubmit() {
      this.currentPage = 1
      this.getDataList();
    },
    /** 清空搜索选项 **/
    resetSubmit() {
      this.formInline = {
        action: "",
        operateName: "",
        operateAccount: "",
        date: ""
      };
      this.currentPage = 1;
      this.pageSize = 10;
      this.total = 1;
      this.startTime = "";
      this.endTime = "";
      this.getDataList();
    },

    /** 拉取列表数据 **/
    getDataList() {
      this.$http
        .get("/admin-center-server/sys/getSysMessages", {
          params: {
            pageNumber: this.currentPage,
            pageSize: this.pageSize,
            sysMessageType: this.sysMessageType
          }
        })
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            console.log(data)
            this.tableData = data.data.list;
            this.total = Number(data.data.total);
          }else{
            this.$message.warning(res.data.message)
          }
        });
    },
    /* 新增站内消息 */
    addStationMessage() {
      console.log("新增站内消息");
      this.$router.push({
        path: "addStationMessage",
        query:{
          messageFlag:0 //新增
        }
      });
    },
    /* 单个删除功能 */
    delItem(row) {
      console.log(row.id);
      let id = Number(row.id);
      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http
            .post("/admin-center-server/sys/deleteSysMessage?id="+id)
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
                this.getDataList();
              }else{
                this.$message.warning(data.message)
              }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.operationLog {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
      display: flex;
      justify-content: space-between;

      .button {
        margin-right: 20px;
      }
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;
    overflow: hidden;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }

    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }

    .paging {
      margin-top: 10px;
      float: right;
    }
  }
}
</style>
