<template>
  <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
    <el-tab-pane label="车辆登记信息查询" name="first">
      <div class="carNum">
        <el-form :model="form">
          <el-form-item label="车牌号:" required label-width="300">
            <el-input v-model.trim="carNum" :οnkeyup="carNum=carNum.replace(/\s/g, '')" placeholder="请输入车牌号" maxlength="8" style="width:200px"></el-input>
            <el-button style="margin-left:10px" @click="getCarInfo">查询</el-button>
          </el-form-item>
        </el-form>
        <div class="carInfList" v-if="carInfoFlag">
          <table class="carInfoTable" cellpadding="0" cellspacing="0" border="1">
            <tr>
              <td>车牌号</td>
              <td>{{carInfo.vehiclenumber}}</td>
            </tr>
            <tr>
              <td>车牌代码</td>
              <td>{{carInfo.licensePlateTypeCode}}</td>
            </tr>
            <tr>
              <td>所有人</td>
              <td>{{carInfo.owner}}</td>
            </tr>
            <tr>
              <td>品牌</td>
              <td>{{carInfo.vehicleBrandModel}}</td>
            </tr>
            <tr>
              <td>引擎号</td>
              <td>{{carInfo.vehicleEngineNumber}}</td>
            </tr>
            <tr>
              <td>载重量</td>
              <td>{{carInfo.capacityTonnage}}</td>
            </tr>
            <tr>
              <td>道路运输证号</td>
              <td>{{carInfo.shippingCert}}</td>
            </tr>
            <tr>
              <td>车辆类型</td>
              <td>{{carInfo.vehicleClassification}}</td>
            </tr>
            <tr>
              <td>长度</td>
              <td>{{carInfo.vehicleLength}}</td>
            </tr>
            <tr>
              <td>高度</td>
              <td>{{carInfo.vehicleHeight}}</td>
            </tr>
            <tr>
              <td>宽度</td>
              <td>{{carInfo.vehicleWidth}}</td>
            </tr>
            <tr>
              <td>营业范围</td>
              <td>{{carInfo.businessScope}}</td>
            </tr>
            <tr>
              <td>注册日期</td>
              <td>{{carInfo.licenseInitialDate}}</td>
            </tr>
            <tr>
              <td>有效期</td>
              <td>{{carInfo.periodStartDate}}-{{carInfo.periodEndDate}}</td>
            </tr>
            <tr>
              <td>营业状态</td>
              <td>{{carInfo.businessState}}</td>
            </tr>
          </table>
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane label="车辆位置信息查询" name="second">
      <div class="carRouteInfo">
        <el-form :model="form" label-width="100px">
          <el-form-item label="车牌号:" required >
            <el-input v-model.trim="form.carNum" placeholder="请输入车牌号" maxlength="8" style="width:200px"></el-input>
          </el-form-item>
          <el-form-item label="车牌颜色" required prop="plateColor">
            <el-select v-model="form.plateColor">
              <el-option 
                v-for="item in plateColorList"
                :key="item.plateColorCode"
                :label="item.plateColorMessage"
                :value="item.plateColorCode"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="时间段:" required>
            <el-date-picker
              v-model="value2"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :picker-options="pickerOptions"
              range-separator="———"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              align="right"
              @change="changeDate"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button @click="queryRoute">查询</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="carRoute">
        <div>
          <div class="amap-page-container">
            <div id="amap-show" v-show="isShowMap" class="amap-demo" style="height: 600px;"></div>
          </div>
        </div>
      </div>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import { lazyAMapApiLoaderInstance } from "vue-amap"; //地图的包
export default {
  data() {
    return {
      activeName: "first",
      carNum: "", //第一个选项卡下的车牌号
      carInfoFlag: false,
      form: {
        carNum: "",
        plateColor: ""
      },
      carInfo: {},
      value2: "", //时间
      startTime: "", //开始时间
      endTime: "", //结束时间
      //限制时间范围选择(两天)
      pickerMinDate: "",
      pickerOptions: {
        //onPick当type="datetimerange"时有效
        onPick: ({ maxDate, minDate }) => {
          this.pickerMinDate = minDate.getTime();
          if (maxDate) {
            this.pickerMinDate = "";
          }
        },
        disabledDate: time => {
          if (this.pickerMinDate != "") {
            let tow = 2 * 24 * 3600 * 1000; //重点是在这里计算相差几天
            let minTime = this.pickerMinDate - tow;
            let maxTime = this.pickerMinDate + tow;
            return time.getTime() < minTime || time.getTime() > maxTime;
          }
        }
      },
      plateColorList: [],
      isShowMap: false
    };
  },
  activated() {
    this.getPlateColorList()
  },
  methods: {
    changeDate(val) {
      const d = new Date(val[1]).getTime()
      const today = new Date().getTime()
      if (d - today > 0) {
       this.value2 = [this.value2[0], this.currentDate()]
      } 
    },
    currentDate() {
      let d = new Date()
      let month = d.getMonth() + 1
      if (month < 10) { month = '0' + month}
      let date = d.getDate()
      if (date < 10) { date = '0' + date }
      let datetime = d.getFullYear() + '-' + month + '-' + date + ' ' + d.getHours() + ':' + d.getMinutes() + ':' + d.getSeconds()
      return datetime
    },
    getPlateColorList() {
      this.$get('/admin-center-server/car/driving/getPlateColorList').then(
        res => {
          this.plateColorList = res
          this.form.plateColor = '2'
        }
      )
    },
    handleClick(tab, event) {
      console.log(tab.name);
    },
    /* 车辆位置查询 */
    queryRoute() {
      // console.log(this.form.carNum);
      // console.log(this.value2);
      this.startTime = this.value2[0];
      this.endTime = this.value2[1];
      if (this.form.carNum == "") {
        this.$message.warning("请填写车牌号进行查询");
        return;
      }
      if (this.form.plateColor == "") {
        this.$message.warning("请选择车牌颜色");
        return;
      }
      if (this.value2 == "") {
        this.$message.warning("请选取时间进行查询");
        return;
      }
      this.initPage();
    },
    /* 获取查询路线 */

    /* 轨迹路线图 */
    initPage() {
      //根据时间和车牌号获取智云的GPS信息
      var that = this;
      this.$http
        .post(
          "/admin-center-server/app/orderItemGps/getGpsVO?vclN=" +
            this.form.carNum +
            "&plateColor=" +
            this.form.plateColor +
            "&qryBtm=" +
            this.startTime +
            "&qryEtm=" +
            this.endTime
        )
        .then(
          res => {
            if (res.data.code == 200) {
              this.isShowMap = true
            } else {
              this.isShowMap = false
              this.$message.warning(res.data.message);
              return
            }
            
            // console.log(res.data.data,"3333")
            var positionObj = res.data.data;
            // console.log(typeof res.data.data);
            var positionArr = [];
            for (var i = 0; i < positionObj.length; i++) {
              var a = [];
              a.push(
                positionObj[i].longitudeDegree,
                positionObj[i].latitudeDegree
              );
              positionArr.push(a);
            }
              this.map = new AMap.Map(
                "amap-show",
                {
                  center: [112.405289, 40.904987],
                  zoom: 6
                },
                AMapUI.loadUI(["misc/PathSimplifier"], PathSimplifier => {
                  if (!PathSimplifier.supportCanvas) {
                    alert("当前环境不支持 Canvas！");
                    return;
                  }

                  //创建组件实例
                  var pathSimplifierIns = new PathSimplifier({
                    zIndex: 100,
                    map: this.map, //所属的地图实例
                    getPath: pathData => {
                      //将gps描述的所有的点数放到全局变量
                      pathData.path.length; //返回轨迹数据中的节点坐标信息，[AMap.LngLat, AMap.LngLat...] 或者 [[lng|number,lat|number],...]
                      return pathData.path;
                    },
                    getHoverTitle: (pathData, pathIndex, pointIndex) => {
                      //返回鼠标悬停时显示的信息
                      if (pointIndex >= 0) {
                        //鼠标悬停在某个轨迹节点上
                        return (
                          pathData.name +
                          "，点:" +
                          pointIndex +
                          "/" +
                          pathData.path.length
                        );
                      }
                      //鼠标悬停在节点之间的连线上
                      return pathData.name + "，点数量" + pathData.path.length;
                    },
                    renderOptions: {
                      //轨迹线的样式
                      pathLineStyle: {
                        lineWidth: 6,
                        strokeStyle: "#C11534",
                        borderWidth: 1,
                        borderStyle: "#cccccc",
                        dirArrowStyle: true
                      }
                    }
                  });

                  //这里构建两条简单的轨迹，仅作示例
                  // var gpsData = eval(data);
                  let gpsData = positionArr;
                  debugger;
                  console.log(gpsData);
                  pathSimplifierIns.setData([
                    {
                      name: "我是一条轨迹",
                      path: gpsData
                    }
                  ]);
                  //第一个位置是图片
                  var nav = pathSimplifierIns.createPathNavigator(0, {
                    loop: true,
                    speed: 400000,
                    pathNavigatorStyle: {
                      width: 16,
                      height: 32,
                      content: PathSimplifier.Render.Canvas.getImageContent(
                        "",
                        onload,
                        onerror
                      ),
                      strokeStyle: null,
                      fillStyle: null
                    }
                  });
                  nav.start();
                })
              );
          },
          error => {
            this.isShowMap = false
            this.$message.warning(error.message);
          }
        );
    },
    msg() {
      this.$notify.info({
        title: "提示",
        message: "暂无轨迹数据"
      });
    },
    /* 第一个选项卡获取车辆信息 */
    getCarInfo() {
      this.$http
        .get(
          "admin-center-server/admin/db_car/db/car?plateNumber=" + this.carNum
        )
        .then(res => {
          console.log(res);
          if (res.data.code == 200) {
            if (res.data.data == null) {
              this.carInfoFlag = false;
              this.$message.warning("没有查到该车辆的信息");
            } else {
              this.carInfoFlag = true;
              this.carInfo = res.data.data;
            }
          } else {
            this.$message.warning(res.data.message);
          }
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
table {
  border-collapse: collapse;
  width: 600px;
  background: #ffffff;
  margin: 20px;
} //去掉百表格的间隙
table th,
table td {
  height: 40px;
  line-height: 40px;
  text-align: center;
}
table td:nth-child(odd) {
  background: #cccccc;
}
.carNum,
.carRouteInfo {
  margin-left: 50px;
}
.amap-page-container {
  width: 96%;
  margin: 0 auto;
}
</style>
