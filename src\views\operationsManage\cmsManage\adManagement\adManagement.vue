<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">筛选查询--广告</div>
      <div class="select-info">
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="120px">
          <el-form-item label="广告名称:">
            <el-input v-model="formInline.title" placeholder="标题关键字"></el-input>
          </el-form-item>
          <el-form-item label="广告位置:">
            <el-select v-model="formInline.adpositionId" placeholder="请选择" clearable>
              <el-option
                v-for="item in positons"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="类型:" label-width="100px">
            <el-select v-model="formInline.type" placeholder="请选择">
              <el-option
                v-for="item in types"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="date" required>
            <el-date-picker
              v-model="value1"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="到期日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
            ></el-date-picker>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
          </el-form-item>
        </el-form>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          label-width="150px"
          style="margin-top: 30px"
        ></el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
        <div>
          <el-button class="releaseMessage" @click="addAdvertise">添加</el-button>
        </div>
      </div>
      <div class="list-main">
        <template>
          <el-table
            ref="table"
            :data="tableData"
            tooltip-effect="dark"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            border
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column prop="title" label="标题"></el-table-column>
            <el-table-column label="展示图片" style="text-align: center">
              <template slot-scope="scope">
                <img :src="scope.row.path" width="100" title="未传图片" />
              </template>
            </el-table-column>

            <el-table-column prop="createdDate" label="创建日期"></el-table-column>

            <el-table-column prop="name" label="位置"></el-table-column>
            <el-table-column prop="beginDate" label="开始时间"></el-table-column>
            <el-table-column prop="endDate" label="结束时间"></el-table-column>
            <el-table-column prop="isShow" label="显示">
              <template scope="scope">
                <el-switch
                  on-text="是"
                  off-text="否"
                  on-color="#5B7BFA"
                  off-color="#dadde5"
                  v-model="scope.row.isShow"
                  @change="change(scope.$index,scope.row)"
                ></el-switch>
              </template>
            </el-table-column>

            <el-table-column fixed="right" label="操作" width="160">
              <template slot-scope="scope">
                <!-- <el-button @click="goDetail(scope.row)" type="text" size="small">查看</el-button> -->
                <el-button type="text" size="small" @click="editor(scope.row)">编辑</el-button>
                <el-button type="text" size="small" @click="delItem(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="flex">
            <div class="delAll">
              <el-button @click="deleteAll">批量删除</el-button>
            </div>
            <div class="pageSize">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="formInline.pageNumber"
                :page-sizes="[20, 40, 60, 80,100]"
                :page-size="formInline.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                style="margin: 10px auto"
              ></el-pagination>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value1: "",
      currentPage4: 4,
      total: null,
      positons: [
        {
          id: "0",
          name: "banner-货主端"
        },
        {
          id: "1",
          name: "启动页—货主端"
        },
        {
          id: "2",
          name: "启动页—调度员端"
        },
        {
          id: "3",
          name: "启动页—司机端"
        }

      ],

      types: [
        {
          value: "0",
          label: "图片"
        },
        {
          value: "1",
          label: "文本"
        }
      ],
      value: "",

      formInline: {
        title: "",
        adpositionId: "", //广告位置
        type: "", //类型
        pageNumber: 1,
        pageSize: 20
      },
      date: "",
      tableData: [],
      multipleSelection: [] //多选的值
    };
  },
  activated() {
    this.getDataList(); //获取列表数据

  },
  methods: {
    /* 查询 */
    onSubmit() {
      // console.log(this.value1, "日期");
      this.formInline.pageNumber =1;
      if (this.value1) {
        this.formInline.beginDate = this.value1[0];
        this.formInline.endDate = this.value1[1];
      }
      this.getDataList();
    },

    /* 多选的值 */
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    /* 选取每页多少条数据 */
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.formInline.pageNumber =1;
      this.formInline.pageSize = JSON.parse(`${val}`);
      console.log(this.formInline.pageSize);
      this.getDataList();
    },
    /* 翻页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.formInline.pageNumber = JSON.parse(`${val}`);
      console.log(this.formInline.pageNumber);
      this.getDataList();
    },
    /* 获取数据 */
    getDataList() {
      var postData = this.formInline;
      console.log(postData, "postData");
      this.$http
        .post("/admin-center-server/ad/queryAdList", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            console.log(data);
            this.tableData = data.data.list;
            this.total = JSON.parse(data.data.total);
            console.log(this.tableData, "tableData----");
          } else {
            this.$message.warning(data.message);
          }
        });
    },

    /* 添加广告 */
    addAdvertise() {
      this.$router.push({
        path: "addAdvertise",
        query: {
          typeFlag: 1
        }
      });
    },
    /* 单个删除功能 */
    delItem(row) {
      console.log(row.id);
      let id = row.id;
      console.log(row.id, "-----id");
      let ids = [];
      ids[0] = Number(row.id);

      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http
            .post("/admin-center-server/ad/deleteByAdIds", ids)
            .then(res => {
              let data = res.data;
              if (data.code === "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
              }
              this.getDataList();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    /* 批量删除 */
    deleteAll() {
      var deleteAllData = this.multipleSelection; //批量删除的数据
      let ids = deleteAllData.map(obj => {
        return obj.id;
      });
      this.dialogVisible = true;
      this.$confirm("此操作将永久删除本条数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.$http
            .post("/admin-center-server/ad/deleteByAdIds", ids)
            .then(res => {
              let data = res.data;
              if (data.code == "200") {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
              }
              this.getDataList();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    /* 表格中的开关 */
    change: function(index, row) {
      console.log(index, row);

      var postData = {
        id: row.id,
        isShow: row.isShow
      };
      this.$http
        .post("/admin-center-server/ad/isShow", this.$qs.stringify(postData))
        .then(res => {
          let data = res.data;
          if (data.code == "200") {
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.getDataList();
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /*  编辑 */
    editor(row) {
      console.log(row);
      this.$router.push({
        path: "addAdvertise",
        query: {
          id:row.id,
          typeFlag:2
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }
    .flex {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
