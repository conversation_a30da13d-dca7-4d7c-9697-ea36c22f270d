<template>
    <div class="app-container receiptDeatil">
        <div class="title">
          <div>
              电子回单详情
          </div>
        </div>
        <div class="mainInfo">
            <div class="inner_title">
                回单信息
            </div>
            <div class="inner_info">
               <ul class="info1">
                   <li>流水号</li>
                   <li>货主手机号</li>
                   <li>付款方名称</li>
                   <li>付款方会员代码</li>
                   <li>付款方用户类型</li>
                   <li>货主名称</li>
                   <li>关联订单编号</li>
                   <li>关联运单编号</li>
               </ul>
                <ul class="info2">
                    <li>{{frontSeqNo}}</li>
                    <li>{{outMobile}}</li>
                    <li>天津鸿飞达科技有限公司东疆保税港区分公司</li>
                    <li>{{outUserSn}}</li>
                    <li>平台</li>
                    <li>{{outUsername}}</li>
                    <li>{{ddOrderNo}}</li>
                    <li>{{ydOrderNo}}</li>
                </ul>
                <ul class="info1">
                    <li>账务处理日期</li>
                    <li>收款方手机号</li>
                    <li>收款方名称</li>
                    <li>收款方会员代码</li>
                    <li>收款方用户类型</li>
                    <li>收款方户名</li>
                    <li>关联调度员单编号</li>
                    <li>交易金额</li>
                </ul>
                <ul class="info2">
                    <li>{{tranTime}}</li>
                    <li>{{inMobile}}</li>
                    <li>{{inName}}</li>
                    <li>{{inUserSn}}</li>
                    <li>{{inType}}</li>
                    <li>{{inUsername}}</li>
                    <li>{{jjOrderNo}}</li>
                    <li>{{tranAmt}}</li>
                </ul>
            </div>
        </div>
         <div class="voucher">
             银行电子回单凭证
         </div>
        <div class="downloadImgDown" id="export_content">
             <div class="title_dl">
                 <div class="title_logo">
                     <div class="logo_box">

                     </div>
                 </div>
                 <div class="title_word">
                   分账账户明细
                 </div>
                 <div class="title_right">
                     回单凭证
                 </div>
             </div>
            <div class="main_info">
                <div class="itemK">
                    <div class="left">财务处理日期: <span>{{tranTime}}</span></div>
                    <div class="right">流水号: <span>{{frontSeqNo}}</span></div>
                </div>
                <div class="itemK">
                    <div class="left">分账账户付款方编号: <span>{{outSubAcctNo}}</span></div>
                    <div class="right">分账账户收款方编号: <span>{{inSubAcctNo}}</span></div>
                </div>
                <div class="itemK">
                    <div class="left">付款方户名: <span>天津鸿飞达科技有限公司东疆保税港区分公司</span></div>
                    <div class="right">收款方户名: <span>{{inUsername}}</span></div>
                </div>
                <div class="itemK">
                    <div class="left">付款方平台端会员代码: <span>{{outUserSn}}</span></div>
                    <div class="right">收款方平台端会员代码: <span>{{inUserSn}}</span></div>
                </div>
                <div class="itemK">
                    <div class="left">金额 (大写) : <span>{{RMB}}</span></div>
                    <div class="right">小写: <span>{{tranAmt}}</span></div>
                </div>
                <div class="itemK">
                    <div class="left">项目专管汇总账户账号: <span>15500328130041</span></div>
                    <div class="right">项目专管汇总账户户名: <span>天津鸿飞达科技有限公司东疆保税港区分公司</span></div>
                </div>
                <div class="itemK" style="border: none">摘要:会员间交易</div>
            </div>
            <div class="remark">
              <div>
                  备注：本凭证所述分账账户付款方和收款方编号，仅用于我行在接受付款方和收款方所 <br> 属交易平台委托的情况下，依据平台交易和结算指令进行分账处理，记载分账状态、明 <br> 细等信息。
              </div>
                <div class="zhang">

                </div>
            </div>
        </div>
        <div style="float: right;margin-top: 20px">
            <el-button type="primary" size="mini" icon="el-icon-download" @click="downLoad">下载</el-button>
        </div>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                frontSeqNo:'',
                outMobile:'',
                outName:'',
                outUserSn:'',
                outType:'',
                outUsername:'',
                ddOrderNo:'',
                ydOrderNo:'',
                tranTime:'',
                inMobile:'',
                inName:'',
                inUserSn:'',
                inType:'',
                inUsername:'',
                jjOrderNo:'',
                tranAmt:'',
                outSubAcctNo:'',
                inSubAcctNo:'',
                RMB:'',
            }
        },
        methods: {
            downLoad(){
                html2canvas(
                    document.getElementById('export_content'),
                    {
                        onrendered: function (canvas) {
                            //未生成pdf的html页面高度
                            let leftHeight = canvas.height;
                            let a4Width = 595.28;
                            let a4Height = 841.89;
                            //一页pdf显示html页面生成的canvas高度;
                            let a4HeightRef = Math.floor(canvas.width / a4Width * a4Height);
                            //pdf页面偏移
                            let position = 0;
                            let pageData = canvas.toDataURL('image/jpeg', 1.0);
                            let pdf = new jsPDF('x', 'pt', 'a4');
                            let index = 1,
                                canvas1 = document.createElement('canvas'),
                                height;
                            pdf.setDisplayMode('fullwidth', 'continuous', 'FullScreen');
                            let pdfName='回单';
                            function createImpl(canvas) {
                                if (leftHeight > 0) {
                                    index++;
                                    var checkCount = 0;
                                    if (leftHeight > a4HeightRef) {
                                        var i = position + a4HeightRef;
                                        for (i = position + a4HeightRef; i >= position; i--) {
                                            var isWrite = true
                                            for (var j = 0; j < canvas.width; j++) {
                                                var c = canvas.getContext('2d').getImageData(j, i, 1, 1).data;

                                                if (c[0] != 0xff || c[1] != 0xff || c[2] != 0xff) {
                                                    isWrite = false
                                                    break
                                                }
                                            }
                                            if (isWrite) {
                                                checkCount++
                                                if (checkCount >= 10) {
                                                    break
                                                }
                                            } else {
                                                checkCount = 0
                                            }
                                        }
                                        height = Math.round(i - position) || Math.min(leftHeight, a4HeightRef);
                                        if(height<=0){
                                            height = a4HeightRef;
                                        }
                                    } else {
                                        height = leftHeight;
                                    }

                                    canvas1.width = canvas.width;
                                    canvas1.height = height;

                                    // console.log(index, 'height:', height, 'pos', position);

                                    var ctx = canvas1.getContext('2d');
                                    ctx.drawImage(canvas, 0, position, canvas.width, height, 0, 0, canvas.width, height);

                                    var pageHeight = Math.round(a4Width / canvas.width * height);
                                    // pdf.setPageSize(null, pageHeight)
                                    if(position != 0){
                                        pdf.addPage();
                                    }
                                    pdf.addImage(canvas1.toDataURL('image/jpeg', 1.0), 'JPEG', 0, 0, a4Width, a4Width / canvas1.width * height);
                                    leftHeight -= height;
                                    position += height;
                                    if (leftHeight > 0) {
                                        setTimeout(createImpl, 500, canvas);
                                    } else {
                                        pdf.save(pdfName + '.pdf');
                                    }
                                }
                            }
                            //当内容未超过pdf一页显示的范围，无需分页
                            if (leftHeight < a4HeightRef) {
                                pdf.addImage(pageData, 'JPEG', 0, 0, a4Width, a4Width / canvas.width * leftHeight);
                                pdf.save(pdfName + '.pdf')
                            } else {
                                try {
                                    pdf.deletePage(0);
                                    setTimeout(createImpl, 500, canvas);
                                } catch (err) {
                                    console.log(err);
                                }
                            }
                        },
                        background: "#fff",
                    }
                )
            },
            getDataList(){
                let id = this.$route.query.id;
                this.$http.post('/admin-center-server/finance/getReconciliationTransactionById?id='+id).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                        this.frontSeqNo = data.data.frontSeqNo;
                        this.outMobile = data.data.outMobile;
                        this.outName = data.data.outName;
                        this.outUserSn = data.data.outUserSn;

                        let outType =  data.data.outType;
                        if(outType=='0'){
                            this.outType = '平台';
                        }else if(outType=='1'){
                            this.outType = '货主';
                        }else if(outType=='2'){
                            this.outType = '调度员';
                        }else if(outType=='3'){
                            this.outType = '司机';
                        }else if(outType=='2'){
                            this.outType = '车主';
                        }else {
                            this.outType = '暂无';
                        }


                        this.outUsername = data.data.outUsername;
                        this.ddOrderNo = data.data.ddOrderNo;
                        this.ydOrderNo = data.data.ydOrderNo;
                        this.tranTime = data.data.tranTime;
                        this.inMobile = data.data.inMobile;
                        this.inName = data.data.inName;
                        this.inUserSn = data.data.inUserSn;
                        let inType =  data.data.inType;
                        if(inType=='0'){
                            this.inType = '平台';
                        }else if(inType=='1'){
                            this.inType = '货主';
                        }else if(inType=='2'){
                            this.inType = '调度员';
                        }else if(inType=='3'){
                            this.inType = '司机';
                        }else if(inType=='2'){
                            this.inType = '车主';
                        }else {
                            this.inType = '暂无';
                        }


                        this.inUsername = data.data.inUsername;
                        this.jjOrderNo = data.data.jjOrderNo;
                        this.tranAmt = data.data.tranAmt;
                        this.outSubAcctNo = data.data.outSubAcctNo;
                        this.inSubAcctNo = data.data.inSubAcctNo;

                        let money = data.data.tranAmt;
                        let fraction = ['角','分'];
                        let digit = ['零','壹','贰','叁','肆','伍','陆','柒','捌','玖'];
                        let unit = [['元','万','亿'],['','拾','佰','仟']];
                        let head = money < 0?'欠':'';
                        money = Math.abs(money);
                        let s = '';
                        for(let i = 0; i < fraction.length; i++){
                            s += (digit[Math.floor(money * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
                        }
                        s = s || '整';
                        money = Math.floor(money);
                        for(let i = 0; i < unit[0].length && money > 0; i++){
                            let p = '';
                            for (let j = 0; j < unit[1].length && money > 0; j++) {
                                p = digit[money % 10] + unit[1][j] + p;
                                money = Math.floor(money / 10);
                            }
                            s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
                        }
                        this.RMB= head + s.replace(/(零.)*零元/,'元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
                    }
                })
            },
        },
        activated() {
            this.getDataList()
        }
    }
</script>

<style>
    .downloadImgDown {
        margin-left: 20px;
        margin-right: 20px;
        margin-top: 10px;
        font-family: Monospace;
        background-color: #ffffff;
        padding-left: 20px;
        padding-right: 20px;
    }
    .downloadImgDown .title_dl {
        height: 130px;
        background-color: #ffffff;
        border-bottom: 3px solid #FF0000;
        padding-top: 10px;
        display: flex;
    }
    .downloadImgDown .title_dl .title_logo {
        width: 1000px;
    }
    .downloadImgDown .title_dl .title_logo .logo_box {
        width: 310px;
        height: 110px;
        background: url("img/pingan.png") no-repeat;
        background-size: 100% 100%;
    }
    .downloadImgDown .title_word {
        width: 1000px;;
        text-align: center;
        font-size: 26px;
        height: 70px;
        line-height: 70px;
        margin-top: 60px;
    }
    .downloadImgDown .title_right {
        /*font-family: "Noto Sans SC";*/
        width: 1000px;
        margin-right: 20px;
        margin-top: 70px;
        text-align: right;
        font-weight: 500;
        font-size: 29px;
    }
    .downloadImgDown .itemK {
        font-size: 16px;
        height: 60px;
        line-height: 60px;
        border-bottom: 1px dashed #DEDEDE;
        /*font-family: "Noto Sans SC";*/
        color: #000000;
        display: flex;
    }
    .downloadImgDown .left {
        width: 1000px;
    }
    .downloadImgDown .right {
        width: 1000px;
    }
    .downloadImgDown .zhang {
        position: absolute;
        width: 200px;
        height: 91px;
        background: url("img/qianzhang.png") no-repeat;
        background-size: 100% 100%;
        left: 800px;
        top: 0px
    }
    .downloadImgDown .remark {
        /*font-family: "Noto Sans SC";*/
        font-size: 20px;
        color: #000000;
        line-height: 30px;
        border-bottom: 2px solid #141414;
        padding-top: 10px;
        padding-bottom: 10px;
        position: relative;
    }
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
    .receiptDeatil{
        overflow: hidden;
        margin-bottom: 20px;
        .title{
            background-color: #ffffff;
            height: 60px;
            border: 1px solid #cccccc;
            line-height: 60px;
            div{
                width: 160px;
                border-right: 1px solid #cccccc;
                height: 60px;
                line-height: 60px;
                text-align: center;
                font-weight: 700;
                font-size: 20px;
            }
        }
        .mainInfo{
            border: 1px solid #cccccc;
            margin-top: 20px;
            background-color: #ffffff;
            padding: 20px;
            .inner_title{
                height: 40px;
                line-height: 40px;
                font-size: 14px;
                color: #000000;
                font-weight: 400;
            }
            .inner_info{
                border-top: 1px solid #cccccc;
                border-right: 1px solid #cccccc;
                display: flex;
                ul{
                    list-style: none;
                    width: 500px;
                    padding: 0;
                    margin: 0;
                    font-size: 14px;
                    li{
                        height: 40px;
                        line-height: 40px;
                    }
                }
                .info1{
                    width: 170px;
                    background-color: rgb(245,245,245);
                    text-align: right;
                    font-weight: 600;
                    border-right: 1px solid #cccccc;
                    border-left: 1px solid #cccccc;
                    li{
                        border-bottom: 1px solid #cccccc;
                        padding-right: 20px;
                    }
                }
                .info2{
                    li{
                        border-bottom: 1px solid #cccccc;
                        /*padding-right: 20px;*/
                        padding-left: 20px;

                    }
                }
            }
        }
        .voucher{
            margin-top: 20px;
            /*background-color: #ffffff;*/
            height: 30px;
            line-height: 30px;
            padding-left: 20px;
            font-size: 14px;
        }
    }
</style>
