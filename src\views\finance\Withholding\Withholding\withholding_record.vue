<template>
  <!-- 调度员 -->
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">代扣代缴查询</div>
      <div class="select-info">
        <el-form :inline="true"
                 :model="formInline"
                 class="demo-form-inline"
                 size="mini"
                 label-width="90px">
          <el-form-item>
            <el-button class="left"
                       @click="onSubmit">导出</el-button>
          </el-form-item>
        </el-form>

      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table ref="multipleTable"
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                    border>
            <el-table-column type="index"
                             label="序号"
                             width="55">
            </el-table-column>
            <el-table-column show-overflow-tooltip
                             v-for="item in tableLabel"
                             :label="item.label"
                             :width="200">
              <template slot-scope="scope">
                <span style="margin-left: 10px;cursor:pointer;"
                      v-if="item.label=='用户名称'"
                      @click="goDriverDetailsfn(scope.row)">{{ scope.row[item.prop] }}</span>
                <span style="margin-left: 10px;cursor:pointer;"
                      v-else-if="item.label=='运单号'"
                      @click="goWaybillDetailsfn(scope.row)">{{ scope.row[item.prop] }}</span>
                <span v-else
                      style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
              </template>
            </el-table-column>

          </el-table>

        </template>
      </div>
    </div>
  </div>
</template>
<script>
const list = '/admin-center-server/taxFrozen/queryTaxOrderItemPageList'//列表
const exportTax = '/admin-center-server/taxFrozen/exportTaxOrderItem'//导出
export default {
  name: "CarsList",
  data () {
    return {
      year: '',
      revenueType: '',
      id: '',
      tableLabel: [
        {
          prop: 'userName',
          label: '用户名称'
        },
        {
          prop: 'idCard',
          label: '身份证号／企业信用代码'
        },
        {
          prop: 'userTypeText',
          label: '角色类型'
        },
        {
          prop: 'mobile',
          label: '手机号'
        },
        {
          prop: 'orderItemSn',
          label: '运单号'
        },
        {
          prop: 'deliveryPlace',
          label: '起运地'
        },
        {
          prop: 'receivePlace',
          label: '到达地'
        },
        {
          prop: 'paymentDate',
          label: '付款时间'
        },
        {
          prop: 'paymentAmount',
          label: '付款金额（元）'
        },
        {
          prop: 'vat',
          label: '增值税'
        },
        {
          prop: 'threeTaxTotal',
          label: '城建税及两项附加'
        },
        {
          prop: 'individualIncomeTax',
          label: '个人所得税'
        },
        {
          prop: 'cargoType',
          label: '货物名称'
        },
        {
          prop: 'consigneeName',
          label: '托运人名称'
        },
        {
          prop: 'consignerName',
          label: '收货人名称'
        }
      ],
      formInline: {},
      tableData: []
    };
  },
  methods: {
    //导出
    onSubmit () {
      this.$http.get(exportTax + `?revenueType=${this.revenueType}&yearDateString=${this.year}`).then(res => {
        window.location.href = res.data.data
      })
    },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },
    //跳转调度员详情
    goDriverDetailsfn (row) {
      //brokerAuthType 目前没有
      if (row.brokerAuthType == '个人') {
        row.driverType = '1'
      } else {
        row.driverType = '2'
      }
      this.$router.push("/agentAccount/accountDetails?id=" + row.userId + '&type=' + row.driverType);
    },
    //跳转运单详情
    goWaybillDetailsfn (row) {
      this.$router.push({
        path: "/transport/transportListDetail",
        query: {
          orderItemId: row.orderItemId,//运单id
          status: row.status,//订单状态
          freezeStatus: row.freezeStatus//0未冻结1冻结
        }
      });
    },
    getData () {
      var url = list + `?taxOrderItemSumId=${this.id}`
      this.$http.get(url).then(res => {
        var data = res.data.data
        for (var i = 0; i < data.list.length; i++) {
          if (data.list[i].userType == 2) {
            data.list[i].userTypeText = '调度员'
          } else if (data.list[i].userType == 3) {
            data.list[i].userTypeText = '司机'
          } else {
            data.list[i].userTypeText = '车主'
          }
        }
        //this.tableData = data.list
        this.tableData = [
          {
            "cargoType": "货品类型",
            "consigneeName": "的就看到",
            "consignerName": "可打开了",
            "deliveryPlace": "快乐大老大拉拉队",
            "freezeStatus": 0,
            'userTypeText': '司机',
            "idCard": "123131313213",
            "individualIncomeTax": 0,
            "mobile": "183183913131",
            "orderItemId": 631,
            "orderItemSn": "2390130103930131",
            "paymentAmount": 90,
            "paymentDate": "2020-1-90",
            "plateNumber": "京DKLAKDL",
            "receivePlace": "打开啦看到啦",
            "status": '',
            "statusEnum": "",
            "threeTaxTotal": 0,
            "userId": 91,
            "userName": "小今天",
            "userType": 0,
            "vat": 10
          }
        ]
      })
    }
  },
  activated () {
    this.year = this.$route.query.year
    this.revenueType = this.$route.query.revenueType
    this.id = this.$route.query.id
    this.getData()
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
