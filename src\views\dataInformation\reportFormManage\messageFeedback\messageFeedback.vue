<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">
        <div>筛选查询</div>
        <div class="button">
          <el-button
            class="left"
            icon="el-icon-search"
            size="mini"
            type="primary"
            @click="queryFun"
          >查询</el-button>
          <el-button
            class="left"
            icon="el-icon-delete"
            size="mini"
            type="danger"
            @click="resetForm"
          >清空筛选</el-button>
        </div>
      </div>
      <div class="select-info">
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="120px">
          <el-form-item label="留言反馈类型:">
            <el-select v-model="formInline.feedbackType" placeholder="请选择留言反馈类型" clearable>
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="反馈用户类型:">
            <el-select v-model="formInline.userType" placeholder="请选择反馈用户类型" clearable>
              <el-option
                v-for="item in userTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="用户手机号：">
            <el-input
              type="text"
              maxlength="11"
              v-model="formInline.mobile"
              oninput="value=value.replace(/[^\d]/g,'')"
              placeholder="请输入用户手机号"
            />
          </el-form-item>
          <el-form-item label="回复状态：">
            <el-select v-model="formInline.replyStatus" placeholder="请选择回复状态">
              <el-option
                v-for="item in replyStatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>数据列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table :data="tableData" border style="width: 100%" :height="tableHeight" ref="table">
            <el-table-column type="index" label="序号" width="50"></el-table-column>
            <el-table-column prop="feedbackTypeName" label="留言反馈类型" width="180"></el-table-column>
            <el-table-column prop="userName" label="反馈用户名称" width="180"></el-table-column>
            <el-table-column prop="userTypeName" label="反馈用户类型" width="180"></el-table-column>
            <el-table-column prop="mobile" label="反馈人手机号" width="180"></el-table-column>
            <el-table-column prop="feedbackContent" label="反馈信息" width="300"></el-table-column>
            <el-table-column prop="callInfo" label="联系方式" width="150"></el-table-column>
            <el-table-column label="图片" width="80">
              <template slot-scope="scope">
                <el-button @click="scanPhoto(scope.row)" type="text" size="small">查看图片</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="replyStatus" label="回复状态" width="160">
              <template slot-scope="scope">
                <p v-if="scope.row.replyStatus == 1">未回复</p>
                <p v-else-if="scope.row.replyStatus == 2">已回复</p>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <el-button @click="reply(scope.row)" type="text" size="small">回复</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="page">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInline.pageNumber"
              :page-sizes="[20, 40, 60, 80,100]"
              :page-size="formInline.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              style="margin: 10px auto"
            ></el-pagination>
          </div>
        </template>
      </div>

      <!-- 展示多张图片的弹窗 -->
      <el-dialog title="图片详情" :visible.sync="dialogPhoto">
        <ul class="photoList">
          <li v-for="(item,index) in imgArr" :key="index">
            <!-- imgurl里放的是前面的http地址，在data里定义 -->
            <img :src="item" alt />
          </li>
        </ul>
      </el-dialog>

      <el-dialog title="回复留言" :visible.sync="dialogReply">
        <el-form :model="replyForm" label-width="100px" class="replyForm">
          <el-form-item label="反馈时间：">
            <el-input v-model="replyForm.feedbackTime" style="width:200px" disabled></el-input>
          </el-form-item>
          <el-form-item label="用户手机号：">
            <el-input v-model="replyForm.mobile" style="width:200px" disabled></el-input>
          </el-form-item>
          <el-form-item label="用户姓名：">
            <el-input v-model="replyForm.userName" style="width:300px" disabled></el-input>
          </el-form-item>
          <el-form-item label="用户类型：">
            <el-input v-model="replyForm.userTypeName" style="width:200px" disabled></el-input>
          </el-form-item>
          <el-form-item label="反馈类型：">
            <el-input v-model="replyForm.feedbackTypeName" style="width:200px" disabled></el-input>
          </el-form-item>
          <el-form-item label="联系方式：">
            <el-input v-model="replyForm.callInfo" style="width:200px" disabled></el-input>
          </el-form-item>
          <el-form-item label="反馈图片：">
            <div style="display:flex">
              <ul class="pictures" v-if="replyForm.imageUrls && replyForm.imageUrls.length >0">
                <li v-for="(item,index) in replyForm.imageUrls" :key="index">
                  <!-- imgurl里放的是前面的http地址，在data里定义 -->
                  <img :src="item" alt />
                </li>
              </ul>
              <p v-if=" replyForm.imageUrls && replyForm.imageUrls.length==0">无图片</p>
              <span
                @click="pictureDetail"
                class="largePicture"
                v-if="replyForm.imageUrls && replyForm.imageUrls.length >0"
              >预览</span>
            </div>
          </el-form-item>

          <el-form-item label="回复留言：">
            <el-input
              type="textarea"
              v-model="replyContent"
              :rows="4"
              @input="replyCntentInput"
              maxlength="200"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogReply = false">取 消</el-button>
          <el-button type="primary" @click="replySure">确 定</el-button>
        </div>
      </el-dialog>

      <el-image-viewer
        :on-close="closeViewer"
        :url-list="pictures"
        style="z-index:9999"
        v-if="dialogImg"
      />
      <!-- 图片预览详情 -->
      <el-dialog title="图片预览" :visible.sync="picDetail"></el-dialog>
    </div>
  </div>
</template>

<script>
import ElImageViewer from "cf-element-ui/packages/image/src/image-viewer";
export default {
  name: "CarsList",
  components: { ElImageViewer },
  data() {
    return {
      formInline: {
        feedbackType: "", //运单号
        userType: "", //司机名称
        pageSize: 20,
        pageNumber: 1,
        id: "", //点击时获取的id
        mobile: "", //用户手机号
        replyStatus: "" //回复状态
      },
      /* 图片左右移动 */

      dialogPhoto: false, //展示多张图的弹窗
      dialogReply: false, //回复留言弹窗
      picDetail: false, //图片预览
      dialogImg: false,
      imgArr: [],
      replyText: "",

      options: [
        {
          value: "0",
          label: "结算相关"
        },
        {
          value: "1",
          label: "收货问题"
        },
        {
          value: "2",
          label: "发货问题"
        },
        {
          value: "3",
          label: "订单相关"
        },
        {
          value: "4",
          label: "充值提现"
        },
        {
          value: "5",
          label: "认证问题"
        },
        {
          value: "6",
          label: "其他问题"
        }
      ],
      userTypes: [
        {
          value: "1",
          label: "客户"
        },
        {
          value: "2",
          label: "调度员"
        },
        {
          value: "3",
          label: "司机/车队长"
        }
      ],
      replyForm: {},
      pictures: [],
      tableHeight: null, //表格的高度
      tableData: [],
      replyStatus: [
        {
          value: "1",
          label: "未回复"
        },
        {
          value: "2",
          label: "已回复"
        }
      ],

      total: null, //总数
      replyContent: "" //留言回复内容
    };
  },
  activated() {
    this.getData();
    this.tableHeight =
      window.innerHeight - this.$refs.table.$el.offsetTop - 160;
    console.log(this.tableHeight);
  },
  methods: {
    onSubmit() {
      console.log("submit!");
    },
    /* 选取每页多少条数据 */
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.formInline.pageNumber = 1;
      this.formInline.pageSize = JSON.parse(`${val}`);
      // console.log(this.formInline.pageSize);
      this.getData();
    },
    /* 翻页 */
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.formInline.pageNumber = JSON.parse(`${val}`);
      console.log(this.formInline.pageNumber);
      this.getData();
    },
    /** 获取数据列表 **/
    getData() {
      let postData = this.formInline;
      let feedbackType = this.formInline.feedbackType;
      let userType = this.formInline.userType;
      let pageNumber = this.formInline.pageNumber;
      let pageSize = this.formInline.pageSize;
      let mobile = this.formInline.mobile;
      let replyStatus = this.formInline.replyStatus;
      this.$http
        .get(
          "/admin-center-server/base/getFeedbacks?feedbackType=" +
            feedbackType +
            "&userType=" +
            userType +
            "&pageNumber=" +
            pageNumber +
            "&pageSize=" +
            pageSize +
            "&mobile=" +
            mobile +
            "&replyStatus=" +
            replyStatus
        )
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.tableData = data.data.list;
            this.total = Number(data.data.total);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 查询 */
    queryFun() {
      this.formInline.pageNumber = 1;
      this.getData();
    },
    /* 晴空筛选 */
    resetForm() {
      this.formInline = {
        feedbackType: "", //运单号
        userType: "", //司机名称
        pageSize: 20,
        pageNumber: 1,
        id: "", //点击时获取的id
        mobile: "", //用户手机号
        replyStatus: "" //回复状态
      };
      this.getData();
    },

    /*  查看图片 */
    scanPhoto(row) {
      var str = row.feedbackImageUrl;
      if (str == "" || str == null) {
        this.$message.warning("留言反馈中没有上传图片");
      } else {
        this.imgArr = str.split(",");
        this.dialogPhoto = true;
      }
    },
    /* 回复留言 */
    reply(row) {
      let id = row.id;
      this.id = row.id;
      console.log(id);
      this.dialogReply = true;
      this.$http
        .post("/admin-center-server/base/getDescById?id=" + id)
        .then(res => {
          console.log(res);
          if (res.data.code === "200") {
            this.replyForm = res.data.data;
            this.pictures = res.data.data.imageUrls;
            console.log(this.pictures);
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },
    /* 查看图大图 */
    pictureDetail() {
      this.dialogImg = true;
    },
    closeViewer() {
      this.dialogImg = false;
    },
    /* 回复留言确定 */
    replySure() {
      let postData = this.replyContent;
      this.$http
        .get(
          "/admin-center-server/base/putInfo?id=" +
            this.id +
            "&replyCntent=" +
            this.replyContent
        )
        .then(res => {
          if (res.data.code == 200) {
            this.$message.success("回复留言成功");
            this.dialogReply = false;
            this.getData(); //回复留言
          } else {
            this.$message.warning(res.data.message);
          }
        });
    },
    /* 监听输入  */
    replyCntentInput(val) {
      console.log(val);
      this.replyContent = val;
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
// .el-form-item {
//   margin-bottom: 0;
// }

.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
      display: flex;
      justify-content: space-between;

      .button {
        margin-right: 20px;
      }
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }
    .page {
      text-align: right;
    }
    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }
  }
}
.photoList {
  li {
    display: inline-block;
    img {
      padding: 10px;
      background: #cfcfcf;
      width: 200px;
      margin: 20px;
    }
  }
}
.largePicture {
  margin-left: 5px;
  color: blue;
  cursor: pointer;
}
.pictures {
  padding: 0;
  margin: 0;

  li {
    display: inline-block;
    img {
      width: 100px;
      height: 100px;
      margin: 10px;
    }
  }
}
</style>
