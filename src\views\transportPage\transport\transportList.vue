<template>
  <div class="app-container carsList"  
    v-loading.fullscreen.lock="totalNumber"
    :element-loading-text="`${this.totalNumber}条运单上报中，请稍后……`"
    element-loading-spinner="el-icon-loading">
    <div class="select-box">
      <div class="top-title">筛选查询</div>
      <div class="select-info">
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="110px">
          <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="运单类型:">
            <el-select class="form-item-content-width" v-model="formInline.typeParams" placeholder="请选择运单类型" size="small" clearable>
              <el-option label="平台单" value="0"></el-option>
              <el-option label="调度员单" value="1"></el-option>
              <el-option label="货主单" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="运单状态:">
            <el-select class="form-item-content-width" v-model="formInline.status" placeholder="请选择" size="small" clearable>
              <!-- <el-option value="0" label="待装货"></el-option>
              <el-option value="1" label="待卸货"></el-option> -->
              <el-option value="10" label="前往装货地"></el-option>
              <el-option value="11" label="到达装货地"></el-option>
              <el-option value="12" label="前往经停地"></el-option>
              <el-option value="13" label="到达经停地"></el-option>
              <el-option value="14" label="前往卸货地"></el-option>
              <el-option value="15" label="到达卸货地"></el-option>
              <el-option value="3" label="待审核"></el-option>
              <el-option value="5" label="货主收货已驳回"></el-option>
              <el-option value="21" label="待平台审核"></el-option>
              <el-option value="22" label="平台已驳回"></el-option>
              <el-option value="6" label="收货已完成"></el-option>
              <el-option value="7" label="已取消"></el-option>
              <el-option value="8" label="装货超时"></el-option>
              <!-- <el-option value="101" label="已结算"></el-option> -->
            </el-select>
          </el-form-item>
          <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="运单来源">
            <el-select class="form-item-content-width" v-model="formInline.dataFrom" placeholder="请选择" size="small" clearable>
              <el-option value="0" label="实时单"></el-option>
              <el-option value="1" label="导入"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="订单号:">
            <el-input
              class="form-item-content-width"
              v-model.trim="formInline.bussinessSn"
              placeholder="请输入订单号"
              size="small"
              maxlength="30"
            ></el-input>
          </el-form-item>
          <el-form-item label="调度单号:">
            <el-input
              class="form-item-content-width"
              v-model.trim="formInline.orderDispatchSn"
              placeholder="请输入调度单号"
              size="small"
              maxlength="30"
            ></el-input>
          </el-form-item>
          <template v-if="isSearchFull">
            <el-form-item label="运单号:">
              <el-input class="form-item-content-width" v-model.trim="formInline.sn" placeholder="请输入运单号" size="small" maxlength="30"></el-input>
            </el-form-item>
            <el-form-item label="运单号(批量):">
              <el-input class="form-item-content-width" v-model.trim="formInline.listSn" placeholder="运单号之间回车换行" size="small" type="textarea"></el-input>
            </el-form-item>
            <el-form-item label="货物名称">
              <el-input class="form-item-content-width" v-model.trim="formInline.cargoName" placeholder="请输入货物名称" size="small"></el-input>
            </el-form-item>
            <el-form-item label="发货人名称:">
              <el-input
                class="form-item-content-width"
                v-model.trim="formInline.consignerName"
                placeholder="请输入发货人名称"
                size="small"
                maxlength="30"
              ></el-input>
            </el-form-item>
            <el-form-item label="发货人电话:">
              <el-input
                class="form-item-content-width"
                v-model.trim="formInline.consignerPhone"
                placeholder="请输入发货人电话"
                size="small"
                maxlength="11"
                @input="checkMobile"
              ></el-input>
            </el-form-item>
            <el-form-item label="收货人:">
              <el-input
                class="form-item-content-width"
                v-model.trim="formInline.consigneeName"
                placeholder="请输入收货人"
                size="small"
                maxlength="30"
              ></el-input>
            </el-form-item>
            <el-form-item label="收货人电话:">
              <el-input
                class="form-item-content-width"
                v-model.trim="formInline.consigneePhone"
                placeholder="请输入收货人手机号"
                size="small"
                maxlength="11"
                @input="checkMobile"
              ></el-input>
            </el-form-item>
            <el-form-item label="运力专员:">
              <el-input
                class="form-item-content-width"
                v-model.trim="formInline.capacityPersonName"
                placeholder="请输入运力专员姓名"
                size="small"
                maxlength="30"
              ></el-input>
            </el-form-item>
            <el-form-item label="销售人员:">
              <el-input
                class="form-item-content-width"
                v-model.trim="formInline.salePersonName"
                placeholder="请输入销售人员姓名"
                size="small"
                maxlength="30"
              ></el-input>
            </el-form-item>
            <el-form-item label="货主:">
              <el-input
                class="form-item-content-width"
                v-model.trim="formInline.businessName"
                placeholder="请输入货主名称"
                size="small"
                maxlength="30"
              ></el-input>
            </el-form-item>
            <el-form-item label="运力供应商:">
              <el-input
                class="form-item-content-width"
                v-model.trim="formInline.brokerMasterName"
                placeholder="请输入运力供应商"
                size="small"
                maxlength="30"
              ></el-input>
            </el-form-item>
            <el-form-item label="调度员:">
              <el-input
                class="form-item-content-width"
                v-model.trim="formInline.dispatcherName"
                placeholder="请输入调度员名称"
                size="small"
                maxlength="30"
              ></el-input>
            </el-form-item>
            <el-form-item label="调度员手机号:">
              <el-input
                class="form-item-content-width"
                v-model.trim="formInline.dispatcherPhone"
                placeholder="请输入调度员手机号"
                size="small"
                maxlength="30"
              ></el-input>
            </el-form-item>
            <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="结算方式:">
              <el-select
                class="form-item-content-width"
                v-model="formInline.paymentTypeParams"
                placeholder="请输入结算方式"
                size="small"
                clearable
              >
                <el-option label="预付款" value="0"></el-option>
                <el-option label="延迟付款" value="1"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="接单司机手机:">
              <el-input
                class="form-item-content-width"
                v-model.trim="formInline.driverPhone"
                placeholder="请输入接单司机号"
                size="small"
                maxlength="11"
                @input="checkMobile"
              ></el-input>
            </el-form-item>
            <el-form-item label="接单车辆:">
              <el-input
                class="form-item-content-width"
                v-model.trim="formInline.carNumber"
                placeholder="请输入车辆车牌号"
                size="small"
                maxlength="30"
              ></el-input>
            </el-form-item>
            <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="所属车队长:">
              <el-input
                class="form-item-content-width"
                placeholder="请输入车队长"
                v-model.trim="formInline.carOwnerName"
                size="small"
                maxlength="30"
              ></el-input>
            </el-form-item>
            <el-form-item label="创建人:">
              <el-input
                class="form-item-content-width"
                placeholder="请输入创建人姓名"
                v-model.trim="formInline.orderCreateUserName"
                size="small"
                maxlength="30"
              ></el-input>
            </el-form-item>
            <el-form-item label="业务类型:">
              <el-select class="form-item-content-width" v-model="formInline.businessTypeCode" size="mini">
                <el-option v-for="item in businessTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="合作平台主体:">
            <el-select v-model="formInline.baseId" placeholder="请选择" size="mini" clearable>
              <el-option v-for="item in $store.state.user.baseInfo" :key="item.id" :value="item.id" :label="item.baseName"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属项目:">
              <el-input
                class="form-item-content-width"
                placeholder="请输入所属项目"
                v-model.trim="formInline.projectName"
                size="small"
              ></el-input>
            </el-form-item>
            <el-form-item label="竞标ID:">
              <el-input
                class="form-item-content-width"
                placeholder="请输入竞标ID"
                v-model.trim="formInline.competitiveBiddingCode"
                size="small"
              ></el-input>
            </el-form-item>
            <el-form-item label="创建日期范围:">
              <el-date-picker
                v-model="date"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="装货时间:">
              <el-date-picker
                v-model="gpsLoadDate"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="卸货时间:">
              <el-date-picker
                v-model="gpsUnloadDate"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="货主收货时间:">
              <el-date-picker
                v-model="acceptDate"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="收款人名称:">
              <el-input
                class="form-item-content-width"
                v-model.trim="formInline.reciveName"
                placeholder="请输入收款人名称"
                size="small"
                maxlength="30"
              ></el-input>
            </el-form-item>
            <el-form-item label="收款人手机号:" label-width="130px">
              <el-input
                class="form-item-content-width"
                v-model.trim="formInline.recivePhone"
                placeholder="请输入收款人手机号"
                size="small"
                maxlength="30"
              ></el-input>
            </el-form-item>
            <el-form-item label="结算状态:">
              <el-select class="form-item-content-width" v-model="formInline.invoiceType" placeholder="请选择" size="small" clearable>
                <el-option value="0" label="未结算"></el-option>
                <el-option value="2" label="结算中"></el-option>
                <el-option value="1" label="已结算"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="上报状态:">
              <el-select class="form-item-content-width" v-model="formInline.reportStatus" placeholder="请选择" size="small" clearable>
                <el-option value="0" label="未上报"></el-option>
                <el-option value="1" label="上报成功"></el-option>
                <el-option value="2" label="上报失败"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="审核人:">
              <el-input
                class="form-item-content-width"
                v-model.trim="formInline.reviewerName"
                placeholder="请输入审核人姓名"
                size="small"
                maxlength="30"
              ></el-input>
            </el-form-item>
            <el-form-item label="审核时间" label-width="128px">
              <el-date-picker
                v-model="examineDate"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
              ></el-date-picker>
            </el-form-item>
            <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="是否删除:">
              <el-select class="form-item-content-width" v-model="formInline.deleteFlag" placeholder="请选择" size="small" clearable>
                <el-option value="0" label="否"></el-option>
                <el-option value="1" label="是"></el-option>
              </el-select>
            </el-form-item>
          </template>
          <el-form-item>
            <el-button style="margin-left: 20px" type="primary" @click="queryFun()" size="small">查询</el-button>
            <el-button class="left" @click="resetForm" icon="el-icon-refresh-right" size="small">重置</el-button>
            <span style="margin-left: 20px; color: #F6A018; cursor: pointer" @click="isSearchFull = !isSearchFull">
              {{ isSearchFull ? '收起 ↑' : '展开 ↓'}}
            </span>
          </el-form-item>
        </el-form>

        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="100px">
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div>
        <el-select v-model="formInline.order" placeholder="按照ID排序" size="small" @change="orderChange" clearable>
          <el-option value="asc" label="按照ID升序"></el-option>
          <el-option value="desc" label="按照ID降序"></el-option>
        </el-select>
      </div>
      <div class="list-title">
        <div>运单列表</div>
        <div v-if="!$store.state.user.userInfo2.hasStandardModeFlag">
          <el-button @click="deleteOrderItems" type="danger">删除运单</el-button>
          <el-button @click="modifyItemCollectionDialog = true" type="primary">运单修改</el-button>
          <!-- <el-button @click="importDialog">导入·修改时间</el-button> -->
          <!-- <el-button>无车承运人推送</el-button> -->
          <!-- <el-button @click="escalationSubmit">上报至监管平台</el-button> -->
        </div>
      </div>
      <div class="list-main">
        <template>
          <el-table
            :data="tableData"
            border
            v-loading="tableLoading"
            style="width: 100%"
            ref="table"
            tooltip-effect="dark"
            @selection-change="handleSelectionChange"
            cell-class-name="table_cell_gray"
            header-cell-class-name="table_header_cell_gray"
          >
            <el-table-column type="selection" fixed="left" width="55"></el-table-column>
            <el-table-column prop="bussinessSn" label="订单号" width="200"></el-table-column>
            <el-table-column prop="orderDispatchSn" label="调度单号" width="200"></el-table-column>
            <el-table-column prop="sn" label="运单号" width="200"></el-table-column>
            <el-table-column prop="projectName" label="所属项目">
              <template slot-scope="scope">
                {{scope.row.projectName ? scope.row.projectName : '-'}}
              </template>
            </el-table-column>
            <el-table-column prop="competitiveBiddingCode" label="竞标ID">
              <template slot-scope="scope">
                {{scope.row.competitiveBiddingCode ? scope.row.competitiveBiddingCode : '-'}}
              </template>
            </el-table-column>
            <el-table-column prop="statusOrderItem" label="运单状态" width="140"></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" prop="dataFrom" label="运单来源" width="140">
              <template slot-scope="scope">
                {{ scope.row.dataFrom === '0' ? '实时单' : '导入' }}
              </template>
            </el-table-column>
            <el-table-column prop="capacityPersonName" label="运力专员" width="120">
              <template slot-scope="scope">
                {{ scope.row.capacityPersonName ? scope.row.capacityPersonName.split('/')[0] : '' }}
              </template>
            </el-table-column>
            <el-table-column prop="salePersonName" label="销售人员" width="120"></el-table-column>
            <el-table-column prop="businessName" label="货主" width="200"></el-table-column>
            <el-table-column prop="baseName" label="合作平台主体" width="120"></el-table-column>
            <el-table-column label="接单司机" width="120">
              <template slot-scope="scope">
                {{ scope.row.driverName }}
                <!-- 除认证成功以外显示红色 -->
                <span v-if="scope.row.authStatusDTO.driverAuthStatus !== '2'" class="reject-text">（{{ scope.row.authStatusDTO.driverAuthStatusStr }}）</span>
              </template>
            </el-table-column>
            <el-table-column prop="driverPhone" label="接单司机手机号" width="120"></el-table-column>
            <el-table-column label="接单车辆" width="120">
              <template slot-scope="scope">
                {{ scope.row.carNumber }}
                <span v-if="scope.row.authStatusDTO.carAuthStatus !== '1'" class="reject-text">（{{ scope.row.authStatusDTO.carAuthStatusStr }}）</span>
              </template>
            </el-table-column>
            <el-table-column label="车辆所有人" width="120">
              <template slot-scope="scope">
                <el-tooltip v-if="scope.row.licenseOwner === scope.row.businessName" placement="bottom">
                  <i class="el-icon-warning-outline owner-icon"></i>
                  <template slot="content">
                    系统检测到“车辆所有人”与“货主名称”相同，请注意审核
                  </template>
                </el-tooltip>
                {{ scope.row.licenseOwner }}
              </template>
            </el-table-column>
            <!-- <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="GPS设备状态" width="120">
              <template slot-scope="scope">
                <template v-if="scope.row.status === '0' || scope.row.status === '1'">
                  <div class="gps-status" :class="getGpsStatusClass(scope.row)">{{ getGpsStatusText(scope.row) }}</div>
                </template>
                <span v-else>-</span>
              </template>
            </el-table-column> -->
            <el-table-column prop="dispatcherName" label="调度员" width="120"></el-table-column>
            <el-table-column prop="dispatcherPhone" label="调度员手机" width="120"></el-table-column>
            <el-table-column prop="brokerMasterName" label="运力供应商" width="120"></el-table-column>

            <el-table-column prop="reciveName" label="收款人名称" width="120"></el-table-column>
            <el-table-column prop="recivePhone" label="收款人手机号" width="120"></el-table-column>
            <!-- <el-table-column prop="ton" label="接单（抢单）吨数" width="140">
              <template slot-scope="scope">
                <span v-if="scope.row.freightCalcType ==='0'">{{ scope.row.ton }}</span>
              </template>
            </el-table-column> -->
            <el-table-column prop="consignerName" label="发货人" width="120"></el-table-column>
            <el-table-column prop="consignerPhone" label="发货人手机" width="120"></el-table-column>
            <el-table-column prop="consigneeName" label="收货人"></el-table-column>
            <el-table-column prop="consigneePhone" label="收货人手机" width="120"></el-table-column>
            <!-- <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" prop="nvoccWaybill" label="无车承运人运单" width="120"></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" prop="nvoccAmount" label="无车承运人资金单" width="140"></el-table-column> -->
            <el-table-column label="货物名称" width="200">
              <template slot-scope="scope">
                {{ scope.row.cargoTypeClassificationValue + ' / ' + scope.row.cargoType }}
              </template>
            </el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" prop="paymentTypeEnum" label="结算方式"></el-table-column>
            <el-table-column prop="invoiceTypeName" label="结算状态"></el-table-column>
            <!-- <el-table-column prop="isBrevityName" label="是否短倒"></el-table-column> -->
            <el-table-column prop="loadDateBegin" label="装货开始日期" width="120"></el-table-column>
            <el-table-column prop="loadDateEnd" label="装货截止日期" width="120"></el-table-column>
            <el-table-column prop="orderCreateUserName" label="创建人" width="120"></el-table-column>
            <el-table-column prop="ydCreatedDate" label="创建日期" width="120"></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="是否多段付" width="120">
              <template slot-scope="scope">
                {{ scope.row.isMultiPay ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column prop="reviewerName" label="审核人" width="120"></el-table-column>
            <el-table-column prop="examineTime" label="审核日期" width="120"></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" prop="deleteFlag" label="是否删除" width="120"></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" fixed="right" prop="reportStatusName" label="上报状态" width="100"></el-table-column>
            <el-table-column fixed="right" label="操作" width="100">
              <template slot-scope="scope">
                <el-button @click="goDetail(scope.row)" type="text" size="small">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="page">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInline.pageNumber"
              :page-sizes="[20, 40, 60, 80, 100, 200, 500, 800]"
              :page-size="formInline.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              style="margin: 10px auto"
            ></el-pagination>
          </div>
        </template>
      </div>

      <!-- 导入的弹窗-->
      <el-dialog title="导入提示" :visible.sync="dialogVisibleDR" :before-close="handleClose">
        <div style="height: 40px;">
          <i class="el-icon-info" style="color: red"></i>
          <span style=" margin-left: 10px;
">导入需按EXCEL模板进行导入</span>
        </div>
        <div class="flex">
          <span style="margin-left:24px">不可修改模板，以免影响导入</span>
          <el-button @click="exportExcel" :loading="loading">导出模板</el-button>
        </div>
        <div></div>
        <span slot="footer" class="dialog-footer flex">
          <p>导入</p>
          <!-- <span>支持格式：.xls,xlsx</span> -->
          <el-upload
            class="upload-demo"
            ref="upload"
            action
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :file-list="fileList"
            :auto-upload="false"
            accept=".xlsx, .xls"
            :http-request="httpRequest"
            :limit="1"
          >
            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
            <el-button
              style="margin-left: 10px;"
              size="small"
              type="success"
              :loading="loading1"
              @click="submitUpload"
            >上传到服务器</el-button>
            <div slot="tip" class="el-upload__tip">只能上传格式：.xls,xlsx的文件</div>
          </el-upload>
        </span>
      </el-dialog>

      <!-- 导入结果的弹窗 -->
      <el-dialog title="导入结果" :visible.sync="importResult" :before-close="handleClose">
        <ul style="max-height:300px;overflow-x: hidden;
        overflow-y: scroll;">
          <li v-for="(item, index) in importResultData" :key="index">{{ item }}</li>
        </ul>
      </el-dialog>

      <!-- 删除运单 -->
      <el-dialog
        title="确定要删除以下运单？"
        :visible.sync="deleteOrderItemsDialog"
        @closed="deleteOrderItemsDialogColosed">
        <div>
          <el-input v-model="deleteOrderItemsReason" placeholder="请输入删除原因" size="small" style="margin-bottom: 10px"></el-input>
          <div v-for="item in multipleSelection" :key="item.id">{{item.sn}}</div>
        </div>
        
        <span slot="footer" class="dialog-footer">
          <el-button @click="deleteOrderItemsDialog = false">取 消</el-button>
          <el-button type="primary" @click="confirmDeleteOrderItems">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 修改收款人 -->
     <el-dialog title="修改运单" :visible.sync="modifyItemCollectionDialog" :before-close="handleModifyItemCollectionClose">
        <div style="height: 40px;">
          <i class="el-icon-info" style="color: red"></i>
          <span style=" margin-left: 10px;">导入需按EXCEL模板进行导入</span>
        </div>
        <div class="flex">
          <span style="margin-left:24px">不可修改模板，以免影响导入</span>
          <el-button @click="exportModifyExcel" :loading="modifyDownloading">导出模板</el-button>
        </div>
        <div></div>
        <span slot="footer" class="dialog-footer flex">
          <p>导入</p>
          <!-- <span>支持格式：.xls,xlsx</span> -->
          <el-upload
            class="upload-demo"
            ref="uploadModify"
            action
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :file-list="modifyList"
            :auto-upload="false"
            accept=".xlsx, .xls"
            :http-request="modifyRequest"
            :limit="1"
          >
            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
            <el-button
              style="margin-left: 10px;"
              size="small"
              type="success"
              :loading="modifyDownloading1"
              @click="submitModifyUpload"
            >上传到服务器</el-button>
            <div slot="tip" class="el-upload__tip">只能上传格式：.xls,xlsx的文件</div>
          </el-upload>
        </span>
      </el-dialog>

    </div>
  </div>
</template>

<script>
export default {
  name: "CarsList",
  data() {
    return {
      loading: false,
      loading1: false,
      totalNumberLoading: false,
      totalNumber: '',
      value1: "",
      value2: "",
      ids: "", //选取的运单id集合
      dialogVisibleDR: false, //导入弹窗
      importResult: false, //导入结果
      multipleSelection: [], //选择的table对象
      businessTypeList: [],
      formInline: {
        typeParams: "", //运单类型
        status: "", //订单状态
        dataFrom: '', //运单来源
        bussinessSn: "", //订单号
        orderDispatchSn:"", // 调度单号
        dispatcherPhone:"", // 调度
        dispatcherName:"", // 调度
        brokerMasterName:"", // 调度
        sn: "", //运单号
        listSn: "", // 批量运单号（逗号隔开）
        consignerName: "", //发货人名称
        consignerPhone: "", //发货人电话
        consigneeName: "", //收货人
        consigneePhone: "", //收货人电话
        businessName: "", //客户名称
        paymentTypeParams: "", //结算方式
        driverPhone: "", //接单司机手机
        carNumber: "", //接单车辆
        loadDateBegin: "", //开始时间
        loadDateEnd: "", //结束时间
        gpsLoadStartTime: "",
        gpsLoadEndTime: "",
        gpsUnloadStartTime: "",
        gpsUnloadEndTime: "",
        acceptStartDate: "",
        acceptEndDate: "",
        carOwnerName: "", //所属车队长
        orderCreateUserName: "", // 创建人姓名
        reciveName: "", //收款人名称
        recivePhone: "", //收款人手机号
        reportStatus: "", // 上报状态
        invoiceType: "", // 结算状态
        reviewerName: "",
        examineStartTime: "",
        examineEndTime: "",
        deleteFlag: "",
        pageNumber: 1,
        pageSize: 20,
        isBrevity: "", //短倒
        order: "" // 排序
      },
      date: [],
      gpsLoadDate: [],
      gpsUnloadDate: [],
      acceptDate: [],
      examineDate: [],
      tableData: [],
      tableHeight: null,
      tableLoading: false,
      total: null,
      fileList: [], //上传的文件
      importResultData: [], //excel表的导入结果
      isSearchFull: true,
      deleteOrderItemsDialog: false,
      deleteOrderItemsReason: '',
      modifyItemCollectionDialog: false,
      modifyList: [],
      modifyDownloading: false,
      modifyDownloading1: false,
    };
  },
  activated() {
    // 初始化列表数据默认显示90天的 
    let currentDate = Date.now()
    let endDate = new Date(new Date(new Date(currentDate).toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
    let startDate = new Date(new Date(new Date(currentDate-90*24*60*60*1000).toLocaleDateString()).getTime())
    this.date = [this.timeFormat(startDate), this.timeFormat(endDate)]
    this.formInline.loadDateBegin = this.timeFormat(startDate);
    this.formInline.loadDateEnd = this.timeFormat(endDate);
    this.getData(); //初始化列表数据
    this.tableHeight = window.innerHeight - this.$refs.table.$el.offsetTop - 180;

    this.$get('/admin-center-server/order/dict/findDictByType', {
      dictType: "businessType"
    })
      .then(res => {
        this.businessTypeList = res
      })
  },
  methods: {
    /** 上报至监管平台 */
    escalationSubmit() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择已结算运单进行上报')
        return
      }

      let disabledItem = this.multipleSelection.find(v => v.isMultiPay)
      if (disabledItem) {
        this.$message.warning('多段付运单无法上报')
        return
      }

      let data = [] 
      this.multipleSelection.forEach(item => {
        if (item.invoiceType != 1) {
          data.push(item.sn)
        }
      })
      if (data.length) {
        this.$alert(
          `
            <div style="width: 100%; word-wrap:break-word; max-height: 500px; overflow: auto;">
              运单编号为
              <b>${data}</b>
              的运单状态非已结算，请重新选择。
            </div>
          `, 
          '提示', 
          {
            confirmButtonText: '确定',
            dangerouslyUseHTMLString: true,
            type: 'warning',
            callback: action => {
              this.multipleSelection.forEach(el => {
                if (el.invoiceType != 1) {
                  this.$refs.table.toggleRowSelection(el)
                }
              })
            }
          }
        );
        return
      }

      let error = [], succ = []
      this.$confirm(
          '您即将上报' + this.multipleSelection.length + '条运单，一旦上报，运单状态将不可恢复，确认继续？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        ).then(async () => {
          this.totalNumber = ''
          for (let i = 0; i < this.multipleSelection.length; i++) {
            this.totalNumber = this.multipleSelection.length - i - 1
            await this.$http
            .post(
              "/admin-center-server/wlhy/report/sendOrderItem?orderItemId=" + this.multipleSelection[i].orderItemId
            )
            .then(res => {
              if (res.data.code === '200') {
                succ.push(this.multipleSelection[i].sn)
              } else {
                error.push(this.multipleSelection[i].sn)
              }
            });
            if (i == this.multipleSelection.length -1) { 
              let html = ''
              if (error.length && !succ.length) {
                html = 
                `
                  <div style="width: 100%; word-wrap:break-word; color: #F56C6C; max-height: 500px; overflow: auto;">
                    <b>${error.length}</b>条运单上报失败，单号：<b>${error}</b>。
                  </div>
                `
              } else if (error.length && succ.length) {
                html = 
                `
                  <div style="width: 100%; max-height: 500px; overflow: auto;">
                    <div style="width: 100%; word-wrap:break-word; color: #67C23A;">
                      <b>${succ.length}</b>条运单上报成功，单号：<b>${succ}</b>。
                    </div>
                    <div style="width: 100%; word-wrap:break-word; margin-top: 10px; color: #F56C6C;">
                      <b>${error.length}</b>条运单上报失败，单号：<b>${error}</b>。
                    </div>
                  </div>
                `
              } else {
                html = 
                `
                  <div style="width: 100%; word-wrap:break-word; color: #67C23A; max-height: 500px; overflow: auto;">
                    <b>${succ.length}</b>条运单上报成功，单号：<b>${succ}</b>。
                  </div>
                `
              }
              this.$alert(html, '提示', {
                  confirmButtonText: '确定',
                  dangerouslyUseHTMLString: true,
                  type: 'info',
                  callback: action => {
                    this.getData()
                  }
                }
              );
            }
          }
        }).catch(() => {
            
        })     
    },
    /** 删除运单 */
    deleteOrderItems() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择要删除的运单')
        return
      }

      this.deleteOrderItemsDialog = true
    },
    deleteOrderItemsDialogColosed() {
      this.deleteOrderItemsReason = ''
    },
    confirmDeleteOrderItems() {
      if (!this.deleteOrderItemsReason) {
        this.$message.warning('请输入删除原因')
        return
      }
      let deletedItems = this.multipleSelection.filter((item) => {
        return item.deleteFlag === '是'
      })

      if (deletedItems.length > 0) {
        this.$message.warning('所选运单存在已删除运单，请重新选择')
        return
      }
      const deleteItems = this.multipleSelection.map((item) => {
        return {
          deleteReason: this.deleteOrderItemsReason,
          id: item.orderItemId,
          sn: item.sn,
          status: item.status,
          statusBd: item.statusBd
        }
      })

     this.$post('/admin-center-server/orderItem/deleteOrder', deleteItems).then(
       res => {
         this.$message.success('操作成功')
         this.deleteOrderItemsDialog = false
         this.deleteOrderItemsReason = ''
         this.getData()
       }
     )
    },
    /** 修改运单收款人 */
    handleModifyItemCollectionClose(done) {
      this.$refs.uploadModify.clearFiles();
      done()
    },
    exportModifyExcel() {
      var ids = this.ids;
      if (ids.length == 0) {
        this.$message.warning("请选取要导出的列表数据");
        return;
      }
      this.modifyDownloading = true;
      this.$http
        .post("/admin-center-server/orderItem/exportOrderItemEdit", ids)
        .then(res => {
          let data = res.data;
          if (data.code == 200) {
            window.location.href = res.data.data;
            this.modifyDownloading = false;
          } else {
            this.$message.warning(data.message);
            this.modifyDownloading = false;
          }
        });
    },
    submitModifyUpload() {
      this.$refs.uploadModify.submit();
    },
    modifyRequest(param) {
      let fileObj = param.file; // 相当于input里取得的files
      let fd = new FormData(); // FormData 对象
      fd.append("file", fileObj); // 文件对象
      let url = "/admin-center-server/orderItem/editOrderItemUploadFile";
      let config = {
        headers: {
          "Content-Type": "multipart/form-data"
        }
      };
      this.$http.post(url, fd, config).then(res => {
        if (res.data.code == 200) {
          this.$message.success("导入成功");
          // this.importResultData = res.data.data;
          this.modifyItemCollectionDialog = false;
          // this.importResult = true;
          this.getData(); //刷新数据
          this.$refs.uploadModify.clearFiles(); //上传完之后清空文件名字
        } else {
          this.$message.warning(res.data.message);
          this.$refs.uploadModify.clearFiles();
        }
      });
    },
    /* 选取每页多少条数据 */
    handleSizeChange(val) {
      this.formInline.pageNumber = 1;
      this.formInline.pageSize = JSON.parse(`${val}`);
      this.getData();
    },
    /* 翻页 */
    handleCurrentChange(val) {
      this.formInline.pageNumber = JSON.parse(`${val}`);
      this.getData();
    },
    /* 升序降序*/
    orderChange() {
      this.getData()
    },
    /* 重置 */
    resetForm() {
      this.formInline = {
        typeParams: "", //运单类型
        status: "", //订单状态
        dataFrom: '', //运单来源
        bussinessSn: "", //订单号
        orderDispatchSn: '',// 调度单号
        dispatcherPhone:"", // 调度
        dispatcherName:"", // 调度
        brokerMasterName:"", // 调度
        sn: "", //运单号
        listSn: "", // 批量运单号（逗号隔开）
        consignerName: "", //发货人名称
        consignerPhone: "", //发货人电话
        consigneeName: "", //收货人
        consigneePhone: "", //收货人电话
        businessName: "", //客户名称
        paymentTypeParams: "", //结算方式
        driverPhone: "", //接单司机手机
        carNumber: "", //接单车辆
        loadDateBegin: "", //开始时间
        loadDateEnd: "", //结束时间
        gpsLoadStartTime: "",
        gpsLoadEndTime: "",
        gpsUnloadStartTime: "",
        gpsUnloadEndTime: "",
        acceptStartDate: "",
        acceptEndDate: "",
        carOwnerName: "", //所属车队长
        orderCreateUserName: "", // 创建人姓名
        reciveName: "", //收款人名称
        recivePhone: "", //收款人手机号
        reportStatus: "", // 上报状态
        invoiceType: "", // 结算状态
        reviewerName: "",
        examineStartTime: "",
        examineEndTime: "",
        deleteFlag: "",
        pageNumber: 1,
        pageSize: 20,
        isBrevity: "", //短倒
        order: "" // 排序
      }
      this.date = [];
      this.gpsLoadDate = []
      this.gpsUnloadDate = []
      this.acceptDate = []
      this.examineDate = []
      this.getData();
    },
    /* 获取运单 */
    getData() {
      this.tableLoading = true
      this.multipleSelection = []
      var postData = this.formInline;
      if (postData.order === '') {
        // 根据后端要求，单独处理order
        postData.order = null
      }
      for (let i in postData) {
        if (postData[i] === '') delete postData[i]
      }
      this.$http
        .post("/admin-center-server/orderItem/queryOrderItemList", postData)
        .then(res => {
          let data = res.data;
          if (data.code == 200) {
            this.tableLoading = false
            this.tableData = res.data.data.list;
            this.total = JSON.parse(data.data.total);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    timeFormat(date) {
      var Y = date.getFullYear() + '-'
      var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-'
      var D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()) + ' '
      var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
      var m = (date.getMinutes() <10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
      var s = (date.getSeconds() <10 ? '0' + date.getSeconds() : date.getSeconds())
      return Y+M+D+h+m+s
    },
    /* 查询的方法 */
    queryFun() {
      this.formInline.pageNumber = 1;
      if (this.date) {
        this.formInline.loadDateBegin = this.date[0];
        this.formInline.loadDateEnd = this.date[1];
      } else {
        this.formInline.loadDateBegin = null;
        this.formInline.loadDateEnd = null;
      }

      if (this.gpsLoadDate) {
        this.formInline.gpsLoadStartTime = this.gpsLoadDate[0];
        this.formInline.gpsLoadEndTime = this.gpsLoadDate[1];
      } else {
        this.formInline.gpsLoadStartTime = null;
        this.formInline.gpsLoadEndTime = null;
      }

      if (this.gpsUnloadDate) {
        this.formInline.gpsUnloadStartTime = this.gpsUnloadDate[0];
        this.formInline.gpsUnloadEndTime = this.gpsUnloadDate[1];
      } else {
        this.formInline.gpsUnloadStartTime = null;
        this.formInline.gpsUnloadEndTime = null;
      }

      if (this.acceptDate) {
        this.formInline.acceptStartDate = this.acceptDate[0];
        this.formInline.acceptEndDate = this.acceptDate[1];
      } else {
        this.formInline.acceptStartDate = null;
        this.formInline.acceptEndDate = null;
      }
      
      this.formInline.examineStartTime = this.examineDate[0]
      this.formInline.examineEndTime = this.examineDate[1]
      this.getData();
    },
    getGpsStatusClass(row) {
      if (row.bindingStatus === '0') {
        return 'gps-status-unbind'
      } else {
        if (row.deviceStatus === '0') {
          return 'gps-status-offline'
        } else {
          return 'gps-status-online'
        }
      }
    },
    getGpsStatusText(row) {
      //bindingStatus 0 未绑定 1 已绑定
      //deviceStatus 0 离线 1 在线
      if (row.bindingStatus === '0') {
        return '未绑定'
      } else {
        if (row.deviceStatus === '0') {
          return '离线'
        } else {
          return '在线'
        }
      }
    },

    /* 查看详情页 */
    goDetail(row) {
      this.$router.push({
        path: "transportListDetail",
        query: {
          orderItemId: row.orderItemId,
          status: row.status
          // freezeStatus: row.freezeStatus
        }
      });
    },
    /* 电话的校验 */
    checkMobile() {
      this.formInline.consignerPhone = this.formInline.consignerPhone.replace(
        /[^\d]/g,
        ""
      );
      this.formInline.consigneePhone = this.formInline.consigneePhone.replace(
        /[^\d]/g,
        ""
      );
      this.formInline.driverPhone = this.formInline.driverPhone.replace(
        /[^\d]/g,
        ""
      );
    },
    /* 导入弹窗 */
    importDialog() {
      this.dialogVisibleDR = true;
    },
    handleClose() {
      this.dialogVisibleDR = false;
      this.importResult = false;
    },
    /* 单选和多选 */
    handleSelectionChange(val) {
      this.multipleSelection = val;
      var result = this.getFields(val, "orderItemId");
      this.ids = result.map(Number);
    },
    //在数组对象中提取某个字段的值组成新的数组
    getFields(input, field) {
      var output = [];
      for (var i = 0; i < input.length; ++i) output.push(input[i][field]);
      return output;
    },
    /* 导出模版 */
    exportExcel() {
      var ids = this.ids;
      if (ids.length == 0) {
        this.$message.warning("请选取要导出的列表数据");
        return;
      }
      this.loading = true;
      this.$http
        .post("/admin-center-server/orderItem/exportOrderItem", ids)
        .then(res => {
          let data = res.data;
          if (data.code == 200) {
            window.location.href = res.data.data;
            this.loading = false;
          } else {
            this.$message.warning(data.message);
            this.loading = false;
          }
        });
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    handleRemove(file, fileList) {
      // console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    httpRequest(param) {
      console.log(param);
      let fileObj = param.file; // 相当于input里取得的files
      console.log(fileObj);
      let fd = new FormData(); // FormData 对象
      fd.append("file", fileObj); // 文件对象
      let url = "/admin-center-server/orderItem/upload/file";
      let config = {
        headers: {
          "Content-Type": "multipart/form-data"
        }
      };
      this.$http.post(url, fd, config).then(res => {
        console.log(res);
        if (res.data.code == 200) {
          this.$message.success("导入成功");
          this.importResultData = res.data.data;
          this.dialogVisibleDR = false;
          this.importResult = true;
          this.getData(); //刷新数据
          this.$refs.upload.clearFiles(); //上传完之后清空文件名字
        } else {
          this.$message.warning(res.data.message);
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }
  .page {
    text-align: right;
  }
  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        height: 38px;
        line-height: 38px;
      }
    }

    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }
  }
}
.reject-text {
  color: #D45353;
}
.gps-status {
  width: 54px;
  height: 22px;
  border-radius: 5px;
  text-align: center;
  line-height: 22px;
  font-size: 12px;
}
.gps-status-unbind {
  color: #606266;
  border: 1px solid #e9e9eb;
  background-color: #f4f4f5;
}
.gps-status-offline {
  border: 1px solid #fde2e2;
  color: rgb(245, 108, 108);
  background-color: #fef1f1;
}
.gps-status-online {
  border: 1px solid #e1f3d8;
  color: rgb(103, 194, 58);
  background-color: #f0f9ec;
}
.owner-icon {
  color: #f6a018;
}
</style>
