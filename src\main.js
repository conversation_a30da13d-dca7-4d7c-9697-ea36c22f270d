import Vue from 'vue'
import App from './App.vue'
import Element from 'cf-element-ui'
import Cookies from 'js-cookie'
import i18n from './lang' // Internationalization
import * as filters from './filters' // global filters
import router from './router'
import store from './store'
import qs from 'qs' //视情况用与不用;
import './utils/http2'
import E from 'wangeditor'
import md5 from 'js-md5'
// import 'element-ui/lib/theme-chalk/index.css'
import './assets/theme/index.css'
import 'normalize.css/normalize.css' // A modern alternative to CSS resets
import '@/styles/index.scss' // global css
import '@/assets/font/style.css'
import '@/assets/scss/index.scss'
//图标iconfont
import '@/assets/style/style.css'
import VueAMap from 'vue-amap'
import './icons' // icon
import './errorLog' // error log
import './permission' // permission control
import common from './utils/common'
import GoEasy from 'goeasy'
import {sm3} from 'sm-crypto'
Vue.prototype.$goEasy = new GoEasy({
    host: 'hangzhou.goeasy.io', //应用所在的区域地址: 【hangzhou.goeasy.io |singapore.goeasy.io】
    appkey: process.env.ENV==='production'?'BC-2da5c792fcee4bdfaf75a75e8d37dd46':'BC-a02bc1d43cee45ab992170adaf1c094d',
    // appkey: "BC-2da5c792fcee4bdfaf75a75e8d37dd46", //替换为您的应用appkey  正式环境
    // appkey: "BC-a02bc1d43cee45ab992170adaf1c094d", //替换为您的应用appkey  测试环境

    onConnected: function () {
        console.log('goEasy 连接成功！')
    },
    onDisconnected: function () {
        console.log('连接断开！')
    },
    onConnectFailed: function (error) {
        console.log('连接失败或错误！')
    }
});
Vue.prototype.playAudio = () => {
    let buttonAudio = document.getElementById('eventAudio');
    let audio = 'http://static.chengfengkuaiyun.com/mp3/tips.mp3';
    buttonAudio.setAttribute('src',audio);
    buttonAudio.play()
};
document.body.addEventListener('click',function( e ){
    let event = e || window.event;
    let target = event.target || event.srcElement;
    let clickMusic = target.getAttribute('clickMusic');
    if(clickMusic==='true') Vue.prototype.playAudio();
    else return false;
});
Vue.use(common);
Vue.use(VueAMap);
import serviceHeader from '@/utils/http'
import serviceHeaderExport from '@/utils/exportHttp'
import {moveToErr} from '@/utils/index.js'


Vue.prototype.$http = serviceHeader;
Vue.prototype.$exportHttp = serviceHeaderExport;
Vue.prototype.$md5 = md5;
Vue.prototype.$sm3 = sm3;
Vue.prototype.$qs = qs;
Vue.prototype.$moveToErr = moveToErr;
Vue.use(Element, {
    size: Cookies.get('size') || 'medium',
});

window._AMapSecurityConfig = {
    securityJsCode: 'ee7ddbdc4ac5c9ddc6dc4edafbda56ca'
}
import AMapLoader from '@amap/amap-jsapi-loader'
AMapLoader.load({
    key: 'f365f40a070b1043aa59d099b4f15e58',
    version: "2.0",
    plugins: [
        'AMap.Geocoder',
        'AMap.Geolocation',
        'AMap.Autocomplete',
        'AMap.PlaceSearch',
        'AMap.Scale',
        'AMap.OverView',
        'AMap.ToolBar',
        'AMap.MapType',
        'AMap.PolyEditor',
        'AMap.CircleEditor',
        'AMap.PolygonEditor'
    ],
    AMapUI: {
        version: '1.1',
        plugins:['overlay/SimpleMarker']
    }
})

Object.keys(filters).forEach(key => {
    Vue.filter(key, filters[key])
});

import DetailRow from '@/components/OrderDetail/DetailRow'
import DetailCol from '@/components/OrderDetail/DetailCol'
import DetailEmpty from '@/components/OrderDetail/DetailEmpty'
Vue.component('detail-row', DetailRow)
Vue.component('detail-col', DetailCol)
Vue.component('detail-empty', DetailEmpty)

import './formatter'

import OrderDetail from '@/components/Order/OrderDetail'
import Dispatch from '@/components/Order/Dispatch'
Vue.component('OrderDetail', OrderDetail)
Vue.component('Dispatch', Dispatch)

import ImageUploader from '@/components/ImageUploader/ImageUploader'
Vue.component('ImageUploader', ImageUploader)

import ImageUploader2 from '@/components/ImageUploader2/ImageUploader2'
Vue.component('ImageUploader2', ImageUploader2)

Vue.config.productionTip = false;
import { webKey } from '@/utils/config'
new Vue({
    data() {
        return {
            webKey
        }
    },
    router,
    store,
    i18n,
    render: h => h(App),
}).$mount('#app');
