<template>
  <div class="userPay">
    <div class="select-box">
      <div class="select-info">
        <el-form
          size="mini"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          label-width="120px"
        >
          <el-form-item label="货主名称:">
            <el-input
              class="form-item-content-width"
              placeholder="请输入客户名称"
              v-model="formInline.shipperName"
            ></el-input>
          </el-form-item>
          <el-form-item label="所属项目:">
            <el-input
              class="form-item-content-width"
              placeholder="请输入所属项目"
              v-model="formInline.projectName"
            ></el-input>
          </el-form-item>
          <el-form-item label="运单号:">
            <el-input
              class="form-item-content-width"
              v-model="formInline.orderItemSn"
              placeholder="请输入运单号"
            ></el-input>
          </el-form-item>
          <el-form-item label="订单号:">
            <el-input
              class="form-item-content-width"
              v-model="formInline.orderSn"
              placeholder="请输入订单号"
            ></el-input>
          </el-form-item>
          <el-form-item label="竞标ID:">
            <el-input
              class="form-item-content-width"
              v-model="formInline.competitiveBiddingCode"
              placeholder="请输入竞标ID"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="货主结算单号:">
            <el-input
              class="form-item-content-width"
              v-model="formInline.orderSettlementSn"
              placeholder="请输入货主结算单号"
            ></el-input>
          </el-form-item> -->
          <el-form-item label="司机姓名:">
            <el-input
              class="form-item-content-width"
              v-model="formInline.driverName"
              placeholder="请输入司机姓名"
            ></el-input>
          </el-form-item>
          <el-form-item label="司机手机号:">
            <el-input
              class="form-item-content-width"
              v-model="formInline.driverPhone"
              placeholder="请输入司机手机号"
            ></el-input>
          </el-form-item>
          <el-form-item label="承运车辆:">
            <el-input
              class="form-item-content-width"
              v-model="formInline.carNumber"
              placeholder="请输入承运车辆"
            ></el-input>
          </el-form-item>
          <el-form-item label="结算状态:">
            <el-select class="form-item-content-width" v-model="formInline.invoiceType" clearable>
              <el-option label="已结算" value="1"></el-option>
              <el-option label="未结算" value="0"></el-option>
              <el-option label="结算中" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开票状态:">
            <el-select class="form-item-content-width" v-model="formInline.invoiceBillStatus" clearable>
              <el-option label="已开票" value="4"></el-option>
              <el-option label="未开票" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="货主支付进度：">
            <el-select v-model="formInline.businessPayStatus" class="form-item-content-width"  clearable>
              <el-option label="未支付" value="0"  key="0"></el-option>
              <el-option label="部分支付" value="1" key="1"></el-option>
              <el-option label="全部支付" value="2"  key="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="运单创建时间:">
            <el-date-picker
              v-model="date"
              :clearable="false"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="合作平台主体:" prop="baseId" label-width="120px">
            <el-select class="form-item-content-width" v-model="formInline.baseId" placeholder="请选择" clearable>
              <el-option
                v-for="item in $store.state.user.baseInfo"
                :key="item.id"
                :value="item.id"
                :label="item.baseName"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              class="left"
              icon="el-icon-search"
              size="mini"
              type="primary"
              @click="onSubmit"
              :loading="searchLoading"
              >查询
            </el-button>
            <el-button
              class="left"
              icon="el-icon-delete"
              size="mini"
              type="danger"
              @click="resetSubmit"
              :loading="searchLoading"
              >清空筛选
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>
          <span>应收合计：<span class="list-title-sum">{{ statisticsData.receivable }}</span>， </span>
          <span>实收合计：<span class="list-title-sum">{{ statisticsData.netReceipts }}</span>， </span>
          <span>应付合计：<span class="list-title-sum">{{ statisticsData.payable }}</span>， </span>
          <span>实付合计：<span class="list-title-sum">{{ statisticsData.actualPayment }}</span>， </span>
          <span>预计利润合计：<span class="list-title-sum">{{ statisticsData.estimatedProfit }}</span>， </span>
          <span>实际利润合计：<span class="list-title-sum">{{ statisticsData.actualProfit }}</span> </span>
        </div>
        <div>
          <el-checkbox style="margin-right: 20px" @change="getDataList" v-model="removeTestData">去掉测试数据</el-checkbox>
          <el-button
            :loading="loading"
            type="primary"
            @click="queryIncomeDetailsFreightDifferenceStatistics"
            size="mini"
            >计算合计金额</el-button
          >
        </div>
      </div>
      <div style="font-size: 14px"></div>
      <div class="list-main">
        <template>
          <el-table
            :data="tableData"
            border
            @selection-change="handleSelectionChange"
            style="width: 100%"
            cell-class-name="table_cell_gray"
            header-cell-class-name="table_header_cell_gray"
            :loading="searchLoading"
          >
            <el-table-column fixed type="index" label="序号" width="50"></el-table-column>
            <el-table-column fixed prop="orderItemSn" label="运单号" width="200">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click=" $router.push(`/transport/transportListDetail?orderItemId=${scope.row.id}` )">{{ scope.row.orderItemSn }}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column
              prop="orderSn"
              label="订单号"
              width="200"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="$router.push(`/order/orderManage/orderDetail?orderBusinessId=${scope.row.orderBusinessId}`)">{{ scope.row.orderSn }}</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="competitiveBiddingCode" label="竞标ID" >
              <template slot-scope="scope">
                <div>
                  {{scope.row.competitiveBiddingCode ? scope.row.competitiveBiddingCode : '-'}}
                </div>
              </template>
            </el-table-column>
             <el-table-column
              prop="businessName"
              label="货主名称"
              width="220"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="
                    $router.push(
                      `/consignorAccount/accountDetails?id=${scope.row.businessId}`
                    )
                  "
                  >{{ scope.row.shipperName }}</el-button
                >
              </template>
            </el-table-column>
            <!-- <el-table-column prop="orderSettlementSn" label="货主结算单号" width="180">
              <template slot-scope="scope">
                <div>
                  {{scope.row.orderSettlementSn ? scope.row.orderSettlementSn : '-'}}
                </div>
              </template>
            </el-table-column> -->
            <el-table-column prop="driverName" label="承运司机" ></el-table-column>
           
            <el-table-column prop="driverPhone" label="司机手机号" width="200" >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="
                    $router.push(`/driverAccount/accountDetails?id=${scope.row.driverId}&type=` )"
                  >{{ scope.row.driverPhone }}</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="carNumber" label="承运车辆" width="200" >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="
                    $router.push(`/carsList/carDetail?carId=${scope.row.carId}` )"
                  >{{ scope.row.carNumber }}</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="paymentStatusValue" label="结算状态" ></el-table-column>
            <el-table-column prop="invoiceBillStatusValue" label="开票状态" ></el-table-column>
            <el-table-column prop="businessPayRatio" label="货主支付进度" width="110">
              <template slot-scope="scope">
                <div v-if="scope.row.businessPayRatio == null">-</div>
                <div v-else>
                  <div v-if="scope.row.businessPayRatio == 0" style="color: red">未支付</div>
                  <div v-if="scope.row.businessPayRatio != 0 && scope.row.businessPayRatio < 100" style="color: red">部分支付</div>
                  <div v-if="scope.row.businessPayRatio == 100" style="color: green">全部支付</div>
                  <div class="pay-progress">
                    <el-progress :text-inside="true" :stroke-width="20" :percentage="Number(scope.row.businessPayRatio)" color="#f6a018" :show-text="false"></el-progress>
                    <div class="pay-progress-value">{{scope.row.businessPayRatio}}%</div>
                  </div>
                  
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="receivable" label="应收" width="100"></el-table-column>
            <el-table-column prop="netReceipts" label="实收" width="100"></el-table-column>
            <el-table-column prop="payable" label="应付" width="100"></el-table-column>
            <el-table-column prop="actualPayment" label="实付" width="100"></el-table-column>
            <el-table-column prop="estimatedProfit" label="预计利润" width="100"></el-table-column>
            <el-table-column prop="actualProfit" label="实际利润" width="100"></el-table-column>
            <el-table-column prop="createdDate" label="创建时间" width="100px" ></el-table-column>
            <el-table-column prop="baseName" label="合作平台主体" width="200px"></el-table-column>
            <el-table-column
            fixed="right"
            label="操作"
            width="100">
            <template slot-scope="scope">
              <el-popover
                @show="queryOrderItemTransaction(scope.row.id, scope.row.orderItemSn)"
                placement="right"
                width="680"
                trigger="click">
                <div class="transaction-list">
                  <div class="list-value"><span>应收：{{scope.row.receivable}}</span> <span>实收：{{scope.row.netReceipts}}</span></div>
                  <div class="list-value"><span>应付：{{scope.row.payable}}</span> <span>实付：{{scope.row.actualPayment}}</span></div>
                </div>
                <el-table :data="orderItemTransaction">
                    <el-table-column width="160" property="time" label="交易时间"></el-table-column>
                    <el-table-column width="180" property="amount" label="交易金额">
                      <template slot-scope="scope">
                        <div :class="{'red':Number(scope.row.amount)<0}">{{scope.row.amount}} <span v-if="scope.row.payProportion">({{scope.row.payProportion + '%'}})</span></div>
                      </template>
                    </el-table-column>
                    <el-table-column width="100" property="typeName" label="费用类型"></el-table-column>
                    <el-table-column width="200" property="transactionSn" label="流水号"></el-table-column>
                </el-table>
                <el-button slot="reference" type="text" size="small">交易明细</el-button>
              </el-popover>
            </template>
            </el-table-column>
          </el-table>
        </template>
      </div>
      <div class="paging">
        <div class="block">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 30, 40, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      gridData: [],
      loading: false,
      dialogVisible: false,
      total: 1,
      currentPage: 1,
      pageSize: 20,
      formInline: {
      },
      date: [],
      tableData: [],
      multipleSelection: [],
      exportData: {},

      statisticsData: { receivable: '',
                        netReceipts: '',
                        payable: '',
                        actualPayment: '',
                        estimatedProfit: '',
                        actualProfit: ''},
      orderItemTransaction: [],
      removeTestData: true,
      searchLoading : false
    };
  },
  methods: {
    /** 查询 **/
    onSubmit() {
      this.currentPage = 1;
      this.getDataList();
    },
    /** 清空搜索选项 **/
    resetSubmit() {
      this.formInline = {};
      this.date = "";
      this.getDataList();
    },
    
    handleClose() {
      this.dialogVisible = false;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.getDataList();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getDataList();
    },
    /** 多选 **/
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    /** 列表 **/
    getDataList() {
      this.searchLoading = true;
      let params = {
        ...this.formInline,
        testDataFlag: this.removeTestData,
        pageNumber: this.currentPage,
        pageSize: this.pageSize,
      };
      if (this.date !== null) {
        params.createdDateStart = this.date[0];
        params.createdDateEnd = this.date[1];
      }
      this.$post("/admin-center-server/settlement/queryIncomeDetailsFreightDifferencePage", params).then(
        (res) => {
          this.searchLoading = false
          this.exportData = params
          this.tableData = res.list;
          this.total = Number(res.total);
          this.queryIncomeDetailsFreightDifferenceStatistics()
        }
      ).catch(()=>{
        this.searchLoading = false;
      })
    },
    queryIncomeDetailsFreightDifferenceStatistics() {
      const params = {testDataFlag: this.removeTestData, ...this.exportData}
      this.$post('/admin-center-server/settlement/queryIncomeDetailsFreightDifferenceStatistics', params).then(
        res => {
          this.statisticsData = res || {receivable: '',
                                        netReceipts: '',
                                        payable: '',
                                        actualPayment: '',
                                        estimatedProfit: '',
                                        actualProfit: ''}
        }
      )
      
    },
    queryOrderItemTransaction(id, sn) {
      this.orderItemTransaction = []
      this.$get('/admin-center-server/settlement/queryOrderItemTransaction', {}, {params: {orderItemId: id, orderItemSn: sn}}).then(
        res => {
          this.orderItemTransaction = res
        }
      )
    }
  },
  activated() {
    this.getDataList();
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.pay-progress {
  position: relative;

  .pay-progress-value {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 100%;
    text-align: center;
    color: #333;
  }
}

.userPay {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;
    .select-info {
      padding-top: 30px;
      padding-bottom: 30px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;
    overflow: hidden;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 16px;
        min-height: 38px;
        line-height: 38px;
      }
    }

    .list-title-sum {
        color: red;
    }

    .list-main {
      width: 100%;
      border: 1px solid #cccccc;
      margin-top: 10px;
    }

    .paging {
      margin-top: 10px;
      float: right;
    }
  }
}

.transaction-list {
  margin: 10px 10px;
  padding-bottom: 10px;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgb(238, 238, 238);
  .list-value {
    width: 40%;
    display: flex;
    justify-content: space-between;

  }
}
.red{
  color: crimson;
}
</style>
