<template>
  <div class="app-container addCar">
    <div class="inner-box">
      <el-form :model="ruleForm" ref="ruleForm" label-width="200px" class="demo-ruleForm">
        <el-form-item label="标题" required>
          <el-input v-model="ruleForm.title" style="width: 220px" placeholder="请输入标题"></el-input>
        </el-form-item>

        <el-form-item label="内容分类" required>
          <el-select v-model="ruleForm.cusId" placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.id"
              :label="item.maxName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否置顶">
          <el-radio-group v-model="ruleForm.isTop">
            <el-radio label="0">否</el-radio>
            <el-radio label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="显示设置">
          <el-switch v-model="ruleForm.isShow"></el-switch>
        </el-form-item>

        <el-form-item label="排序" required>
          <el-input v-model.number="ruleForm.sort" style="width: 220px" placeholder="请填写序号"></el-input>
        </el-form-item>

        <el-form-item>
          <div class="editor-wrapper">
            <div ref="editor"></div>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button @click="commitInfo" v-if="typeFlag == 1">提交</el-button>
          <el-button @click="update" v-if="typeFlag == 2">提交</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import { client, OSS_REGION } from "@/utils/alioss";

// 引入wangEditor富文本
import E from "wangeditor";
import "wangeditor/release/wangEditor.min.css";

export default {
  data() {
    return {
      Aliyun: {},
      options: [],
      value: "",

      editor: "",
      info: "",
      typeFlag: "",
      dialogVisible: false,
      ruleForm: {
        title: "",
        cusId: "", //内容分类ID
        isTop: "1", //是否置顶
        sort: null, //排序
        content: "", // 文本内容
        isShow: false //是否显示设置
      },
      picUrl: "", //上传成功的图片地址
      menus: [
        // 'head',  // 标题
        "bold", // 粗体
        "fontSize", // 字号
        "fontName", // 字体
        "italic", // 斜体
        "underline", // 下划线
        "strikeThrough", // 删除线
        "foreColor", // 文字颜色
        "backColor", // 背景颜色
        "link", // 插入链接
        "list", // 列表
        "justify", // 对齐方式
        "quote", // 引用
        // 'emoticon',  // 表情
        "image", // 插入图片
        // 'table',  // 表格
        // 'video',  // 插入视频
        // 'code',  // 插入代码
        "undo", // 撤销
        "redo" // 重复
      ],
      editorContent: ""
    };
  },

  created() {
    this.getAliyunData();
  },

  activated() {
    this.typeFlag = this.$route.query.typeFlag;
    if (this.typeFlag == 2) {
      //编辑
      this.detail(); //数据回显
    }
    this.getSelectData();
    this.initEditor();
  },
  methods: {
    getAliyunData() {
      const ossApiUrl = "/base-center-server/sts/oss_signature"; // 获取oss签名的地址
      // 获取oss签名
      this.$http.get(ossApiUrl).then(res => {
        if (res.data.code == "200") {
          let data = res.data;
          this.Aliyun = data.data;
          this.Aliyun.region = OSS_REGION;
          // return data
        }
      });
    },

    handleRemove(file, fileList) {
      // console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },

    resetForm(formName) {
      this.$refs[formName].resetFields();
    },

    /* 下拉选列表的数据 */
    getSelectData() {
      this.$http
        .post("/admin-center-server/articel/getCusArticelSortByCategory")
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.options = data.data;
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 提交*/
    commitInfo() {
      this.ruleForm.content = this.editorContent;
      let postData = this.ruleForm;
      console.log(postData, "-----postData");
      if (this.ruleForm.title == "") {
        this.$message({
          type: "warning",
          message: "请输入标题"
        });
        return;
      }

      if (this.ruleForm.cusId == "") {
        this.$message({
          type: "warning",
          message: "请选择文章分类"
        });
        return;
      }
      if (this.ruleForm.sort == "") {
        this.$message({
          type: "warning",
          message: "请填写序号"
        });
        return;
      }

      this.$http
        .post("/admin-center-server/articel/addArticle", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "添加成功"
            });
            this.$router.go(-1);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 修改 */
    update() {
      let postData = this.ruleForm;
      postData.id = this.$route.query.id;

      postData.content = this.editor.txt.html();
      if (this.ruleForm.title == "") {
        this.$message({
          type: "warning",
          message: "请输入标题"
        });
        return;
      }

      if (this.ruleForm.cusId == "") {
        this.$message({
          type: "warning",
          message: "请选择文章分类"
        });
        return;
      }
      if (this.ruleForm.sort == "") {
        this.$message({
          type: "warning",
          message: "请填写序号"
        });
        return;
      }
      console.log(postData, "postData----");
      this.$http
        .post("/admin-center-server/articel/updateCusArticel", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "修改成功"
            });
            this.$router.go(-1);
          } else {
            this.$message.warning(data.message);
          }
        });
    },
    /* 回显 */
    detail() {
      let id = this.$route.query.id;
      this.$http
        .get("/admin-center-server/articel/getByIdCusArticelId?id=" + id)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            let content = data.data.content;

            this.ruleForm = {
              title: data.data.title,
              isShow: data.data.isShow,
              sort: data.data.sort,
              isTop: data.data.isTop,
              cusId: data.data.cusId,
              content: this.editor.txt.html(content)
            };
          }
        });
    },
    /* 上传图片 */
     ossUpload(param,result) {
      console.log(param)
      let file = param[0]; // 文件的
      console.log(file)
      const tmpcnt = file.name.lastIndexOf(".");
      const exname = file.name.substring(tmpcnt + 1);
      const fileName =
        "/" +
        this.Aliyun.bucket +
        "/" +
        this.Aliyun.dir +
        this.random_string(15) +
        "." +
        exname;
      client(this.Aliyun)
        .put(fileName, file)
        .then(res => {
          if (res.res.status === 200) {
            // 上传
             let imgUrl = res.res.requestUrls[0];
              this.picUrl = imgUrl;
              console.log(this.picUrl);

              this.$message({
                type: "success",
                message: "上传成功"
              });
              this.hideUpload = true;
              // console.log(result)
              result(this.picUrl);
          } else {
            this.$message.error(res.res.message);
          }
        });
    },

     /* 随机的名字 */
    random_string(len) {
      len = len || 32;
      var chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
      var maxPos = chars.length;
      var pwd = "";
      for (var i = 0; i < len; i++) {
        pwd += chars.charAt(Math.floor(Math.random() * maxPos));
      }
      return pwd;
    },


    initEditor() {
      this.editor = new E(this.$refs.editor);
      this.editor.customConfig.menus = this.menus; // menu菜单
      this.editor.customConfig.uploadImgMaxSize = 2 * 1024 * 1024;
      // 限制最多上传6张图片
      this.editor.customConfig.uploadImgMaxLength = 6;
      // 设置超时
      this.editor.customConfig.uploadImgTimeout = 3 * 60 * 1000;
      // 关闭粘贴样式的过滤
      this.editor.customConfig.pasteFilterStyle = false;
      // 忽略粘贴内容中的图片
      this.editor.customConfig.pasteIgnoreImg = true;
      this.editor.customConfig.customUploadImg = async (files, insert) => {
        await this.ossUpload(files, insert);
        // insert(this.picUrl);
      };
      this.editor.customConfig.onchange = html => {
        this.editorContent = html;
        console.log(this.editorContent);
      };
      this.editor.create();
      this.getHtml();
    },
    // 获取html
    async getHtml() {
      if (!this.html) return;
      let html = this.html;
      if (isUrl(this.html)) {
        const res = await CommonServer.getHtml(this.html);
        html = res.data;
      }
      this.$nextTick(() => {
        this.editorContent = html;
        this.editor.txt.html(html);
      });
    },
    // 获取编辑器内容
    getContent() {
      console.log(this.editorContent);
      // this.$emit('getContent', editor.txt.html())
    },
    // 清除内容
    clearHtml() {
      this.editor && this.editor.txt.clear();
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss">
.addCar {
  background-color: #ffffff;
  .tip {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 40px 40px 0;
    font-size: 12px;
    em {
      margin-right: 5px;
    }
  }
  .inner-box {
    // margin-left: 10%;
    width: 70%;
    .upload-box {
      width: 100%;
      height: 100%;
      position: relative;
      .icon-XZ {
        width: 92px;
        height: 92px;
        margin: 0 auto;
        background: url("../../../../assets/xiazai.png") no-repeat;
        background-size: 100% 100%;
      }
      .icon-word {
        width: 100%;
        height: 20px;
        line-height: 20px;
        font-size: 10px;
        position: absolute;
        bottom: 25px;
        left: 0px;
        color: #cccccc;
      }
    }
  }
}
.w-e-menu {
  z-index: 2 !important;
}
.w-e-text-container {
  z-index: 1 !important;
}
.editor {
  margin-top: 30px;
}

.myQuillEditor {
  height: 400px;
}
.editCommit {
  margin: 100px;
}
</style>
