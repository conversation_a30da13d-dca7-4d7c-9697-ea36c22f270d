<template>
  <div class="main-box">
    <div class="base-info">
      <div class="title-box">
        <div>基本信息</div>
        <slot></slot>
      </div>
    
      <div class="list-main">
        <template>
          <detail-row>
            <detail-col :span="6" label="调度单号" :value="tableData.orderDispatchSn"></detail-col>
            <detail-col :span="6" label="货主名称" :value="tableData.businessName"></detail-col>
            <detail-col :span="6" label="货物名称" :value="tableData.cargoTypeClassificationValue + '/' +tableData.cargoType"></detail-col>
            <detail-col :span="6" label="运费单价" :value="unitMoneyFormatter(tableData.freightCalcType, tableData.freight)"></detail-col>
            <detail-col :span="6" label="运力供应商" :value="tableData.brokerMasterName"></detail-col>
            <detail-col :span="6" label="调度员" :value="tableData.dispatcherName || '-'"></detail-col>
            <detail-col :span="6" label="调度员手机号" :value="tableData.dispatcherPhone || '-'"></detail-col>
            <detail-col :span="6" label="调度单状态" :value="tableData.statusString"></detail-col>
            <detail-col :span="6" label="创建时间" :value="tableData.createTime"></detail-col>
            <detail-col :span="6" label="总货量" :value="unitFormatter(tableData.freightCalcType, tableData.sumTon)"></detail-col>
            <detail-col :span="6" label="剩余货量" :value="unitFormatter(tableData.freightCalcType, tableData.remainTon)"></detail-col>
          </detail-row>
        </template>
      </div>
    </div>
    <div class="drive-info">
      <div class="title-box">
        <div>收发信息</div>
      </div>

      <div class="list-main">
        <detail-row>
          <detail-col :span="6" label="装车点" :value="tableData.deliveryPlace"></detail-col>
          <detail-col :span="6" label="发货联系人" :value="tableData.consignerName"></detail-col>
          <detail-col :span="6" label="发货人电话" :value="tableData.consignerPhone"></detail-col>
          <detail-col :span="6" label="开始时间" :value="tableData.loadDateBegin"></detail-col>
          <detail-col :span="6" label="卸货点" :value="tableData.receivePlace"></detail-col>
          <detail-col :span="6" label="收货联系人" :value="tableData.consigneeName"></detail-col>
          <detail-col :span="6" label="收货人电话" :value="tableData.consigneePhone"></detail-col>
          <detail-col :span="6" label="截止时间" :value="tableData.loadDateEnd"></detail-col>
        </detail-row>
      </div>
    </div>

    <div class="driver-info">
      <div class="title-box">
        <div>运单统计</div>
      </div>
      <div class="list-main">
        <template>
          <detail-row>
            <detail-col :span="6" label="待装货" :value="unitFormatter(tableData.freightCalcType, tableData.dzh)"></detail-col>
            <detail-col :span="6" label="待卸货" :value="unitFormatter(tableData.freightCalcType, tableData.dxh)"></detail-col>
            <detail-col :span="6" label="待收货" :value="unitFormatter(tableData.freightCalcType, tableData.dsh)"></detail-col>
            <detail-col :span="6" label="货主收货驳回" :value="unitFormatter(tableData.freightCalcType, tableData.hzshbh)"></detail-col>
            <detail-col :span="6" label="待平台审核" :value="unitFormatter(tableData.freightCalcType, tableData.dptsh)"></detail-col>
            <detail-col :span="6" label="平台驳回" :value="unitFormatter(tableData.freightCalcType, tableData.ptbh)"></detail-col>
            <detail-col :span="6" label="收货完成" :value="unitFormatter(tableData.freightCalcType, tableData.shwc)"></detail-col>
            <detail-col :span="6" label="" value=""></detail-col>
            <detail-col :span="6" label="未结算" :value="unitFormatter(tableData.freightCalcType, tableData.wjs)"></detail-col>
            <detail-col :span="6" label="已结算" :value="unitFormatter(tableData.freightCalcType, tableData.yjs)"></detail-col>

          </detail-row>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: ['tableData'],
  methods: {
    getNormalLoss() {
      let normalLoss = this.tableData.consumeRatio
      if (this.tableData.consumeRatio === null) return ''
      if (this.tableData.consumeType === '0') {
        return normalLoss + '%'
      } else if (this.tableData.consumeType === '1') {
        return normalLoss + '千克/车'
      } else {
        return ''
      }
    }
  },
  computed: {
    received() {
      if (this.tableData.freightCalcType === '0') {
        return this.tableData.receivedTons !== null ? this.tableData.receivedTons + '吨' : ''
      } else {
        return this.tableData.reciveOrderIem !== null ? this.tableData.reciveOrderIem + '车' : ''
      }
    }
  }
}
</script>
<style scoped lang="scss">
.main-box {
  background-color: white;
  .title-box {
    font-size: 16px;
    font-weight: 700;
    height: 40px;
    line-height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  .title-box el-button {
    margin-right: 10px;
  }
  .list-box {
    margin-top: 20px;
    border: 1px solid #cccccc;
    border-left: none;
    .item-title {
      display: flex;
      flex-direction: row;
      div {
        width: 500px;
        height: 50px;
        font-weight: bold;
        font-size: 16px;
        line-height: 50px;
        border: 1px solid #cccccc;
        border-top: none;
        border-right: none;
        background-color: rgb(249, 252, 250);
        text-align: center;
      }
    }
    .item-info {
      display: flex;
      flex-direction: row;
      div {
        font-size: 14px;
        width: 500px;
        height: 50px;
        line-height: 50px;
        text-align: center;
        border: 1px solid #cccccc;
        border-top: none;
        border-right: none;
        border-bottom: none;
      }
    }
  }
  .base-info,
  .drive-info,
  .other-info,
  .driver-info {
    padding: 20px;
  }
}
</style>