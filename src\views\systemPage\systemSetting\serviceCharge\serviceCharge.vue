<template>
    <div class="app-container serviceCharge">
        <div class="title">
            数据列表
        </div>
        <div class="list-box">
            <template>
                <el-table
                        :data="tableData"
                        border
                        style="width: 100%">
                    <el-table-column
                            fixed
                            type="index"
                            label="序号"
                            width="50">
                    </el-table-column>
                    <el-table-column
                            prop="feeName"
                            label="手续费名称"
                            width="150">
                    </el-table-column>
                    <el-table-column
                            prop="feeTypeName"
                            label="收取类型"
                            width="120">
                    </el-table-column>
                    <el-table-column
                            prop="fee"
                            width="200"
                            label="每吨/每单收取(元)"
                          >
                    </el-table-column>
                    <el-table-column
                            prop="status"
                            label="启用状态"
                            :formatter='statusType'
                            width="120">
                    </el-table-column>
                    <el-table-column
                            prop="operateAccount"
                            label="操作人"
                            width="120">
                    </el-table-column>
                    <el-table-column
                            prop="operateTime"
                            label="操作时间"
                           >
                    </el-table-column>
                    <el-table-column
                            fixed="right"
                            label="操作"
                            width="100"
                            >
                        <template slot-scope="scope">
                            <el-button disabled @click="handleClick(scope.row)" type="text" size="small">修改</el-button>
                            <el-button disabled type="text" size="small" v-if="scope.row.status==='0'" @click="changeStatus(scope.row)">禁用</el-button>
                            <el-button disabled type="text" size="small" v-if="scope.row.status==='1'" @click="changeStatus(scope.row)">启动</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </template>

        </div>

        <!--修改弹窗-->
        <el-dialog
                title="修改平台手续费"
                :visible.sync="dialogVisible"
                width="30%"
                :before-close="handleClose">
            <div>
                <el-form ref="form" :model="form" label-width="100px">
                    <el-form-item label="收取类型:">
                        <el-input  :placeholder="feeType" :disabled="true"></el-input>
                    </el-form-item>
                    <el-form-item label="每吨/单收取:">
                        <el-input v-model="form.fee" placeholder="请输入(元)"></el-input>
                    </el-form-item>
                    <el-form-item label=" ">
                        <el-radio v-model="form.radio" label="0">启用</el-radio>
                         <el-radio v-model="form.radio" label="1">禁用</el-radio>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
    <el-button @click="dialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="suerChange">确 定</el-button>
  </span>
        </el-dialog>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                dialogVisible: false,
                tableData: [],
                feeType:'',
                form: {
                    fee: '',
                    radio:'',
                },
                changeId:'',
            }
        },
        methods: {
            statusType(row){
              if (row.status === '0') {
                    return '启动'
                } else if (row.status === '1') {
                    return '禁用'
                }
            },
            changeStatus(row){
               let id = row.id
               let status = ''
               if(row.status==='0'){
                   status=1
               }else if(row.status==='1'){
                   status=0
               }
               this.$http.post('admin-center-server/sys/updateServiceStatus?id='+id+'&status='+status).then(res=>{
                   let data =res.data;
                   if(data.code==='200'){
                      this.getDataList()
                   }
               })
            },
            /** 修改行 打开弹窗请求回显 **/
            handleClick(row) {
                let id = row.id
                this.changeId = id
                this.$http.get('/admin-center-server/sys/getServiceFeesInfo',{
                    params:{
                        id:id
                    }
                }).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){
                        this.dialogVisible = true
                        let feeType =data.data.feeType
                         this.form.fee = row.fee
                         this.form.radio = row.status
                        if(feeType==='0'){
                            this.feeType ='按吨'
                        }else if(feeType==='1'){
                            this.feeType ='按单'
                        }
                    }
                })
            },
            /** 确定修改 **/
            suerChange(){
                this.dialogVisible = false
                let obj={
                    id:this.changeId,
                   status:this.form.radio,
                   fee:this.form.fee
                }
                this.$http.post('/admin-center-server/sys/updateServiceFee',obj).then(res=>{
                    let data = res.data;
                    if(data.code==='200'){

                        this.getDataList()
                    }
                })
            },
            handleClose(done) {
                this.dialogVisible=false
                // this.$confirm('确认关闭？')
                //     .then(_ => {
                //         done();
                //     })
                //     .catch(_ => {
                //     });
            },
            onSubmit() {
            },
            /** 获取数据列表 **/
            getDataList(){
              this.$http.get('/admin-center-server/sys/getServiceFeeList').then(res=>{
                  let data = res.data;
                  if(data.code==='200'){
                    this.tableData = data.data
                  }
              })
            },
        },
        activated(){
          this.getDataList()
        }
    }
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
    .serviceCharge {
        .title {
            font-size: 16px;
            font-weight: 700;
            height: 40px;
            line-height: 40px;
            padding-left: 10px;
            border-bottom: 1px solid #cccccc;
            background-color: #ffffff;
        }

        .list-box {
            padding: 20px;
            margin-top: 10px;
            background-color: #ffffff;
        }
    }
</style>
