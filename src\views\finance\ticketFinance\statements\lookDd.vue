/**
  编辑结算单下的 查看订单
**/
<template>
  <div class="app-container detailedList">
    <div class="select-box">
      <div class="top-title">订单查看</div>
      <div class="curTotalInfo"><span>结算单号：201911150929178296  客户名称：王奔奔 </span></div>
    </div>
    <div class="select-time">
      <el-row>
        <el-date-picker
                size="mini"
                v-model="date"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
        </el-date-picker>
        <el-button style="margin-left: 20px" size="mini" icon="el-icon-search" type="primary">查 询</el-button>
      </el-row>
    </div>
    <div class="list-box">
      <el-row>
        <div class="list-right">
          <div class="right-title" style="display: flex;justify-content: space-between"><span>数据列表</span>
          <span style="margin-right: 10px"> 当前搜索结果总计：开票吨数3402.5吨；开票金额1328150.1632</span>
          </div>
          <div class="list-main">
            <template>
              <el-table :data="tableData" border style="width: 100%">
                <el-table-column prop="num" label="序号" type="index" width="50"></el-table-column>
                <el-table-column prop="sn" label="清单号"></el-table-column>
                <el-table-column prop="finalStatementSn" label="结算单号"></el-table-column>
                <el-table-column prop="amount" label="开票金额(元)"></el-table-column>
                <el-table-column prop="ton" label="开票吨数(吨)"></el-table-column>
                <el-table-column :formatter="invoiceStatus" prop="invoice" label="清单状态"></el-table-column>
                <el-table-column prop="createdDate" label="创建日期"></el-table-column>
<!--                <el-table-column fixed="right" label="操作" width="180">-->
<!--                  <template slot-scope="scope">-->
<!--                    <el-button @click="handleClick(scope.row)" type="text" size="small">查看</el-button>-->
<!--                    <el-button type="text" size="small" @click="exportRow(scope.row)">导出</el-button>-->
<!--                  </template>-->
<!--                </el-table-column>-->
              </el-table>
            </template>
          </div>
          <div class="paging">
            <div class="block">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="10"
                layout="total, sizes, prev, pager, next, jumper"
                :total="1"
              ></el-pagination>
            </div>
          </div>
        </div>
      </el-row>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      date:'',
      currentPage:1,
      tableData: []
    };
  },
  methods: {
    /** 清单状态 **/
    invoiceStatus(row) {
      if (row.invoice === "0") {
        return "待开票";
      } else if (row.invoice === "1") {
        return "待结算";
      } else if (row.invoice === "2") {
        return "已结算";
      } else if (row.invoice == null) {
        return "暂无";
      }
    },
    /** 导出行 **/
    exportRow(row) {
      let id = row.id;
      this.$http.get('/admin-center-server/app/invoice_schedule/exportSchedules',{
        params:{
          invoiceScheduleId:id
        }
      }).then(res=>{
        let data = res.data;
        if(data.code===200){
          let uploadUrl = data.data;
          window.location.href=uploadUrl;
        }
      })
    },

    /** 编辑行 同新增参数 **/
    handleClick(row) {
      console.log(row);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
    getDataList() {
      let id = sessionStorage.getItem("qdId");
      this.$http
        .get("/admin-center-server/app/invoice_schedule/list", {
          params: {
            finalStatementId: id,
            pageNumber: 1,
            pageSize: 10
          }
        })
        .then(res => {
          let data = res.data;
          console.log(data);
          if (data.code === "200") {
            this.tableData = data.data.list;
          }
        });
    },
    getTitleInfo(){
      let id = sessionStorage.getItem("qdId");
       this.$http.get('/admin-center-server/app/invoice_schedule/getCounts',{
          params:{
            finalStatementId:id
          }
       }).then(res=>{
         let data = res.data;
         console.log(data);
       })
    },
  },
  activated() {
    this.getDataList();
    this.getTitleInfo();
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.tip {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 0 40px 40px 0;
  font-size: 12px;
  em {
    margin-right: 5px;
  }
}
.inner-box {
  margin-left: 10%;
  width: 70%;
  .upload-box {
    width: 100%;
    height: 100%;
    position: relative;
    .icon-XZ {
      width: 92px;
      height: 92px;
      margin: 0 auto;
      background-size: 100% 100%;
    }
    .icon-word {
      width: 100%;
      height: 20px;
      line-height: 20px;
      font-size: 10px;
      position: absolute;
      bottom: 25px;
      left: 0px;
      color: #cccccc;
    }
  }
}

.detailedList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 16px;
      font-weight: 700;
      height: 40px;
      line-height: 40px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 20px;
    }
    .curTotalInfo {
      padding-left: 20px;
      height: 30px;
      line-height: 30px;
      font-size: 12px;
      color: #999999;
    }
  }
.select-time{
  background: #ffffff;
  height: 50px;
  padding-left: 10px;
  padding-top: 10px;
  margin-top: 10px;
}
  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;
    overflow: hidden;

    .list-right {
      padding-left: 10px;

      .right-title {
        font-size: 14;
        font-weight: 700;
      }

      .list-main {
        width: 100%;
        border: 1px solid #cccccc;
        margin-top: 10px;
      }

      .paging {
        margin-top: 10px;
        float: right;
      }
    }
  }
}
</style>
