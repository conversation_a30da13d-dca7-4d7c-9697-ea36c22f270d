<template>
  <div class="app-container addCar">
    <div class="tip">
      <div>发布消息</div>
      <div>
        <em style="color: red">*</em>为必填项
      </div>
    </div>
    <div class="inner-box">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item :label="title" required>
          <el-input v-model="ruleForm.title" style="width: 220px" placeholder="请输入" maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="推送时间" required>
          <el-radio-group v-model="ruleForm.resource" @change="eventTypeFun">
            <el-radio label="1">立即</el-radio>
            <el-radio label="2">延迟</el-radio>
          </el-radio-group>
          <el-date-picker
            style="margin-left:16px"
            v-model="ruleForm.dateTime"
            type="datetime"
            placeholder="选择日期时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            v-if="ruleForm.resource == 2"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="消息内容" required>
          <!-- <div id="wangeditor"> -->
          <!-- <div id="editor"> -->
          <!-- <p>欢迎使用 <b>wangEditor</b> 富文本编辑器</p> -->
          <!-- </div> -->
          <!-- </div> -->
          <el-input type="textarea" :rows="15" v-model="text"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="commit" v-if="typeFlag == 1">提交</el-button>
          <el-button @click="upDateMessage" v-if="typeFlag == 2">修改</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
// 引入wangEditor富文本
import E from "wangeditor";
import "wangeditor/release/wangEditor.min.css";
import { get } from "http";
const btnmenu = [
  "head", //标题
  "bold", //粗体
  "fontSize", //字号
  "fontName", //字体
  "italic", //斜体
  "undeline", //下划线
  "strikeThrough", //删除线
  "foreColor", //文字颜色
  "backColor", //背景颜色
  "link", //插入链接
  "list", //列表
  "justify", //对齐方式
  "quote", //引用
  "emoticon", //表情
  "image", //插入图片
  "table", //表格
  "video", //插入视频
  "code", //插入代码
  "undo", //撤销
  "redo" // 重复
];
export default {
  data() {
    return {
      exconten: "",
      editor: "",
      info: "",
      editorContent: "",
      title: "消息标题",
      dialogImageUrl: "",
      radio: "1",
      eventType: "1",
      push: "1",
      typeFlag: "",
      dialogVisible: false,
      imgPath: "", //图片路径

      text: "", //消息
      ruleForm: {
        title: "", //活动标题
        resource: "1",
        dateTime: ""
      },
      rules: {
        name: [
          { required: true, message: "请输入活动名称", trigger: "blur" },
          { message: "长度在 3 到 5 个字符", trigger: "blur" }
        ],
        region: [{ required: true, message: "请选择车型", trigger: "change" }],
        zongzhong: [{ required: true, message: "填写总重", trigger: "blur" }],
        zaizhong: [{ required: true, message: "填写载重", trigger: "blur" }],

        resource: [
          { required: true, message: "请选择活动资源", trigger: "change" }
        ]
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            }
          },
          {
            text: "昨天",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            }
          },
          {
            text: "一周前",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            }
          }
        ]
      },
      value1: "",
      value2: "",
      value3: ""
    };
  },

  activated() {
    /* 富文本分为修改和新增 */
    let flag = this.$route.params.flag; // flag=1; 新增；2 编辑
    this.typeFlag = this.$route.params.flag;
    this.ruleForm.dateTime = this.$route.params.releaseDate
    this.ruleForm.resource = this.$route.params.push
    console.log(flag, "------flag");

    this.editor = new E("#editor");
    // 自定义菜单配置
    this.editor.customConfig.menus = btnmenu;
    // 配置上传图片为base64
    this.editor.customConfig.uploadImgShowBase64 = true;
    // 配置图片上传服务器
    // this.editor.customConfig.uploadImgServer = "/upload";
    // 隐藏“网络图片”tab
    this.editor.customConfig.showLinkImg = false;
    // withCredentials（跨域传递 cookie）
    this.editor.customConfig.withCredentials = true;
    // 自定义header
    this.editor.customConfig.uploadHeader = {};
    // 后端接收上传文件的参数名
    this.editor.customConfig.uploadFileName = "";
    // 将图片大小限制为2M
    this.editor.customConfig.uploadImgMaxSize = 2 * 1024 * 1024;
    // 限制最多上传6张图片
    this.editor.customConfig.uploadImgMaxLength = 6;
    // 设置超时
    this.editor.customConfig.uploadImgTimeout = 3 * 60 * 1000;
    // 关闭粘贴样式的过滤
    this.editor.customConfig.pasteFilterStyle = false;
    // 忽略粘贴内容中的图片
    this.editor.customConfig.pasteIgnoreImg = true;
    // 自定义处理粘贴的文本内容
    this.editor.customConfig.pasteTextHandle = function(content) {
      // content 即粘贴过来的内容（html 或 纯文本），可进行自定义处理然后返回
      // return content + '<p>在粘贴内容后面追加一行</p>'
      return content;
    };
    // url 即插入图片的地址(插入网络图片的回调)
    this.editor.customConfig.linkImgCallback = function(url) {
      console.log(url);
    };
    // 插入链接的校验
    this.editor.customConfig.linkCheck = function(text, link) {
      console.log(text); // 插入的文字
      console.log(link); // 插入的链接
      return true; // 返回 true 表示校验成功
      // return '验证失败' // 返回字符串，即校验失败的提示信息
    };
    // 插入网络图片的校验
    this.editor.customConfig.linkImgCheck = function(src) {
      console.log(src); // 图片的链接
      return true; // 返回 true 表示校验成功
      // return '验证失败' // 返回字符串，即校验失败的提示信息
    };
    // 创建富文本编辑器
    this.editor.create();

    // 获取富文本内容
    if (flag == 1) {
      //新增
      this.editor.txt.html();
      this.text = ''
      this.ruleForm.title = ''
      this.ruleForm.resource = '1'
    } else if (flag == 2) {
      //修改
      // let content = this.$route.params.content; //获取内容
      // this.editor.txt.html(content);
      this.text =  this.$route.params.content
      this.ruleForm.title = this.$route.params.title; //标题
    }
    // 编辑区域的z-index默认为100
    this.editor.customConfig.zIndex = 100;
    // 图片上传回调
    this.editor.customConfig.uploadImgHooks = {
      fail: (xhr, editor, result) => {
        /**插入图片回调失败 */
      },
      success: (xhr, editor, result) => {
        /**图片上传成功回调 */
      },
      timeout: (xhr, editor, result) => {
        /**网络超时回调 */
      },
      error: (xhr, editor, result) => {
        /**图片上传错误回调 */
      },
      customInsert: (insertImg, result, editor) => {
        /**图片上传成功，插入图片回调 */
      }
    };
    // 上传图片的错误提示默认使用alert弹出，你也可以自定义用户体验更好的提示方式
    this.editor.customConfig.customAlert = function(info) {
      // info 是需要提示的内容
      alert("自定义提示：" + info);
      console.log(info);
    };
    this.editor.customConfig.customUploadImg = function(files, insert) {
      // files 是 input 中选中的文件列表
      // insert 是获取图片 url 后，插入到编辑器的方法
      // 上传代码返回结果之后，将图片插入到编辑器中
      insert(imgUrl);
    };
    // 监听实时富文本输入内容
    this.editor.customConfig.onchange = html => {
      this.editorContent = html;
      console.log(this.editorContent, "-------");
    };
  },
  methods: {
    handleRemove(file, fileList) {
      // console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          alert("submit!");
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    /* 选择活动类型 */
    eventTypeFun(val) {
      console.log(this.ruleForm.resource);
      if (this.ruleForm.resource == 1) {
        this.push = "1";
      } else {
        this.push = "2";
      }
    },
    /* 新添加的提交 */
    commit() {
      this.editorContent = this.editor.txt.html();
      console.log(this.editorContent, "-------");
      this.editor.txt.html("");
      var postData = {};
      if (this.ruleForm.resource == 1) {
        // 立即发送
        postData = {
          push: Number(this.push),
          content: this.text, //现在改成文本输入
          releaseUserId: this.$route.params.id, //发布人员
          title: this.ruleForm.title
        };
        if (this.ruleForm.title == "") {
          this.$message({
            type: "warning",
            message: "请输入消息标题"
          });
          return;
        }
        if (this.ruleForm.text == "") {
          this.$message({
            type: "warning",
            message: "请输消息内容"
          });
          return;
        }
      } else if (this.ruleForm.resource == 2) {
        postData = {
          push: Number(this.push),
          content: this.text,
          releaseDate: this.ruleForm.dateTime, //发送时间
          releaseUserId: this.$route.params.id, //发布人员
          title: this.ruleForm.title
        };
        if (this.ruleForm.title == "") {
          this.$message({
            type: "warning",
            message: "请输入消息标题"
          });
          return;
        }

        if (this.ruleForm.dateTime == "") {
          this.$message({
            type: "warning",
            message: "请选择日期时间"
          });
          return;
        }
        if (this.ruleForm.text == "") {
          this.$message({
            type: "warning",
            message: "请输消息内容"
          });
          return;
        }
      }
      console.log(postData, "-----postData");

      this.$http
        .post("/admin-center-server/message/addMessage", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$router.go(-1);
          } else {
            this.$message.warning(data.message);
          }
        });
    },

    /* 修改的提交 */
    upDateMessage() {
      // this.editorContent = this.editor.txt.html();
      this.editorContent = this.text;
      console.log(this.editorContent, "-------");
      this.editor.txt.html("");
      var postData = {};
      if (this.ruleForm.resource == 1) {
        // 立即发送
        postData = {
          push: Number(this.push),
          content: this.editorContent,
          releaseUserId: this.$route.params.id, //发布人员
          title: this.ruleForm.title,
          id: this.$route.params.id
        };
      } else if (this.ruleForm.resource == 2) {
        postData = {
          push: Number(this.push),
          content: this.editorContent,
          releaseDate: this.ruleForm.dateTime, //发送时间
          releaseUserId: this.$route.params.id, //发布人员
          title: this.ruleForm.title,
          id: this.$route.params.id
        };
      }
      console.log(postData, "-----postData");
      this.$http
        .post("/admin-center-server/message/updateMessage", postData)
        .then(res => {
          let data = res.data;
          if (data.code === "200") {
            this.$message({
              type: "success",
              message: "修改成功!"
            });
            this.$router.go(-1);
          }
        });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss">
.w-e-menu {
  z-index: 2 !important;
}
.w-e-text-container {
  z-index: 1 !important;
}
.editor {
  margin-top: 30px;
}
.addCar {
  background-color: #ffffff;
  .tip {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 40px 40px 0;
    font-size: 12px;
    em {
      margin-right: 5px;
    }
  }
  .inner-box {
    // margin-left: 10%;
    width: 70%;
    .upload-box {
      width: 100%;
      height: 100%;
      position: relative;
      .icon-XZ {
        width: 92px;
        height: 92px;
        margin: 0 auto;
        background: url("../../../../assets/xiazai.png") no-repeat;
        background-size: 100% 100%;
      }
      .icon-word {
        width: 100%;
        height: 20px;
        line-height: 20px;
        font-size: 10px;
        position: absolute;
        bottom: 25px;
        left: 0px;
        color: #cccccc;
      }
    }
  }
}

.myQuillEditor {
  height: 400px;
}
.editCommit {
  margin: 100px;
}
</style>
