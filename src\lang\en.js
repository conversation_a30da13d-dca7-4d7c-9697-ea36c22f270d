export default {
    route: {
        dashboard: 'Dashboard',
        introduction: 'Introduction',
        documentation: 'Documentation',
        guide: 'Guide',
        errorPages: 'Error Pages',
        page401: '401',
        page404: '404',
        errorLog: 'Error Log',
        permission: 'Permission',
        pagePermission: 'Page Permission',
        directivePermission: 'Directive Permission',
        //中台用户
        userNav: 'UserNav',
        // 货主管理
        consignorAccount: 'ConsignorAccount',
        consignorSubAccount: 'ConsignorSubAccount',
        consignorExamine: 'ConsignorExamine',
        //调度员管理
        agentAccount: 'AgentAccount',
        agentSubAccount: 'AgentSubAccount',
        agentExamine: 'AgentExamine',
        //司机
        driverAccount: 'DriverAccount',
        driverExamine: 'DriverExamine',

        orderNav: 'OrderNav',
        // 订单
        orderManage: 'OrderManage',
        agentOrder: 'AgentOrder',

        //运单
        transportNav:'TransportNav',
        //运单管理
        transportList:'TransportList',

        //调度
        dispatchNav:'DispatchNav',
        carsList:'CarsList',
        CarDetail:'CarDetail',//查看车辆详情

        carsAttestation:'CarsAttestation',
        carsEmpower:'CarsEmpower',
        carsBelong:'CarsBelong',
    },
    login: {
        title: 'Login Form',
        logIn: 'Log in',
        username: 'Username',
        password: 'Password'
    },
    navbar: {
        logOut: 'Log Out',
        dashboard: 'Dashboard',
        github: 'Github',
        theme: 'Theme',
        size: 'Global Size'
    },
    guide: {
        description: 'The guide page is useful for some people who entered the project for the first time. You can briefly introduce the features of the project. Demo is based on ',
        button: 'Show Guide'
    },
    tagsView: {
        refresh: 'Refresh',
        close: 'Close',
        closeOthers: 'Close Others',
        closeAll: 'Close All'
    },
    permission: {
        roles: 'Your roles',
        switchRoles: 'Switch roles',
        tips: 'In some cases it is not suitable to use v-permission, such as element Tab component or el-table-column and other asynchronous rendering dom cases which can only be achieved by manually setting the v-if.'
    }
}
