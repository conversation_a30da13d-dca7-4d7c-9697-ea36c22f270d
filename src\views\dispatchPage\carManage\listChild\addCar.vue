<template>
  <div class="app-container">
    <div class="tip">
      <div>挂车仅需上传驾驶证</div>
      <div>*为必填项</div>
    </div>
    <el-form
      :model="form"
      ref="form"
      :rules="rules"
      label-width="170px"
      class="form">
      <el-form-item label="关联司机：" prop="driverMobile">
        <el-autocomplete
          class="form-input"
          :fetch-suggestions="handleDriverInput"
          @change="handleDriverChange"
          v-model="form.driverMobile"
          placeholder="请输入在本平台认证成功的司机手机号">
          <template slot-scope="scope">{{ scope.item.label }}</template>
        </el-autocomplete>
      </el-form-item>
      <el-form-item label="关联货主：">
        <el-autocomplete
          class="form-input"
          :fetch-suggestions="handleOwnerInput"
          @change="handleOnwerChange"
          v-model="form.businessMobile"
          placeholder="代货主添加时需填写此项，按货主名称搜索">
          <template slot-scope="scope">{{ scope.item.label }}</template>
        </el-autocomplete>
      </el-form-item>
      <el-form-item prop="drivingLicense">
        <template slot="label">
          <span class="required-asterisk">*</span> 主车行驶证：
        </template>
        <ImageUploader2 @change="licenseImageOcr" type="uploader" description="正页"></ImageUploader2>
        <ImageUploader2 @change="licenseBackImageOcr" type="uploader" description="副页"></ImageUploader2>
        <ImageUploader2 @change="licenseExpireImageOcr" type="uploader" description="到期页"></ImageUploader2>
      </el-form-item>
      <el-form-item label="车牌号：" prop="plateNumber">
        <el-input v-model="form.plateNumber" placeholder="OCR自动识别"></el-input>
      </el-form-item>
      <el-form-item label="车辆类型：" prop="carModelId">
        <CarTypeSelector v-model="form.carModelId" @changeName="handleCarModelNameChange" placeholder="OCR自动识别" ref="carTypeSelector" class="carTypeSelector"></CarTypeSelector>
      </el-form-item>
      <el-form-item label="准牵引总质量：" prop="tractionMass">
        <el-input v-model="form.tractionMass" placeholder="OCR自动识别">
          <template #append>kg</template>
        </el-input>
        <div class="form-tip">牵引车必填</div>
      </el-form-item>
      <el-form-item label="核定载质量：" prop="capacityTonnage">
        <el-input v-model="form.capacityTonnage" placeholder="OCR自动识别">
          <template #append>kg</template>
        </el-input>
        <div class="form-tip">非牵引车必填</div>
      </el-form-item>
      <el-form-item label="道路运输证：" prop="shippingCertImage">
        <div>4.5吨及以下普通货车无需上传</div>
        <ImageUploader2 @change="shippingCertImageOcr" type="uploader"></ImageUploader2>
      </el-form-item>
      <el-form-item label="道路运输证号：" prop="shippingCert">
        <el-input v-model="form.shippingCert" placeholder="OCR自动识别"></el-input>
        <div class="abnormal">
          <el-checkbox v-model="isShippingAbnormal">存在异常</el-checkbox>
          <el-select v-if="isShippingAbnormal" v-model="form.shippingCertWarnMemo">
            <el-option value="网站无相关信息"></el-option>
            <el-option value="网站查询过期"></el-option>
            <el-option value="道路运输证号与网站不一致"></el-option>
          </el-select>
        </div>
      </el-form-item>
      <el-form-item label="道路运输证号有效期：" prop="shippingCertExpire">
        <el-date-picker v-model="form.shippingCertExpire" placeholder="OCR自动识别" value-format="yyyy-MM-dd"></el-date-picker>
      </el-form-item>
      <el-form-item label="车主声明：">
        <div>行驶证所属人非个人时需上传</div>
        <ImageUploader2 @change="url => form.statementImage = url" type="uploader"></ImageUploader2>
      </el-form-item>
      <el-form-item label="人车合照：">
        <div>上传人车同框照片</div>
        <ImageUploader2 @change="url => form.carImage = url" type="uploader"></ImageUploader2>
      </el-form-item>
      <el-form-item>
        <el-button @click="submit" type="primary">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import CarTypeSelector from '../components/CarTypeSelector'
export default {
  components: {
    CarTypeSelector
  },
  data() {
    return {
      form: {},
      rules: {
        driverMobile: [
          { required: true, message: '关联司机不能为空', trigger: 'change' }
        ],
        drivingLicense: [
          {
            validator: (rule, value, cb) => {
              if (!this.form.licenseImage) {
                return cb('请上传行驶证正页')
              }
              if (!this.form.licenseBackImage) {
                return cb('请上传行驶证副页')
              }
              if (!this.form.licenseExpireImage) {
                return cb('请上传行驶证到期页')
              }
              cb()
            }
          }
        ],
        plateNumber: [
          { required: true, message: '车牌号不能为空', trigger: 'blur' }
        ],
        carModelId: [
          { required: true, message: '车辆类型不能为空', trigger: 'change' }
        ],
        tractionMass: [
          {
            validator: (rule, value, cb) => {
              if (this.isMainTractor && !this.form.tractionMass) {
                return cb('准牵引总质量不能为空')
              }
              cb()
            },
            trigger: 'blur'
          }
        ],
        capacityTonnage: [
          {
            validator: (rule, value, cb) => {
              if (!this.isMainTractor && !this.form.capacityTonnage) {
                return cb('核定载质量不能为空')
              }
              cb()
            },
            trigger: 'blur'
          }
        ],
        shippingCertImage: [
          {
            validator: (rule, value, cb) => {
              //如果货车并且核定载质量<4500，或者是挂车，不做校验
              if (
                this.form.carModelName && this.form.carModelName.includes('货车') && Number(this.form.capacityTonnage) < 4500
                ||
                this.form.carModelName && this.form.carModelName.includes('挂车')
              ) {
                return cb()
              } else if (!this.form.shippingCertImage) {
                return cb('请上传道路运输证')
              }
              cb()
            },
            trigger: 'blur'
          }
        ]
      },
      isShippingAbnormal: false,
      isDanger:false
    }
  },
  computed: {
    isMainTractor() {
      return typeof this.form.carModelName === 'string' && this.form.carModelName.includes('牵引车')
    }
  },
  activated() {
    this.form = {}
    this.isShippingAbnormal = false
  },
  methods: {
    handleDriverInput(v, cb) {
      if (!v) {
        return cb([])
      }
      let params = {
        userType: 3,
        interfaceType: 0
      }
      if (v.match(/^\d*$/)) {
        params.type = 1
        params.mobile = v
      } else {
        params.type = 2
        params.name = v
      }
      this.$post('/admin-center-server/car/relevancyUser', params)
        .then(res => {
          let list = res.map(v => {
            return {
              value: v.mobile,
              label: `${v.name}/${v.mobile}`
            }
          })
          cb(list)
        })
    },
    handleDriverChange() {
      this.form.driverMobile = ''
    },
    handleOwnerInput(v, cb) {
      if (!v) {
        return cb([])
      }
      let params = {
        userType: 1,
        type: 2,
        name: v,
        interfaceType: 0
      }
      this.$post('/admin-center-server/car/relevancyUser', params)
        .then(res => {
          let list = res.map(v => {
            return {
              value: v.mobile,
              label: v.name
            }
          })
          cb(list)
        })
    },
    handleOnwerChange() {
      this.form.businessMobile = ''
    },
    submit() {
      if(this.isDanger){
        this.$message.error('提交失败，原因：车辆使用性质不符合平台要求')
        return
      }
      this.$refs.form.validate()
        .then(() => {
          let params = { ...this.form }
          params.type = 0 //固定值
          if (this.isShippingAbnormal) {
            params.shippingCertWarnFlag = 1
          } else {
            params.shippingCertWarnFlag = 0
          }
          this.$post('/admin-center-server/car/add', {
            ...params
          })
            .then(() => {
              this.$message.success('操作成功')
              this.$router.go(-1)
            })
        })
    },
    handleCarModelNameChange(name) {
      this.$set(this.form, 'carModelName', name)
    },
    licenseImageOcr(url) {
      this.$set(this.form, 'licenseImage', url)
      if (!url) return
      this.$post('/order-center-server/ocr/ocrImage', {
        url,
        type: 3,
        side: 'front'
      })
        .then(res => {
          let data = res.vehicleLicenseRes
          if(data.licensesUseCharacter){
            if(data.licensesUseCharacter.includes('客运')){
              this.isDanger = true
              this.$message.error('车辆使用性质不符合平台要求')
            }else{
              this.isDanger = false
            }
          }
          this.$set(this.form, 'plateNumber', data.plateNumber)
          this.$refs.carTypeSelector.selectByName(data.carModelName)
          this.form.licenseOwner = data.licenseOwner
          this.form.licensesUseCharacter = data.licensesUseCharacter
          this.form.cypp = data.cypp
          this.form.licensesVin = data.licensesVin
          if (data.licensesVin && !this.shippingCert) this.form.licenseNumber = data.licensesVin
          this.form.licensesIssueOrg = data.licensesIssueOrg
          this.form.licensesRegisterDate = data.licensesRegisterDate
          this.form.licensesIssueDate = data.licensesIssueDate
          this.form.carArea = data.carArea
        })
    },
    licenseBackImageOcr(url) {
      this.$set(this.form, 'licenseBackImage', url)
      if (!url) return
      this.$post('/order-center-server/ocr/ocrImage', {
        type: 3,
        url,
        side: 'back'
      })
        .then(res => {
          let data = res.vehicleLicenseRes
          //licenseNumber 先取副页的shippingCert，如果是null或者是空，取正页的licensesVin
          if (data.shippingCert) {
            this.form.licenseNumber = data.shippingCert
            this.shippingCert = data.shippingCert
          }
          if (data.sumCapacityTonnage) this.form.sumCapacityTonnage = data.sumCapacityTonnage * 1000
          if (data.curbWeight) this.form.curbWeight = data.curbWeight * 1000
          if (data.capacityTonnage) this.$set(this.form, 'capacityTonnage', data.capacityTonnage * 1000)
          if (data.tractionMass) this.$set(this.form, 'tractionMass', data.tractionMass * 1000)
          if (data.carSize) this.form.carSize = data.carSize
          this.form.energyType = data.energyType
        })
    },
    licenseExpireImageOcr(url) {
      this.$set(this.form, 'licenseExpireImage', url)
      if (!url) return
      this.$post('/order-center-server/ocr/ocrImage', {
        type: 3,
        side: 'expire',
        url
      })
        .then(res => {
          let data = res.vehicleLicenseRes
          this.form.licenseExpire = data.deputyLicenseExpire
        })
    },
    shippingCertImageOcr(url) {
      this.$set(this.form, 'shippingCertImage', url)
      if (!url) return
      this.$post('/order-center-server/ocr/ocrImage', {
        url,
        type: 6,
      })
        .then(res => {
          let data = res.roadTransportCertificateRes
          this.$set(this.form, 'shippingCert', data.permitNumber)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.tip {
  display: flex;
  justify-content: space-between;
  color: #DD2042;
  div:first-child {
    font-size: 16px;
  }
  div:last-child {
    font-size: 12px;
  }
}
.form {
  margin-top: 30px;
  .el-input,
  .carTypeSelector,
  .el-autocomplete {
    width: 300px;
  }
}
.abnormal {
  margin-top: 10px;
  .el-checkbox {
    margin-right: 10px;
  }
}
.form-tip {
  margin-left: 10px;
  display: inline-block;
  color: rgb(221, 32, 66);
}
</style>