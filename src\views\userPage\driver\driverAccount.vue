<template>
  <div class="app-container carsList">
    <div class="select-box">
      <div class="top-title">账户查询</div>
      <div class="select-info">
        <el-form :inline="true"
                 :model="formInline"
                 ref="formInline"
                 class="demo-form-inline"
                 size="mini"
                 label-width="90px">
          <el-form-item label="司机名称:"
                        prop='nameOrCompanyName'>
            <el-input style='width:280px'
                      v-model="formInline.nameOrCompanyName"
                      :placeholder="`司机${!$store.state.user.userInfo2.hasStandardModeFlag ? '/个人车队长姓名、运力供应商公司' : ''}名称`"></el-input>
          </el-form-item>
          <el-form-item label="手机号:"
                        prop='mobile'>
            <el-input v-model="formInline.mobile"
                      oninput="value=value.replace(/[^\d]/g,'')"
                      maxlength="11"
                      placeholder="请输入注册手机号"></el-input>
          </el-form-item>
          <el-form-item v-if="!$store.state.user.userInfo2.hasStandardModeFlag"
                        label="数据来源："
                        label-width="100px"
                        prop='dataFrom'>
            <el-select v-model="formInline.dataFrom"
                       placeholder="请选择"
                       clearable
                       label-width="10px">
              <el-option v-for="(item, index) in userSourceList"
                         :label="item.name"
                         :value="item.id"
                         :key="index"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="认证状态："
                        label-width="130px"
                        prop='authStatus'>
            <el-select v-model="formInline.authStatus"
              placeholder="请选择认证状态"
              clearable
              label-width="10px">
            <el-option v-for="(item, index) in statusList"
              :key="index"
              :label="item.name"
              :value="item.id"></el-option>
            </el-select>
            <el-radio-group v-if="!$store.state.user.userInfo2.hasStandardModeFlag" v-model="formInline.driverType" class="type-radio">
              <el-radio label="99">所有</el-radio>
              <el-radio label="1">司机</el-radio>
              <el-radio label="3">个人车队长</el-radio>
              <el-radio label="5">运力供应商</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="注册时间："
                        label-width="100px"
                        prop='date'>
            <el-col>
              <el-date-picker v-model="registerDate"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              type="datetimerange"
                              range-separator="至"
                              start-placeholder="选择开始时间"
                              end-placeholder="选择结束时间"
                              :default-time="['00:00:00', '23:59:59']">
              </el-date-picker>
            </el-col>
          </el-form-item>
          <el-form-item label="身份证号："
                        prop='idCard'>
            <el-input v-model="formInline.idCard"
                      oninput="value=value.replace(/[^\d]/g,'')"
                      placeholder="请输入身份证号"></el-input>
          </el-form-item>
          <el-form-item label="从业资格证是否异常：" prop="employmentCertWarnFlag" label-width="180px">
            <el-select v-model="formInline.employmentCertWarnFlag">
              <el-option label="不限" value=""></el-option>
              <el-option label="是" value="1"></el-option>
              <el-option label="否" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="临期状态：" prop="nearExpirationStatus">
            <el-select v-model="formInline.nearExpirationStatus">
              <el-option label="不限" value=""></el-option>
              <el-option label="即将到期" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button class="left"
                       icon="el-icon-search"
                       @click="onSubmit">查询</el-button>
            <el-button class="left"
                       @click="resetForm('formInline')"
                       icon="el-icon-refresh-right">重置</el-button>
            <el-button class="left"
                       @click="refreshfn"
                       icon="el-icon-refresh-right">刷新</el-button>
          </el-form-item>
        </el-form>

      </div>
    </div>
    <div class="list-box">
      <div class="list-title">
        <div>账户列表</div>
      </div>
      <div class="list-main">
        <template>
          <el-table ref="multipleTable"
                    :data="tableData"
                    tooltip-effect="dark"
                    style="width: 100%"
                    :height="tableHeight"
                    @selection-change="handleSelectionChange"
                    border>
            <el-table-column type="selection"
                             width="55"></el-table-column>
            <el-table-column type="index"
                             label="序号"
                             width="55">
            </el-table-column>
            <el-table-column label="司机名称" prop="name" width="150"></el-table-column>
            <el-table-column label="手机号" prop="mobile" width="120"></el-table-column>
            <el-table-column label="身份证号" prop="idCard" width="180"></el-table-column>
            <el-table-column label="注册时间" prop="createTime" width="180">
              <template slot-scope="scope">
                {{ handleTime(scope.row.createTime)  }}
              </template>
            </el-table-column>
            <el-table-column label="邀请码" prop="inviteCode"></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="认证类型" prop="driverType" width="140"></el-table-column>
            <el-table-column label="司机认证状态" prop="driverAuthStatusName" width="120"></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="车队长认证状态" prop="carOwnerStatusName" width="120"></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="运力供应商认证状态" prop="transportationCapacitySupplierStatusName" width="120"></el-table-column>
            <el-table-column label="从业资格证异常" width="120">
              <template slot-scope="scope">
                {{ scope.row.employmentCertWarnFlag === '1' ? '是' : '否' }}
              </template>
            </el-table-column>
            <el-table-column label="创建人" prop="createUser" width="150"></el-table-column>
            <el-table-column label="账户状态" prop="deleteFlag"></el-table-column>
            <el-table-column v-if="!$store.state.user.userInfo2.hasStandardModeFlag" label="数据来源" prop="dataFrom"></el-table-column>
            <el-table-column fixed="right"
                             min-width="220"
                             label="操作">
              <template slot-scope="scope">
                <el-button @click="goDetail(scope.row)"
                           type="text"
                           size="small">查看</el-button>
                <el-button
                          v-if="scope.row.driverAuthStatus === '2' || scope.row.transportationCapacitySupplierStatus === '2'"
                          type="text"
                           @click="editfn(scope.row)"
                           size="small">编辑</el-button>
                <el-button type="text"
                           @click="resetPassword(scope.row)"
                           size="small">重置密码</el-button>
                <el-button type="text"
                           v-if="scope.row.driverAuthStatus == 2 || scope.row.carOwnerAuthStatus == 2"
                           @click="modifyPhoneNumber(scope.row)"
                           size="small">修改手机号</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page="pageNumber"
                         :page-sizes="[10,20, 40, 60, 80,100]"
                         :page-size="pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total"
                         class="pagination"></el-pagination>
        </template>
      </div>
    </div>


    <el-dialog
      title="修改手机号"
      :visible.sync="dialogVisible"
      :before-close="handleColse"
      width="40%">
      <div class="modify-title">
        <div>请注意：</div>
        <div>1、修改手机号操作，将注销原平安子账户，重新注册新的平安子账号，所以修改前请确保用户钱包余额为0</div>
        <div>2、如果原手机号已验证，则新手机号需重新验证</div>
      </div>
      <div class="modify-content">
        <div class="modify-item">司机姓名： {{selectDriverInfo.name}}</div>
        <div class="modify-item">原手机号： {{selectDriverInfo.mobile}} (<span class="mobile-status">{{selectDriverInfo.mobileAuthStatus == 1 ? '已验证' : '未验证'}}</span>) </div>
        <div class="modify-item">新手机号：
           <el-input 
           v-model="newPhoneNumber" 
           size="small"
           type="number"
           oninput="if(value.length>11)value=value.slice(0,11)"
           style="width: 150px"
           placeholder="请输入新手机号"></el-input>
           </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelChangePhoneNumber">取 消</el-button>
        <el-button type="primary" @click="confirmChangePhoneNumber">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
const updateUserPwd = '/admin-center-server/commonUser/updateUserPwd'//重置密码
export default {
  name: "CarsList",
  data () {
    return {
      dialogVisible: false,
      selectDriverInfo: {},
      newPhoneNumber: '',
      tableHeight: null, //表格的高度
      userSourceList: [
        {
          name: '注册',
          id: '0'
        },
        {
          name: '手动添加',
          id: '1'
        },
      ],
      statusList: [
        {
          name: '未认证',
          id: '1'
        },
        {
          name: '认证成功',
          id: '2'
        },
        {
          name: '认证失败（驳回）',
          id: '3'
        },
        {
          name: '认证中',
          id: '4'
        },
        {
          name: '认证过期',
          id: '5'
        },
        {
          name: '补充资源认证中',
          id: '6'
        },
        {
          name: '补充资料驳回',
          id: '7'
        }
      ],
      pageSize: 10,
      total: 0,
      pageNumber: 1,
      formInline: {
        nameOrCompanyName: "",
        mobile: "",
        dataFrom: "",
        authStatus: '',
        driverType: '99',
        idCard: '',
        employmentCertWarnFlag: '',
        nearExpirationStatus: ''
      },
      registerDate: null,
      tableData: []
    };
  },
  methods: {
    handleTime(time){
      if (time && time.indexOf('.')!== -1) {
        return time.split('.')[0]
      }
      return time
    },
    // 刷新当前页
    refreshfn () {
      this.$router.go(0)
    },
    //重置
    resetForm (formName) {
      this.$refs[formName].resetFields();
      this.registerDate = null
      this.pageNumber = 1
      this.getData()
    },
    addnewfn () { this.$router.push("/consignorAccount/addUser?urlType=3"); },
    editfn (row) {
      if (row.transportationCapacitySupplierStatus === '2') {
        this.$router.push({
          path: '/driverExamine/supplierAuditDetail',
          query: {
            id: row.id,
            type: 'edit'
          }
        })
        return
      }
      //编辑页面的条件
      if (row.driverType == '司机') {
        row.driverType = 'driver'
      } else if (row.driverType == '司机(个人车队长)') {
        row.driverType = 'driverBoss'
      } else if (row.driverType == '个人车队长') {
        row.driverType = 'singleBoss'
      } else if (row.driverType == '企业车队长') {
        row.driverType = 'enterpriseBoss'
      }
      this.$router.push("/consignorAccount/addDriver?urlType=3&id=" + row.id + '&driverType=' + row.driverType);
    },
    onSubmit () {
      this.pageNumber = 1
      this.getData()
    },
    //重置密码
    resetPassword (row) {
      var that = this
      this.$confirm('是否确认重置该账户密码？重置之后该账户登录密码即为a12345678？', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.post(updateUserPwd + '?pwd=' + this.$sm3('a12345678').toUpperCase() + '&userId=' + row.id).then(res => {
          this.$message({
            type: 'success',
            message: '操作成功'
          });
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消重置'
        });
      });
    },
    handleColse(done) {
      this.newPhoneNumber = ''
      done()
    },
    cancelChangePhoneNumber() {
      this.newPhoneNumber = ''
      this.dialogVisible = false
    },
    modifyPhoneNumber(row) {
      this.dialogVisible = true
      this.selectDriverInfo = row
    },
    confirmChangePhoneNumber() {
      this.$post('/admin-center-server/updateDriverMobile?mobile=' + this.newPhoneNumber + '&userId=' + this.selectDriverInfo.id).then(
        res => {
          if (res.code == 0) {
            this.newPhoneNumber = ''
            this.dialogVisible = false
            this.$message.success('修改手机号成功')
            this.pageNumber = 1
            this.getData()
          }
        },
      )
    },
    goDetail (row) {
      var type = ''
      if (row.carOwnerAuthStatus != 2 && row.driverAuthStatus == 2) {
        type = 1
      }
      if (row.carOwnerAuthStatus == 2 && row.driverAuthStatus == 2 && row.carAuthType == 1) {
        type = 2
      }
      if (row.carOwnerAuthStatus == 2 && row.driverAuthStatus != 2 && row.carAuthType == 1) {
        type = 3
      }
      if ((row.transportationCapacitySupplierStatus === '2' || row.transportationCapacitySupplierStatus === '3' || row.transportationCapacitySupplierStatus === '4' || row.transportationCapacitySupplierStatus === '5' || row.transportationCapacitySupplierStatus === '6' || row.transportationCapacitySupplierStatus === '7') && row.carOwnerAuthStatus !== '2' && row.driverAuthstatus !== '2') {
        type = 5
      }
      this.$router.push("/driverAccount/accountDetails?id=" + row.id + '&type=' + type);
    },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },
    handleSizeChange (val) {
      this.pageNumber = 1
      this.pageSize = val
      this.getData()
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange (val) {
      this.pageNumber = val
      this.getData()
      console.log(`当前页: ${val}`);
    },
    getData () {
      let params = {
        ...this.formInline,
        pageSize: this.pageSize,
        pageNumber: this.pageNumber
      }
      if (this.registerDate !== null) {
        params.minCreateTime = this.registerDate[0]
        params.maxCreateTime = this.registerDate[1]
      }
      this.$post('/admin-center-server/commonUser/driverAccount/list', params)
        .then(res => {
          this.tableData = res.list
          this.total = Number(res.total)
        })
    }
  },
  activated () {
    if (this.$store.state.user.userInfo2.hasStandardModeFlag) {
      this.formInline.driverType = '1'
    } else {
      this.formInline.driverType = '99'
    }
    
    this.getData()
    this.tableHeight =
      window.innerHeight - this.$refs.multipleTable.$el.offsetTop - 160;
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  ::v-deep input::-webkit-outer-spin-button,
  ::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }
  ::v-deep input[type="number"] {
    -moz-appearance: textfield;
  }
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
  .modify-title {
    color: red;
  }
  .modify-content {
    margin-top: 30px;
    margin-left: 40px;
    .modify-item {
      display: flex;
      justify-content: start;
      align-items: center;
      height: 50px;
    }
    .mobile-status {
      color: red;
    }
  }
}
.type-radio {
  margin-left: 15px;
  .el-radio {
    margin-right: 10px;
  }
}
</style>
