<template>
  <div class="app-container carsList">
    <el-button class="left"
               icon="el-icon-plus"
               @click="addrolefn">添加角色</el-button>
    <div class="list-box">
      <div class="list-title">
        <div>角色模板列表</div>
      </div>
      <div class="list-main">
        <el-table ref="multipleTable"
                  :data="tableData"
                  tooltip-effect="dark"
                  style="width: 100%"
                  @selection-change="handleSelectionChange"
                  border>
          <el-table-column type="index"
                           label="序号"
                           width="55">
          </el-table-column>
          <el-table-column show-overflow-tooltip
                           v-for="item in tableLabel"
                           :label="item.label"
                           :width="item.width ? item.width : 150">
            <template slot-scope="scope">
              <span style="margin-left: 10px">{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>

          <el-table-column fixed="right"
                           label="操作">
            <template slot-scope="scope">
              <el-button @click="goDetail(scope.row)"
                         type="text"
                         size="small">编辑权限</el-button>
              <el-button type="text"
                         @click='deletefn(scope.$index, scope.row)'
                         size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
const deleteRole = '/admin-center-server/commonUser/deleteRole'//删除
const getRoles = '/admin-center-server/commonUser/getRoles'//列表
export default {
  name: "CarsList",
  comments: {},
  data () {
    return {
      type: '',
      tableLabel: [
        {
          prop: 'roleName',
          label: '角色名称',
          width: 400
        }
      ],
      tableData: []
    };
  },
  methods: {
    addrolefn () {
      this.$router.push("/consignorSubAccount/addrole?type=" + this.type);
    },
    onSubmit () {
      console.log("submit!");
    },
    goDetail (row) {
      console.log(row);
      this.$router.push("/consignorSubAccount/addrole?id=" + row.roleId + '&type=' + this.type);
    },
    handleSelectionChange (val) {
      this.multipleSelection = val;
    },

    handleSizeChange (val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange (val) {
      console.log(`当前页: ${val}`);
    },
    deletefn (index, row) {
      this.$confirm('是否删除' + row.roleName, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$http.post(deleteRole + `?roleId=${row.roleId}`).then(res => {
            var that = this
            this.$message(res.data.message);
            setTimeout(function () {
              that.getData()
            }, 3000);

          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    getData () {
      this.$http.get(getRoles + `?userType=${this.type}`).then(res => {
        console.log(res)
        this.tableData = res.data.data
      })

    }
  },
  activated () {
    this.type = this.$route.query.type;
    this.getData()
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.carsList {
  .select-box {
    /*height: 260px;*/
    background-color: #ffffff;

    .top-title {
      font-size: 14px;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
      border-bottom: 1px solid #cccccc;
    }

    .select-info {
      padding-top: 10px;
      padding-bottom: 10px;
    }
  }

  .list-box {
    background-color: #ffffff;
    margin-top: 20px;
    padding: 10px;

    .list-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      div {
        font-size: 14px;
        height: 30px;
        line-height: 30px;
      }
    }

    .list-main {
      width: 100%;
      //   border: 1px solid #cccccc;
      margin-top: 10px;
    }
    .releaseMessage {
      margin-right: 20px;
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>
