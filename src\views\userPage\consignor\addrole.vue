<template>
  <div>
    <el-container>
      <el-main>
        <el-form ref="form"
                 :model="form"
                 :rules="rules"
                 label-width="100px">
          <el-form-item label="角色名称:"
                        prop='user'>
            <el-input v-model="form.user"
                      maxlength="10"
                      placeholder="请输入角色名称"></el-input>
          </el-form-item>
        </el-form>
        <el-tree :data="data"
                 show-checkbox
                 node-key="id"
                 ref='tree'
                 default-expand-all
                 :default-checked-keys="checkedID"
                 @check="handleNodeClick"
                 :props="defaultProps">
        </el-tree>
        <div style='margin-top:10px;font-size:14px;'>
          <el-checkbox v-model="checked"
                       @change="checkedAll" /> 全选/反选
        </div>
        <el-button @click="getCheckedKeys('form')">保存</el-button>
      </el-main>
    </el-container>
  </div>
</template>

<script>
const getResourceTree = '/admin-center-server/commonUser/getResourceTree'//tree
const addRole = '/admin-center-server/commonUser/addRole'//添加
const getResourceInfo = '/admin-center-server/commonUser/getResourceInfo'//回显
const updateRoleResource = '/admin-center-server/commonUser/updateRoleResource'//修改
export default {
  data () {
    return {
      type: '',
      id: '',
      checked: false, //checkbox的值
      checkedID: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      data: [],
      form: {
        user: ''
      },
      rules: {
        user: [
          { required: true, message: '请输入角色名称', trigger: 'blur' },
        ]
      },
    };
  },
  methods: {
    handleNodeClick (node, value) {
      this.data2 = value.checkedNodes
      console.log(this.data2)
    },
    getCheckedKeys (formName) {

      var that = this
      this.$refs[formName].validate(valid => {
        if (valid) {
          var v = that.form
          var ids = this.$refs.tree.getCheckedKeys();
          var arr = [];
          for (var i = 0; i < this.data2.length; i++) {
            if (this.data2[i].children) {
              if (this.data2[i].children.length == 0) {
                arr.push(this.data2[i].id)
              }
            } else {
              arr = this.data2
            }
          }
          arr = arr.join('#')

          var data = {
            userType: 1,
            resourcesIds: arr,
            roleId: this.id ? this.id : '',
            roleName: v.user
          }
          if (this.id) {
            this.$http.post(updateRoleResource, data).then(res => {
              var that = this
              if (res.data.code == '200') {
                this.$message({
                  message: res.data.message,
                  type: 'success'
                });
                setTimeout(function () {
                  that.$router.push("/consignorSubAccount/roleManagement?type=1");
                }, 3000);
              } else {
                this.$message.error(res.data.message);
              }
            })
          } else {
            this.$http.post(addRole, data).then(res => {
              var that = this
              if (res.data.code == '200') {
                this.$message({
                  message: res.data.message,
                  type: 'success'
                });
                setTimeout(function () {
                  that.$router.push("/consignorSubAccount/roleManagement?type=1");
                }, 3000);
              } else {
                this.$message.error(res.data.message);
              }
            })
          }

        } else {
          console.log('error submit!!')
          return false
        }
      })

    },
    checkedAll () {
      if (this.checked) {
        //全选
        this.$refs.tree.setCheckedNodes(this.data);
      } else {
        //取消选中
        this.$refs.tree.setCheckedKeys([]);
      }
    },
    getdata () {
      this.$http.get(getResourceTree + '?userType=' + this.type).then(res => {
        this.data = res.data.data
        if (this.id) {
          this.$http.get(getResourceInfo + '?roleId=' + this.id).then(res => {
            this.checkedID = res.data.data.resourcesIds.split('#')
            this.data2 = this.checkedID
            this.form.user = res.data.data.roleName
          })

        }
      })


    }
  },
  created () {
    this.id = this.$route.query.id;
    this.type = this.$route.query.type;
    this.getdata()
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.el-button {
  width: 100%;
  margin: 20px auto;
}
.el-container {
  width: 600px;
}
</style>
